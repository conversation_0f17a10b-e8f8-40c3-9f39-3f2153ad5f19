<div class="card">
    <div class="card-body">
        <div class="row">
            <div class="col-12">
                <h1>{{title}}</h1>
                <form method='POST' id="generalForm">
                    <div class="row">
                        <div class="col-md-2">
                            <label for="filter_company">Client</label>
                            <input class='form-control' type='text' name='filter_company' id='filter_company'
                                   value="{{filters.filter_company}}"/>
                        </div>
                        <div class="col-md-2">
                            <label for="filter_masterclient">Masterclient</label>
                            <input class='form-control' type='text' name='filter_masterclient'
                                   id='filter_masterclient' value="{{filters.filter_masterclient}}"/>
                        </div>
                        <div class="col-md-2">
                            <label for="filter_range_start">Submitted after:</label>
                            <input class='form-control' type='date' name='filter_range_start'
                                   id='filter_range_start' value="{{filters.filter_range_start}}"/>
                        </div>
                        <div class="col-md-2">
                            <label for="filter_range_end">Submitted before:</label>
                            <input class='form-control' type='date' name='filter_range_end'
                                   id='filter_range_end' value="{{filters.filter_range_end}}"/>
                        </div>

                        <div class="col-md-4" style="padding-top:30px">
                            <input type='SUBMIT' class='btn btn-light btn-sm waves-effect' value='Search'/>
                        </div>
                    </div>
                    <br>
                    <label for="filter_referral">Relevant Activities</label>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                       name="relevant_activities" id="none" value="none"
                                    {{#if filters.relevantActivities.none }} checked {{/if}} />
                                <label class="custom-control-label" for="none">None</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                       name="relevant_activities" id="banking_business"
                                       value="Banking Business"
                                    {{#if filters.relevantActivities.banking_business }} checked
                                    {{/if}} />
                                <label class="custom-control-label" for="banking_business">Banking
                                    Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                       name="relevant_activities" id="insurance_business"
                                       value="Insurance Business"
                                    {{#if filters.relevantActivities.insurance_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label" for="insurance_business">Insurance
                                    Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                       name="relevant_activities" id="fund_management_business"
                                       value="Fund management Business"
                                    {{#if filters.relevantActivities.fund_management_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label" for="fund_management_business">Fund
                                    Management Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                       name="relevant_activities" id="service_centre_business"
                                       value="Distribution and service centre Business"
                                    {{#if filters.relevantActivities.service_centre_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label"
                                       for="service_centre_business">Service Centre
                                    Business</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                       name="relevant_activities" id="finance_leasing_business"
                                       value="Finance and leasing Business"
                                    {{#if filters.relevantActivities.finance_leasing_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label"
                                       for="finance_leasing_business">Finance/Leasing Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                       name="relevant_activities" id="headquarters_business"
                                       value="Headquarters Business"
                                    {{#if filters.relevantActivities.headquarters_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label"
                                       for="headquarters_business">Headquarters
                                    Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                       name="relevant_activities" id="shipping_business"
                                       value="Shipping Business"
                                    {{#if filters.relevantActivities.shipping_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label" for="shipping_business">Shipping
                                    Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                       name="relevant_activities" id="holding_business"
                                       value="Holding Business (Pure Equity Holding entities)"
                                    {{#if filters.relevantActivities.holding_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label" for="holding_business">Holding
                                    Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input"
                                       name="relevant_activities" id="intellectual_property_business"
                                       value="Intellectual property Business"
                                    {{#if filters.relevantActivities.intellectual_property_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label"
                                       for="intellectual_property_business">Intellectual Property
                                    Business</label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label for="filter_other">Other</label>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input"
                                               name="resident_outside_bvi" id="resident_outside_bvi" value="Yes"
                                            {{#if filters.resident_outside_bvi }} checked {{/if}} />
                                        <label class="custom-control-label" for="resident_outside_bvi">Tax residency
                                            claim outside BVI</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <br>
                <br>

                <table id="selection-datatable" class="table dt-responsive nowrap">
                    <thead>
                    <tr>
                        <th class="all">ID</th>
                        <th class="all">Name</th>
                        <th class="all">Status</th>
                        <th class="all">Date created</th>
                        <th class="all">Export date</th>
                        <th class="all">Payment method</th>
                        <th class="all">Payment Received</th>
                        <th class="all">Payment Reference</th>
                        <th class="all">Export files</th>
                    </tr>
                    </thead>


                    <tbody>
                    {{#each data}}
                        <tr data-id="{{_id}}">
                            <td>{{_id}}</td>
                            <td>{{name}}</td>
                            <td>{{status}}</td>
                            <td>{{formatDate createdAt ../STANDARD_DATE_FORMAT}}</td>
                            <td>{{formatDate exportedAt ../STANDARD_DATE_FORMAT}}</td>
                            <td>{{payment.type}}</td>
                            <td>{{formatDate payment.paidAt ../STANDARD_DATE_FORMAT}}</td>
                            <td>{{payment.reference}}</td>
                            <td style="vertical-align: middle;">
                                {{#if authentication.isClientManagementSuperUser}}
                                <input type="button" name="exportFilesBtn" id="export_files_{{_id}}"
                                       class="btn btn-primary waves-effect waves-light"
                                       onclick="event.stopPropagation(); exportFiles('{{_id}}')" value="Export">
                                {{/if}}
                            </td>
                        </tr>
                    {{/each}}

                    </tbody>
                </table>

            </div>
        </div>
        <div class="row">
            <div class="col-12">&nbsp;</div>
        </div>
        <div class="row">
            <div class="col-12 text-sm-center form-inline">
                <div class="ml-3 mr-2">
                    <a href='/client-management/'
                       class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                </div>
                <div class="form-group mr-2">
                    {{#if authentication.isClientManagementSuperUser}}
                    <button id="btn-export-incorporation-xls" class="btn btn-primary" style="display: none;"><i
                            class="mdi mdi mdi-content-save-edit mr-2"></i> Export to xls
                    </button>
                    {{/if}}
                </div>
                <!--<div class="form-group">
                    <input id="demo-input-search2" type="text" placeholder="Search" class="form-control" autocomplete="off">
                </div>-->
            </div>
        </div>

    </div>
</div>

<form method="POST" action="./export" id="submitForm" name="submitForm">
    <input type="hidden" name="entryIds" value=""/>
</form>

<form method="POST" action="./export-incorporations/export-xls" id="submitIncorporationXls" name="submitIncorporationXls">
    <input type="hidden" name="incorporationIds" value=""/>
</form>

<form method="POST" action="./export-incorporations/export-evidences" id="submitFormFiles" name="submitFormFiles">
    <input type="hidden" name="incorporationId" value=""/>
</form>

<script type="text/javascript">
    $(document).ready(function () {

        let showLimitAlert = "{{showLimitAlert}}" === 'true';
        if (showLimitAlert) {
            toastr["warning"]('Maximum number of records reached. Please refine your search to reduce the size of query.', 'Limit reached!', {
                "timeOut": 100000,
            });
        }

        //$("#basic-datatable").DataTable({language:{paginate:{previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"}},drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")}});var a=$("#datatable-buttons").DataTable({lengthChange:!1,buttons:["copy","print","pdf"],language:{paginate:{previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"}},drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")}});
        let table = $("#selection-datatable").DataTable({
            dom: 'Blfrtip',

            "columnDefs": [{"visible": false, "targets": [0]}],

            select: {style: "multi"},
            buttons: [{
                text: 'Select All On Page',
                action: function () {
                    table.rows({
                        page: 'current'
                    }).select();
                }
            },
                {
                    text: '<i class="far fa-square"></i>',
                    titleAttr: 'unselect all',
                    action: function () {
                        table.rows({
                            page: 'current'
                        }).deselect();
                    }
                }],
            language: {
                paginate: {previous: "<i class='mdi mdi-chevron-left'>", next: "<i class='mdi mdi-chevron-right'>"}
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
            }
        })

        table.on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                if (table.rows('.selected').data().length) {
                    $('#btn-export-incorporation-xls').show();
                    // $('#btn-export-files').show();
                    // $('#btn-export-invoicing').show();
                } else {
                    $('#btn-export-incorporation-xls').hide();
                    // $('#btn-export-files').hide();
                    // $('#btn-export-invoicing').hide();
                }
            }
        });
        table.on('deselect', function (e, dt, type, indexes) {
            if (type === 'row') {
                if (table.rows('.selected').data().length) {
                    $('#btn-export-incorporation-xls').show();
                    // $('#btn-export-files').show();
                    // $('#btn-export-invoicing').show();
                } else {
                    $('#btn-export-incorporation-xls').hide();
                    // $('#btn-export-files').hide();
                    // $('#btn-export-invoicing').hide();
                }
            }
        });

        $('#btn-export-incorporation-xls').click(function () {
            let data = table.rows('.selected').data();

            let incorporationIds = [];
            for (let idx = 0; idx < data.length; idx++) {
                incorporationIds.push(data[idx][0])
            }
            let xlsForm = document.forms["submitIncorporationXls"];
            xlsForm.elements["incorporationIds"].value = incorporationIds.join(";");
            xlsForm.submit();
        });
    });

    function exportFiles(id) {
        var filesForm = document.forms["submitFormFiles"];
        filesForm.elements["incorporationId"].value = id;
        filesForm.submit();
    }
</script>

<main class="">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h1>{{title}}</h1>
                        <br>
                        <!-- STATISTICS -->
                        <div class="container-fluid p-0">
                            <div class="row">
                                <div class="col-12  ">
                                    <div class="card-group w-100 d-flex flex-wrap text-right">
                                        <div class="card  border mr-2 mb-2 fr-statistics-cards">
                                            <div class="card-header pt-2 pb-1 bg-gray h-50px">
                                                <h5>Total unassigned</h5>
                                            </div>
                                            <div class="card-body p-1">
                                                <p class="card-text">{{statistics.totalUnassigned}}</p>
                                            </div>
                                        </div>
                                        <div class="card  border mr-2  mb-2 fr-statistics-cards">
                                            <div class="card-header pt-2 pb-1 bg-info h-50px">
                                                <h5>Total in progress</h5>
                                            </div>
                                            <div class="card-body p-1">
                                                <p class="card-text">{{statistics.totalAssigned}}</p>
                                            </div>
                                        </div>
                            
                                        <div class="card  border mr-2  mb-2 fr-statistics-cards">
                                            <div class="card-header pt-2 pb-1  bg-info h-50px">
                                                <h5>Total in progress by myself</h5>
                                            </div>
                                            <div class="card-body p-1">
                                                <p class="card-text">{{statistics.totalAssignedMe}}</p>
                                            </div>
                                        </div>
                                        <div class="card border mr-2  mb-2 fr-statistics-cards">
                                            <div class="card-header pt-2 pb-1  bg-success h-50px">
                                                <h5>Total completed</h5>
                                            </div>
                                            <div class="card-body p-1">
                                                <p class="card-text">{{statistics.totalCompleted}}</p>
                                            </div>
                                        </div>
                            
                            
                                        <div class="card border mr-2  mb-2 fr-statistics-cards">
                                            <div class="card-header pt-2 pb-1 bg-success h-50px">
                                                <h5>Total completed by myself</h5>
                                            </div>
                                            <div class="card-body p-1">
                                                <p class="card-text">{{statistics.totalCompletedMe}}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-100 d-block d-md-none"></div>
                            </div>
                        </div>
                        <div class="container-fluid p-0">
                            <div class="row">
                                <div class="col-12  ">
                                    <div class="card-group w-100 text-right">
                                        <div class="card border mr-2 mb-2 fr-statistics-cards">
                                            <div class="card-header pt-2 pb-1 bg-gray h-50px">
                                                <h5>Total exempted companies under review</h5>
                                            </div>
                                            <div class="card-body p-1">
                                                <p class="card-text">{{statistics.totalExemptedUnderReview}}</p>
                                            </div>
                                        </div>
                        
                                        <div class="card border  mr-2  mb-2 fr-statistics-cards">
                                            <div class="card-header pt-2 pb-1 bg-success h-50px">
                                                <h5>Total exempted companies confirmed</h5>
                                            </div>
                                            <div class="card-body p-1">
                                                <p class="card-text">{{statistics.totalExemptedConfirmed}}</p>
                                            </div>
                                        </div>
                                         <div class="card border mr-2  mb-2 fr-statistics-cards">
                                            <div class="card-header pt-2 pb-1 bg-success h-50px">
                                                <h5>Total in penalty </h5>
                                            </div>
                                            <div class="card-body p-1">
                                                <p class="card-text">{{statistics.totalInPenalty}}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="w-100 d-block d-md-none"></div>
                            </div>
                        </div>

                        <hr>

                        <!-- FILTERS -->
                        <form method='GET' id="officerQueueFiltersForm" action="?page={{pagination.pageNumber}}&pageSize={{pagination.pageSize}}">
                            <div class="row gx-1">
                                <div class="col-12 col-md-6 col-lg-4 col-xl-2  ">
                                    <label for="filterCompanyName">Entity Name</label>
                                    <input class='form-control filter' type='text' name='companyName' id='filterCompanyName'
                                        value="{{filters.companyName}}" />
                                </div>

                                <div class="col-12 col-md-6 col-lg-4 col-xl-2  ">
                                    <label for="filterMasterclient">Master Client Code</label>
                                    <input class='form-control filter' type='text' name='masterclientcode' id='filterMasterclient'
                                        value="{{filters.masterclientcode}}" />
                                </div>

                                <div class="col-12 col-md-6 col-lg-4 col-xl-2 ">
                                    <label for="filterSubmittedDateRange">
                                        Submitted date
                                        <i class="fa fa-info-circle fa-lg text-info " data-toggle="tooltip" data-placement="top"
                                            title="Search within a submission date range"></i>
                                    </label>
                                    <input class='form-control filter' type='text' name='submittedDateRange' id='filterSubmittedDateRange'
                                        autocomplete="off" {{#if filters.submittedDateRange}}
                                        value="{{filters.submittedDateRange.start}} - {{filters.submittedDateRange.end}}" {{else}} value="" {{/if}} />
                                </div>


                                <div class="col-12 col-xl-6   mb-1">
                                    <label for="filterType">Status</label> <br>
                                    <div class="d-lg-flex  justify-content-left">
                                        <div class="checkbox-list mr-2">
                                      
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStateAssignedMe" class="custom-control-input filter" name="states[]"
                                                    value="ASSIGNED_ME" {{#ifContains 'ASSIGNED_ME' filters.states }} checked {{/ifContains}}>
                                                <label for="filterStateAssignedMe" class="custom-control-label">ASSIGNED - ME</label>
                                            </div>

                                            {{#if permissions.searchAll}}

                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStateAssigned" class="custom-control-input filter" name="states[]"
                                                    value="ASSIGNED" {{#ifContains 'ASSIGNED' filters.states }} checked {{/ifContains}}>
                                                <label for="filterStateAssigned" class="custom-control-label">ASSIGNED  - ALL</label>
                                            </div>

                                            {{/if}}


                                        </div>

                                        <div class="checkbox-list mr-2">
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStateCompletedMe" class="custom-control-input filter" name="states[]"
                                                    value="COMPLETED_ME" {{#ifContains 'COMPLETED_ME' filters.states }} checked {{/ifContains}}>
                                                <label for="filterStateCompletedMe" class="custom-control-label">COMPLETED - ME</label>
                                            </div>
                                            
                                            {{#if permissions.searchAll}}
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStateCompleted" class="custom-control-input filter" name="states[]"
                                                    value="COMPLETED" {{#ifContains 'COMPLETED' filters.states }} checked {{/ifContains}}>
                                                <label for="filterStateCompleted" class="custom-control-label">COMPLETED - ALL</label>
                                            </div>
                                            {{/if}}
                                        </div>

                                        <div class="checkbox-list "> 
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStateUnassigned" class="custom-control-input filter" name="states[]"
                                                    value="UNASSIGNED" {{#ifContains 'UNASSIGNED' filters.states }} checked {{/ifContains}}>
                                                <label for="filterStateUnassigned" class="custom-control-label">UNASSIGNED</label>
                                            </div>

                                            {{#if permissions.searchExemptedCompanies}}
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStateConfirmedExempted" class="custom-control-input filter" name="states[]"
                                                    value="CONFIRMED_EXEMPTED" {{#ifContains 'CONFIRMED_EXEMPTED' filters.states }} checked {{/ifContains}}>
                                                <label for="filterStateConfirmedExempted" class="custom-control-label">CONFIRMED EXEMPTED COMPANIES</label>
                                            </div>
                                            {{/if}}
                                        </div>
                                    </div>

                                </div>
                            </div>
                            {{#if permissions.searchAll}}
                            <div class="row ">
                                <div class="col-12 col-md-6 col-lg-6 col-xl-4">
                                    <label for="filterUser">User</label> 
                                    <span class="fa-stack tooltip-wrapper" style="margin-top: -15px" data-toggle="tooltip" data-placement="right"
                                        title="Displayed users in the dropdown are specifically those who have been assigned a report or have completed one.">
                                        <i class="fa fa-info-circle fa-stack text-info fa-lg ml-1"></i>
                                    </span>
                                    <br>
                                    <select id="filterUser" class="form-control filter filter-array-field w-100" name="user">
                                        <option value="" disabled>Select an option</option>
                                        <option value="ALL" {{#ifEquals 'ALL' filters.user }} selected {{/ifEquals}}>All</option>
                                        {{#each filterUsers}}
                                        <option value="{{this}}" {{#ifEquals this ../filters.user }} selected {{/ifEquals}}>{{this}}</option>
                                        {{/each}}
                                    </select>
                                </div>
                            </div>
                            {{/if}}

                            <div class="row mt-3">
                                <div class="col-12  ">
                                        <button type="submit" form="officerQueueFiltersForm" class='btn btn-primary waves-effect mr-2 px-3'>
                                            Search
                                        </button>
                                        <button type="button" form="officerQueueFiltersForm" id="clearFormBtn"
                                            class='btn btn-secondary waves-effect '>
                                            Reset filters
                                        </button>
                                        <div class="btn-group" role="group">
                                            <button type="button" id="selectAllTableBtn" class='btn btn-secondary waves-effect '>Select All On Page</button>
                                            <button type="button" id="unselectAllTableBtn" class='btn btn-secondary waves-effect ' title='unselect all'>
                                                <i class="far fa-square"></i>
                                            </button>
                                        </div>
                                </div>
                            </div>
                        </form>
                        <br /><br />

                        <!-- TABLE RESULTS -->
                        {{>shared/table-pagination pagination=result formName="officerQueueFiltersForm" tableId="financialReportsTable" searching="true"}}
                        <table id="financialReportsTable" class="table w-100 nowrap">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Entity Name</th>
                                    <th>Incorporation Number</th>
                                    <th>Master Client Code</th> 
                                    <th>FP Start Date</th>
                                    <th>FP End Date</th>
                                    <th>Created</th>
                                    <th>Submitted</th>
                                    <th>Officer</th>
                                    <th>Status</th>
                                    <th></th>
                                    <th></th>
                                    {{#if permissions.assignRequest}}
                                    <th></th>
                                    {{/if}}
                                    <th></th>
                                    
                                    
                                </tr>
                            </thead>
                            <tbody>
                                {{#each result.data}}
                                <tr data-allow-select="{{allowReassignment}}">
                                    <td>{{_id}}</td>
                                    <td>{{companyName}}</td>
                                    <td>{{incorporationNumber}}</td>
                                    <td>{{masterClientCode}}</td>
                                    <td>
                                        {{formatDate financialPeriodStart ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td>
                                        {{formatDate financialPeriodEnd ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td>
                                        {{formatDate createdAt ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td>
                                        {{formatDate submittedAt ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td>{{currentOfficer}}</td>
                                    <td>{{status}}</td>
                                    <td>
                                        {{#if canPick}}
                                        <button type="button" class="btn btn-primary waves-effect waves-light width-md border-white rounded pickReportBtn" 
                                            data-report-id="{{_id}}">
                                            Pick
                                        </button>
                                        {{/if}}

                                        {{#if canOpen}}
                                            <a href="/financial-report-management/{{_id}}" 
                                                class="btn btn-primary waves-effect waves-light width-md text-white border-white rounded">Open</a>
                                        {{/if}}

                                        {{#if ../permissions.aproveRequest}}
                                            {{#if enableApprovalFlow}}
                                            <button type="button" class="btn btn-primary waves-effect waves-light width-md border-white rounded"
                                                data-target="#approveExemptCompanyModal" data-toggle="modal" data-report-id="{{_id}}">
                                                Approve
                                            </button>
                                            {{/if}}
                                        {{/if}}

                                    </td>
                                    <td>
                                        {{#if enablePdfDownload}}
                                        <a href="/financial-report-management/{{_id}}/report.pdf" target="_blank"
                                            class="btn btn-primary  waves-effect waves-light width-md text-white border-white rounded">Download</a>
                                        {{/if}}
                                    </td>

                                    {{#if ../permissions.assignRequest}}
                                    <td> 
                                        {{#if allowReassignment}}
                                        <button type="button" class="btn btn-primary waves-effect waves-light width-md border-white rounded assignReportBtn"
                                            data-report-id="{{_id}}">
                                            Assign
                                        </button>

                                        <button type="button" class="btn btn-primary waves-effect waves-light width-md border-white rounded declineRequestBtn"
                                            data-report-id="{{_id}}">
                                            Decline request
                                        </button>
                                        {{/if}}

                                        {{#if enablePenaltyAssign}}
                                            <button type="button" class="btn btn-primary waves-effect waves-light width-md border-white rounded assignReportBtn"
                                                data-report-id="{{_id}}">
                                                In Penalty Assign
                                            </button>
                                        {{/if}}

                                        {{#if enablePenaltyUnassign}}
                                        <button type="button" class="btn btn-primary waves-effect waves-light width-md border-white rounded unassignReportBtn"
                                            data-report-id="{{_id}}">
                                            In Good Standing
                                        </button>
                                        {{/if}}
                                    </td>
                                    {{/if}}

                                    <td>
                                        {{#if enableAttachmentsDownload}}
                                        <a href="/financial-report-management/{{_id}}/download-files" target="_blank"
                                            class="btn btn-primary waves-effect waves-light  text-white border-white rounded">Download support attachments</a>
                                        {{/if}}
                                    </td>
                                    
                                    
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                       
                        
                        <br>
                        <!--FOOTER BUTTONS-->
                        <div class="row">
                            <div class="col-12 mb-3 d-flex flex-row">
                                <div class="mr-2 ">
                                    <a href='/financial-report-management/' class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                                </div>

                                {{#if permissions.assignRequest}}
                                <div data-toggle="tooltip" data-placement="top" title="Re-assign all selected table entries">
                                    <button id="assignMultipleBtn" class="btn btn-primary width-lg " style="display: none;"
                                        data-toggle="modal" data-target="#assignBulkModal">
                                        Assign selected reports
                                    </button>
                                </div>
                                {{/if}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{>financial-report/assign-bulk-modal}}
    {{>financial-report/approve-modal}}
</main>


<script type='text/javascript' src='/javascripts/form-advanced.init.js'></script>

<script type="text/javascript">
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body',
        boundary: 'window'
    });
    let selectedId;
    let selectedCompany;
    let $financialReportsTable;

    $(document).ready(function () {
        $('#filterUser').select2();

        $("#filterSubmittedDateRange").flatpickr({
            mode: "range",
            dateFormat: "m/d/Y",
            autoclose: true,
            monthSelectorType: "dropdown",
            allowInput: true,
            locale: {
                rangeSeparator: ' - '
            },

        })

        $financialReportsTable = $("#financialReportsTable").DataTable({
            dom: "lrtip",
            columnDefs: [{ "visible": false, "targets": [0] }],
            scrollX: !0,
            select: { style: "multi" },
            paging:false,
            info: false,
            sort: false
        });

        $financialReportsTable.on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                const selectedRowData = $financialReportsTable.row(indexes).node();;
                const allowSelectRow = $(selectedRowData).data('allow-select');

                if (allowSelectRow !== true) {
                    $(selectedRowData).removeClass('selected');
                    e.preventDefault();
                    return false;

                }
                if ($financialReportsTable.rows('.selected').data().length) {
                    $("#assignMultipleBtn").show();
                } else {
                    $('#assignMultipleBtn').hide();
                }
            }
        });
        
        $financialReportsTable.on('deselect', function (e, dt, type, indexes) {
            if (type === 'row') {
                if ($financialReportsTable.rows('.selected').data().length) {
                    $("#assignMultipleBtn").show();
                } else {
                    $('#assignMultipleBtn').hide();
                }
            }
        });


        $("#selectAllTableBtn").on('click', function () {
            let showButton = false;
            $financialReportsTable.rows({ page: 'current' }).nodes().each((node) => {
                if ($(node).data('allow-select') === true) {
                    showButton = true;
                    $(node).addClass('selected');
                }
            })

            if (showButton) {
                $("#assignMultipleBtn").show();
            }

        })

        $("#unselectAllTableBtn").on('click', function () {
            $financialReportsTable.rows({ page: 'current' }).deselect();
            $("#assignMultipleBtn").hide();
        })

    });

    $("#clearFormBtn").on('click', () => {
        $("#officerQueueFiltersForm")[0].reset();
        $("#officerQueueFiltersForm input[type=text]").val('');
        $('#officerQueueFiltersForm input[type=checkbox]').prop('checked', false);
        $("#officerQueueFiltersForm select").val('');
        $("#filterStateAssignedMe").prop('checked', true);
        $("#filterStateCompletedMe").prop('checked', true);
        $("#filterStateCompleted").attr('disabled', true);
        $("#filterStateAssigned").attr('disabled', true);
        $("#filterUser").val('').trigger('change');
        $("#filterUser").attr('disabled', true);
    })

    $(".pickReportBtn").on('click', async function(e){
        $(".pickReportBtn").prop("disabled", true);
        e.preventDefault();

        const id = $(this).data('report-id');

        const result = await Swal.fire({
            type: 'info',
            title: 'Do you want to pick this report?',
            showCancelButton: true,
            confirmButtonText: 'Confirm',
            reverseButtons: true,
            confirmButtonColor: "#4938D7",
            showLoaderOnConfirm: true,
            allowOutsideClick: () => !Swal.isLoading(),
            backdrop: true,
            preConfirm: async (value) => {
                try {
                    const response = await $.ajax({
                        type: 'POST',
                        url: `/financial-report-management/${id}/assign`,
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        },
                        data: JSON.stringify({ autoAssign: true }) 
                    });

                    if (response.status === 200) {
                        return response;
                    } else {
                        Swal.fire('Error', response.error ? response.error : 'An error occurred while assigning the report to the officer', 'error');
                        
                        return false;
                    }
                } catch (error) {
                    Swal.fire('Error', 'An error occurred while assigning the report to the officer', 'error');
                    $(".pickReportBtn").prop("disabled", false);
                    return false;
                }
            }
        });
        if (result?.value?.status === 200) {
            Swal.fire('Success', result.value?.message ? result.value.message : 'Accounting record assigned successfully.', 'success').then(() => {
                if(result.value.openReport === true){
                    window.location.href = `/financial-report-management/${id}`;
                }else{
                    location.reload();
                }
                
            });
        }else{
            $(".pickReportBtn").prop("disabled", false);
        }


    })


    $(".assignReportBtn").on('click', async function (e) {
        $(".assignReportBtn").prop("disabled", true);
        $(".unassignReportBtn").prop("disabled", true);
        e.preventDefault();

        const id = $(this).data('report-id');

        const result = await Swal.fire({
            type: 'info',
            title: 'Assign this work to:',
            showCancelButton: true,
            confirmButtonText: 'Confirm',
            reverseButtons: true,
            confirmButtonColor: "#4938D7",
            showLoaderOnConfirm: true,
            backdrop: true,
            input: 'email',
            inputPlaceholder: 'Enter  the email officer',
            allowOutsideClick: () => !Swal.isLoading(),
            inputValidator: (result) => {
                if(!result || !validateEmailFormat(result)){
                    return 'Please enter a valid email'
                } 
            },
            preConfirm: async (newOfficerValue) => {
                try {
                    const response = await $.ajax({
                        type: 'POST',
                        url: `/financial-report-management/${id}/assign`,
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        },
                        data: JSON.stringify({ newOfficer: newOfficerValue, autoAssign: false  })
                    });
                    if (response.status === 200) {
                        return response;
                    } else {
                        Swal.fire('Error', response.error ? response.error : 'An error occurred while assigning the report to the officer', 'error');
                        $(".assignReportBtn").prop("disabled", false);
                        $(".unassignReportBtn").prop("disabled", false);
                        return false;
                    }
                } catch (error) {
                    console.log("error ", error);
                    Swal.fire('Error', error.responseJSON?.error ? error.responseJSON?.error :  'An error occurred while assigning the report to the officer', 'error');
                    $(".assignReportBtn").prop("disabled", false);
                    $(".unassignReportBtn").prop("disabled", false);
                    return false;
                }
            }
        });

        if (result?.value?.status === 200) {
            Swal.fire('Success', result.value?.message ? result.value.message : 'Accounting record assigned successfully.', 'success').then(() => {
                location.reload();
            });
        } else {
            $(".assignReportBtn").prop("disabled", false);
            $(".unassignReportBtn").prop("disabled", false);
        }
    })

    $(".unassignReportBtn").on('click', async function (e) {
        $(".assignReportBtn").prop("disabled", true);
        $(".unassignReportBtn").prop("disabled", true);
        e.preventDefault();

        const id = $(this).data('report-id');

        const result = await Swal.fire({
            type: 'info',
            title: 'I confirm that the Company has paid all penalties to the Registry and complete the pending submission',
            showCancelButton: true,
            confirmButtonText: 'Confirm',
            reverseButtons: true,
            confirmButtonColor: "#4938D7",
            showLoaderOnConfirm: true,
            backdrop: true,
            allowOutsideClick: () => !Swal.isLoading(),
            preConfirm: async (newOfficerValue) => {
                try {
                    const response = await $.ajax({
                        type: 'POST',
                        url: `/financial-report-management/${id}/unassign-in-penalty`,
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    });
                    if (response.status === 200) {
                        return response;
                    } else {
                        Swal.fire('Error', response.error ? response.error : 'An error occurred while attempting to unassign the in penalty report', 'error');
                        $(".unassignReportBtn").prop("disabled", true);
                        $(".assignReportBtn").prop("disabled", false);
                        return false;
                    }
                } catch (error) {
                    console.log("error ", error);
                    Swal.fire('Error', error.responseJSON?.error ? error.responseJSON?.error : 'An error occurred while attempting to unassign the in penalty report', 'error');
                    $(".unassignReportBtn").prop("disabled", true);
                    $(".assignReportBtn").prop("disabled", false);
                    return false;
                }
            }
        });

        if (result?.value?.status === 200) {
            Swal.fire('Success', result.value?.message ? result.value.message : 'Accounting record assigned successfully.', 'success').then(() => {
                location.reload();
            });
        } else {
            $(".assignReportBtn").prop("disabled", false);
            $(".unassignReportBtn").prop("disabled", false);
        }
    })


    $(".declineRequestBtn").on('click', async function (e) {
        $(".declineRequestBtn").prop("disabled", true);
        e.preventDefault();

        const id = $(this).data('report-id');

        const result = await Swal.fire({
            type: 'info',
            title: 'Do you want to decline this request?',
            showCancelButton: true,
            confirmButtonText: 'Confirm',
            reverseButtons: true,
            confirmButtonColor: "#4938D7",
            showLoaderOnConfirm: true,
            backdrop: true,
            input: 'textarea',
            inputPlaceholder: 'Enter the reason...',
            allowOutsideClick: () => !Swal.isLoading(),
            inputValidator: (result) => {
                if(!result || result.length === 0){
                    return 'Please enter a reason to decline the request'
                } 
            },
            preConfirm: async (reasonValue) => {
                try {
                    const response = await $.ajax({
                        type: 'POST',
                        url: `/financial-report-management/${id}/decline-request`,
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        },
                        data: JSON.stringify({ declineReason: reasonValue })
                    });
                    if (response.status === 200) {
                        return response;
                    } else {
                        Swal.fire('Error', response.error ? response.error : 'An error occurred while decline the request', 'error');
                        $(".declineRequestBtn").prop("disabled", false);
                        return false;
                    }
                } catch (error) {
                    console.log("error ", error);
                    Swal.fire('Error', error.responseJSON?.error ? error.responseJSON?.error :  'An error occurred while decline the request', 'error');
                    $(".declineRequestBtn").prop("disabled", false);
                    return false;
                }
            }
        });

        if (result?.value?.status === 200) {
            Swal.fire('Success', result.value?.message ? result.value.message : 'Accounting record assigned successfully.', 'success').then(() => {
                location.reload();
            });
        } else {
            $(".declineRequestBtn").prop("disabled", false);
        }
    })


    $("#filterStateAssignedMe").on('change', function(e){
        const isChecked = $(this).prop('checked');

        if(isChecked){
            $("#filterStateAssigned").prop('checked', false);
            $("#filterStateAssigned").prop('disabled', true);
            
            

            const isCompleted = $("#filterStateCompleted").prop('checked');
            $("#filterUser").val(isCompleted ? "ALL" : "").trigger('change');
            $("#filterUser").prop('disabled', true);

        }else{
            $("#filterStateAssigned").prop('disabled', false);
            
            const isCompletedMe = $("#filterStateCompletedMe").prop('checked');
            if(!isCompletedMe){
                $("#filterUser").prop('disabled', false);
            }
            
        }
    })

    $("#filterStateAssigned").on('change', function (e) {
        const isChecked = $(this).prop('checked');

        if (isChecked) {
            $("#filterStateAssignedMe").prop('checked', false);
            $("#filterStateAssignedMe").prop('disabled', true);
        } else {
            $("#filterStateAssignedMe").prop('disabled', false);
        }
    })

    $("#filterStateCompletedMe").on('change', function (e) {
        const isChecked = $(this).prop('checked');
        if (isChecked) {
            $("#filterStateCompleted").prop('checked', false);
            $("#filterStateCompleted").prop('disabled', true);

            const isAssigned = $("#filterStateAssigned").prop('checked');
            $("#filterUser").val(isAssigned ? "ALL" : "").trigger('change');
            $("#filterUser").prop('disabled', true);
        } else {
            $("#filterStateCompleted").prop('disabled', false);

            const isAssignedMe = $("#filterStateAssignedMe").prop('checked');
            if (!isAssignedMe) {
                $("#filterUser").prop('disabled', false);
            }
        }
    })

    $("#filterStateCompleted").on('change', function (e) {
        const isChecked = $(this).prop('checked');

        if (isChecked) {
            $("#filterStateCompletedMe").prop('checked', false);
            $("#filterStateCompletedMe").prop('disabled', true);
        } else {
            $("#filterStateCompletedMe").prop('disabled', false);
        }
    })

    function validateEmailFormat(email) {
        const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }
</script>

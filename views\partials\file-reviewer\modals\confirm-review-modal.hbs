{{!-- CONFIRM REVIEW MODAL --}}
<div class="modal fade" id="confirmReviewModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <div id="message_modal_validate_review">

                </div>
                <div class="mt-1 text-justify">
                    <label for="modalInputReviewComment"><small></small></label> <br>
                    <textarea class="form-control" name="modalInputReviewComment" id="modalInputReviewComment"
                              placeholder="Add comment..." rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="button" class="btn solid royal-blue" id="confirmReviewButton">Confirm</button>
            </div>
        </div>
    </div>
</div>

<script>
    let companyIdReview = '';
    let statusReview;
    let commentReview = '';
    let officer = '';
    const modalInfoValidateReview = {
        "validateReview": {
            "modalMessage": 'You are about to submit the File Review to a <b>Quality Assurance Officer.</b>',
            "validatedMessage": 'You are about to submit the File Review to a <b>Quality Assurance Officer.</b> Are you sure? Not everything is marked as complete.',
            "successMessage": 'The review was submitted successfully',
            "errorMessage": 'There was an error submitting your review'
        },
        "validateCompliance": {
            "modalMessage": 'Are you sure you want to validate this file review?',
            "successMessage": 'The review was submitted successfully',
            "errorMessage": 'There was an error submitting your review'
        }
    };
    $('#confirmReviewModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        companyIdReview = button.data('id');
        statusReview = button.data('status');
        officer = button.data('officer');
        if (officer === "fileReviewer"){
            $.ajax({
                type: 'POST',
                url: '/file-reviewer/open-file-review/' + companyIdReview + '/validate-review',
                data: {},
                success: function (response) {
                    if (response.success) {
                        if (response.isIncomplete === true) {
                            $('#message_modal_validate_review').html(modalInfoValidateReview[statusReview].validatedMessage);
                        } else {
                            $('#message_modal_validate_review').html(modalInfoValidateReview[statusReview].modalMessage);
                        }

                    } else {
                        $('#message_modal_validate_review').html(modalInfoValidateReview[statusReview].errorMessage);
                    }

                },
                error: function () {
                    Swal.fire('Error', modalInfoValidateReview[statusReview].errorMessage, 'error').then(() => {
                        $('#confirmValidateModal').modal('hide');
                    });
                },
            });
        }
        else{
            $('#message_modal_validate_review').html(modalInfoValidateReview[statusReview].modalMessage);
        }

    });

    $('#confirmReviewButton').on('click', function () {
        commentReview = $('#modalInputReviewComment').val();

        if (officer === "fileReviewer") {
            $.ajax({
                type: 'POST',
                url: '/file-reviewer/submit-file-review',
                data: {
                    companyId: companyIdReview, status: statusReview, comment: commentReview
                },
                success: () => {
                    Swal.fire('Success', modalInfoValidateReview[statusReview].successMessage, 'success').then(() => {
                        location.href = '/file-reviewer/file-review-list';
                    });
                },
                error: (err) => {
                    $('#confirmReviewModal').modal('hide');
                    Swal.fire('Error', modalInfoValidateReview[statusReview].errorMessage, 'error');
                },
            });
        }
        else{
            $.ajax({
                type: 'POST',
                url: '/file-reviewer/compliances/' + companyIdReview + '/validate',
                data: {
                    status: 'compliance', comment: commentReview
                },
                success: () => {
                    Swal.fire('Success', modalInfoValidateReview[statusReview].successMessage, 'success').then(() => {
                        location.href = '/file-reviewer/compliances';
                    });
                },
                error: (err) => {
                    $('#confirmReviewModal').modal('hide');
                    Swal.fire('Error', modalInfoValidateReview[statusReview].errorMessage, 'error');
                },
            });
        }


    });


</script>

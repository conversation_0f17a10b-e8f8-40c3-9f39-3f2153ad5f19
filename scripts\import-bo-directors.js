const BeneficialOwnerModel = require('../models/beneficialowners');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const xlsx = require('xlsx');
const fs = require('fs');
const moment = require('moment');
const {countries} = require('../utils/constants');
dotenv.config();


try {
  mongoose.connect(process.env.MONGODB, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    useFindAndModify: false,
  }, function (err) {
    throw err;
  });
  const db = mongoose.connection;
  db.on('error', console.error.bind(console, 'MongoDB connection error:'));
  // Start proccess - Process submissions from excel file
  const submissions = processSubmissions();
  // Search imported submissions in database, update if found
  // and create new if not
  if (submissions.length > 0){
    importBeneficialOwners(submissions).then(r => {
      console.log("IMPORT FINISH ", r)
    });
  }

} catch (e) {
  console.log(e);
}

function processSubmissions() {
  try {
    const data = new Uint8Array(fs.readFileSync('FS-S-D part11.xlsx'));
    const workbook = xlsx.read(data, {
      type: "array",
      cellText: false,
      cellDates: true,
    });

    let beneficialOwners = [];
    let naturalPerson = {
      principalAddress: {}, 
      residentialAddress: {},
      officeAddress: {},
      corporation: {}
    };
    // Each sheet name is a vaccination date
    for (const beneficial of workbook.SheetNames) {
      const worksheet = workbook.Sheets[beneficial];
      // Map worksheet as submission array
      for (let cell in worksheet) {
        let cellAsString = cell.toString();
        // SAVE TITLES FOR VALIDATION
        const cellNum = cellAsString.match(/\d+/g);
        if (cellAsString[1] !== "r" && cellAsString[1] !== "m" && cellNum > 1) {
          cellAsString = cellAsString.replace(/[0-9]/g, '');
          if (cellAsString === "A") {
            naturalPerson.code = worksheet[cell].v;
          }
          if (cellAsString === "B") {
            naturalPerson.companyNumber = worksheet[cell].v;
          }
          if (cellAsString === "C") {
            naturalPerson.companyName = worksheet[cell].v;
          }
          if (cellAsString === "D") {
            naturalPerson.type = worksheet[cell].v;
          }
          if (cellAsString === "E") {
            const appointmentDate = worksheet[cell].v;
            naturalPerson.appointmentDate =  appointmentDate ? new Date(moment(appointmentDate).format('YYYY-MM-DD')) : undefined;
          }
          if (cellAsString === "F") {
            const ceasedDate = worksheet[cell].v;
            naturalPerson.ceasedDate = ceasedDate ? new Date(moment(ceasedDate).format('YYYY-MM-DD')) : undefined;
          }
          if (cellAsString === "G") {
            naturalPerson.firstName = worksheet[cell].v;
          }
          if (cellAsString === "H") {
            naturalPerson.middleName = worksheet[cell].v;
          }
          if (cellAsString === "I") {
            naturalPerson.lastName = worksheet[cell].v;
          }
          if (cellAsString === "J") {
            naturalPerson.formerFirstName = worksheet[cell].v;
          }
          if (cellAsString === "K") {
            naturalPerson.formerMiddleName = worksheet[cell].v;
          }
          if (cellAsString === "L") {
            naturalPerson.formerLastName = worksheet[cell].v;
          }
          if (cellAsString === "M") {
            const dob =  worksheet[cell].v;
            naturalPerson.dateOfBirth = dob ? new Date(moment(dob).format('YYYY-MM-DD')) : undefined;
          }
          if (cellAsString === "N") {
            const placeOfBirthCode = worksheet[cell].v;
            let placeOfBirth;
            if(placeOfBirthCode && placeOfBirthCode.length > 0){
              placeOfBirth = countries.find((c) => c.alpha_2_code.toUpperCase() === placeOfBirthCode.toUpperCase());
            }
            naturalPerson.placeOfBirth = placeOfBirth ? placeOfBirth.name : placeOfBirthCode;
          }
          if (cellAsString === "O") {
            const nationality =  worksheet[cell].v;
            let country;
            if(nationality && nationality.length > 0){
              country = countries.find((c) => c.nationality.some((item) => item === nationality.toLowerCase()));
            }
            naturalPerson.nationality = country ? country.name : nationality;
          }
          if (cellAsString === "P") {
            naturalPerson.principalAddress.primaryAddress = worksheet[cell].v;
          }
          if (cellAsString === "Q") {
            naturalPerson.principalAddress.secondaryAddress = worksheet[cell].v;
          }
          if (cellAsString === "R") {
            naturalPerson.principalAddress.city = worksheet[cell].v;
          }
          if (cellAsString === "S") {
            naturalPerson.principalAddress.state = worksheet[cell].v;
          }
          if (cellAsString === "T") {
            naturalPerson.principalAddress.postalCode = worksheet[cell].v;
          }
          if (cellAsString === "U") {
            const countryCode =  worksheet[cell].v;
            let country;
            if(countryCode && countryCode.length > 0){
              country = countries.find((c) => c.alpha_2_code.toUpperCase() === countryCode.toUpperCase());
            }
            naturalPerson.principalAddress.country  = country ? country.name : countryCode;
          }
          if (cellAsString === "V") {
            naturalPerson.residentialAddress.primaryAddress = worksheet[cell].v;
          }
          if (cellAsString === "W") {
            naturalPerson.residentialAddress.secondaryAddress = worksheet[cell].v;
          }
          if (cellAsString === "X") {
            naturalPerson.residentialAddress.city = worksheet[cell].v;
          }
          if (cellAsString === "Y") {
            naturalPerson.residentialAddress.state = worksheet[cell].v;
          }
          if (cellAsString === "Z") {
            naturalPerson.residentialAddress.postalCode = worksheet[cell].v;
          }
          if (cellAsString === "AA") {
            const countryCode =  worksheet[cell].v;
            let country;
            if(countryCode && countryCode.length > 0){
              country = countries.find((c) => c.alpha_2_code.toUpperCase() === countryCode.toUpperCase());
            }
            naturalPerson.residentialAddress.country  = country ? country.name : countryCode;
          }
          if (cellAsString === "AB") {
            naturalPerson.corporation.corporateName = worksheet[cell].v;
          }
          if (cellAsString === "AC") {
            naturalPerson.corporation.corporateNumber = worksheet[cell].v;
          }
          if (cellAsString === "AD") {
            naturalPerson.corporation.entityType = worksheet[cell].v;
          }
          if (cellAsString === "AE") {
            naturalPerson.corporation.corporateDate = worksheet[cell].v ?
              worksheet[cell].v : null;
          }
          if (cellAsString === "AF") {
            naturalPerson.corporation.corporatePlace = worksheet[cell].v;
          }
          if (cellAsString === "AG") {
            naturalPerson.officeType = worksheet[cell].v;
          }
          if (cellAsString === "AH") {
            naturalPerson.officeAddress.primaryAddress = worksheet[cell].v;
          }
          if (cellAsString === "AI") {
            naturalPerson.officeAddress.secondaryAddress = worksheet[cell].v;
          }
          if (cellAsString === "AJ") {
            naturalPerson.officeAddress.city = worksheet[cell].v;
          }
          if (cellAsString === "AK") {
            naturalPerson.officeAddress.state = worksheet[cell].v;
          }
          if (cellAsString === "AL") {
            naturalPerson.officeAddress.postalCode = worksheet[cell].v;
          }
          if (cellAsString === "AM") {
            const countryCode =  worksheet[cell].v;
            let country;
            if(countryCode && countryCode.length > 0){
              country = countries.find((c) => c.alpha_2_code.toUpperCase() === countryCode.toUpperCase());
            }
            naturalPerson.officeAddress.country  = country ? country.name : countryCode;
          }
          if (cellAsString === "AN") {
            naturalPerson.email = worksheet[cell].v;
          }
          if (cellAsString === "AO") {
            const createdAt = worksheet[cell].v;
            naturalPerson.createdAt = createdAt ? new Date(moment(createdAt).format('YYYY-MM-DD')) : new Date();
          }
          if (cellAsString === "AP") {
            const submittedAt = worksheet[cell].v;
            naturalPerson.submittedAt = submittedAt ? new Date(moment(submittedAt).format('YYYY-MM-DD')) : undefined;
          }
          if (cellAsString === "AQ") {
            const updatedAt = worksheet[cell].v;
            naturalPerson.updatedAt = updatedAt ? new Date(moment(updatedAt).format('YYYY-MM-DD')) : new Date();
          }
          if (cellAsString === "AR") {
            const fileDate = worksheet[cell].v;
            naturalPerson.personOrEntity = 'PERSON';
            naturalPerson.fileDate = fileDate ? new Date(moment(fileDate).format('YYYY-MM-DD')) : undefined;
            beneficialOwners.push(naturalPerson);

            naturalPerson = {
              principalAddress: {},
              residentialAddress: {},
              officeAddress: {},
              corporation: {}
            };
          }
        }

      }
    }
    return beneficialOwners;
  } catch (e) {
    console.log("Error processing xlsx data: ", e);
    return []
    }

}

async function importBeneficialOwners(submissions) {
  try {
    const importLog = [['ID','Code', 'Last Name', 'DOB', 'Import date', 'Action']];
    for (let submission of submissions) {
      let regExLastname = '^' + submission.lastName + '$';
      const application = await BeneficialOwnerModel.findOne({
        'lastName': {'$regex': regExLastname, $options: 'i'},
        'dateOfBirth': submission.dateOfBirth
      });
      if (application) {
          // Application found but had both vaccines already applied, then skip
          importLog.push([
            application._id.toString(),
            application.code,
            application.lastName,
            application.dateOfBirth,
            new Date(),
            'SKIPPED'
          ]);
      } else {
        // Application not found, then create new one
        const newApplication = new BeneficialOwnerModel(submission);
        newApplication.partitionkey = 'beneficialowner';
        const savedApplication = await newApplication.save();
        importLog.push([
          savedApplication._id.toString(),
          savedApplication.code,
          savedApplication.lastName,
          savedApplication.dateOfBirth,
          new Date(),
          'CREATED NEW BO/DIRECTOR'
        ]);
      }
    }
    if (importLog.length - 1 === submissions.length) {
      const filename = 'import_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
      const logWorkbook = xlsx.utils.book_new();
      const logWorksheet = xlsx.utils.aoa_to_sheet(importLog);
      xlsx.utils.book_append_sheet(logWorkbook, logWorksheet, 'Import ' + moment.utc().format('YYYY-MM-DD'));
      xlsx.writeFile(logWorkbook, filename);
    }
    return {"success":  true};
  } catch (e) {
    console.log(e);
    return {"success":  false};
  }
}



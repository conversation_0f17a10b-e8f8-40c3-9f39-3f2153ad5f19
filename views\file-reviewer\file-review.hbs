<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            Company Name:<span class="font-weight-bold pl-1">{{ file.companyName }}</span>
                        </h3>
                    </div>
                    <div class="card-body">
                        {{#if pep}}
                            <div class="alert alert-warning" role="alert">
                                <span class="font-weight-bold">Be aware</span>: in this company structure there is at
                                least one PEP present!
                            </div>
                        {{/if}}
                        <div id="internal-comments" class="pb-2">
                            <h4 class="font-weight-bold mb-2">Internal Comments</h4>
                            {{#each file.comments}}
                                <div class="row">
                                    <div class="col-12 {{#ifEquals role 'CO'}} {{#unless
                                            to}} text-danger{{/unless}}{{/ifEquals}}">
                                        <span>({{#formatDate date "DD-MM-YYYY" }}{{/formatDate}})</span>
                                        <span class="font-weight-bold">{{username }}</span>
                                        <span>
                                            {{#ifCond role "&&" to}}
                                                - Sent from {{ from }} to {{to}}
                                            {{/ifCond}}
                                            : {{ comment }}
                                        </span>
                                    </div>
                                </div>
                            {{/each}}
                        </div>
                        <form id="fileReviewForm" method="POST" action="/file-reviewer/open-file-review/{{ id }}">
                            <!-- STANDARD FILES TABLE -->
                            <div id="standard-files-table" class="table-responsive pt-4">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th style="width: 45%;">Files Required</th>
                                        <th style="width: 10%;" class="text-center">Present</th>
                                        <th style="width: 10%;">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" name="allPresentCheck"
                                                    class="custom-control-input presentCheck"
                                                    id="allPresentCheck"
                                                    form="fileReviewForm"
                                                />
                                                <label
                                                    class="custom-control-label "
                                                    for="allPresentCheck"
                                                >All</label>
                                            </div>
                                        </th>
                                        <th style="width: 35%;">Explanation</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each standardFileQuestions }}
                                        <tr>
                                            <td id="filename-{{id}}" {{#ifEquals internal "Entity Information Form (duly executed)"}} data-toggle="tooltip" data-placement="bottom" title="Called by a variety of names in earlier years i.e. company information form or incorporation questionnaire etc." {{/ifEquals}}
                                            {{#ifEquals internal "Relevant Person Information Form (duly executed)"}} data-toggle="tooltip" data-placement="right" title="Introduced during 2019, does not exist for older companies" {{/ifEquals}}
                                            {{#ifEquals internal "Subscriber’s Resolution"}} data-toggle="tooltip" data-placement="top" title="Attachment of first director" {{/ifEquals}}
                                            {{#ifEquals internal "Acceptance letter"}} data-toggle="tooltip" data-placement="top" title="Consent / acceptance as a director of the company" {{/ifEquals}}
                                            >{{ internal }}</td>
                                            <td class="text-center">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox" name="files[{{ id }}][present]"
                                                           class="custom-control-input presentCheck"
                                                           id="standard-file-present-{{ @key }}"
                                                           form="fileReviewForm"
                                                        {{#if present}}
                                                           checked
                                                        {{/if}}
                                                    />
                                                    <label
                                                            class="custom-control-label"
                                                            for="standard-file-present-{{ @key }}"
                                                    ></label>
                                                </div>

                                            </td>
                                            <td class="text-center">
                                                <button
                                                        type="button"
                                                        form="fileReviewForm"
                                                        class="btn solid royal-blue upload-button"
                                                        data-toggle="modal"
                                                        data-target="#upload-modal"
                                                        id="standard-file-{{ @key }}"
                                                        name="standardFileUpload-{{ @key }}"
                                                        data-review-id="{{ ../id }}"
                                                        data-field="{{ internal }}"
                                                        data-row-id="{{ id }}"
                                                        data-row="{{ @key }}"
                                                        data-file-group="{{ fileGroup }}"
                                                        data-provided="{{provided}}"
                                                    {{#if uploadFiles}}
                                                        {{#if provided}}
                                                            style="background-color:#F5AA26;border-color:#F5AA26;"
                                                        {{else}}
                                                            style="background-color:#0AC293;border-color:#0AC292;"

                                                        {{/if}}
                                                    {{/if}}
                                                >
                                                    {{#if uploadFiles}}
                                                        Modify
                                                    {{else}}
                                                        Upload
                                                    {{/if}}
                                                </button>
                                            </td>
                                            <td>
                                                <textarea
                                                        name="files[{{ id }}][explanation]"
                                                        id="standard-file-explanation-{{ @key }}"
                                                        class="form-control"
                                                        form="fileReviewForm"
                                                        rows="1"
                                                >{{ explanation }}</textarea>
                                            </td>
                                        </tr>
                                    {{/each}}
                                    <tr>
                                        <td>Company Activity</td>
                                        <td class="text-center">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" name="companyActivity[present]"
                                                       class="custom-control-input presentCheck" id="company-activity-present"
                                                       form="fileReviewForm"
                                                    {{#if companyActivityReview.present }}
                                                       checked
                                                    {{/if}}
                                                />
                                                <label
                                                        class="custom-control-label"
                                                        for="company-activity-present"
                                                ></label>
                                            </div>
                                        </td>
                                        <td>
                                            <select data-toggle="tooltip" data-placement="right" title='If "other" note explanation in the box' name="companyActivity[select]" id="company-activity" class="custom-select" style="width:240px">
                                                <option value="none">Select...</option>
                                                <option value="banking-business">Banking Business</option>
                                                <option value="insurance-business">Insurance Business</option>
                                                <option value="fund-management-business">Fund management business</option>
                                                <option value="finance-and-leasing-business">Finance and leasing business</option>
                                                <option value="headquarters-business">Headquarters business</option>
                                                <option value="shipping-business">Shipping business</option>
                                                <option value="holding-business">Holding business</option>
                                                <option value="intellectual-property-business">Intellectual property business</option>
                                                <option value="distribution-and-service-centre-business">Distribution and service centre business</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </td>
                                        <td>
                                              <textarea
                                                      name="companyActivity[explanation]"
                                                      id="company-activity-explanation"
                                                      class="form-control"
                                                      form="fileReviewForm"
                                                      rows="1"
                                              >{{ companyActivityReview.explanation }}</textarea>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!-- STANDARD FILES END -->
                            <!-- RECORD KEEPING DETAILS -->
                            <hr class="mt-3"/>
                            <div class="mb-3">
                                <h4>Record Keeping Details</h4>
                                {{#if recordKeepingQuestions }}
                                    <!-- RECORD HOLDER AND PRIMARY ADDRESS -->
                                    <div class="row mt-2">
                                        <div class="col-2 py-1">
                                            <label for="records-holder" data-toggle="tooltip" data-placement="right" title="Name of the person keeping the records of the company including the accounting records
                                            ">Record Holder</label>
                                        </div>
                                        <div class="col-4 py-1">
                                            <input id="records-holder" name="records[recordHolder]" type="text"
                                                   class="form-control" required
                                                   value="{{recordKeepingQuestions.recordHolder}}"/>
                                        </div>

                                        <div class="col-2 py-1">
                                            <label for="records-primary-address">Address - 1st Line</label>
                                        </div>
                                        <div class="col-4 py-1">
                                            <input id="records-primary-address" name="records[primaryAddress]"
                                                   type="text" class="form-control"
                                                   value="{{recordKeepingQuestions.primaryAddress}}"/>
                                        </div>
                                    </div>
                                    <!-- SECONDARY ADDRESS AND COUNTRY -->
                                    <div class="row mt-2">
                                        <div class="col-2 py-1">
                                            <label for="records-secondary-address">Address - 2nd Line</label>
                                        </div>
                                        <div class="col-4 py-1">
                                            <input id="records-secondary-address" name="records[secondaryAddress]"
                                                   type="text" class="form-control"
                                                   value="{{recordKeepingQuestions.secondaryAddress}}"/>
                                        </div>

                                        <div class="col-2">
                                            <label for="records[operationCountry]">Country of record keeping</label>
                                        </div>
                                        <div class="col-4">
                                            {{>file-reviewer/shared/select-country selectId="records[operationCountry]" value=recordKeepingQuestions.operationCountry}}
                                        </div>
                                    </div>
                                    <!-- STATE AND CITY -->
                                    <div class="row mt-2">
                                        <div class="col-2 py-1">
                                            <label for="records-state">State</label>
                                        </div>
                                        <div class="col-4 py-1">
                                            <input id="records-state" name="records[state]" type="text"
                                                   class="form-control"
                                                   value="{{recordKeepingQuestions.state}}"/>
                                        </div>

                                        <div class="col-2 py-1">
                                            <label for="records-city">City</label>
                                        </div>
                                        <div class="col-4 py-1">
                                            <input id="records-city" name="records[city]" type="text"
                                                   class="form-control"
                                                   value="{{recordKeepingQuestions.city}}"/>
                                        </div>
                                    </div>
                                    <!-- POSTAL CODE AND EMAIL -->
                                    <div class="row mt-2">
                                        <div class="col-2 py-1">
                                            <label for="records-postal-code">Postal Code</label>
                                        </div>
                                        <div class="col-4 py-1">
                                            <input id="records-postal-code" name="records[postalCode]" type="text"
                                                   class="form-control"
                                                   value="{{recordKeepingQuestions.postalCode}}"/>
                                        </div>

                                        <div class="col-2 py-1">
                                            <label for="records-email">E-mail</label>
                                        </div>
                                        <div class="col-4 py-1">
                                            <input id="records-email" name="records[email]" type="text"
                                                   class="form-control"
                                                   value="{{recordKeepingQuestions.email}}"/>
                                        </div>
                                    </div>
                                {{/if}}
                            </div>
                            <!-- FREE QUESTION CHECK -->
                            {{#each freeQuestions }}
                                <div id="previousFreeQuestionsWrap">
                                    <div class="row freeQuestions" id="oldFreeQuestions-{{ @key }}">
                                        <div class="col-1 d-flex align-items-center pt-2 justify-content-end">
                                            <button class="btn btn-danger btn-xs delete-old-fq" data-row="{{ @key }}">
                                                <i class="dripicons-cross"></i>
                                            </button>
                                        </div>
                                        <div class="col-11">
                                            <div class="row">
                                                <div class="col-6 form-group">
                                                    <label for="freeQuestions-{{ @key }}-external">External Free
                                                        Question</label>
                                                    <textarea
                                                            class="form-control"
                                                            name="freeQuestions[{{ @key }}][external]"
                                                            id="freeQuestions-{{ @key }}-external"
                                                            form="fileReviewForm"
                                                            rows="1"
                                                    >{{ external }}</textarea>
                                                </div>
                                                <div class="col-6 form-group">
                                                    <label for="freeQuestions-{{ @key }}-internal">Internal Trident
                                                        Comment</label>
                                                    <textarea
                                                            class="form-control"
                                                            name="freeQuestions[{{ @key }}][internal]"
                                                            id="freeQuestions-{{ @key }}-internal"
                                                            form="fileReviewForm"
                                                            rows="1"
                                                    >{{internal}}</textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {{/each}}
                            <div id="newFreeQuestionWrap">
                            </div>
                            <div class="row mt-4">
                                <div class="col-12 d-flex justify-content-end">
                                    <div class="custom-control custom-checkbox">
                                        <button type="button" class="btn btn-xs solid royal-blue py-1 px-2"
                                                id="freeQuestionButton" data-id="{{ id }}"><i
                                                class="dripicons-plus"></i></button>
                                        <span class="font-weight-bold">Add free questions</span>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <!-- CARD BODY END -->
                    <!-- CARD FOOTER NAV -->
                    <div class="row mt-2 justify-content-between">
                        <div class="col-md-2">
                            <a href="/file-reviewer/confirm-type/{{ id }}"
                               class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>
                        </div>
                        <div class="col-10 d-flex justify-content-end">
                            {{#ifCond status '!=' 'SENT TO CLIENT'}}
                                <button
                                        type="button"
                                        id="request-client-files-button"
                                        class="btn solid royal-blue mx-1 px-4"
                                        data-toggle="modal"
                                        data-target="#requestClientModal"
                                        data-id="{{ id }}"
                                        data-mcc="{{file.masterClientCode}}"
                                        data-page="1"
                                >
                                    Request Files From Client
                                </button>

                            {{/ifCond}}

                            {{#ifCond status '==' 'SENT TO CLIENT'}}
                                <button
                                        type="button"
                                        id="cancel-request-client-files-button"
                                        class="btn btn-danger mx-1 px-4"
                                        data-toggle="modal"
                                        data-target="#cancelRequestClientModal"
                                        data-id="{{ id }}"
                                        data-page="1"
                                >
                                    Cancel File Request
                                </button>

                            {{/ifCond}}

                            {{#ifCond status '!=' 'ON HOLD'}}
                                {{#ifCond status '!=' 'SENT TO CLIENT'}}
                                    <button
                                            type="button"
                                            id="on-hold-file-review-button"
                                            class="btn solid royal-blue mx-1 px-4"
                                            data-toggle="modal"
                                            data-target="#saveModal"
                                            data-id="{{ id }}"
                                            data-page="1"
                                    >
                                        Put On Hold
                                    </button>
                                {{/ifCond}}
                            {{/ifCond}}


                            <button
                                    type="button"
                                    id="save-file-review-button"
                                    class="btn solid royal-blue mx-1 px-4"
                                    data-toggle="modal"
                                    data-target="#saveModal"
                                    data-id="{{ id }}"
                                    data-page="1"
                            >
                                Save
                            </button>

                            <input id="next-button" name="next-button" type="submit" form="fileReviewForm"
                                   class="btn solid royal-blue mx-1 px-3" value="Continue to Next Page"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{>file-reviewer/save-file-review-modal}}
    {{>file-reviewer/upload-file-modal}}
    {{>file-reviewer/modals/send-to-request-client-modal}}
    {{>file-reviewer/modals/cancel-request-client-modal}}
</main>

<script type="text/javascript" src="/templates/freequestion.precompiled.js"></script>
<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script>
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body',
        boundary: 'window'
    });
    let freeQuestions = $(".freeQuestions").length;
    let activityValue = "{{companyActivity.activity}}";
    // for the question check toggle
    $(".question-check").on('change', function () {
        let group = this.id.replace("Check", "");
        let label = $("label[for='" + this.id + "']");
        // change label
        if ($(this).is(':checked')) {
            label.text('Yes');
        } else {
            label.text('No');
        }
        // expand table
        if (group === "businessOwner") {
            if ($(this).is(':checked')) {
                $('#' + group + 'Info').show(150);
                $('#shareholderCheckWrapper').show(150);
            } else {
                $('#' + group + 'Info').hide(150);
                $('#shareholderCheckWrapper').hide(150);
                $('#shareholderInfo').hide(150);
            }
        } else if (group === "trustee") {
            if ($(this).is(':checked')) {
                $('#no' + group + 'Info').hide(150);
                $('#' + group + 'Info').show(150);
            } else {
                $('#' + group + 'Info').hide(150);
                $('#no' + group + 'Info').show(150);
            }
        } else {
            if ($(this).is(':checked')) {
                $('#' + group + 'Info').show(150);
            } else {
                $('#' + group + 'Info').hide(150);
            }
        }
    });
    $('#freeQuestionButton').on('click', function () {
        let template = Handlebars.templates.freequestion;
        let d = {
            row: freeQuestions
        }
        let html = template(d);
        $('#newFreeQuestionWrap').append(html);
        freeQuestions++;
    });

    $('.delete-old-fq').on('click', function () {
        let row = $(this).data('row');
        $('#oldFreeQuestions-' + row).remove();
    });
    //values for the companyActivity select
    if (activityValue === '') {
        $('#company-activity').val("none");
    } else {
        $('#company-activity option[value="{{companyActivity.activity}}"]').attr('selected', 'selected');
        $('#company-activity').val("{{companyActivity.activity}}");
    }

    $('#allPresentCheck').on('change', function (){
        const checked = $(this).is(':checked');
        if(checked){
            $('.presentCheck').prop('checked', true);
        }
        else {
            $('.presentCheck').prop('checked', false);
        }
    });
</script>

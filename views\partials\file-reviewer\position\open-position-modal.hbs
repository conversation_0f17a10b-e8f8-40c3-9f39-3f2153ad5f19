{{!-- POSITION MODAL --}}
<div
        class="modal fade"
        id="openPositionModal"
        tabindex="-1"
        role="dialog"
        aria-hidden="true"
        style="overflow-y: scroll;"
>
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Position</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3">
                <p class="text-muted text-center">LOADING...</p>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="/templates/peekposition.precompiled.js"></script>
<script type="text/javascript">
    $('#openPositionModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        let reviewId =  button.data('review-id');

        let orgId = button.data('org');
        let positionId = button.data('row');

        $.get('/file-reviewer/reviews/'+ reviewId +'/relations/' + orgId + '/positions/' + positionId, function (data) {

            console.log(data);
            let template = Handlebars.templates.peekposition;
            let d = {
                position: data.position,
                positionFiles: data.positionFiles,
                positionInformation: data.positionInformation,
                reviewId: reviewId
            };
            let html = template(d);
            $('#openPositionModal .modal-body').html(html);
        });
    });
</script>

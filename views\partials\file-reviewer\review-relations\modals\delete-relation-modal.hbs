<!-- DELETE OWNER MODAL -->
<div class="modal fade" id="deleteRelationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Relation</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">Are you sure you want to delete this relation?</div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button id="deleteRelationButton" type="button" class="btn btn-danger">
                    Delete
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    let reviewId = '';
    let relationId = '';
    let relationType = '';
    let relationGroup = '';
    $('#deleteRelationModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        reviewId = button.data('review-id');
        relationId = button.data('relation-id');
        relationGroup = button.data('group');
        relationType = button.data('type');
    });

    $('#deleteRelationButton').click(function () {
        $.ajax({
            type: 'DELETE',
            url: '/file-reviewer/open-file-review/' + reviewId + '/relations/' + relationId,
            data: { type: relationType, group: relationGroup },
            success: function () {
                Swal.fire(
                        'Success',
                        'The relation has been deleted successfully',
                        'success'
                ).then(() => {
                    location.reload();
                });

            },
            error: function () {
                Swal.fire(
                        'Error',
                        'There was an error while trying to delete the relation',
                        'error'
                );
            },
        });
    });
</script>

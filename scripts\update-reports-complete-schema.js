const FinancialReportModel = require('../models/financialreport')
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config();
const mongoose = require('mongoose');


async function runScript() {
  let connection;

  try {
    connection = await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const result = await updateReportsForSelfServiceComplete();

    console.log('Script executed successfully', result);
  } catch (error) {
    console.error('Error in the script:', error);
  } finally {
    if (connection) {
      await connection.disconnect();
    }
  }
}

async function updateReportsForSelfServiceComplete() {
  try {
    const reports = await FinancialReportModel.aggregate([
      {
        $match: {
          'reportDetails.serviceType': { $ne: 'self-service-prepare' },
          'completeDetails': { $exists: true, $not: { $in: [null, {}] } },
          'completeDetails.income.otherIncome': {
            $type: 'array',
            $not: {
              $elemMatch: {
                $not: { $type: 'number' }
              }
            }
          },
          'completeDetails.assets.otherAssets': {
            $type: 'array',
            $not: {
              $elemMatch: {
                $not: { $type: 'number' }
              }
            }
          },
          'completeDetails.expenses.otherExpenses': {
            $type: 'array',
            $not: {
              $elemMatch: {
                $not: { $type: 'number' }
              }
            }
          },
          'completeDetails.liabilities.otherLiabilities': {
            $type: 'array',
            $not: {
              $elemMatch: {
                $not: { $type: 'number' }
              }
            }
          }
        }
      },
      {
        "$set": {
          "completeDetails.income.otherIncome": {
            "$map": {
              "input": "$completeDetails.income.otherIncome",
              "as": "value",
              "in": {
                "value": "$$value",
                "description": ''
              }
            }
          },
          "completeDetails.assets.otherAssets": {
            "$map": {
              "input": "$completeDetails.assets.otherAssets",
              "as": "value",
              "in": {
                "value": "$$value",
                "description": ''
              }
            }
          },
          "completeDetails.expenses.otherExpenses": {
            "$map": {
              "input": "$completeDetails.expenses.otherExpenses",
              "as": "value",
              "in": {
                "value": "$$value",
                "description": ''
              }
            }
          },
          "completeDetails.liabilities.otherLiabilities": {
            "$map": {
              "input": "$completeDetails.liabilities.otherLiabilities",
              "as": "value",
              "in": {
                "value": "$$value",
                "description": ''
              }
            }
          }
        }
      }
    ])

    let updateLog = [['Entry ID', 'Company', 'Update date', 'Action']];
    if(reports.length > 0) {
      for (const report of reports) {
        try {
          const result = await FinancialReportModel.findOneAndUpdate(
            { _id: report._id },
            { $set: report },
            { new: true }
          );

          if (result) {
            updateLog.push([report._id?.toString(), report.companyData.name, new Date(), 'SUCCESS']);
          } else {
            updateLog.push([report._id?.toString(), report.companyData.name, new Date(), 'FAILED UPDATING']);
          }
        } catch(e) {
          updateLog.push([report._id?.toString(), report.companyData.name, new Date(), 'FAILED UPDATING']);
        }
      }
    }

    const filename = 'update_schema_for_self_service_complete_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'entries ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

  
    return { "success": true, "totalRows": updateLog.length - 1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}
runScript()
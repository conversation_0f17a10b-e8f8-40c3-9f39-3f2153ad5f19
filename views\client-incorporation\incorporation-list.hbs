<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h2>{{ title }}</h2>
                    </div>
                    <!-- CONTENT -->
                    <div class="card-body">
                        <!-- NEW INCORPORATIONS TABLE -->
                        <div id="automatic-new-incorporations-table" class="row">
                            <h5 class="pl-1 pb-1">NEW INCORPORATIONS / NAME RESERVATIONS:</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-sm table-small-font">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 20%;">Company Name</th>
                                        <th scope="col" style="width: 10%;">MCC</th>
                                        <th scope="col" style="width: 12%;">Submitted at</th>
                                        <th scope="col" style="width: 13%;">Days open</th>
                                        <th scope="col" style="width: 20%;">Incorporation Status</th>
                                        <th scope="col" style="width: 15%;">Submission Status</th>
                                        <th scope="col" style="width: 10%;" class="text-center">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each submissions.newSubmissions}}
                                        <tr>
                                            <td id="auto-company-name-{{ _id }}">{{ name }}</td>
                                            <td id="auto-company-mcc-{{ _id }}">{{ masterClientCode }}</td>
                                            <td id="auto-date-received-{{ _id }}">
                                                {{#formatDate submittedAt ../STANDARD_DATE_FORMAT}} {{/formatDate}}
                                            </td>
                                            <td>
                                                {{daysOpen}}
                                            </td>
                                            <td class="text-uppercase">
                                                {{ incorporationStatus }}
                                            </td>
                                            <td  class="text-uppercase">
                                                {{ status }}
                                            </td>
                                            <td class="pl-2 py-1 text-center align-middle">
                                                <button type="button"
                                                        data-id="{{_id}}"
                                                        class="pick-file btn  solid royal-blue w-100">
                                                    Pick
                                                </button>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="7" class="text-center font-italic">There are no new
                                                incorporations
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                        <!-- NEW INCORPORATIONS TABLE END -->

                        <!-- IN PROGRESS TABLE -->
                        <div id="automatic-assign-table" class="row">
                            <h5 class="pl-1 pb-1">IN REVIEW:</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-sm table-small-font">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 20%;">Company Name</th>
                                        <th scope="col" style="width: 10%;">MCC</th>
                                        <th scope="col" style="width: 12%;">Submitted at</th>
                                        <th scope="col" style="width: 13%;">Days open</th>
                                        <th scope="col" style="width: 20%;">Incorporation Status</th>
                                        <th scope="col" style="width: 15%;">Submission Status</th>
                                        <th scope="col" style="width: 10%;" class="text-center">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each submissions.inProgress}}
                                        <tr>
                                            <td id="auto-company-name-{{ _id }}">{{ name }}</td>
                                            <td id="auto-company-mcc-{{ _id }}">{{ masterClientCode }}</td>
                                            <td id="auto-date-received-{{ _id }}">
                                                {{#formatDate submittedAt ../STANDARD_DATE_FORMAT}} {{/formatDate}}
                                            </td>
                                            <td>
                                                {{daysOpen}}
                                            </td>
                                            <td class="text-uppercase">
                                                {{ incorporationStatus }}
                                            </td>
                                            <td  class="text-uppercase">
                                                {{ status }}
                                            </td>
                                            <td class="pl-2 py-1 text-center align-middle">
                                                <a href="/client-incorporation/{{ _id }}"
                                                   class="btn solid royal-blue w-100">
                                                    Open
                                                </a>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="7" class="text-center font-italic">There are no new
                                                incorporations
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                        <!-- ASSIGNED BY OFFICER TABLE END -->

                        <!-- PENDING CLIENT INFORMATION TABLE -->
                        <div id="automatic-pending-client-table" class="row">
                            <h5 class="pl-1 pb-1">PENDING CLIENT INFORMATION:</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-sm table-small-font">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 20%;">Company Name</th>
                                        <th scope="col" style="width: 10%;">MCC</th>
                                        <th scope="col" style="width: 12%;">Date requested</th>
                                        <th scope="col" style="width: 13%;">Days open</th>
                                        <th scope="col" style="width: 20%;">Incorporation Status</th>
                                        <th scope="col" style="width: 15%;">Submission Status</th>
                                        <th scope="col" style="width: 10%;" class="text-center">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each submissions.pendingClient}}
                                        <tr>
                                            <td id="auto-company-name-{{ _id }}">{{ name }}</td>
                                            <td id="auto-company-mcc-{{ _id }}">{{ masterClientCode }}</td>
                                            <td id="auto-date-received-{{ _id }}">
                                                {{#if lastRequestInformationDate}}
                                                    {{#formatDate lastRequestInformationDate ../STANDARD_DATE_FORMAT}} {{/formatDate}}
                                                {{/if}}
                                            </td>
                                            <td>
                                                {{daysOpen}}
                                            </td>
                                            <td  class="text-uppercase">
                                                {{ incorporationStatus }}
                                            </td>
                                            <td  class="text-uppercase">
                                                {{ status }}
                                            </td>
                                            <td class="pl-2 py-1 text-center align-middle">
                                                <a href="/client-incorporation/{{ _id }}"
                                                   class="btn solid royal-blue w-100">
                                                    Open
                                                </a>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="7" class="text-center font-italic">There are no new
                                                incorporations
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- PENDING CLIENT INFORMATION END -->


                        <!-- RETURNED BY CLIENT TABLE -->
                        <div id="automatic-returned-client-table" class="row">
                            <h5 class="pl-1 pb-1">RETURNED BY CLIENT:</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-sm table-small-font">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 20%;">Company Name</th>
                                        <th scope="col" style="width: 10%;">MCC</th>
                                        <th scope="col" style="width: 12%;">Date received</th>
                                        <th scope="col" style="width: 13%;">Days open</th>
                                        <th scope="col" style="width: 20%;">Incorporation Status</th>
                                        <th scope="col" style="width: 15%;">Submission Status</th>
                                        <th scope="col" style="width: 10%;" class="text-center">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each submissions.returnedByClient}}
                                        <tr>
                                            <td id="auto-company-name-{{ _id }}">{{ name }}</td>
                                            <td id="auto-company-mcc-{{ _id }}">{{ masterClientCode }}</td>
                                            <td id="auto-date-received-{{ _id }}">
                                                {{#if lastReturnedInformationDate}}
                                                    {{#formatDate lastReturnedInformationDate ../STANDARD_DATE_FORMAT}} {{/formatDate}}
                                                {{/if}}
                                            </td>
                                            <td>
                                                {{daysOpen}}
                                            </td>
                                            <td class="text-uppercase">
                                                {{ incorporationStatus }}
                                            </td>
                                            <td  class="text-uppercase">
                                                {{ status }}
                                            </td>
                                            <td class="pl-2 py-1 text-center align-middle">
                                                <a href="/client-incorporation/{{ _id }}"
                                                   class="btn solid royal-blue w-100">
                                                    Open
                                                </a>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="7" class="text-center font-italic">There are no new
                                                incorporations
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- RETURNED BY CLIENT TABLE END -->

                        <!-- SEARCH TABLE -->
                        <div id="all-table" class="row mt-3">
                            <div class="col-12">
                                <form method="GET" id="submitForm">
                                    <div class="row my-3">
                                        <div class="col-md-4">
                                            <label for="filter">Search:</label>
                                            <input class='form-control' type='text' name='filter'
                                                   data-toggle="tooltip" data-placement="left"
                                                   title="Enter at least 3 characters" placeholder="Search incorporations..."
                                                    {{#if filters.search}}value="{{filters.search}}" {{/if}}
                                                   id='filter'/>
                                        </div>

                                        <div class="col-md-4 pt-2 d-flex align-items-center">
                                            <div class="custom-control custom-checkbox ">
                                                <input class="custom-control-input pt-3"
                                                       name="notShowCompleted" type="checkbox" id="showNotCompleteChk"
                                                    {{#if filters.showIncomplete}}checked {{/if}}
                                                       value="YES">
                                                <label class="custom-control-label" for="showNotCompleteChk">
                                                    Show all requests that are not completed
                                                </label>
                                            </div>
                                        </div>

                                        <div class="col-md-2 d-flex align-items-end">
                                            <input type='SUBMIT' class='btn btn-light btn-sm waves-effect'
                                                   value='Search'/>
                                        </div>

                                        <div class="col-md-2 d-flex align-items-end">
                                            <button type="button" class="btn btn-sm   solid royal-blue w-100" onclick="exportIncorporationsToXLS()">Export XLS</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="table-responsive">
                                <table id="scroll-horizontal-datatable" class="table table-striped table-sm table-small-font w-100 nowrap">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 20%;">Company Name</th>
                                        <th scope="col" style="width: 10%;">MCC</th>
                                        <th scope="col" style="width: 25%;">Assigned to</th>
                                        <th scope="col" style="width: 20%;">Incorporation Status</th>
                                        <th scope="col" style="width: 15%;">Submission Status</th>
                                        <th scope="col" style="width: 10%;" class="text-center">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each submissions.searchedSubmissions}}
                                        <tr>
                                            <td>{{ name }}</td>
                                            <td>{{ masterClientCode }}</td>
                                            <td>{{ user.username }}</td>
                                            <td class="text-uppercase">
                                                {{ incorporationStatus }}
                                            </td>
                                            <td  class="text-uppercase">
                                                {{ status }}
                                            </td>
                                            <td class="pl-2 py-1 text-right align-right">
                                                {{#if ../authentication.isIncorporationSuperAdmin}}
                                                    {{#if user.username}}
                                                        <button  style="min-width: 70px"
                                                                class="btn  btn-sm btn-warning waves-effect waves-light"
                                                                data-toggle="modal"
                                                                data-target="#reAssignApplicationModal"
                                                                data-mcc="{{masterclientcode}}"
                                                                data-name="{{user.username}}"
                                                                data-incorporation-id="{{_id}}">
                                                            Re-assign
                                                        </button>
                                                    {{/if}}

                                                {{/if}}
                                                {{#unless user.username}}
                                                    {{#ifEquals status 'SUBMITTED'}}
                                                        <button
                                                                type="button" style="min-width: 70px"
                                                                data-id="{{_id}}"
                                                                class="btn btn-sm  solid royal-blue pick-file">
                                                            Pick
                                                        </button>
                                                    {{/ifEquals}}
                                                    {{#ifEquals status 'NAME REVIEW'}}
                                                        <button  style="min-width: 70px"
                                                                type="button"
                                                                data-id="{{_id}}"
                                                                class="btn btn-sm solid royal-blue pick-file ">
                                                            Pick
                                                        </button>
                                                    {{/ifEquals}}

                                                {{/unless}}

                                                {{#ifEquals status 'IN PROGRESS'}}
                                                    <a href="/client-incorporation/{{ _id }}"  style="min-width: 70px"
                                                       class="btn btn-sm solid royal-blue">
                                                        Open
                                                    </a>
                                                {{/ifEquals}}
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="6" class="text-center font-italic">
                                                There are no submissions
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- SEARCH TABLE END -->
                    </div>
                    <!-- CONTENT END -->
                    <div class="row mt-2">
                        <div class="col-md-2">
                            <a href="/" class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>client-incorporation/modals/re-assign-modal}}
<script type="text/javascript">
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body'
    });

    $(document).ready(function () {
        $("#scroll-horizontal-datatable").DataTable({
            scrollX: !0,
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>",
                },
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
    });

    $(".pick-file").on("click", function () {
        const id = $(this).data('id');
        $.ajax({
            url: "/client-incorporation/pick-incorporation",
            method: "POST",
            data: {
                id: id,
            },
            success: function (res) {
                location.href = '/client-incorporation/list';
            },
            error: function (err) {
                if (err.status === 400) {
                    Swal.fire('Error', 'You already picked a file', 'error');
                } else if (err.status === 500) {
                    Swal.fire('Error', 'There was an internal error', 'error');
                }
            },
        });
    });


    function  exportIncorporationsToXLS() {
      const filterText = $("#filter").val();
      const showIncomplete = $("#showNotCompleteChk").is(":checked");

      window.open('/client-incorporation/list/export-xls?search='+filterText+"&showIncomplete="+showIncomplete);
    }

</script>

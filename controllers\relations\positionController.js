const {filereview: FileReviewModel, positionReview: positionReview} = require('../../models/filereview');
const NaturalPersonModel = require('../../models/naturalperson');
const OrganizationModel = require('../../models/organization');
const relationController = require('./relationController');
const ConfigModel = require('../../models/config');
const {v4: uuidv4} = require('uuid');


function setFormValuesToPosition(position, positionReviewObject, form, uploadedFiles=null) {
    const sections = ["details", "identification", "residentialAddress", "mailingAddress", "advisorDetails", "taxResidence",
        "principalAddress", "worldCheck"];


    sections.forEach((sectionKey) => {
        const formSection = form[sectionKey];
        if (positionReviewObject[sectionKey]) {
            positionReviewObject[sectionKey]["complete"] = !!(form[sectionKey] && form[sectionKey].correct);
        }

        if (formSection) {
            const reviewSection = position[sectionKey] ? position[sectionKey] : {};
            const sectionKeys = Object.keys(formSection);
            sectionKeys.forEach((key) => {
                if (key === "files") {
                    if (reviewSection[key]) {
                        const files = reviewSection[key];
                        const fileKeys = Object.keys(formSection[key]);

                        fileKeys.forEach((fileKey) => {
                            if (files[fileKey]) {
                                files[fileKey].present = !!formSection[key][fileKey].present;
                                files[fileKey].explanation = formSection[key][fileKey].explanation ?
                                    formSection[key][fileKey].explanation : '';
                            }
                        });
                        reviewSection[key] = files;
                    }
                } else {
                    reviewSection[key] = formSection[key]
                }
            });
            let files = reviewSection["files"];
            if (files) {
                files.forEach((file, index) => {
                    if (!file.id) {
                        file.id = uuidv4();
                    }

                    const tempFiles = uploadedFiles ? uploadedFiles[sectionKey] : {};
                    if (tempFiles) {
                        const newFiles = tempFiles[index] ? tempFiles[index] : [];
                        if (newFiles.length) {
                            file.uploadFiles = file.uploadFiles.concat(newFiles);
                            file.present = true;
                        }

                    }
                });

                reviewSection["files"] = files;
            }
            position[sectionKey] = reviewSection;
        }
    });

    return {position: position, positionReviewObject: positionReviewObject};
}

exports.openSearchPosition = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.reviewId);
        let org = await OrganizationModel.findById(req.params.organizationId);

        if (review && review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
            res.render('file-reviewer/position/search-position', {
                user: req.session.user,
                title: org.details.organizationName + ': Add Position',
                review: review,
                reviewId: req.params.reviewId,
                organizationId: req.params.organizationId,
            });
        } else {
            res.redirect('/file-reviewer/open-file-review/' + req.params.companyId + '/beneficial-owners');
        }
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.getPositionsByFilters = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.reviewId);

        let positions = [];
        let filters = {};
        let query = [];

        if (review && review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
            let organization = await OrganizationModel.findById(req.params.organizationId);

            filters = {
                name: req.body.name ? req.body.name : '',
                birthDate: req.body.birthDate ? req.body.birthDate : '',
                nationality: req.body.nationality ? req.body.nationality : ''
            };

            if (filters.name && filters.name.length > 2) {
                const filterName = {
                    "$or": [{"details.fullName": {$regex: filters.name, $options: 'i'}},
                        {"details.firstName": {$regex: filters.name, $options: 'i'}},
                        {"details.middleName": {$regex: filters.name, $options: 'i'}},
                        {"details.lastName": {$regex: filters.name, $options: 'i'}}]
                };
                query.push(filterName);
            }
            if (filters.birthDate && filters.birthDate.length > 2) {
                query.push({"details.birthDate": new Date(req.body.birthDate)});
            }
            if (filters.nationality && filters.nationality.length > 2) {
                query.push({"details.nationality": {$regex: req.body.nationality, $options: 'i'}});
            }
            if (query.length) {
                positions = await NaturalPersonModel.find({$and: query}).limit(100);
            }

            if (positions.length) {
                positions = positions.map((position) => {
                    let positionsGroups = [];
                    if (organization.positions.length) {
                        positionsGroups = organization.positions.filter((pos) => pos &&  pos.referenceId.toString() === position._id.toString())
                            .map((pos) => pos.type);
                    }
                    position["positionGroups"] = positionsGroups;
                    return position
                });
            }
            res.render('file-reviewer/position/search-position', {
                user: req.session.user,
                filters: filters,
                canCreateRelation: true,
                positions: positions,
                reviewId: req.params.reviewId,
                organizationId: req.params.organizationId,
            });
        } else {
            res.redirect('/file-reviewer/dashboard');
        }
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.assignPickedPosition = async function (req, res) {
    try {

        let review = await FileReviewModel.findById(req.params.reviewId);
        let organization;
        if (review && review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
            organization = await OrganizationModel.findById(req.params.organizationId);
            const positionRelation = await NaturalPersonModel.findById(req.params.relationId);


            if (positionRelation) {
                for (let group of req.body.relationGroup) {
                    let existsPos = organization.positions.find(
                        (pos) => pos && pos.referenceId === positionRelation._id && pos.type === group);
                    if (!existsPos) {
                        organization.positions.push({referenceId: positionRelation._id, type: group})
                        existsPos = organization.positions[organization.positions.length -1]
                    }
                    const posIndex = review.positions.findIndex((pos) =>  pos && pos.referenceId === existsPos._id);

                    if (posIndex === -1){
                        const reviewPosition = new positionReview({
                            referenceId: existsPos._id
                        });
                        review.positions.push(reviewPosition)
                    }
                }
            }

            await organization.save();
            await review.save();
            return res.status(200).json({"success": true});
        } else {
            return res.status(404).end();
        }
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.openPosition = async function (req, res) {
    try {
        relationController.clearTempUploadFiles(req);
        let review = await FileReviewModel.findById(req.params.reviewId);
        let org = await OrganizationModel.findById(req.params.organizationId);

        if (review && review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
            const configTemplate = await ConfigModel.findOne({});

            if (!configTemplate) {
                console.log("Configuration not found");
                res.redirect('/file-reviewer/dashboard');
            }
            if (org) {
                res.render('file-reviewer/position/add-position', {
                    user: req.session.user,
                    title: review.companyName + ': Add Position',
                    review: review,
                    position: configTemplate.relationFiles,
                    org: org,
                    id: req.params.reviewId
                });
            } else {
                res.redirect(
                    '/file-reviewer/open-file-review/' + req.params.reviewId + '/beneficial-owners'
                );
            }
        } else {
            res.redirect('/file-reviewer/dashboard');
        }
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.addPosition = async function (req, res) {

    try {
        const configTemplate = await ConfigModel.findOne({});
        const review = await FileReviewModel.findById(req.params.reviewId);
        let org = await OrganizationModel.findById(req.params.organizationId);

        if (org && req.body.positionType) {
            const fileGroupFiles = configTemplate.relationFiles.naturalFiles;
            let fileReviewPositionInfo = new positionReview();
            let position = new NaturalPersonModel({
                type: 'natural',
                lockedByFileReview: req.params.reviewId,
                pep: false,
                createdAt: new Date(),
                updatedAt: new Date(),
                details: {
                    files: fileGroupFiles.details ?
                        fileGroupFiles.details : [],
                    fullName: req.body.details.fullName,
                    firstName: req.body.details.firstName,
                    middleName: req.body.details.middleName,
                    lastName: req.body.details.lastName,
                    occupation: req.body.details.occupation,
                    birthDate: req.body.details.birthDate,
                    nationality: req.body.details.nationality,
                    countryBirth: req.body.details.countryBirth,
                },
                identification: {
                    files: fileGroupFiles.identification ?
                        fileGroupFiles.identification : [],
                    identificationType: req.body.identification.identificationType,
                    issueCountry: req.body.identification.issueCountry,
                    expiryDate: req.body.identification.expiryDate,
                    valid: !!(req.body.identification && req.body.identification.valid),
                },
                residentialAddress: {
                    primaryAddress: req.body.residentialAddress.primaryAddress,
                    secondaryAddress: req.body.residentialAddress.secondaryAddress,
                    country: req.body.residentialAddress.country,
                    state: req.body.residentialAddress.state,
                    postalCode: req.body.residentialAddress.postalCode,
                    city: req.body.residentialAddress.city,
                },
                mailingAddress: {
                    primaryAddress: req.body.mailingAddress.primaryAddress,
                    secondaryAddress: req.body.mailingAddress.secondaryAddress,
                    country: req.body.mailingAddress.country,
                    state: req.body.mailingAddress.state,
                    postalCode: req.body.mailingAddress.postalCode,
                    city: req.body.mailingAddress.city,
                },
                taxResidence: {
                    confirmation: !!(req.body.taxResidence && req.body.taxResidence.confirmation)
                },
                advisorDetails: {
                    firstName: req.body.advisorDetails.firstName,
                    middleName: req.body.advisorDetails.middleName,
                    lastName: req.body.advisorDetails.lastName,
                    firmName: req.body.advisorDetails.firmName,
                    phone: req.body.advisorDetails.phone,
                    email: req.body.advisorDetails.email,
                    nationality: req.body.advisorDetails.nationality,
                    incorporationCountry: req.body.advisorDetails.incorporationCountry,
                },
                principalAddress: {
                    primaryAddress: req.body.principalAddress.primaryAddress,
                    secondaryAddress: req.body.principalAddress.secondaryAddress,
                    country: req.body.principalAddress.country,
                    state: req.body.principalAddress.state,
                    postalCode: req.body.principalAddress.postalCode,
                    city: req.body.principalAddress.city,
                },
                worldCheck: {
                    files: fileGroupFiles.worldCheck ? fileGroupFiles.worldCheck : [],
                },
                partitionkey: 'naturalperson',
            });

            const positionObject = setFormValuesToPosition(position, fileReviewPositionInfo, req.body, req.session.relationFiles);
            position = positionObject["position"];
            fileReviewPositionInfo = positionObject["positionReviewObject"];

            // save
            await position.save();
            fileReviewPositionInfo.referenceId = position._id;
            for (let type of req.body.positionType) {
                org.positions.push({referenceId: position.id, type: type});

                const posId = org.positions[org.positions.length - 1]._id;
                fileReviewPositionInfo.type = type;
                fileReviewPositionInfo.referenceId = posId;
                const positionReviewIndex = review.positions.findIndex((pos) => pos.referenceId === posId);
                if (positionReviewIndex > -1) {
                    review.positions.set(positionReviewIndex, fileReviewPositionInfo);
                } else {
                    review.positions.push(fileReviewPositionInfo);
                }
            }

            await org.save();
            review.markModified("positions");
            await review.save();
            return res.status(200).end();
        } else {
            return res.status(400).end();
        }
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.peekPosition = async function (req, res) {
    try {
        let organization = await OrganizationModel.findById(req.params.relationId);
        let review = await FileReviewModel.findById(req.params.reviewId);
        let positionInformation;
        if (review) {
            positionInformation = review.positions.find((pos) => pos.referenceId.toString() === req.params.positionId);
        }

        if (organization) {
            const hasPosition = organization.positions.find((pos) => pos && pos._id.toString() === req.params.positionId);
            if(!hasPosition){
                return res.status(404).end();
            }

            let position = await NaturalPersonModel.findById(hasPosition.referenceId.toString());
            let positionFiles =[];
            if (position){
                positionFiles = [...position.identification.files, ...position.worldCheck.files]
            }

            return res.status(200).json({
                position: position,
                positionId: hasPosition._id,
                organizationId: organization._id,
                positionFiles: positionFiles,
                positionInformation: positionInformation
            });


        } else {
            return res.status(404).end();
        }
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.openEditPosition = async function (req, res) {
    try {
        relationController.clearTempUploadFiles(req);
        let review = await FileReviewModel.findById(req.params.reviewId);
        let org = await OrganizationModel.findById(req.params.relationId);
        let position;
        let positionInformation = {};

        if (review && review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
            const orgPosition = org.positions.find((pos) => pos && pos._id.toString() === req.params.positionId);

            if (orgPosition){
                position = await NaturalPersonModel.findById(orgPosition.referenceId.toString());
                positionInformation = review.positions.find(
                    (position) => position.referenceId.toString() === req.params.positionId);
            }

            if (org && position) {
                res.render('file-reviewer/position/edit-position', {
                    user: req.session.user,
                    title: review.companyName + ': Edit Position',
                    review: review,
                    position: position,
                    positionInformation: positionInformation,
                    positionReferenceId: req.params.positionId,
                    positionType: orgPosition.type,
                    organization: org,
                    id: req.params.reviewId,
                });
            } else {
                res.redirect('/file-reviewer/open-file-review/' + req.params.reviewId + '/beneficial-owners');
            }
        } else {
            res.redirect('/file-reviewer/dashboard');
        }
    } catch (error) {
        console.log(error);
        res.redirect('/');
    }
};

exports.editPosition = async function (req, res) {
    try {
        console.log("body: ", req.body);
        const review = await FileReviewModel.findById(req.params.reviewId);
        let organization = await OrganizationModel.findById(req.params.relationId);
        let position;
        if (review && review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {

            const orgPosition = organization.positions.find((pos) => pos._id.toString() === req.params.positionId);
            if (!orgPosition){
                return res.status(400).end();
            }

            position = await NaturalPersonModel.findById(orgPosition.referenceId.toString());
            if (!position){
                return res.status(400).end();
            }

            const positionIndex = review.positions.findIndex(
                (pos) => pos.referenceId.toString() === orgPosition._id.toString());

            if (positionIndex === -1){
                return res.status(400).end();
            }
            let fileReviewPositionInfo = review.positions[positionIndex];
            const positionObject = setFormValuesToPosition(position, fileReviewPositionInfo, req.body, req.session.relationFiles);
            position = positionObject["position"];
            fileReviewPositionInfo = positionObject["positionReviewObject"];
            review.positions.set(positionIndex, fileReviewPositionInfo);

            await position.save();
            organization.markModified('positions');
            await organization.save();
            await review.save();
            return res.status(200).end();
        } else {
            res.redirect('/file-reviewer/dashboard');
        }
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.deletePosition = async function (req, res) {
    try {
        let review = await FileReviewModel.findById(req.params.reviewId);
        console.log("req, ", req.body);
        let idx;
        if (review && review.fileReview.username.toLowerCase() === req.session.user.username.toLowerCase()) {
            let org = await OrganizationModel.findById(req.params.relationId);

            idx = org.positions.findIndex((pos) => pos && pos._id.toString() === req.params.positionId);
            if (idx > -1) {
                org.positions.splice(idx, 1);
                org.markModified('positions');
                await org.save();
            }

            idx = review.positions.findIndex((pos) => pos && pos.referenceId.toString() === req.params.positionId);
            // const totalPositionByReview = org.positions.filter((pos) => pos && pos.referenceId == req.params.positionId);

            if (idx > -1) {
                review.positions.splice(idx, 1);
                review.markModified('positions');
                await review.save();
            }
        }
        else{
            res.redirect('/file-reviewer/dashboard');
        }

        return res.status(200).end();
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

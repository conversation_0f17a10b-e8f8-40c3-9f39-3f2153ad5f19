{{!-- UNASSIGN CLIENT MODAL --}}
<div class="modal fade" id="unassignModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <form action="" class="form" id="unassignOfficerForm">
                    <div class="row">
                        <label class="col-form-label col-sm-5 pt-0 text-left" for="createRelationType">
                            Please select the officer to unassign:</label>

                        <div class="col-sm-7 text-left pl-2">
                            <div class="custom-control custom-radio" id="fileReviewerOfficer" style="display:none;">
                                <input
                                        type="radio"
                                        class="custom-control-input officerCheck"
                                        name="officers"
                                        id="officer1"
                                        value="file-reviewer"
                                />
                                <label class="custom-control-label" for="officer1">File Reviewer officer</label>
                            </div>
                            <div class="custom-control custom-radio" id="qualityOfficer"  style="display:none;">

                                <input
                                        type="radio"
                                        class="custom-control-input officerCheck"
                                        name="officers"
                                        id="officer2"
                                        value="quality-assurance"
                                />
                                <label class="custom-control-label" for="officer2">Quality Assurance officer</label>
                            </div>
                            <div class="custom-control custom-radio" id="complianceOfficer"  style="display:none;">

                                <input
                                        type="radio"
                                        class="custom-control-input officerCheck"
                                        name="officers"
                                        id="officer3"
                                        value="compliance"
                                />
                                <label class="custom-control-label" for="officer3">Compliance officer</label>
                                <div class="invalid-feedback">
                                    You must select at least one officer.
                                </div>
                            </div>

                        </div>
                    </div>
                </form>

            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="submit" class="btn solid royal-blue" id="unassignBtn" form="unassignOfficerForm">
                    Confirm
                </button>
            </div>
        </div>
    </div>
</div>

<script>

    let unassignId;

    $('#unassignModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        const statusPerOfficer = {
          "fr": ['SUBMITTED','ASSIGNED', 'ON HOLD', 'SEND TO FR BY QA',
              'SEND TO FR BY CO'],
            "qa": ['REVIEWED', 'IN PROGRESS BY QA', 'SEND TO QA BY CO', 'ASSIGNED QA BY CO'],
            "co": ['COMPLIANCE', 'VALIDATED QA', 'COMPLIANCE BY FR', 'COMPLIANCE BY QA']
        }
        unassignId = button.data('id');
        const reviewStatus = button.data('review-status');
        ;

        if (statusPerOfficer['fr'].includes(reviewStatus)){
          $("#fileReviewerOfficer").show();
        }
        else if(statusPerOfficer['qa'].includes(reviewStatus)){
            $("#fileReviewerOfficer").show();
            $("#qualityOfficer").show();
        }
        else if(statusPerOfficer['co'].includes(reviewStatus)){
            $("#fileReviewerOfficer").show();
            $("#qualityOfficer").show();
            $("#complianceOfficer").show();
        }
    });

    $("#unassignOfficerForm").submit(function (e) {
        e.preventDefault();
        $("#unassignBtn").prop('disabled', true);
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/compliances/' + unassignId + '/unassign-officer',
            data: $(this).serialize(),
            success: (response) => {
              if (response.status === 200){
                  Swal.fire('Success', response.message, 'success').then(() => {
                    $("#unassignModal").modal('hide');
                      $("#unassignBtn").prop('disabled', false);
                      location.reload();
                  });
              }
              else{
                  Swal.fire('Error', response.message, 'error').then(() => {
                      $("#unassignModal").modal('hide');
                      $("#unassignBtn").prop('disabled', false);
                  });
              }

            },
            error: (err) => {
              console.log(err);
                Swal.fire('Error', 'There was an error at moment to unassign the officer.', 'error');
                $("#unassignBtn").prop('disabled', false);
            },
        });
    });

    $('#unassignModal').on('hidden.bs.modal', function (event) {
        $(".officerCheck").prop('checked', false);
        $("#unassignBtn").prop('disabled', false);
        $("#fileReviewerOfficer").hide();
        $("#qualityOfficer").hide();
        $("#complianceOfficer").hide();
    });
</script>

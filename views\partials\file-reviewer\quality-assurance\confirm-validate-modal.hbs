{{!-- CONFIRM CLIENT MODAL --}}
<div class="modal fade" id="confirmValidateModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <div id="message_modal_validate">

                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="button" class="btn solid royal-blue" id="sendToValidateButton"
                    data-status="send-client">Confirm</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    let validateId;
    let statusModal;
    let responseValidate;
    const modalInfoValidate = {
        "validate-qa": {
            "modalMessage": 'You are about to validate this file review, and it will be marked as validated. <b>Are you sure?</b>',
            "validatedMessage": 'You are about to validate this file review, and it will be marked as validated. <b>Are you sure?</b><br><br> <b>Please note:</b> Not all files are validated and therefore it will be send back to the File Review officer.',
            "successMessage": 'The review was validated successful',
            "infoMessage": 'Not everything in the file review was marked as "Validated". <br>For this reason we have sent this file review back to the File Reviewer.',
            "errorMessage": 'There was an error while trying to validate the review'
        }
    };

    $('#confirmValidateModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        validateId = button.data('id');
        statusModal = button.data('status');
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/quality-assurance-review/' + validateId + '/validate-review',
            data: {},
            success: function (response) {
                if (response.success) {
                    if (response.isIncomplete === true) {
                        $('#message_modal_validate').html(modalInfoValidate[statusModal].validatedMessage);
                        responseValidate = response.isIncomplete;
                    }
                    else {
                        $('#message_modal_validate').html(modalInfoValidate[statusModal].modalMessage);
                        responseValidate = response.isIncomplete;
                    }

                }
                else {
                    $('#message_modal_validate').html(modalInfoValidate[statusModal].errorMessage);
                }

            },
            error: function () {
                Swal.fire('Error', modalInfoValidate[statusModal].errorMessage, 'error').then(() => {
                    $('#confirmValidateModal').modal('hide');
                });
            },
        });
    });

    $('#sendToValidateButton').on('click', function (e) {
        e.preventDefault();
        $(this).prop('disabled', true);
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/quality-assurance-review/' + validateId + '/submit-review',
            data: {
                status: statusModal, responseValidate: responseValidate
            },
            success: function (response) {
                $(this).prop('disabled', false);
                if (response.success) {
                    if (response.isIncomplete === true) {
                        Swal.fire('', modalInfoValidate[statusModal].infoMessage, 'info').then(() => {
                            $('#confirmValidateModal').modal('hide');
                            location.href = '/file-reviewer/quality-assurance-list/';
                        });
                    } else {
                        Swal.fire('Success', modalInfoValidate[statusModal].successMessage, 'success').then(() => {
                            $('#confirmValidateModal').modal('hide');
                            location.href = '/file-reviewer/quality-assurance-list/';
                        });
                    }
                } else {
                    Swal.fire('Error', modalInfoValidate[statusModal].errorMessage, 'error').then(() => {
                        $('#confirmValidateModal').modal('hide');
                    });
                }
            },
            error: function () {
                $(this).prop('disabled', false);
                Swal.fire('Error', modalInfoValidate[statusModal].errorMessage, 'error').then(() => {
                    $('#confirmValidateModal').modal('hide');
                });
            },
        });
    });
</script>

<!-- NOFITY MODAL -->
<div class="modal fade" id="requestClientModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">REQUEST FILES FROM CLIENT</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mb-1">
                    <div class="col-md-12 text-center">
                        <h3 class="font-weight-bold text-center">Are you sure?</h3>
                    </div>
                </div>

                <div class="row m-1">
                    <div class="col-md-12 ">
                        <span >The following files will be requested from the MCC: <strong id="mccTitle"></strong></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 px-4">
                        <ul id="requested-files" style="list-style-type: disc !important; padding-left: 1em !important;">

                        </ul>
                    </div>
                </div>

                <br>
                <div class="row">
                    <div class="col-md-12">
                        <label for="requestClientFilesComment">Provide note for client: </label>
                        <textarea class="form-control" name="requestClientFilesComment" id="requestClientFilesComment" rows="3" form="fileReviewForm"
                                  placeholder="The client will see this message..."></textarea>
                    </div>
                </div>


            </div>
            <div class="modal-footer justify-content-between">
                <div class="col-6">
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                        Close
                    </button>
                </div>
                <div class="d-flex col-6 justify-content-end">
                    <button
                            id="confirmRequestClientButton"
                            type="button"
                            form="fileReviewForm"
                            class="btn solid royal-blue modal-action-btn"
                    >
                        Confirm
                    </button>

                    <button id="spinnerSendBtn" class="btn solid royal-blue" type="button" disabled style="display: none">
                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        Sending...
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

    let fid = '';
    $('#requestClientModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget);

        fid = button.data('id');
        let mcc = button.data('mcc');
        $("#mccTitle").text(mcc);

        let pendingFiles = [];
        let form = $('#fileReviewForm').serializeJSON();

        if (form.files){
          let keys = Object.keys(form.files);

          keys.forEach((key) => {
            if (!form.files[key].present){
                let fileText = $("#filename-"+key).text();
                if (fileText && fileText !== 'World Check Completed'){
                  pendingFiles.push(fileText);
                }
            }
          })
        }

        if (pendingFiles.length > 0){
            for (let i = 0; i < pendingFiles.length; i++) {
              let pendingFile = '<li style="list-style-type: disc !important;" >' + pendingFiles[i] + '</li>';
              $("#requested-files").append(pendingFile)
            }

            $("#confirmRequestClientButton").prop('disabled', false);
        }
        else{
            Swal.fire('Error', 'There are no files to be requested from the client!', 'error').then(()=> {
              $("#requestClientModal").modal('toggle');
            });
            // $("#requested-files").append('<li style="list-style-type: disc !important;" > Not found files to request  </li>');
            // $("#confirmRequestClientButton").prop('disabled', true);
        }
    });

    $('#confirmRequestClientButton').click(function (e) {
        e.preventDefault();
        $(this).prop('disabled', true);
        $(this).hide();
        $("#spinnerSendBtn").show();
        let btnId = this.id;

        let form = $('#fileReviewForm').serialize();
        form = form.concat('&btnId=' + btnId + '&page=' + 1 + '&isRequestClient=true');
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/reviews/'+ fid + '/request-client-files',
            data: form,
            success: function (res) {
                $("#spinnerSendBtn").hide();
                Swal.fire('Success', 'Review has been sent to client successfully', 'success').then(() => {
                    location.href = '/file-reviewer/file-review-list';
                });

            },
            error: function (err) {
                $("#spinnerSendBtn").hide();
                $("#confirmRequestClientButton").prop('disabled', false);
                $("#confirmRequestClientButton").show();
                Swal.fire('Error', 'Error requesting the files from client... Try again later', 'error');
            },
        });
    });

    $('#requestClientModal').on('hidden.bs.modal', function (event) {
        $("#mccTitle").text('');
        $("#requested-files").html('');
        $("#confirmRequestClientButton").prop('disabled', true);
        $("#confirmRequestClientButton").show();
        $("#spinnerSendBtn").hide();
    });
</script>
<!-- NOFITY MODAL END -->

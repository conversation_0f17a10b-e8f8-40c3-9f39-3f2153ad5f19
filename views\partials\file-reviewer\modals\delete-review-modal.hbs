{{!-- UNASSIGN CLIENT MODAL --}}
<div class="modal fade" id="deleteReviewModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Delete Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3">
                <div class="text-center">
                    <h1>
                        <i class="fa fa-exclamation-triangle text-danger" aria-hidden="true"></i>
                    </h1>

                    <h4>Are you sure?</h4>
                    <label for="">You will to delete <strong id="company-name-lbl"></strong> File Review.</label>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="button" class="btn solid royal-blue" id="confirmDeleteButton">Confirm</button>
            </div>
        </div>
    </div>
</div>

<script>

    let deleteId;

    $('#deleteReviewModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        deleteId = button.data('review-id');
        const name = button.data('name');
        $("#company-name-lbl").text(name)

    });

    $('#confirmDeleteButton').on('click', function (event) {
        event.preventDefault();
        $(this).prop('disabled', true);
        $.ajax({
            type: 'DELETE',
            url: '/file-reviewer/reviews/' + deleteId,
            success: () => {
                Swal.fire('Success','File Review has been deleted successfully' , 'success').then(() => {
                    $(this).prop('disabled', false);
                    $("#deleteReviewModal").hide();
                    location.href = '/file-reviewer/compliances';
                });
            },
            error: (err) => {
                $(this).prop('disabled', false);
                Swal.fire('Error', 'There was an error deleting the File Review', 'error');
            },
        });

    });


</script>

const PdfPrinter = require('pdfmake');
const moment = require('moment');
const { ACCOUNTING_SERVICE_TYPES} = require('../../utils/financialReportConstants');

exports.generateFinancialReportPdf = function (report, res) {
  const printer = new PdfPrinter({
    Helvetica: {
      normal: 'Helvetica',
      bold: 'Helvetica-Bold',
      italics: 'Helvetica-Oblique',
      bolditalics: 'Helvetica-BoldOblique'
    }
  });

  const STYLES = {
    header: {
      fontSize: 13,
      bold: true,
      margin: [0, 0, 0, 10],
      color: '#123863'
    },
    subheader: {
      fontSize: 12,
      bold: true,
      margin: [0, 10, 0, 5],
      color: '#123863'
    },
    table: {
      margin: [0, 3, 0, 15]
    },
    priceTable: {
      margin: [0, 0, 0, 0]
    },
    tableHeader: {
      bold: true,
      color: 'black',
      fontSize: 11,
    },
    tableText: {
      bold: false,
      color: 'black',
      fontSize: 11,
      lineHeight: 1.2,
      margin: [0, 2, 0, 0]
    },
    tablePrices: {
      alignment: 'right',
      margin: [0, 0, 15, 0]
    },
    tablePriceText: {
      margin: [0, 0, 15, 0],
      fontSize: 10
    }
  };

  const docDefinition = {
    watermark: {
      text:  report.status === 'IN PROGRESS' || report.status === 'RE-OPEN' || report.status === 'SAVED' || report.status === 'HELP IN PROGRESS' ? 'PREVIEW' : '',
      color: '#0081B4',
      opacity: 0.2,
      bold: true,
      italics: false
    },
    footer: function (currentPage, pageCount) {
      const diffPages = pageCount > 3 ? pageCount - 3 : 1;

      if (currentPage > diffPages) {
        return [
          { text: 'Page ' + (currentPage - diffPages) + ' of ' + (pageCount - diffPages), alignment: 'center', fontSize: 10 }
        ];
      }
    },
    content: [
      {
        image: 'data:image/png;base64,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',
        width: 180,
        alignment: 'right',
        margin: [0, 0, 0, 20]
      },
      { text: 'BVI Business Companies - Annual Financial Return', style: 'header' },
      { text: 'Entity Details', style: 'subheader' },
      {
        style: 'table',
        table: {
          widths: ['50%', '25%', '25%'],
          body: createARCompanyInfoTable(report)
        },
        layout: {
          hLineWidth: function (i, node) {
            return (i === 0 || i === node.table.body.length) ? 1 : 0;
          },
          vLineWidth: function (i, node) {
            return (i === 0 || i === node.table.widths.length) ? 1 : 0;
          },
          hLineColor: function (i, node) {
            return i === 0 || i === node.table.body.length ? '#0070C0' : '';
          },
          vLineColor: function (i, node) {
            return i === 0 || i === node.table.widths.length ? '#0070C0' : '';
          },
        },
      },
      { text: 'Report Details', style: 'subheader' },
      {
        style: 'table',
        table: {
          widths: ['50%', '50%'],
          body: createARDetailsTable(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 1,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
      },
      { text: 'Declaration', style: 'subheader' },
      {
        style: 'table',
        table: {
          widths: ['*'],
          body: createARConfirmationTable(report)
        },
        layout: {
          hLineWidth: function (i, node) {
            return (i === 0 || i === node.table.body.length) ? 1 : 0;
          },
          vLineWidth: function (i, node) {
            return (i === 0 || i === node.table.widths.length) ? 1 : 0;
          },
          hLineColor: function (i, node) {
            return i === 0 || i === node.table.body.length ? '#0070C0' : '';
          },
          vLineColor: function (i, node) {
            return i === 0 || i === node.table.widths.length ? '#0070C0' : '';
          },
        }
      },
      {
        style: 'table',
        table: {
          widths: ['*'],
          body: [
            [{ text: [{ text: 'Name of the person stating the declaration: ', bold: true }, { text: report.declaration?.name ? report.declaration?.name : '' }], margin: [0, 10, 0, 10], style: 'tableText' }],
            [{ text: 'Relation to entity: ' + (report.declaration?.relation ? report.declaration?.relation : ''), style: 'tableText' }],
            [{ text: 'Phone number: ' + (report.declaration?.phone ? report.declaration?.phone : ''), style: 'tableText' }],
            [{ text: 'Email: ' + (report.declaration?.email ? report.declaration?.email : ''), margin: [0, 5, 0, 5], style: 'tableText' }]
          ]
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        }
      }
    ],
    styles: STYLES,
    defaultStyle: { font: 'Helvetica' }
  };


  if (report.reportDetails?.isExemptCompany !== true) {
    const isTridentServiceCompleted = (report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_COMPLETE ||
      report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_DROP)
      console.log(isTridentServiceCompleted)
   
    if (report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_COMPLETE || isTridentServiceCompleted) {
      docDefinition.content.push({ text: 'ANNUAL FINANCIAL RETURN', style: 'header', pageBreak: 'before', alignment: 'center', margin: [0, 40, 0, 0] })

      docDefinition.content.push({ text: report.companyData.name, style: 'subheader', alignment: 'center', margin: [0, 40, 0, 40] })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [350],
          body: [
            [{ text: 'FINANCIAL INFORMATION FOR YEAR ENDING', alignment: 'center', margin: [0, 5, 0, 0] }],
            [{ text: formatDate(report.financialPeriod.end, "YYYY-MM-DD"), alignment: 'center', margin: [0, 10, 0, 5] }]
          ]
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 20]
      })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [200, 142],
          body: createAssetsAndLiabilitiesTable(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 0],
        pageBreak: 'after'
      })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [200, 142],
          body: createIncomeAndExpensesReport(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 0]
      })

      
    }
    if (report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE) {

      const mappedReport = report.getMappedSchema();

      if(mappedReport){
        let mappedFieldsRows= []
        mappedReport.forEach((item) => {
          if(item.isArray){
            mappedFieldsRows.push([{ text: item.label, style: 'tablePriceText' }, { text: `Total items  ${item.value.length}`, style: 'tableText' }]);
            item.value.forEach((itemArrayValue) => {
              itemArrayValue.forEach((field) => {
                mappedFieldsRows.push([{ text: "-" + field.label, style: 'tablePriceText' }, { text: field.value, style: 'tableText' }]);
              })
            })
          }else{
            mappedFieldsRows.push([{ text: item.label, style: 'tablePriceText' }, { text: item.value, style: 'tableText' }]);
          }
          
        })
        docDefinition.content.push({ text: "Inputs Summary", style: 'subheader', alignment: 'center', margin: [0, 40, 0, 40] })
        docDefinition.content.push({
          style: 'table',
          table: {
            widths: ["70%", "30%"],
            body: mappedFieldsRows
          },
          layout: {
            hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
            vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
            hLineColor: () => '#0070C0',
            vLineColor: () => '#0070C0',
          }
        })
      }

      docDefinition.content.push({ text: 'ANNUAL FINANCIAL RETURN', style: 'header', pageBreak: 'before', alignment: 'center', margin: [0, 40, 0, 0] })

      docDefinition.content.push({ text: report.companyData.name, style: 'subheader', alignment: 'center', margin: [0, 40, 0, 40] })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [350],
          body: [
            [{ text: 'FINANCIAL INFORMATION FOR YEAR ENDING', alignment: 'center', margin: [0, 5, 0, 0] }],
            [{ text: formatDate(report.financialPeriod.end, "YYYY-MM-DD"), alignment: 'center', margin: [0, 10, 0, 5] }]
          ]
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 20]
      })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [200, 142],
          body: createAssetsAndLiabilitiesTable(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 0],
        pageBreak: 'after'
      })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [200, 142],
          body: createIncomeAndExpensesReportForOption2(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 30, 20, 0],
        pageBreak: 'after'
      })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [200, 142],
          body: createIncomeAndExpensesReport(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 0]
      })

    }

  }

  let pdfDoc = printer.createPdfKitDocument(docDefinition);

  let chunks = [];
  let result;

  pdfDoc.on('data', function (chunk) {
    chunks.push(chunk)
  });
  pdfDoc.on('end', function () {
    result = Buffer.concat(chunks);
    res.contentType('application/pdf');
    res.setHeader('Content-Disposition', 'inline; filename=AR-' + report.companyData.masterclientcode + '-' + report.companyData.code + '-' + formatDate(report.submittedAt, "YYYYMMDD") + '.pdf');
    res.send(result)
  });
  pdfDoc.end()
}

function createIncomeAndExpensesReportForOption2(report) {
  const tableBody = []
    tableBody.push([{ text: 'INCOME STATEMENT', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10] }, {}]);
    tableBody.push([{ text: 'REVENUE', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues._revenue, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'COST OF SALES', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(report.calculatedValues._costOfSales) * -1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'GROSS PROFIT', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(report.calculatedValues._profit, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);

    //expenses table
    tableBody.push([{ text: 'EXPENSES', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10], bold: true }, {}]);
    tableBody.push([{ text: 'Operating expenses', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(report.calculatedValues._operationExpenses) * -1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: `Other Expenses`, style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(report.calculatedValues._otherExpenses) * -1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Income tax expense', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(report.calculatedValues.incomeTaxExpenses) * -1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'TOTAL EXPENSES', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(Math.abs(report.calculatedValues._totalExpenses) * -1, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);
    tableBody.push([{ text: 'NET INCOME', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 10] }, { text: formatToCurrencyLocaleNumber(report.calculatedValues._netIncome, report.currency), style: 'tablePrices', margin: [0, 15, 15, 10] }]);
  return tableBody
}


function createARCompanyInfoTable(report) {
  let tableBody = [];
  tableBody.push([{ text: ['Company Name: ', { text: report.companyData.name, bold: true }], style: 'tableText', margin: [0, 2, 0, 2] }, { text: 'Fiscal Period:', style: 'tableText', bold: true, alignment: 'right', margin: [0, 2, 5, 0] }, {}],);
  tableBody.push([{ text: ['Company Identity Code: ', { text: report.companyData.code, bold: true }], style: 'tableText', margin: [0, 2, 0, 2] }, { text: 'Start Date:', alignment: 'left', style: 'tableText', margin: [45, 2, 0, 0] }, { text: formatDate(report.financialPeriod.start, "DD MMMM YYYY"), alignment: 'left', style: 'tableText' }])
  tableBody.push([{ text: ['Master Client Code: ', { text: report.companyData.masterclientcode, bold: true }], style: 'tableText', margin: [0, 2, 0, 2] }, { text: 'End Date:', alignment: 'left', style: 'tableText', margin: [45 , 2, 0, 0] }, { text: formatDate(report.financialPeriod.end, "DD MMMM YYYY"), alignment: 'left', style: 'tableText' }]);
  tableBody.push([{ text: 'Registered Agent: ', style: 'tableText', margin: [0, 10, 0, 0] }, { text: 'Date submitted:', alignment: 'right', margin: [0, 10, 0, 0], style: 'tableText' }, { text: formatDate(report.initialSubmitDate ? report.initialSubmitDate : report.submittedAt, "DD MMMM YYYY"), alignment: 'left', bold: true, margin: [0, 10, 0, 0], style: 'tableText' }]);
  tableBody.push([{ text: 'Trident Trust Company (BVI) Limited', style: 'tableText', bold: true }, {}, {}]),
    tableBody.push([{}, { text: ['Status: ', { text: report.getStatusLabel(), bold: true }], alignment: 'left', colSpan: 2, margin: [45, 0, 0, 0], style: 'tableText' }, {}])


    if (report.reopened?.details?.length > 0) {
      let reopenedCount = 0;
      report.reopened.details.forEach((reopened) => {
        if (reopened.resubmittedAt) {
          reopenedCount++;
          tableBody.push([{text: `Date resubmitted (${reopenedCount}):`, style: 'tableText', margin:[0,3,0,0]}, {text: formatDate(reopened.resubmittedAt, "DD MMMM YYYY"), colSpan: 2, margin:[45,3,0,0], bold: true}, {}]);
        }
      });
    }
  return tableBody;
}

function createARDetailsTable(report) {
  let tableBody = [];

  const serviceTypes = {
    "self-service-complete": "Self Service: Completion of Company’s Annual Return in the prescribed format",
    "self-service-prepare": "Self Service: Preparation of accounting records thought Trident Accounting Tool and submission of Annual Return",
    "trident-service-complete": "Trident Service: Reformatting of existing accounting records to prescribed form",
    "trident-service-drop": "Trident Service:  Preparation of accounting records and submission of Annual Return"
  }

  const expemtTypes = {
    "in-liquidation": 'IN LIQUIDATION',
    "stock-exchange": 'LISTED COMPANY',
    "is-regulated": 'REGULATED COMPANY',
    "tax-return-filed": 'TAX RETURN WITH BVI INLAND REVENUE DEPT'
  }

  tableBody.push([{ text: 'Is your company an exempt company?', style: 'tableText' }, { text: formatBoolean(report.reportDetails.isExemptCompany), style: 'tableText' }]);

  if (report.reportDetails.isExemptCompany === true) {
    tableBody.push([{ text: 'Type of exemption', style: 'tableText' }, { text: expemtTypes[report.reportDetails.exemptCompanyType], style: 'tableText' }]);
    tableBody.push([{ text: 'Additional Remarks', style: 'tableText' }, { text: report.reportDetails.exemptCompanyExplanation, style: 'tableText' }]);
  } else {

    if (report.reportDetails.isFirstYearOperation !== true) {
      tableBody.push([{ text: "Is there a change of financial year?", style: 'tableText' }, { text: formatBoolean(report.reportDetails.isThereFinancialYearChange) }]);
    }

    if (report.reportDetails.serviceType === "trident-service-drop") {
      tableBody.push([{ text: "Specify assistance required", style: 'tableText' }, { text: report.reportDetails.assistanceRequired}]);
  
    }

    tableBody.push([{ text: "Is this the company's first year of operation?", style: 'tableText' }, { text: formatBoolean(report.reportDetails.isFirstYearOperation) }]);
    tableBody.push([{ text: 'Service requested', style: 'tableText', margin: [0, 15, 10, 10], alignment: 'justify' }, { text: serviceTypes[report.reportDetails.serviceType], margin: [0, 10, 10, 10] }]);
  }

  return tableBody;
}

// eslint-disable-next-line no-unused-vars
function createAssetsAndLiabilitiesTable(report) {
  const tableBody = []
  const assetsValues = report.completeDetails?.assets ?? {};
  const liabilitiesValues = report.completeDetails?.liabilities ?? {};
  const cashAndEquivalents = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.cashAmount : report.cashTransactions.totalBankAccounts
  const balanceOfLoans = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.loansAndReceivables : (report.assets.balanceOfLoansReceivables ? report.assets.balanceOfLoansReceivables : 0)
  const balanceOfInvestment = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.investmentsAssetsAmount : (report.assets.totalAmountOfInvestments ? report.assets.totalAmountOfInvestments : 0)
  const tangibleAssets = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.fixedAssetsAmount : (report.assets.valueTangibleAssetsEndPeriod ? report.assets.valueTangibleAssetsEndPeriod : 0)
  const intangibleAssets = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.intangibleAssetsAmount : (report.assets.intangibleAssetsEndFinancialPeriod ? report.assets.intangibleAssetsEndFinancialPeriod : 0)

  // Assets Table
  tableBody.push([{ text: 'BALANCE SHEET/STATEMENT OF FINANCIAL POSITION', style: 'tableHeader', colSpan: 2, margin: [0, 10, 0, 10] }, {}])
  tableBody.push([{ text: 'ASSETS', style: 'tableHeader' }, {}])
  tableBody.push([{ text: 'Cash and Cash Equivalents', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(cashAndEquivalents, ''), style: 'tablePrices' }]);
  tableBody.push([{ text: 'Loans and Receivables', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(balanceOfLoans, ''), style: 'tablePrices' }]);
  tableBody.push([{ text: 'Investments and Other Financial Assets', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(balanceOfInvestment, ''), style: 'tablePrices' }]);
  tableBody.push([{ text: 'Tangible fixed Assets', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(tangibleAssets, ''), style: 'tablePrices' }]);
  tableBody.push([{ text: 'Intangible Assets', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(intangibleAssets, ''), style: 'tablePrices' }]);

  const valueofOtherAssets = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.totalOtherAssets : (report.assets.valueOfOtherAssets ? report.assets.valueOfOtherAssets : 0)

  const totalAssetsValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.total : report.calculatedValues?.totalAssets ?? 0
  tableBody.push([{ text: `Other Assets`, style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(valueofOtherAssets, ''), style: 'tablePrices' }]);

  tableBody.push([{ text: 'TOTAL ASSETS', style: 'tablePriceText', margin: [0, 15, 0, 0], bold: true }, { text: [(report.currency ?? '') + ' ', { text: formatToCurrencyLocaleNumber(totalAssetsValue, ''), style: 'tablePrices' }], alignment: 'right', margin: [0, 15, 15, 0] }]);
  // liabilities table 

  const accountsPayableValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? liabilitiesValues.accountsPayable : (report.liabilities.accountsPayableBalance ? report.liabilities.accountsPayableBalance : 0)
  const longTermsDebtsValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? liabilitiesValues.longTermDebts : (report.liabilities.ltDebtsClosingBalance ? report.liabilities.ltDebtsClosingBalance : 0)


  tableBody.push([{ text: 'LIABILITIES', colSpan: 2, style: 'tableHeader', margin: [0, 15, 0, 10] }, {}]);
  tableBody.push([{ text: 'Accounts Payable', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(accountsPayableValue, ''), style: 'tablePrices' }]);
  tableBody.push([{ text: 'Long-term Debts', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(longTermsDebtsValue, ''), style: 'tablePrices' }]);

  const otherLiabilitiesValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? liabilitiesValues.totalOtherLiabilities : (report.liabilities.otherLiabilitiesTotal ? report.liabilities.otherLiabilitiesTotal : 0)
  const totalLiabilitiesValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? liabilitiesValues.total : report.liabilities.totalLiabilities
  const shareholdersValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? report.completeDetails?.shareholderEquity?? 0 : report.calculatedValues?.shareholderEquality ?? 0
  tableBody.push([{ text: `Other Liabilities`, style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(otherLiabilitiesValue, ''), style: 'tablePrices' }]);

  tableBody.push([{ text: 'TOTAL LIABILITIES', style: 'tablePriceText', margin: [0, 15, 0, 15], bold: true }, { text: formatToCurrencyLocaleNumber(totalLiabilitiesValue, report.currency), style: 'tablePrices', margin: [0, 15, 15, 15] }]);
  tableBody.push([{ text: "SHAREHOLDER'S EQUITY", style: 'tablePriceText', margin: [0, 15, 0, 15], bold: true },
  { text: formatToCurrencyLocaleNumber(shareholdersValue, report.currency), style: 'tablePrices', margin: [0, 15, 15, 15] }]);
  return tableBody

}


function createIncomeAndExpensesReport(report) {
  const tableBody = []
  if (report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE) {
    const incomeValues = report.completeDetails?.income ?? {};
    const expensesValues = report.completeDetails?.expenses ?? {};

    tableBody.push([{ text: 'INCOME STATEMENT', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10] }, {}]);
    tableBody.push([{ text: 'REVENUE', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(incomeValues.total, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'COST OF SALES', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(incomeValues.costOfSales) *-1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'GROSS PROFIT', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(report.completeDetails?.grossProfit?? 0, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);

    //expenses table
    tableBody.push([{ text: 'EXPENSES', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10], bold: true }, {}]);
    tableBody.push([{ text: 'Operating expenses', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(expensesValues.operatingExpenses) *-1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: `Other Expenses`, style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(expensesValues.totalOtherExpenses) * -1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Income tax expense', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(expensesValues.incomeTax) * -1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'TOTAL EXPENSES', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(Math.abs(expensesValues.totalOfExpenses) * -1, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);
    tableBody.push([{ text: 'NET INCOME', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 10] }, { text: formatToCurrencyLocaleNumber(report.completeDetails?.netIncome??0, report.currency), style: 'tablePrices', margin: [0, 15, 15, 10] }]);
  } else {

    tableBody.push([{ text: 'INCOME ', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10] }, {}]);
    tableBody.push([{ text: 'Divided Income', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.dividedIncome?? 0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Loan Interest Income', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber((report.assets?.interestReceivableOnTheLoan ? report.assets.interestReceivableOnTheLoan : 0), ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Net Gain on Investment', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.netGainInvestment??0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Other income', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.otherIncome??0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'TOTAL INCOME', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.totalIncome??0, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);

    tableBody.push([{ text: 'EXPENSES ', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10] }, {}]);
    tableBody.push([{ text: 'Net Loss on investment', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.netLossInvestment?? 0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Company Administration fees', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.liabilities.compAdminFees, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Portfolio management fees and Related Services', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.portfolioFees?? 0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Bank Fees', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.bankFees??0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Loan interest expense', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.liabilities.loanInterestAccrued, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Income tax expenses', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.incomeTaxExpenses??0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Other Expenses', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.otherExpenses??0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'TOTAL EXPENSES', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.totalExpenses??0, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);
    tableBody.push([{ text: 'NET PROFIT (LOSS)', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.netProfit??0, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);

  }


  return tableBody
}

function createARConfirmationTable(report) {
  let tableBody = [];
  tableBody.push([{ text: 'I confirm that:', styles: 'tableText', fontSize: 10, margin: [0, 3, 0, 0] }])

  let total = report.payment?.total || 0;

  if (!total && report.reportDetails.isExemptCompany !== true){
    if (report.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_COMPLETE) {
      total = report.companyData?.accountingRecordsModule?.selfServiceCompleteAnnualReturnAmount;
    }

    if (report.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE) {
      total = report.companyData?.accountingRecordsModule?.selfServicePrepareAnnualReturnAmount;
    }
    if (report.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_COMPLETE) {
      total = report.companyData?.accountingRecordsModule?.tridentServiceCompleteAnnualReturnAmount;
    }
    if (report.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_DROP) {
      total = report.companyData?.accountingRecordsModule?.tridentServiceDropAccountingRecordsAmount;
    }
  } else {
    total = 190
  }

  const declarationOptions = [];

  if (parseFloat(report.version) > 1) {
    declarationOptions.push({ text: 'I have the authority to act on behalf of the company and further confirm that the details presented in this portal, along with any document(s) submitted while completing this accounting return, have been provided to me by the Company’s Director and to the best of the Company’s Directors knowledge and belief, are true and accurate.', margin: [0, 2] })
    declarationOptions.push({ text: 'The Registered Agent has a legitimate interest in processing any personal data provided above to satisfy the Entity’s compliance with relevant BVI law. I further acknowledge that the processing of such personal data may include its transfer to BVI competent authorities, and that the Registered Agent’s processing of any personal data will be done in accordance with Trident Trust Data Privacy Policy, which I have read and understood.', margin: [0, 2] })
  } else {
    declarationOptions.push({ text: 'The details presented in this portal, along with any document(s) submitted while completing this accounting return, are, to the best of my knowledge and belief, accurate and true.', margin: [0, 2] })

    if (report.reportDetails.isExemptCompany !== true) {
      declarationOptions.push({ text: 'Except for the assets and liabilities explicitly outlined in the form, the Company, to the best of my knowledge, does not possess any additional assets or have any other liabilities.', margin: [0, 2] });
    }
    declarationOptions.push({ text: `The assets allocated to the Company and the funds utilized for all Trident's services originate from lawful sources. Additionally, the submission fee of US$${total} must be paid to fulfill the submission process.`, margin: [0, 2] })
    declarationOptions.push({ text: 'I have the authority to act on behalf of the company.', margin: [0, 2] })
    declarationOptions.push({ text: 'The Registered Agent has a legitimate interest in processing any personal data provided above to satisfy the Entity’s compliance with relevant BVI law. I further acknowledge that the processing of such personal data may include its transfer to BVI competent authorities, and that the Registered Agent’s processing of any personal data will be done in accordance with Trident Trust Data Privacy Policy, which I have read and understood.', margin: [0, 2] })
  }
  tableBody.push([{
    ul: declarationOptions, 
    margin: [20, 0, 0, 0], fontSize: 10, lineHeight: 1.3
  }])
  return tableBody;
}


function formatDate(date, format) {
  if (date) {
    if (typeof (date) === "string") {
      return moment(date).format(format);
    }
    else {
      date = new Date(date.getTime() + (date.getTimezoneOffset() * 60000));
      return moment(date).format(format);
    }
  } else {
    return '';
  }
}

function formatBoolean(boolValue) {
  if (boolValue == null || boolValue == undefined) {
    return "";
  }
  return (boolValue ? "Yes" : "No")
}

function formatToCurrencyLocaleNumber(number, currency) {
  let val = number && Number(number) ? number : 0;
  return currency + " " + Number(val).toLocaleString('en', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}
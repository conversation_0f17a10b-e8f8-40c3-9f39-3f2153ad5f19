# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript
trigger:
    - Releases/*
pool:
  vmImage: windows-latest
jobs:
- job: Build
  workspace:
      clean: all
  steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '18.12.1'
    displayName: 'Install Node.js'

  - script: |
      npm install
    displayName: 'npm install'


  - task: Npm@1
    inputs:
      command: custom
      customCommand: 'run lint'
    displayName: Run ESLint


  - script: |
      npm run build
    displayName: 'npm build'


  - task: CopyFiles@2
    inputs:
      sourceFolder: '$(Build.SourcesDirectory)'
      contents: |
        **
        !scripts\**
        !.vscode\**
        !.eslintrc.json
        !.gitignore         
        !azure-pipelines.yml
        !.deployment
        !buildRelease.yml
        !buildValidation.yml
      targetFolder: $(Build.ArtifactStagingDirectory)/npm
    displayName: 'Copy package'  
  

  - task: PublishPipelineArtifact@1
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/npm'
      artifactName: npm
    displayName: 'Publish npm artifact'

  - task: ArchiveFiles@2
    inputs:
      rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/npm'
      includeRootFolder: false

#  - task: AzureWebApp@1
#    inputs:
#      azureSubscription: 'TT-AZDevOps-Connection-ClientPortal-Public'
#      appType:   'webApp'
#      appName: 'tbah-clientportal'
#      resourceGroupName: 'tbah-clientportal-apps-rg'
#      deployToSlotOrASE: true
#      slotName: 'acc'
#      package: 'D:\a\1\a\*.zip'
#      replaceExistingArchive: true
#      deploymentMethod:  'zipDeploy'

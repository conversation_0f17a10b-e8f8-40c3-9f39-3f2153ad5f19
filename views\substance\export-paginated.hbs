<div class="card">
    <div class="card-body">
        <div class="row">
            <div class="col-12">
                <h1>{{title}}</h1>
                <form method='GET' id="substanceExportForm" action="?page={{pagination.pageNumber}}&pageSize={{pagination.pageSize}}">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="filter_company">Company</label>
                                    <input class='form-control' type='text' name='filter_company' id='filter_company'
                                        value="{{filters.filter_company}}" />
                                    <div class="mt-2">
                                        <label for="submittedDateRange">
                                            Submitted Date
                                            <i class="fa fa-info-circle fa-lg text-info " data-toggle="tooltip" data-placement="top"
                                                title="Search within a payment date range"></i>
                                        </label>
                                        <input class='form-control filter' type='text' name='submittedDateRange' autocomplete="off"
                                            id='submittedDateRange'
                                            {{#if filters.submittedDateRange}}
                                            value="{{filters.submittedDateRange.start}} - {{filters.submittedDateRange.end}}"
                                            {{else}}
                                            value=""
                                            {{/if}}
                                            />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="filter_masterclient">Masterclient</label>
                                    <input class='form-control' type='text' name='filter_masterclient' id='filter_masterclient'
                                        value="{{filters.filter_masterclient}}" />
                                        
                                <div class="mt-2">
                                    <label for="companyIncorporatedRange">
                                        Company incorporated Date
                                        <i class="fa fa-info-circle fa-lg text-info " data-toggle="tooltip" data-placement="top"
                                            title="Search within a payment date range"></i>
                                    </label>
                                    <input class='form-control filter' type='text' name='companyIncorporatedRange' autocomplete="off"
                                        id='companyIncorporatedRange'
                                        {{#if filters.companyIncorporatedRange}}
                                        value="{{filters.companyIncorporatedRange.start}} - {{filters.companyIncorporatedRange.end}}"
                                        {{else}}
                                        value=""
                                        {{/if}}
                                        />
                                </div>
                                </div>
                            </div>
                            </div>
                        <div class="col-md-2">
                            <label for="filter_referral">Referral Office</label>
                            <input class='form-control' type='text' name='filter_referral' id='filter_referral'
                                value="{{filters.filter_referral}}" />
                                
                                <div class="mt-2">
                                    <label for="financialPeriodDateRange">
                                        Financial period end 
                                        <i class="fa fa-info-circle fa-lg text-info " data-toggle="tooltip" data-placement="top"
                                            title="Search within a payment date range"></i>
                                    </label>
                                    <input class='form-control filter' type='text' name='financialPeriodDateRange' autocomplete="off"
                                        id='financialPeriodDateRange'
                                        {{#if filters.financialPeriodDateRange}}
                                        value="{{filters.financialPeriodDateRange.start}} - {{filters.financialPeriodDateRange.end}}"
                                        {{else}}
                                        value=""
                                        {{/if}}
                                        />
                                </div>
                        </div>
                        <div class="col-md-2">
                            <label for="filter_exported">Is exported</label>
                            <select   class="form-control" name="filter_exported" id="filter_exported" data-toggle="select2" data-value="{{filters.filter_exported}}">
                                <option value="" {{#ifEquals filters.filter_exported '' }} selected {{/ifEquals}}>ALL</option>
                                <option value="exported" {{#ifEquals filters.filter_exported 'exported' }} selected {{/ifEquals}}>EXPORTED</option>
                                <option value="not-exported" {{#ifEquals filters.filter_exported 'not-exported' }} selected {{/ifEquals}}>NOT EXPORTED</option>
                            </select>

                            <label class="my-1 mt-2" for="filter_other">Other</label>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="resident_outside_bvi" id="resident_outside_bvi"
                                    value="Yes" {{#if filters.resident_outside_bvi }} checked {{/if}} />
                                <label class="custom-control-label" for="resident_outside_bvi">Tax
                                    residency claim outside BVI</label>
                            </div>
                            <br>
                        </div>
                        <div class="col-12 col-lg-8 col-xl-4 px-2">
                                    <label for="filterRelevantActivities">Relevant Activities</label> <br>
                                    <select id="filterRelevantActivities" class="form-control filter filter-array-field w-100" name="relevantActivities[]" multiple>
                                            <option value="" disabled>Select one or more relevant activities</option>
                                            <option value="none" {{#ifContains 'none' filters.relevantActivities }} selected {{/ifContains}}>None</option>
                                            <option value="banking_business" {{#ifContains 'banking_business' filters.relevantActivities }} selected {{/ifContains}}>
                                                Banking Business</option>
                                            <option value="insurance_business" {{#ifContains 'insurance_business' filters.relevantActivities }} selected {{/ifContains}}>
                                                Insurance Business</option>
                                            <option value="fund_management_business" {{#ifContains 'fund_management_business' filters.relevantActivities }} selected {{/ifContains}}>
                                                Fund Management Business
                                            </option>
                                            <option value="finance_leasing_business"  {{#ifContains 'finance_leasing_business' filters.relevantActivities }} selected {{/ifContains}}>
                                                Finance/Leasing Business
                                            </option>
                                            <option value="headquarters_business" {{#ifContains 'headquarters_business' filters.relevantActivities }} selected {{/ifContains}}>
                                                Headquarters Business</option>
                                            <option value="shipping_business"  {{#ifContains 'shipping_business' filters.relevantActivities }} selected {{/ifContains}}>
                                                Shipping Business</option>
                                            <option value="holding_business"  {{#ifContains 'holding_business' filters.relevantActivities }} selected {{/ifContains}}>
                                                Holding Business</option>
                                            <option value="intellectual_property_business"  {{#ifContains 'intellectual_property_business' filters.relevantActivities }} selected {{/ifContains}}>
                                                Intellectual Property Business
                                            </option>
                                            <option value="service_centre_business" {{#ifContains 'service_centre_business' filters.relevantActivities }} selected {{/ifContains}}>
                                                Service Centre Business
                                            </option>
                                      </select>
                                      <div class="row-md-2" style="padding-top:30px">
                                        <button type="submit" form="substanceExportForm" class='btn btn-primary waves-effect '>Search </button>
                                        <button type="button" form="substanceExportForm" id="clearFormBtn" class='btn btn-secondary waves-effect '>Reset filters
                                        </button>
                                      </div>
                        </div>
                        
                   
                    </div>
                </form>
                <br />
                <div class="row">
                    <div class="col-md-12">
                        <h5 >
                            How does the search work?
                            <span class="fa-stack tooltip-wrapper" style="margin-top: -10px" data-toggle="tooltip" data-container="body"
                                data-placement="top" data-html="true" title="
                                    <small><b>Default search when entering page:</b> <br>
                                        The initial search result (loaded upon entering this page) contains the latest 100 submissions that were paid. If you
                                        are using the column headers to filter the search result, this filter will only affect the initial search query. This
                                        means by default it will only filter the latest 100 paid submissions according to the arrows on the column headers.
                                        <br>
                                        <b>Manual search: </b><br>    
                                        If you do a manual search result based on any of the search criteria’s available, the initial search results (the 100
                                        paid) will be replaced with a new search result set. Once the initial set is replaced the column header filters will
                                        work on the new set to match your specific search result.
                                    </small>">
                                <i class="fa text-primary fa-info-circle fa-stack fa-lg"></i>
                            </span>
                        </h5>
                    </div>
                </div>
                <br />
                 {{>shared/table-pagination pagination=result formName="substanceExportForm" tableId="selection-datatable" searching="true"}}
                <table id="selection-datatable" class="table dt-responsive nowrap w-100">
                    <thead>
                        <tr>
                            <th class="all">ID</th>
                            <th class="all">Entity</th>
                            <th class="all">Entity Code</th>
                            <th class="all">Status</th>
                            <th class="all">Submitted date</th>
                            <th class="all">Export date</th>
                            <th class="all">Resubmitted date</th>
                            <th class="all">Financial Period Start Date</th>
                            <th class="all">Financial Period End Date</th>
                            <th class="all">Payment method</th>
                            <th class="all">Payment Received</th>
                            <th class="all">Payment Reference</th>
                            <th class="all">Export files</th>
                        </tr>
                    </thead>


                    <tbody>
                        {{#each result.data}}
                            <tr data-id="{{id}}">
                                <td>{{id}}</td>
                                <td>{{company_data.name}}</td>
                                <td>{{company_data.code}}</td>
                                <td>{{status}}</td>

                                <td data-sort="{{formatDate submitted_at 'YYYY-MM-DD HH:mm'}}">
                                    {{formatDate submitted_at ../STANDARD_DATE_FORMAT}}
                                </td>
                                <td data-sort="{{formatDate exported_at 'YYYY-MM-DD HH:mm'}}">
                                    {{formatDate exported_at ../STANDARD_DATE_FORMAT}}
                                </td>
                                <td data-sort="{{formatDate resubmitted_at 'YYYY-MM-DD HH:mm'}}">
                                    {{#if resubmitted_at}}
                                        {{formatDate resubmitted_at ../STANDARD_DATE_FORMAT}}
                                    {{/if}}
                                </td>
                                <td data-sort="{{formatDate entity_details.financial_period_begins 'YYYY-MM-DD HH:mm'}}">
                                    {{formatDate entity_details.financial_period_begins ../STANDARD_DATE_FORMAT}}
                                </td>
                                <td data-sort="{{formatDate entity_details.financial_period_ends 'YYYY-MM-DD HH:mm'}}">
                                    {{formatDate entity_details.financial_period_ends ../STANDARD_DATE_FORMAT}}
                                </td>
                                <td>{{payment.payment_type}}</td>
                                <td  data-sort="{{formatDate payment.payment_received_at 'YYYY-MM-DD HH:mm'}}">
                                    {{formatDate payment.payment_received_at ../STANDARD_DATE_FORMAT}}
                                </td>
                                <td>{{payment.payment_reference}}</td>
                                <td style="vertical-align: middle;">
                                    <input type="button" name="exportFilesBtn" id="export_files_{{id}}"
                                        class="btn btn-primary waves-effect waves-light"
                                        onclick="event.stopPropagation(); exportFiles('{{id}}')" value="Export">
                                </td>
                            </tr>
                        {{/each}}

                    </tbody>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col-12">&nbsp;</div>
        </div>
        <div class="row">
            <div class="col-12 text-sm-center form-inline">
                <div class="form-group mr-2 hidden">
                    <button id="btn-export-csv" class="btn btn-primary"><i class="mdi mdi mdi-content-save-edit mr-2"></i> Export to CSV</button>
                </div>
                <div class="ml-3 mr-2">
                    <a href='/substance/'
                        class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                </div>
                <div class="form-group mr-2">
                    <button id="btn-export-xls" class="btn btn-primary" style="display: none;"><i class="mdi mdi mdi-content-save-edit mr-2"></i> Export to xls</button>
                </div>
                <div class="form-group mr-2">
                    <button id="btn-export-files" class="btn btn-primary" style="display: none;"><i class="mdi mdi mdi-content-save-edit mr-2"></i> Export evidence files</button>
                </div>
            </div>
        </div>

    </div>
</div>

<form method="POST" action="./export" id="submitForm" name="submitForm">
    <input type="hidden" name="entryIds" value="" />
</form>

<form method="POST" action="./export-xls" id="submitFormXls" name="submitFormXls">
    <input type="hidden" name="entryIds" value="" />
</form>

<form method="POST" action="./export-evidence" id="submitFormFiles" name="submitFormFiles">
    <input type="hidden" name="entryIds" value="" />
</form>
<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript">

$(document).ready(function(){
    $("#companyIncorporatedRange, #submittedDateRange, #financialPeriodDateRange").flatpickr({
        mode: "range",
        dateFormat: "m/d/Y",
        autoclose: true,
        monthSelectorType: "dropdown",
        allowInput: true,
        locale: {
            rangeSeparator: ' - '
        },
    })

    $('#filterRelevantActivities').select2();

    $('[data-toggle="tooltip"]').tooltip({
        trigger: 'hover',
        container: 'body'

    });

        let table = $("#selection-datatable").DataTable({
            dom: "lrtip",
            columnDefs: [{ "visible": false, "targets": [0] }],
            scrollX: !0,
            "order": [],
            select: { style: "multi" },
            paging:false,
            info: false,
            sort: false,
            buttons: [{
                text: 'Select All On Page',
                action: function () {
                    table.rows({
                        page: 'current'
                    }).select();
                }
            },
                {
                    text: '<i class="far fa-square"></i>',
                    titleAttr: 'unselect all',
                    action: function () {
                        table.rows({
                            page: 'current'
                        }).deselect();
                    }
                }]
        })
    
    table.on('select', function ( e, dt, type, indexes ) {
        if ( type === 'row' ) {
            if(table.rows('.selected').data().length) {
                $('#btn-export-xls').show();
                $('#btn-export-files').show();
            } else {
                $('#btn-export-xls').hide();
                $('#btn-export-files').hide();
            }
        }
    });
  
    table.on('deselect', function ( e, dt, type, indexes ) {
        if ( type === 'row' ) {
            if(table.rows('.selected').data().length) {
                $('#btn-export-xls').show();
                $('#btn-export-files').show();
            } else {
                $('#btn-export-xls').hide();
                $('#btn-export-files').hide();
            }
        }
    });


    $('#btn-export-csv').click( function () {
        var data = table.rows('.selected').data();

        var entryIds = [];
        for (var idx=0; idx < data.length; idx++) {
            entryIds.push(data[idx][0])
        }
        var oForm = document.forms["submitForm"];
        oForm.elements["entryIds"].value = entryIds.join(';');
        oForm.submit();
    });

    $('#btn-export-xls').click(function () {
        var data  = table.rows('.selected').data();

        var entryIds = [];
        for (var idx=0; idx < data.length; idx++) {
            entryIds.push(data[idx][0])
        }
        var xlsForm = document.forms["submitFormXls"];
        xlsForm.elements["entryIds"].value = entryIds.join(";");
        xlsForm.submit();
    });

    $('#btn-export-files').click(function () {
        var data  = table.rows('.selected').data();

        var entryIds = [];
        for (var idx=0; idx < data.length; idx++) {
            entryIds.push(data[idx][0])
        }
        var filesForm = document.forms["submitFormFiles"];
        filesForm.elements["entryIds"].value = entryIds.join(";");
        filesForm.submit();
    });

    $("#clearFormBtn").on('click', () => {
        $("#substanceExportForm")[0].reset();
        $("#substanceExportForm input").val('');
        $("#filter_exported").val('').trigger('change');
        $('#substanceExportForm input[type=checkbox]').prop('checked', false);
         $('#filterRelevantActivities').val([]).trigger("change");
    })
})



function exportFiles(id) {
    var entryIds = [];
    entryIds.push(id);
    var filesForm = document.forms["submitFormFiles"];
    filesForm.elements["entryIds"].value = entryIds.join(";");
    filesForm.submit();
}

</script>
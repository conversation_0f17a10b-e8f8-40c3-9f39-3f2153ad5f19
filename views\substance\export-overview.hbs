<div class="card">
    <div class="card-body">
        <div class="row">
            <div class="col-12">
                <h1>{{title}}</h1>
                <form method='POST' id="generalForm">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="filter_company">Company</label>
                                    <input class='form-control' type='text' name='filter_company' id='filter_company'
                                        value="{{filters.filter_company}}" />
                                </div>
                                <div class="col-md-6">
                                    <label for="filter_masterclient">Masterclient</label>
                                    <input class='form-control' type='text' name='filter_masterclient' id='filter_masterclient'
                                        value="{{filters.filter_masterclient}}" />
                                </div>
                            </div>
                            <div class="row my-1">
                                <div class="col-md-6">
                                    <label for="filter_submitted_range_start">Submitted after:</label>
                                    <input class='form-control' type='date' name='filter_submitted_range_start'
                                        id='filter_submitted_range_start' value="{{filters.filter_submitted_range_start}}" />
                                </div>
                                <div class="col-md-6">
                                    <label for="filter_submitted_range_end">Submitted before:</label>
                                    <input class='form-control' type='date' name='filter_submitted_range_end'
                                        id='filter_submitted_range_end' value="{{filters.filter_submitted_range_end}}" />
                                </div>
                            </div>
                            <div class="row my-1">
                                <div class="col-md-6">
                                    <label for="filter_incorporated_range_start">Company incorporated after:</label>
                                    <input class='form-control' type='date' name='filter_incorporated_range_start'
                                        id='filter_incorporated_range_start' value="{{filters.filter_incorporated_range_start}}" />
                                </div>
                                <div class="col-md-6">
                                    <label for="filter_incorporated_range_end">Company incorporated before:</label>
                                    <input class='form-control' type='date' name='filter_incorporated_range_end'
                                        id='filter_incorporated_range_end' value="{{filters.filter_incorporated_range_end}}" />
                                </div>
                            </div>
                            <div class="row my-1">
                                <div class="col-md-6">
                                    <label for="filter_financial_period_range_start">Financial period end after:</label>
                                    <input class='form-control' type='date' name='filter_financial_period_range_start'
                                        id='filter_financial_period_range_start' value="{{filters.filter_financial_period_range_start}}" />
                                </div>
                                <div class="col-md-6">
                                    <label for="filter_financial_period_range_end">Financial period end before:</label>
                                    <input class='form-control' type='date' name='filter_financial_period_range_end'
                                        id='filter_financial_period_range_end' value="{{filters.filter_financial_period_range_end}}" />
                                </div>
                            </div>

                        </div>
                    
                        <div class="col-md-2">
                            <label for="filter_referral">Referral Office</label>
                            <input class='form-control' type='text' name='filter_referral' id='filter_referral'
                                value="{{filters.filter_referral}}" />
                    
                            <label class="my-1" for="filter_referral ">Relevant Activities</label>


                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="relevant_activities" id="none" value="none" {{#if
                                    filters.relevantActivities.none }} checked {{/if}} />
                                <label class="custom-control-label" for="none">None</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="relevant_activities" id="banking_business"
                                    value="banking_business" {{#if filters.relevantActivities.banking_business }} checked {{/if}} />
                                <label class="custom-control-label" for="banking_business">Banking
                                    Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="relevant_activities" id="insurance_business"
                                    value="insurance_business" {{#if filters.relevantActivities.insurance_business }}checked {{/if}} />
                                <label class="custom-control-label" for="insurance_business">Insurance
                                    Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="relevant_activities" id="fund_management_business"
                                    value="fund_management_business" {{#if filters.relevantActivities.fund_management_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label" for="fund_management_business">Fund
                                    Management Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="relevant_activities" id="finance_leasing_business"
                                    value="finance_leasing_business" {{#if filters.relevantActivities.finance_leasing_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label" for="finance_leasing_business">Finance/Leasing Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="relevant_activities" id="headquarters_business"
                                    value="headquarters_business" {{#if filters.relevantActivities.headquarters_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label" for="headquarters_business">Headquarters
                                    Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="relevant_activities" id="shipping_business"
                                    value="shipping_business" {{#if filters.relevantActivities.shipping_business }}checked {{/if}} />
                                <label class="custom-control-label" for="shipping_business">Shipping
                                    Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="relevant_activities" id="holding_business"
                                    value="holding_business" {{#if filters.relevantActivities.holding_business }}checked {{/if}} />
                                <label class="custom-control-label" for="holding_business">Holding
                                    Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="relevant_activities"
                                    id="intellectual_property_business" value="intellectual_property_business" {{#if
                                    filters.relevantActivities.intellectual_property_business }}checked {{/if}} />
                                <label class="custom-control-label" for="intellectual_property_business">Intellectual Property
                                    Business</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="relevant_activities" id="service_centre_business"
                                    value="service_centre_business" {{#if filters.relevantActivities.service_centre_business }}checked
                                    {{/if}} />
                                <label class="custom-control-label" for="service_centre_business">Service Centre
                                    Business</label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label for="filter_exported">Is exported</label>
                            <select   class="form-control" name="filter_exported" id="filter_exported" data-toggle="select2" data-value="{{filters.filter_exported}}">
                                <option value="" {{#ifEquals filters.filter_exported '' }} selected {{/ifEquals}}>ALL</option>
                                <option value="exported" {{#ifEquals filters.filter_exported 'exported' }} selected {{/ifEquals}}>EXPORTED</option>
                                <option value="not-exported" {{#ifEquals filters.filter_exported 'not-exported' }} selected {{/ifEquals}}>NOT EXPORTED</option>
                            </select>

                            <label class="my-1" for="filter_other">Other</label>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="resident_outside_bvi" id="resident_outside_bvi"
                                    value="Yes" {{#if filters.resident_outside_bvi }} checked {{/if}} />
                                <label class="custom-control-label" for="resident_outside_bvi">Tax
                                    residency claim outside BVI</label>
                            </div>
                            <br>
                        </div>
                    
                        <div class="col-md-2" style="padding-top:30px">
                            <button type="submit" form="generalForm" class='btn btn-primary waves-effect '>Search </button>
                            <button type="button" form="generalForm" id="clearFormBtn" class='btn btn-secondary waves-effect '>Reset filters
                            </button>
                        </div>
                   
                    </div>
                </form>
                <br />
                <div class="row">
                    <div class="col-md-12">
                        <h5 >
                            How does the search work?
                            <span class="fa-stack tooltip-wrapper" style="margin-top: -10px" data-toggle="tooltip" data-container="body"
                                data-placement="top" data-html="true" title="
                                    <small><b>Default search when entering page:</b> <br>
                                        The initial search result (loaded upon entering this page) contains the latest 100 submissions that were paid. If you
                                        are using the column headers to filter the search result, this filter will only affect the initial search query. This
                                        means by default it will only filter the latest 100 paid submissions according to the arrows on the column headers.
                                        <br>
                                        <b>Manual search: </b><br>    
                                        If you do a manual search result based on any of the search criteria’s available, the initial search results (the 100
                                        paid) will be replaced with a new search result set. Once the initial set is replaced the column header filters will
                                        work on the new set to match your specific search result.
                                    </small>">
                                <i class="fa text-primary fa-info-circle fa-stack fa-lg"></i>
                            </span>
                        </h5>
                    </div>
                </div>
                <br />
                <table id="selection-datatable" class="table dt-responsive nowrap w-100">
                    <thead>
                        <tr>
                            <th class="all">ID</th>
                            <th class="all">Entity</th>
                            <th class="all">Entity Code</th>
                            <th class="all">Status</th>
                            <th class="all">Submitted date</th>
                            <th class="all">Export date</th>
                            <th class="all">Resubmitted date</th>
                            <th class="all">Financial Period Start Date</th>
                            <th class="all">Financial Period End Date</th>
                            <th class="all">Payment method</th>
                            <th class="all">Payment Received</th>
                            <th class="all">Payment Reference</th>
                            <th class="all">Export files</th>
                        </tr>
                    </thead>


                    <tbody>
                        {{#each data}}
                            <tr data-id="{{id}}">
                                <td>{{id}}</td>
                                <td>{{company_data.name}}</td>
                                <td>{{company_data.code}}</td>
                                <td>{{status}}</td>

                                <td data-sort="{{formatDate submitted_at 'YYYY-MM-DD HH:mm'}}">
                                    {{formatDate submitted_at ../STANDARD_DATE_FORMAT}}
                                </td>
                                <td data-sort="{{formatDate exported_at 'YYYY-MM-DD HH:mm'}}">
                                    {{formatDate exported_at ../STANDARD_DATE_FORMAT}}
                                </td>
                                <td data-sort="{{formatDate resubmitted_at 'YYYY-MM-DD HH:mm'}}">
                                    {{#if resubmitted_at}}
                                        {{formatDate resubmitted_at ../STANDARD_DATE_FORMAT}}
                                    {{/if}}
                                </td>
                                <td data-sort="{{formatDate entity_details.financial_period_begins 'YYYY-MM-DD HH:mm'}}">
                                    {{formatDate entity_details.financial_period_begins ../STANDARD_DATE_FORMAT}}
                                </td>
                                <td data-sort="{{formatDate entity_details.financial_period_ends 'YYYY-MM-DD HH:mm'}}">
                                    {{formatDate entity_details.financial_period_ends ../STANDARD_DATE_FORMAT}}
                                </td>
                                <td>{{payment.payment_type}}</td>
                                <td  data-sort="{{formatDate payment.payment_received_at 'YYYY-MM-DD HH:mm'}}">
                                    {{formatDate payment.payment_received_at ../STANDARD_DATE_FORMAT}}
                                </td>
                                <td>{{payment.payment_reference}}</td>
                                <td style="vertical-align: middle;">
                                    {{#if show_attachments_download}}
                                    <input type="button" name="exportFilesBtn" id="export_files_{{id}}"
                                        class="btn btn-primary waves-effect waves-light"
                                        onclick="event.stopPropagation(); exportFiles('{{id}}')" value="Export">
                                    {{/if}}
                                </td>
                            </tr>
                        {{/each}}

                    </tbody>
                </table>

            </div>
        </div>
        <div class="row">
            <div class="col-12">&nbsp;</div>
        </div>
        <div class="row">
            <div class="col-12 text-sm-center form-inline">
                <div class="form-group mr-2 hidden">
                    <button id="btn-export-csv" class="btn btn-primary"><i class="mdi mdi mdi-content-save-edit mr-2"></i> Export to CSV</button>
                </div>
                <div class="ml-3 mr-2">
                    <a href='/substance/'
                        class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                </div>
                <div class="form-group mr-2">
                    <button id="btn-export-xls" class="btn btn-primary" style="display: none;"><i class="mdi mdi mdi-content-save-edit mr-2"></i> Export to xls</button>
                </div>
                <div class="form-group mr-2">
                    <button id="btn-export-files" class="btn btn-primary" style="display: none;"><i class="mdi mdi mdi-content-save-edit mr-2"></i> Export evidence files</button>
                </div>
            </div>
        </div>

    </div>
</div>

<form method="POST" action="./export" id="submitForm" name="submitForm">
    <input type="hidden" name="entryIds" value="" />
</form>

<form method="POST" action="./export-xls" id="submitFormXls" name="submitFormXls">
    <input type="hidden" name="entryIds" value="" />
</form>

<form method="POST" action="./export-evidence" id="submitFormFiles" name="submitFormFiles">
    <input type="hidden" name="entryIds" value="" />
</form>
<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript">
$(document).ready(function(){
    $('[data-toggle="tooltip"]').tooltip({
        trigger: 'hover',
        container: 'body'

    });

    let showLimitAlert = "{{showLimitAlert}}" === 'true';
    if (showLimitAlert) {
        toastr["warning"]('Maximum number of records reached. Please refine your search to reduce the size of query.', 'Limit reached!', {
            "timeOut": 100000,
        });
    }

    var table = $("#selection-datatable").DataTable({
        dom: 'Blfrtip',
        "columnDefs": [{ "visible": false, "targets": [0] }],
        "scrollX": true,
        select:{style:"multi"},
        "order": [],
        buttons: [ {
            text: 'Select All On Page',
            action: function() {
                table.rows({
                page: 'current'
                }).select();
            }
        },
        {
            text: '<i class="far fa-square"></i>',
            titleAttr: 'unselect all',
            action: function() {
                table.rows({
                page: 'current'
                }).deselect();
            }
        }],
        language:{
                paginate:{previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"}
        },
        drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")}
    })

    table.on('select', function ( e, dt, type, indexes ) {
        if ( type === 'row' ) {
            if(table.rows('.selected').data().length) {
                $('#btn-export-xls').show();
                $('#btn-export-files').show();
            } else {
                $('#btn-export-xls').hide();
                $('#btn-export-files').hide();
            }
        }
    });
    table.on('deselect', function ( e, dt, type, indexes ) {
        if ( type === 'row' ) {
            if(table.rows('.selected').data().length) {
                $('#btn-export-xls').show();
                $('#btn-export-files').show();
            } else {
                $('#btn-export-xls').hide();
                $('#btn-export-files').hide();
            }
        }
    });


    $('#btn-export-csv').click( function () {
        var data = table.rows('.selected').data();

        var entryIds = [];
        for (var idx=0; idx < data.length; idx++) {
            entryIds.push(data[idx][0])
        }
        var oForm = document.forms["submitForm"];
        oForm.elements["entryIds"].value = entryIds.join(';');
        oForm.submit();
    });

    $('#btn-export-xls').click(function () {
        var data  = table.rows('.selected').data();

        var entryIds = [];
        for (var idx=0; idx < data.length; idx++) {
            entryIds.push(data[idx][0])
        }
        var xlsForm = document.forms["submitFormXls"];
        xlsForm.elements["entryIds"].value = entryIds.join(";");
        xlsForm.submit();
    });

    $('#btn-export-files').click(function () {
        var data  = table.rows('.selected').data();

        var entryIds = [];
        for (var idx=0; idx < data.length; idx++) {
            entryIds.push(data[idx][0])
        }
        var filesForm = document.forms["submitFormFiles"];
        filesForm.elements["entryIds"].value = entryIds.join(";");
        filesForm.submit();
    });

    $("#clearFormBtn").on('click', () => {
        $("#generalForm")[0].reset();
        $("#generalForm input").val('');
        $("#filter_exported").val('').trigger('change');
        $('#generalForm input[type=checkbox]').prop('checked', false);
    })
});

function exportFiles(id) {
    var entryIds = [];
    entryIds.push(id);
    var filesForm = document.forms["submitFormFiles"];
    filesForm.elements["entryIds"].value = entryIds.join(";");
    filesForm.submit();
}

</script>

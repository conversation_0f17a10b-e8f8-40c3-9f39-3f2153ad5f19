{{!-- CONFIRM CLIENT MODAL --}}
<div class="modal fade" id="informationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Information Details</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <div id="showInfo" style="display: none">

                </div>

            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/templates/financial-report/informationmodalcontent.precompiled.js"></script>
<script type="text/javascript">


    $('#informationModal').on('show.bs.modal', async function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        let companyId = button.data('company-id');
        console.log(companyId)
        $.ajax({
            type: 'GET',
            url: '/financial-report-management/' + companyId +'/show-company-information',
            timeout: 5000,
            success: (response) => {
                if (response.status !== 200){
                    Swal.fire('Error', 'There was an error getting the submission information', 'error').then(()=>{
                        $('#informationModal').modal('hide');
                    });
                }
                else{
                  console.log(response)
                    let template = Handlebars.templates.informationmodalcontent;
                    let d = {
                        data: response.data,
                    };
                    let html = template(d);
                    $('#showInfo').html(html);
                    $('#showInfo').show()
                }
            },
            error: (err) => {
                console.log(err);
                Swal.fire('Error', 'There was an error getting the submission information', 'error').then(()=>{
                    $('#informationModal').modal('hide');
                });

            },
        });
    });


    $('#informationModal').on('hide.bs.modal', function () {
        $("#showInfo").html('');
        $('#showInfo').hide();
    });


</script>

{{!-- UNASSIGN CLIENT MODAL --}}
<div class="modal fade" id="resendElectronicInvitationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;"><strong>Send Electronic Id
                    Invitation</strong></h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <h4><strong>Are you sure?</strong></h4>
                    <span>This will send the ID request to <strong id="email-resend"></strong> address.</span>
                </div>
                <br>
                <br>
                <form class="form" novalidate id="sendElectronicIdForm">
                    <div class="row mt-2">
                        <div class="col-6">
                            <label>Would you like to change the e-mail address?*</label>
                        </div>
                        <div class="col-6 text-left">
                            <div class="custom-control custom-radio custom-control-inline">
                                <input
                                        type="radio"
                                        class="custom-control-input"
                                        id="changeEmailYes"
                                        name="changeEmail"
                                        value="YES"
                                        required
                                />
                                <label class="custom-control-label" for="changeEmailYes">Yes</label>
                            </div>
                            <div class="custom-control custom-radio custom-control-inline">
                                <input
                                        type="radio"
                                        class="custom-control-input"
                                        id="changeEmailNo"
                                        name="changeEmail"
                                        checked
                                        value="NO"
                                />
                                <label class="custom-control-label" for="changeEmailNo">No</label>
                            </div>
                        </div>
                    </div>
                    <div class="row  mt-2" id="newEmailRow" style="display: none">
                        <div class="col-6">
                            <label for="newEmailAddress">New e-mail address*</label>
                        </div>
                        <div class="col-6">
                            <input name="newEmailAddress" id="newEmailAddress" type="email"
                                   class="form-control"/>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <label>Please select the template:</label>
                        </div>
                        <div class="col-6">
                            <div class="custom-control custom-radio">
                                <input
                                        type="radio"
                                        class="custom-control-input"
                                        id="firstTimeTemplate"
                                        name="emailTemplate"
                                        value="new-invitation-template" required
                                />
                                <label class="custom-control-label" for="firstTimeTemplate">New first time
                                    template</label>
                            </div>
                            <div class="custom-control custom-radio ">
                                <input
                                        type="radio"
                                        class="custom-control-input"
                                        id="incorrectInformationTemplate"
                                        name="emailTemplate"
                                        value="incorrect-information-template"
                                />
                                <label class="custom-control-label" for="incorrectInformationTemplate">Document
                                    incomplete/incorrect</label>
                            </div>
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="submit" form="sendElectronicIdForm" class="btn solid royal-blue" id="confirmResendButton">
                    Confirm
                </button>
                <button id="loaderSending" class="btn  solid royal-blue" type="button" style="display: none" disabled>
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    Loading...
                </button>
            </div>
        </div>
    </div>
</div>

<script>

    let resendButton;
    let relaId;

    $('#resendElectronicInvitationModal').on('show.bs.modal', function (event) {
        resendButton = $(event.relatedTarget); // Button that triggered the modal
        $("#email-resend").text(resendButton.data('email'))
    });

    $('#sendElectronicIdForm input[type="email"]').on('keyup', function () {
        const valid_email = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        const val = $(this).val();
        const bad = !val.match(valid_email);
        $(this).toggleClass('is-invalid', bad);
    });


    $("input[name='changeEmail']").on('change', function () {
        const val = $(this).val();
        if (val === "YES") {
            $("#newEmailRow").show(200);
            $("#newEmailAddress").prop('required', true);
        } else {
            $("#newEmailRow").hide(200);
            $("#newEmailAddress").prop('required', false);
        }
    });

    $("#sendElectronicIdForm").submit(function (event) {
        let invalidRadios = false;
        event.preventDefault();
        $("#loaderSending").show();
        $("#confirmResendButton").hide();
        $('input[required]:visible').trigger('keyup');
        $("input[type='radio'][required]:visible").each(function () {
            const val = $('input[name="' + this.name + '"]:checked').val();
            if (val === undefined) {
                $('input[name="' + this.name + '"]').toggleClass("is-invalid", true);
                invalidRadios = true;
            } else {
                $('input[name="' + this.name + '"]').toggleClass("is-invalid", false);
            }
        });
        if ($(".is-invalid:visible").length === 0 && !invalidRadios) {
            $("#confirmResendButton").prop('disabled', true);
            const incorporationId = resendButton.data('incorporation-id');
            const relationId = resendButton.data('relation-id');
            $.ajax({
                type: 'POST',
                url: '/client-incorporation/' + incorporationId + '/relations/' + relationId + '/send-electronic-invitation',
                data: $(this).serialize(),
                success: () => {
                    Swal.fire('Success', 'Electronic Id invitation has been send successfully', 'success').then(() => {
                        $("#confirmResendButton").show();
                        $("#loaderSending").hide();
                        $("#resendElectronicInvitationModal").modal('hide');
                        location.reload();
                    });
                },
                error: (err) => {
                    $("#loaderSending").hide();
                    $("#confirmResendButton").show();
                    Swal.fire('Error', 'There was an error sending the invitation', 'error');
                },
            });
        }
        else{
            $("#loaderSending").hide();
            $("#confirmResendButton").show();
        }


    });


</script>

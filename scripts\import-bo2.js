const BeneficialOwnerModel = require('../models/beneficialowners');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const xlsx = require('xlsx');
const fs = require('fs');
const moment = require('moment');
const {countries} = require('./utils/constants');
dotenv.config();


try {
  mongoose.connect(process.env.MONGODB, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    useFindAndModify: false,
  }, function (err) {
    throw err;
  });
  const db = mongoose.connection;
  db.on('error', console.error.bind(console, 'MongoDB connection error:'));
  // Start process - Process submissions from excel file
  const submissions = processSubmissions();
  // Search imported submissions in database, update if found
  // and create new if not
  if (submissions.length > 0){
    importBeneficialOwners(submissions).then(r => {
      console.log("IMPORT2 FINISH ", r)
    });
  }
  else{
    console.log("EMPTY NEW BENEFICIAL OWNERS")
  }

} catch (e) {
  console.log(e);
}

function processSubmissions() {
  try {
    const data = new Uint8Array(fs.readFileSync('FF-S-BO part11.xlsx'));
    const workbook = xlsx.read(data, {
      type: "array",
      cellText: false,
      cellDates: true,
      sheetStubs: true,
    });

    let beneficialOwners = [];
    let naturalPerson = {
      principalAddress: {},
      residentialAddress: {},
      officeAddress: {},
      corporation: {},
      boInformation: {},
      mailingAddress: {},
      contactInformation: {},
      createdAt: new Date()
    };
    // Each sheet name is a vaccination date
    for (const beneficial of workbook.SheetNames) {
      const worksheet = workbook.Sheets[beneficial];
      // Map worksheet as submission array

      for (let cell in worksheet) {
        let cellAsString = cell.toString();
        // SAVE TITLES FOR VALIDATION
        const cellNum = cellAsString.match(/\d+/g);
        if (cellAsString[1] !== "r" && cellAsString[1] !== "m" && cellNum > 1) {
          cellAsString = cellAsString.replace(/[0-9]/g, '');
          if (cellAsString === "A") {
            naturalPerson.code = worksheet[cell].v;
          }
          if (cellAsString === "B") {
            naturalPerson.type = worksheet[cell].v;
          }
          if (cellAsString === "C") {
            let personOrEntity = worksheet[cell].v;
            if (!personOrEntity || personOrEntity.length < 1){
              personOrEntity = "Person"
            }
            naturalPerson.personOrEntity = personOrEntity.toUpperCase();
          }
          if (cellAsString === "D") {
            naturalPerson.salutation = worksheet[cell].v;
          }
          if (cellAsString === "E") {
            naturalPerson.fullName = worksheet[cell].v;
          }
          if (cellAsString === "F") {
            const dob = worksheet[cell].v
            naturalPerson.dateOfBirth = dob ? new Date(moment(dob).format('YYYY-MM-DD')) : undefined;
          }
          if (cellAsString === "G") {
            naturalPerson.firstName = worksheet[cell].v;
          }
          if (cellAsString === "H") {
            naturalPerson.middleName = worksheet[cell].v;
          }
          if (cellAsString === "I") {
            naturalPerson.lastName = worksheet[cell].v;
          }
          if (cellAsString === "J") {
            const nationality =  worksheet[cell].v;
            let country;
            if(nationality && nationality.length > 0){
              country = countries.find((c) => c.nationality.some((item) => item === nationality.toLowerCase()));
            }
            naturalPerson.nationality = country ? country.name : nationality;
          }
          if (cellAsString === "K") {
            const nationality =  worksheet[cell].v;
            let country;
            if(nationality && nationality.length > 0){
              country = countries.find((c) => c.nationality.some((item) => item === nationality.toLowerCase()));
            }
            naturalPerson.secondNationality = country ? country.name : nationality;
          }
          if (cellAsString === "L") {
            const nationality =  worksheet[cell].v;
            let country;
            if(nationality && nationality.length > 0){
              country = countries.find((c) => c.nationality.some((item) => item === nationality.toLowerCase()));
            }
            naturalPerson.otherNationality = country ? country.name : nationality;
          }
          if (cellAsString === "M") {
            const countryName =  worksheet[cell].v;
            let country;
            if(countryName && countryName.length > 0){
              country = countries.find((c) => c.name.toUpperCase() === countryName.toUpperCase());
            }
            naturalPerson.placeOfBirth =  country ? country.name : countryName;
          }
          if (cellAsString === "N") {
            const countryName =  worksheet[cell].v;
            let country;
            if(countryName && countryName.length > 0){
              country = countries.find((c) => c.name.toUpperCase() === countryName.toUpperCase());
            }
            naturalPerson.residence = country ? country.name : countryName;
          }
          if (cellAsString === "O") {
            const countryName =  worksheet[cell].v;
            let country;
            if(countryName && countryName.length > 0){
              country = countries.find((c) => c.name.toUpperCase() === countryName.toUpperCase());
            }
            naturalPerson.secondResidence = country ? country.name : countryName;
          }
          if (cellAsString === "P") {
            const countryName =  worksheet[cell].v;
            let country;
            if(countryName && countryName.length > 0){
              country = countries.find((c) => c.name.toUpperCase() === countryName.toUpperCase());
            }
            naturalPerson.thirdResidence =  country ? country.name : countryName;
          }
          if (cellAsString === "Q") {
            naturalPerson.idType = worksheet[cell].v;
          }
          if (cellAsString === "R") {
            naturalPerson.passportNumber = worksheet[cell].v;
          }
          if (cellAsString === "S") {
            const countryName =  worksheet[cell].v;
            let country;
            if(countryName && countryName.length > 0){
              country = countries.find((c) => c.name.toUpperCase() === countryName.toUpperCase());
            }
            naturalPerson.passportCountry =  country ? country.name : countryName;
          }
          if (cellAsString === "T") {
            const expirationDate = worksheet[cell].v;

            naturalPerson.passportExpiration =expirationDate ? new Date(moment(expirationDate).format('YYYY-MM-DD')) : undefined;
          }
          if (cellAsString === "U") {
            naturalPerson.principalAddress.primaryAddress = worksheet[cell].v;
          }
          if (cellAsString === "V") {
            naturalPerson.principalAddress.secondaryAddress = worksheet[cell].v;
          }
          if (cellAsString === "W") {
            naturalPerson.principalAddress.city = worksheet[cell].v;
          }
          if (cellAsString === "X") {
            naturalPerson.principalAddress.state = worksheet[cell].v;
          }
          if (cellAsString === "Y") {
            const countryName =  worksheet[cell].v;
            let country;
            if(countryName && countryName.length > 0){
              country = countries.find((c) => c.name.toUpperCase() === countryName.toUpperCase());
            }
            naturalPerson.principalAddress.country  = country ? country.name : countryName;
          }
          if (cellAsString === "Z") {
            naturalPerson.principalAddress.postalCode = worksheet[cell].v;
          }
          if (cellAsString === "AA") {
            naturalPerson.mailingAddress.primaryAddress = worksheet[cell].v;
          }
          if (cellAsString === "AB") {
            naturalPerson.mailingAddress.secondaryAddress = worksheet[cell].v;
          }
          if (cellAsString === "AC") {
            naturalPerson.mailingAddress.city = worksheet[cell].v;
          }
          if (cellAsString === "AD") {
            naturalPerson.mailingAddress.state = worksheet[cell].v;
          }
          if (cellAsString === "AE") {
            const countryName =  worksheet[cell].v;
            let country;
            if(countryName && countryName.length > 0){
              country = countries.find((c) => c.name.toUpperCase() === countryName.toUpperCase());
            }
            naturalPerson.mailingAddress.country = country ? country.name : countryName;
          }
          if (cellAsString === "AF") {
            naturalPerson.mailingAddress.postalCode = worksheet[cell].v;
          }
          if (cellAsString === "AG") {
            naturalPerson.contactInformation.phoneNumber = worksheet[cell].v;
          }
          if (cellAsString === "AH") {
            naturalPerson.contactInformation.workPhoneNumber = worksheet[cell].v;
          }
          if (cellAsString === "AI") {
            naturalPerson.contactInformation.mobileNumber = worksheet[cell].v;
          }
          if (cellAsString === "AJ") {
            naturalPerson.contactInformation.faxNumber = worksheet[cell].v;
          }
          if (cellAsString === "AK") {
            naturalPerson.email = worksheet[cell].v;
          }
          if (cellAsString === "AL") {
            naturalPerson.contactInformation.workEmail = worksheet[cell].v;
          }
          if (cellAsString === "AM") {
            naturalPerson.officeType = worksheet[cell].v;
          }
          if (cellAsString === "AN") {
            naturalPerson.comment = worksheet[cell].v;
          }
          if (cellAsString === "AO") {
            naturalPerson.contactInformation.fax =  worksheet[cell].v;
          }
          if (cellAsString === "AP") {
            naturalPerson.contactInformation.additionalEmail =  worksheet[cell].v;
          }
          if (cellAsString === "AQ") {
            naturalPerson.updateAt =  worksheet[cell].v;
            const updatedAt = worksheet[cell].v;
            naturalPerson.updatedAt = updatedAt ? new Date(moment(updatedAt).format('YYYY-MM-DD')) : undefined;
          }
          if (cellAsString === "AR") {
            const entryDate = worksheet[cell].v;
            naturalPerson.entryDate = entryDate ? new Date(moment(entryDate).format('YYYY-MM-DD')) : undefined;
          }
          if (cellAsString === "AX") {
            naturalPerson.corporation.name =  worksheet[cell].v;
          }
          if (cellAsString === "AY") {
            naturalPerson.recordType =  worksheet[cell].v;
          }
          if (cellAsString === "AZ") {
            naturalPerson.gender =  worksheet[cell].v;
          }
          if (cellAsString === "BA") {
            naturalPerson.nationalId =  worksheet[cell].v;
          }
          if (cellAsString === "BB") {
            naturalPerson.boInformation.type =  worksheet[cell].v;
          }
          if (cellAsString === "BC") {
            naturalPerson.formerFirstName =  worksheet[cell].v;
          }
          if (cellAsString === "BD") {
            naturalPerson.formerMiddleName =  worksheet[cell].v;
          }
          if (cellAsString === "BE") {
            naturalPerson.formerLastName =  worksheet[cell].v;
          }
          if (cellAsString === "BF") {
            naturalPerson.boInformation.secondaryType =  worksheet[cell].v;
          }
          if (cellAsString === "BG") {
            naturalPerson.boInformation.note =  worksheet[cell].v;
          }
          if (cellAsString === "BH") {
            naturalPerson.boInformation.beneficialNumber =  worksheet[cell].v;
          }
          if (cellAsString === "BI") {
            naturalPerson.boInformation.exemptedPerson =  worksheet[cell].v;
          }
          if (cellAsString === "BJ") {
            naturalPerson.boInformation.registeredEntity =  worksheet[cell].v;
          }
          if (cellAsString === "BK") {
            naturalPerson.boInformation.jurisdiction =  worksheet[cell].v;
          }
          if (cellAsString === "BL") {
            naturalPerson.boInformation.regulatorName =  worksheet[cell].v;
          }
          if (cellAsString === "BM") {
            naturalPerson.boInformation.sovereignName =  worksheet[cell].v;
          }
          if (cellAsString === "BN") {
            naturalPerson.boInformation.ownershipPercent =  worksheet[cell].v;
          }
          if (cellAsString === "BO") {
            naturalPerson.boInformation.corporateStatus =  worksheet[cell].v;
          }
          if (cellAsString === "BP") {
            naturalPerson.boInformation.notSend =  worksheet[cell].v;
          }
          if (cellAsString === "BQ") {
            naturalPerson.boInformation.corporateStockCode =  worksheet[cell].v;
          }
          if (cellAsString === "BR") {
            naturalPerson.boInformation.stockExchange =  worksheet[cell].v;
          }

          if (cellAsString === "BS") {
            naturalPerson.boInformation.trustName = worksheet[cell].v;

            if(naturalPerson.code){
              if(!naturalPerson.createdAt) {
                naturalPerson.createdAt = new Date();
              }
              beneficialOwners.push(naturalPerson);
            }

            naturalPerson = {
              principalAddress: {},
              residentialAddress: {},
              officeAddress: {},
              corporation: {},
              boInformation: {},
              mailingAddress: {},
              contactInformation: {}
            };
          }
        }

      }
    }
    return beneficialOwners;
  } catch (e) {
    console.log("Error processing xlsx data: ", e);
    return []
    }

}

async function importBeneficialOwners(submissions) {
  try {
    const importLog = [['ID','Code', 'Last Name', 'DOB', 'Import date', 'Action']];
    for (let submission of submissions) {
      let regExLastname = '^' + submission.lastName + '$';
      const application = await BeneficialOwnerModel.findOne({
        'lastName': {'$regex': regExLastname, $options: 'i'},
        'dateOfBirth': submission.dateOfBirth
      });
      if (application) {
          // Application found but had both vaccines already applied, then skip
          importLog.push([
            application._id.toString(),
            application.code,
            application.lastName,
            application.dateOfBirth,
            new Date(),
            'SKIPPED'
          ]);
      } else {
        // Application not found, then create new one
        const newApplication = new BeneficialOwnerModel(submission);
        newApplication.partitionkey = 'beneficialowner';
        const savedApplication = await newApplication.save();
        importLog.push([
          savedApplication._id.toString(),
          savedApplication.code,
          savedApplication.lastName,
          savedApplication.dateOfBirth,
          new Date(),
          'CREATED NEW BO/DIRECTOR'
        ]);
      }
    }
    if (importLog.length - 1 === submissions.length) {
      const filename = 'import_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
      const logWorkbook = xlsx.utils.book_new();
      const logWorksheet = xlsx.utils.aoa_to_sheet(importLog);
      xlsx.utils.book_append_sheet(logWorkbook, logWorksheet, 'Import ' + moment.utc().format('YYYY-MM-DD'));
      xlsx.writeFile(logWorkbook, filename);
    }
    return {"success":  true};
  } catch (e) {
    console.log(e);
    return {"success":  false};
  }
}



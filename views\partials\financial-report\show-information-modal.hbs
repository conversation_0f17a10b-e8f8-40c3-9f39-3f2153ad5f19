<div class="modal fade" id="showInformationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Information Details</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <div id="showInfo" style="display: none">

                </div>

            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/templates/financial-report/showinformationmodalcontent.precompiled.js"></script>
<script>
    $('#showInformationModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        let reportId = button.data('report-id');
        $.ajax({
            type: 'GET',
            url: '/financial-report-management/' + reportId+'/show-information',
            timeout: 5000,
            success: (data) => {
                if (data.status !== 200){
                    Swal.fire('Error', 'There was an error getting the submission information', 'error').then(()=>{
                        $('#showInformationModal').modal('hide');
                    });
                }
                else{
                    let template = Handlebars.templates.showinformationmodalcontent;
                    let d = {
                        reportData: data.reportData,
                    };
                    let html = template(d);
                    $('#showInfo').html(html);
                    $('#showInfo').show();
                    $("#table-request").DataTable({
                        "pageLength": 3,
                        "lengthChange": false,
                        "ordering": false,
                        language: {
                            paginate: {
                                previous: "<i class='mdi mdi-chevron-left'>",
                                next: "<i class='mdi mdi-chevron-right'>"
                            }
                        },
                        bFilter: false,
                        lengthMenu: [[3, 5, 10], [3, 5, 10]],
                        drawCallback: function () {
                            //$(this.api().table().header()).hide();
                            $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
                            $(".paginate_button.active a").css({"background-color": "transparent", "color": "black" ,"font-weight": "bold"});
                        }
                    });

                }
            },
            error: (err) => {
                console.log(err);
                Swal.fire('Error', 'There was an error getting the submission information', 'error').then(()=>{
                    $('#showInformationModal').modal('hide');
                });

            },
        });
    });


    $('#showInformationModal').on('hide.bs.modal', function () {
        $("#showInfo").html('');
        $('#showInfo').hide();
    });


</script>

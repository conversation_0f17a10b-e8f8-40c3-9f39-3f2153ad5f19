{{!-- CONFIRM MODAL --}}
<div class="modal fade" id="confirmReviewerOfficerModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="message_modal" class="modal-body p-3 text-justify">
            </div>
            <div class="modal-body p-3 text-justify">
                <p>Comment:</p><textarea id="modalInputComent" rows="8" cols="60"
                                         placeholder="Escribe aquí tus comentarios"></textarea>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="button" class="btn solid royal-blue" id="sendFileReviewerOfficerButton"
                        data-status="send-officer">Confirm
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    let statusReviewText;
    let statusReviewId;
    let submitComment;
    $('#confirmReviewerOfficerModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        statusReviewText = button.data('status');
        statusReviewId = button.data('id');
        submitComment = $('#modalInputComent').val();

        if (statusReviewText === "send-officer") {
            $('#message_modal').html('You are about to submit the File Review back to a File Reviewer Officer.');
        } else if (statusReviewText === "compliance") {
            $('#message_modal').html('You are about to submit the File Review back to a Compliance.');
        }
    });
    $('#sendFileReviewerOfficerButton').on('click', function () {
        if (statusReviewText === "send-officer") {
            $.ajax({
                type: 'POST',
                url: '/file-reviewer/quality-assurance-review/' + statusReviewId + '/submit-review',
                data: {status: statusReviewText, comment: submitComment},
                success: () => {
                    Swal.fire('Success', 'The review has been submitted to the Reviewer Officer successfully', 'success').then(() => {
                        location.href = '/file-reviewer/quality-assurance-list';
                    });
                },
                error: (err) => {
                    $('#confirmReviewerOfficerModal').modal('hide');
                    Swal.fire('Error', 'There was an error submitting your review to the Reviewer Officer', 'error');
                },
            });
        } else if (statusReviewText === "compliance") {
            $.ajax({
                type: 'POST',
                url: '/file-reviewer/quality-assurance-review/' + statusReviewId + '/submit-review',
                data: {status: statusReviewText, comment: submitComment},
                success: () => {
                },
                error: (err) => {
                    $('#confirmReviewerOfficerModal').modal('hide');
                    Swal.fire('Error', 'There was an error submitting your review to the Compliance', 'error');
                },
            });
        }
    });
</script>

const CompanyModel = require("../models/company").schema;
const FinancialReportModel = require('../models/financialreport')
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config();


const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const result = await updateAccountingModule();

    console.log('Script executed successfully', result);
  } catch (error) {
    console.error('Error in the script:', error);
  } finally {
    mongoose.disconnect();
  }
}


async function updateAccountingModule() {
  try {

    let updateLog = [['ID', 'Company Code', 'Update date', 'Action']];
    const companiesToUpdate = await CompanyModel.aggregate([
      {
        $match: {
          "financialReportDetails": { "$exists": true }
        }
      },
      {
        $project: {
          _id: 1,
          code: 1,
          masterclientcode: 1,
          financialReportDetails: 1,
          accountingRecordsModule: 1
        }
      }
    ]);


    console.log("companiesToUpdate  ", companiesToUpdate);
    if (companiesToUpdate.length > 0) {
      for (let i = 0; i < companiesToUpdate.length; i++) {
        console.log('processing ' + i + '  from ' + companiesToUpdate.length)

        const company = companiesToUpdate[i];

        try {

          const selfServiceCompleteAnnualReturnAmount = company.accountingRecordsModule?.selfServiceCompleteAnnualReturnAmount ?
            company.accountingRecordsModule?.selfServiceCompleteAnnualReturnAmount : 
            company.financialReportDetails?.selfServiceCompleteAnnualReturnAmount;

          const selfServicePrepareAnnualReturnAmount = company.accountingRecordsModule?.selfServicePrepareAnnualReturnAmount ?
            company.accountingRecordsModule?.selfServicePrepareAnnualReturnAmount :
            company.financialReportDetails?.selfServicePrepareAnnualReturnAmount;

          const tridentServiceCompleteAnnualReturnAmount = company.accountingRecordsModule?.tridentServiceCompleteAnnualReturnAmount ?
            company.accountingRecordsModule?.tridentServiceCompleteAnnualReturnAmount :
            company.financialReportDetails?.tridentServiceCompleteAnnualReturnAmount;

          const tridentServiceDropAccountingRecordsAmount = company.accountingRecordsModule?.tridentServiceDropAccountingRecordsAmount ?
            company.accountingRecordsModule?.tridentServiceDropAccountingRecordsAmount :
            company.financialReportDetails?.tridentServiceDropAccountingRecordsAmount;

          let accountingRecords = {
            active: company.accountingRecordsModule?.active ?? false,
            selfServiceCompleteAnnualReturnAmount: selfServiceCompleteAnnualReturnAmount,
            selfServicePrepareAnnualReturnAmount: selfServicePrepareAnnualReturnAmount,
            tridentServiceCompleteAnnualReturnAmount: tridentServiceCompleteAnnualReturnAmount,
            tridentServiceDropAccountingRecordsAmount: tridentServiceDropAccountingRecordsAmount,
            firstFinancialPeriodStart: company.financialReportDetails?.firstFinancialPeriodStart ? company.financialReportDetails?.firstFinancialPeriodStart :  null,
            firstFinancialPeriodEnd: company.financialReportDetails?.firstFinancialPeriodEnd ? company.financialReportDetails?.firstFinancialPeriodEnd : null,
          };

          console.log("accountingRecords ", accountingRecords);

          
          const result = await CompanyModel.updateOne(
            { _id: company._id }, 
            { 
              $set: { 
                "accountingRecordsModule.active": accountingRecords.active,
                "accountingRecordsModule.selfServiceCompleteAnnualReturnAmount": accountingRecords.selfServiceCompleteAnnualReturnAmount,
                "accountingRecordsModule.selfServicePrepareAnnualReturnAmount": accountingRecords.selfServicePrepareAnnualReturnAmount,
                "accountingRecordsModule.tridentServiceCompleteAnnualReturnAmount": accountingRecords.tridentServiceCompleteAnnualReturnAmount,
                "accountingRecordsModule.tridentServiceDropAccountingRecordsAmount": accountingRecords.tridentServiceDropAccountingRecordsAmount,
                "accountingRecordsModule.firstFinancialPeriodStart": accountingRecords.firstFinancialPeriodStart,
                "accountingRecordsModule.firstFinancialPeriodEnd": accountingRecords.firstFinancialPeriodEnd,
              },
              $unset: { "financialReportDetails": 1 }
            },
            { strict:false }
          );

          await FinancialReportModel.updateMany(
            {
              "companyData.code": company.code,
              "companyData.masterclientcode": company.masterclientcode
            },
            { 
              $set: { 
                "companyData.accountingRecordsModule.active": accountingRecords.active,
                "companyData.accountingRecordsModule.selfServiceCompleteAnnualReturnAmount": accountingRecords.selfServiceCompleteAnnualReturnAmount,
                "companyData.accountingRecordsModule.selfServicePrepareAnnualReturnAmount": accountingRecords.selfServicePrepareAnnualReturnAmount,
                "companyData.accountingRecordsModule.tridentServiceCompleteAnnualReturnAmount": accountingRecords.tridentServiceCompleteAnnualReturnAmount,
                "companyData.accountingRecordsModule.tridentServiceDropAccountingRecordsAmount": accountingRecords.tridentServiceDropAccountingRecordsAmount,
                "companyData.accountingRecordsModule.firstFinancialPeriodStart": accountingRecords.firstFinancialPeriodStart,
                "companyData.accountingRecordsModule.firstFinancialPeriodEnd": accountingRecords.firstFinancialPeriodEnd,
              },
              $unset: { "companyData.financialReportDetails": 1 }
            },
            { strict: false}
          );

    
          if (result.nModified > 0) {
            updateLog.push([company._id?.toString(), company.code, new Date(), 'SUCCESS']);
          } else {
            updateLog.push([company._id?.toString(), company.code, new Date(), 'ERROR: NOT FOUND']);
          }
          
        } catch (error) {
          console.error('Error:', error.message);
          updateLog.push([company._id?.toString(), company.code, new Date(), 'ERROR UPDATING']);
        }
      }
    }


    // create entities bo
    console.log("companies updated ", updateLog.length - 1);

    const filename = 'update_ar_company_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'companies ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalRows": updateLog.length - 1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}


runScript();
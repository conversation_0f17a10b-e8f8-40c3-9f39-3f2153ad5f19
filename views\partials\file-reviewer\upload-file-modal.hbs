<div
        id="upload-modal"
        class="modal fade"
        tabindex="-1"
        role="dialog"
        aria-labelledby="uploadModal"
        style="display: none;"
        aria-hidden="true"
>
    <div class="modal-dialog modal-lg contour container-fluid">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="upload-modal-title">
                    Upload file: <span id="upload-modal-file-label" class="font-weight-bold"></span>
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <p>
                                Maximum of <span id="maxUpload"></span> File(s), PDF only. File must not be password
                                protected.
                            </p>
                            <div id="uploadModalForm" class="dropzone">
                                <div class="fallback">
                                    <input name="fileUploaded" type="file" multiple/>
                                </div>
                                <div class="dz-message needsclick">
                                    <i class="h1 text-muted dripicons-cloud-upload"></i>
                                    <h3>Drop files here or click to upload.</h3>
                                    <span class="text-muted">Files will be automatically uploaded</span>
                                </div>
                            </div>
                            <div id="uploadedFiles" class="mt-2 text-center text-muted">

                            </div>
                        </div>
                        <!-- end card-body-->
                        <div class="modal-footer justify-content-end pb-0">
                            <button type="button" class="btn solid royal-blue" data-dismiss="modal">
                                <i class="mdi mdi-send mr-1"></i>Close
                            </button>
                        </div>
                    </div>
                    <!-- end card-->
                </div>
                <!-- end col -->
            </div>
            <!-- end row -->
        </div>
    </div>
</div>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/uploadedfiles.precompiled.js"></script>

<script type="text/javascript">
    let row;
    $("#uploadedFiles").html('');
    Dropzone.autoDiscover = false;
    $(function () {
        let field = '';
        let name = '';
        let id = '';
        let rowIndex;

        let fileGroup = '';
        let button = '';
        const myDropZone = new Dropzone('#uploadModalForm', {
            url: '/',
            acceptedFiles: 'application/pdf',
            autoProcessQueue: true,
            parallelUploads: 3,
            maxFiles: 3,
            maxFilesize: 5,
            paramName: function () {
                return 'fileUploaded';
            },
            uploadMultiple: true,
            init: function () {
                this.on('processing', function () {
                    this.options.url = '/file-reviewer/company-file-review/upload-document/' + id;
                });
                this.on('success', function () {
                    $('#standard-file-present-' + row).prop('checked', true);
                    let $fileUpload = $('#standard-file-' + row);                   
                    $fileUpload.text('Modify');
                    $fileUpload.css({
                        'background-color': '#0AC292',
                        'border-color': '#0AC292',
                    });
                    refreshUploadedFiles(id, fileGroup, rowIndex, row);
                    
                });
                this.on('sending', function (file, xhr, formData) {
                    if (!formData.has('fileType')) {
                        formData.append('fileType', field);
                    }
                    if (!formData.has('rowIndex')) {
                        formData.append('rowIndex', rowIndex);
                    }
                    if (!formData.has('fileGroup')) {
                        formData.append('fileGroup', fileGroup);
                    }
                });

                this.on('errormultiple', function (files, response) {
                });

                this.on('maxfilesexceeded', function (file) {
                });

                this.on('resetFiles', function () {
                    if (this.files.length !== 0) {
                        for (i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                        this.files.length = 0;
                    }
                    $('#maxUpload').text(this.options.maxFiles);
                });
            },
        });

        $('#upload-modal').on('show.bs.modal', function (event) {
            button = $(event.relatedTarget); // Button that triggered the modal
            name = button.data('field'); //name of the file
            field = name.replace(/[\s\’\'\/\(\)]/g, ''); //formatted name with no special chars
            id = button.data('review-id'); // _id
            rowIndex = button.data('row-id'); // row index
            row = button.data('row'); // -> @key
            fileGroup = button.data('file-group'); // standard, bo, listed...
            $('#upload-modal-file-label').text(name);
            // GET FILES UPLOADED PREVIOUSLY
            refreshUploadedFiles(id, fileGroup, rowIndex);

            let isProvided = false;
            isProvided =  button.data('provided');

            if (isProvided){
              disableProvidedField(id, rowIndex, row)
            }

            const modal = $(this);
            const objDZ = Dropzone.forElement('#uploadModalForm');
            objDZ.emit('resetFiles');
        });


        function disableProvidedField(reviewId, rowIndex, row){
            try {
                $.ajax({
                    type: 'POST',
                    url: '/file-reviewer/company-file-review/update-file-provided-field',
                    data: {
                        reviewId: reviewId,
                        fileId: rowIndex
                    },
                    timeout: 5000,
                    success: function (data) {
                        if (data.status === 200){
                            let $fileUpload = $('#standard-file-' + row);
                            $fileUpload.text('Modify');
                            $fileUpload.css({
                                'background-color': '#0AC293',
                                'border-color': '#0AC293',
                            });
                            button.data('provided', false);
                        }
                    },
                    error: function (res) {
                        console.log("Error updating the provided files value: ", res)
                    },
                });
            }catch (e) {
                console.log("Error updating the provided files value: ", e)
            }
        }
    });

    function deleteFile(id, fileGroup, rowId, fileId, blobName) {
        $.ajax({
            type: 'DELETE',
            url: '/file-reviewer/company-file-review/uploaded-files/',
            data: {
                reviewId: id,
                group: fileGroup,
                rowId: rowId,
                fileId: fileId,
                blobName: blobName,
                type: "standard"
            },
            success: function (res) {
                if (res.result) {
                    refreshUploadedFiles(id, fileGroup, rowId);
                }
            },
            dataType: 'json',
        });
        return false;
    }

    function downloadFile(reviewId,type,  rowId, fileId) {
        const url = "/file-reviewer/reviews/" + reviewId + "/download/standard/" + rowId + "/" + fileId ;
        window.open(url,"_blank");
        return false;
    }

    function refreshUploadedFiles(id, fileGroup, rowId) {
        $.ajax({
            type: 'GET',
            url: '/file-reviewer/company-file-review/uploaded-files',
            data: {
                reviewId: id,
                fileGroup: fileGroup,
                fileId: rowId,
                type: "standard"
            },
            timeout: 5000,
            success: function (data) {
                let template = Handlebars.templates.uploadedfiles;
                let d = {
                    id: id,
                    files: data.files ? data.files : [],
                    group: fileGroup,
                    rowId: rowId,
                };
                let html = template(d);
                $('#uploadedFiles').html(html);
                if (data && data.files && data.files.length === 0) {
                    $('#standard-file-present-' + row).prop('checked', false);
                    let $fileUpload = $('#standard-file-' + row);
                    $fileUpload.text('Upload');
                    $fileUpload.css({
                        'background-color': '#0081b4',
                        'border-color': '#0081b4',
                    });
                }
            },
            error: function (res) {
                Swal.fire('Error', 'There was an error getting files', 'error');
            },
        });

    }


    $("#upload-modal").on("hidden.bs.modal", function(){
        $("#uploadedFiles").html('');
    });
</script>

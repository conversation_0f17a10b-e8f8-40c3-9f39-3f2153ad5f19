const excel = require("node-excel-export");
const moment = require("moment");
const {filereview: FileReviewModel} = require('../../models/filereview');
const { getContainerClient } = require('../../utils/azureStorage');
const httpConstants = require('http2').constants;

exports.getDashboard = async function (req, res) {
  try {
    console.log(new Date().toString() + "data loading");

    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_FILE_REVIEW_STATISTICS,
      process.env.AZURE_STORAGE_ACCOUNT,
      process.env.AZURE_STORAGE_ACCESS_KEY
    );

    const blobClient = containerClient.getBlobClient("statistics.json");
    const response = await blobClient.downloadToBuffer();
    const text = response.toString();
    res.render('file-reviewer/management-statistics/statistics-companies-dashboard',
    {
        user: req.session.user,
        title: "File Review Statistics",
        allData: JSON.parse(text),
    });
  } catch (error) {
    console.log(error);
    res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
  }
};

exports.exportStatisticsXls = async function (req, res, next) {
  try {
    const documentsData = await searchStatisticsReviewsToExport(req);

    const styles = {
      headerTable: {
        fill: {
          fgColor: {
            rgb: "ffffff",
          },
        },
        border: {
          top: {style: "thin", color: "000000"},
          bottom: {style: "thin", color: "000000"},
          left: {style: "thin", color: "000000"},
          right: {style: "thin", color: "000000"},
        },
        font: {
          color: {
            rgb: "000000",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
    };

    const specification = {
      companyCode: {
        displayName: "Code",
        headerStyle: styles.headerTable,
        width: 120,
      },
      companyName: {
        displayName: "Company Name",
        headerStyle: styles.headerTable,
        width: 200,
      },
      fileReviewName: {
        displayName: "File Reviewer Name",
        headerStyle: styles.headerTable,
        width: 200,
      },
      qualityAssuranceName: {
        displayName: "Quality Assurance Name",
        headerStyle: styles.headerTable,
        width: 200,
      },
      complianceName: {
        displayName: "Compliance Name",
        headerStyle: styles.headerTable,
        width: 200,
      },
      status: {
        displayName: "Status",
        headerStyle: styles.headerTable,
        width: 180,
      },
      reviewDate: {
        displayName: "Review Date",
        headerStyle: styles.headerTable,
        width: 120,
      },
      validatedQaDate: {
        displayName: "Quality Assurance Date",
        headerStyle: styles.headerTable,
        width: 150,
      },
      validatedCoDate: {
        displayName: "Compliance Date",
        headerStyle: styles.headerTable,
        width: 120,
      },
      createdAt: {
        displayName: "Created at",
        headerStyle: styles.headerTable,
        width: 120,
      }
    };
    const report = excel.buildExport([
      {
        name: "FileReviews",
        specification: specification,
        data: documentsData,
      },
    ]);

    res.attachment("FileReviews.xlsx");
    return res.send(report);
  } catch (error) {
    console.log(error);
    next(error);
  }
};

async function searchStatisticsReviewsToExport() {
  try {
    let reviewsToExport = [];
    const reviews = await FileReviewModel.find({"status.code": {"$ne": 'NOT STARTED'}},
      {_id: 1, status: 1, createdAt: 1, companyName: 1, companyCode: 1, fileReview: 1, qualityAssurance: 1, compliance: 1})

    console.log(reviews.length);

    if(reviews && reviews.length > 0){
      reviewsToExport = reviews.map((r) => {

        let frDate = '';
        let qaDate = '';
        let coDate = '';

        if ( r.fileReview && r.fileReview.validatedDate ){
          frDate = moment(r.fileReview.validatedDate).format("YYYY-MM-DD");
        }
        else if(r.status.code === 'REVIEWED' && !r.fileReview.validatedDate){
          frDate = moment(r.status.statusDate).format("YYYY-MM-DD");
        }

        if ( r.qualityAssurance && r.qualityAssurance.validatedDate ){
          qaDate = moment(r.qualityAssurance.validatedDate).format("YYYY-MM-DD");
        }
        else if(r.status.code === 'VALIDATED QA' && !r.qualityAssurance.validatedDate){
          qaDate = moment(r.status.statusDate).format("YYYY-MM-DD");
        }

        if ( r.compliance && r.compliance.validatedDate){
          coDate = moment(r.compliance.validatedDate).format("YYYY-MM-DD");
        }
        else if(r.status.code === 'VALIDATED CO' && !r.compliance.validatedDate){
          coDate = moment(r.status.statusDate).format("YYYY-MM-DD");
        }

        return {
          companyCode: r.companyCode,
          companyName: r.companyName,
          fileReviewName: r.fileReview ? r.fileReview.name : '',
          qualityAssuranceName: r.qualityAssurance ? r.qualityAssurance.name : '',
          complianceName: r.compliance ? r.compliance.name : '',
          status: r.status.code,
          reviewDate: frDate,
          validatedQaDate: qaDate,
          validatedCoDate: coDate,
          createdAt: moment(r.createdAt).format("YYYY-MM-DD")

        }
      })
    }
    return reviewsToExport;
  } catch (error) {
    console.log(error);
    return [];
  }
}

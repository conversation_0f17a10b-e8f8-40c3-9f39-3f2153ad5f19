<div id="trustForm">

    <!-- TRUST DETAILS -->
    <div id="trustDetails">
        <div class="row mt-3">
            <div class="col-2">
                <label for="trust-details-organization-name">Trust Name*</label>
            </div>
            <div class="col-4">
                <input type="text" class="form-control"  id="trust-details-organization-name"  name="details[organizationName]"
                       value="{{relation.details.organizationName}}"/>
            </div>
            <div class="col-2">
                <label for="trust-details-incorporation-number">Trustee Name</label>
            </div>
            <div class="col-4">
                <input id="trust-details-incorporation-number" class="form-control" type="text" name="details[incorporationNumber]"
                       value="{{relation.details.incorporationNumber}}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="trust-details-tax-residence">Tax Residence</label>
            </div>
            <div class="col-4">
<!--                <input id="trust-details-tax-residence" class="form-control" type="text" name="details[taxResidence]"-->
<!--                       value="{{relation.details.taxResidence}}"/>-->
                {{>file-reviewer/shared/select-country selectId="details[taxResidence]"
                        value=relation.details.taxResidence}}
            </div>
            <div class="col-2">
                <label for="trust-details-registration-number"
                >Business Registration Number (if applicable)</label
                >
            </div>
            <div class="col-4">
                <input class="form-control" type="text"  id="trust-details-registration-number" name="details[businessNumber]"
                       value="{{relation.details.businessNumber}}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="trust-details-incorporation-date">Date of Trust Establishment</label>
            </div>
            <div class="col-4">
                <input class="form-control" type="date" id="trust-details-incorporation-date" name="details[incorporationDate]"
                       value="{{#formatDate relation.details.incorporationDate "YYYY-MM-DD"}} {{/formatDate }}"/>
            </div>
            <div class="col-2">
                <label for="details[incorporationCountry]">Country of Incorporation</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[incorporationCountry]"
                        value=relation.details.incorporationCountry}}
            </div>
        </div>

        <!-- DETAILS TABLE -->
        {{>file-reviewer/shared/relation-file-table tableId="detailsTable"  name="details" group="trust"
                files=(ternary newRelation relation.trustFiles.details relation.details.files)}}

        <!-- DETAILS PARTNER TABLE -->
        {{>file-reviewer/shared/certificate-partner-table group="trust"
                partnerFiles=(ternary newRelation relation.trustFiles.detailsPartner relation.detailsPartner.files)
                relationId=relation._id}}

        <div class="row pt-3">
            <div class="col-12 d-flex justify-content-end">
                <div class="custom-control custom-checkbox">
                    <input
                            type="checkbox"
                            class="custom-control-input completeCheck"
                            name="details[correct]"
                            id="trust-details-correct"
                        {{#if relationInformation.details.complete}} checked {{/if}}
                    />
                    <label class="custom-control-label" for="trust-details-correct"
                    >Complete Information</label
                    >
                </div>
            </div>
        </div>
    </div>
    <hr class="mt-2"/>

    <!-- PRINCIPAL ADDRESS DETAILS -->
    <div id="principalAddressDetails">
        <h4>Principal Address</h4>
        {{>file-reviewer/review-relations/sections/address-details-form group="trust"
                principalAddress=relation.principalAddress formType="principalAddress"
                relationInformation=relationInformation.principalAddress}}
    </div>
    <hr class="mt-2" />

    <!-- MAILING ADDRESS DETAILS -->
    <div id="mailingAddressDetails">
        <h4>Mailing Address</h4>
        {{>file-reviewer/review-relations/sections/address-details-form group="trust"
                principalAddress=relation.mailingAddress formType="mailingAddress"
                relationInformation=relationInformation.mailingAddress}}
    </div>
    <hr class="mt-2" />

    <!-- MUTUAL FUND DETAILS -->
    <div class="mutualFundDetails">
        {{>file-reviewer/review-relations/sections/mutual-fund-details-form group="trust"
                mutualFund=(ternary newRelation relation.trustFiles.mutualFund relation.mutualFundDetails) relationId=relation._id}}
    </div>
    <hr class="mt-2" />

    <!--WORLD CHECK DETAILS -->
    <div  id="worldCheckSection" >
        {{>file-reviewer/review-relations/sections/world-check-details-form
                worldCheck=(ternary newRelation relation.trustFiles.worldCheck relation.worldCheck)
                relationInformation=relationInformation.worldCheck }}
    </div>

</div>

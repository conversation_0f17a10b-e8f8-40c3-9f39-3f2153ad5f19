<div
        id="upload-temp-modal"
        class="modal fade"
        tabindex="-1"
        role="dialog"
        aria-labelledby="uploadModal"
        style="display: none;"
        aria-hidden="true"
>
    <div class="modal-dialog modal-lg contour container-fluid">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="upload-modal-temp-title">
                    Upload file: <span id="upload-modal-temp-label" class="font-weight-bold"></span>
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <p>
                                Maximum of <span id="maxUploadTemp"></span> File(s), PDF only. File must not be
                                password protected.
                            </p>
                            <div id="uploadModalTempForm" class="dropzone">
                                <div class="fallback">
                                    <input name="fileUploaded" type="file" multiple/>
                                </div>
                                <div class="dz-message needsclick">
                                    <i class="h1 text-muted dripicons-cloud-upload"></i>
                                    <h3>Drop files here or click to upload.</h3>
                                    <span class="text-muted">Files will be automatically uploaded</span>
                                </div>
                            </div>
                            <div id="uploadedTempFiles" class="mt-2 text-center text-muted"></div>
                        </div>
                        <!-- end card-body-->
                        <div class="modal-footer justify-content-end pb-0">
                            <button type="button" class="btn solid royal-blue" data-dismiss="modal">
                                <i class="mdi mdi-send mr-1"></i>Close
                            </button>
                        </div>
                    </div>
                    <!-- end card-->
                </div>
                <!-- end col -->
            </div>
            <!-- end row -->
        </div>
    </div>
</div>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/uploadedfiles.precompiled.js"></script>

<script type="text/javascript">
    Dropzone.autoDiscover = false;

    let rowindex;
    let button = '';
    let fileType = '';
    let fileId = '';
    let row;
    let fileGroup = '';

    let id = '';
    let reviewId = '';
    let relationId = '';
    $(function () {
        let field = '';
        let name = '';
        let tempDropzone = new Dropzone('#uploadModalTempForm', {
            url: '/',
            acceptedFiles: 'application/pdf',
            autoProcessQueue: true,
            parallelUploads: 3,
            maxFiles: 3,
            maxFilesize: 5,
            paramName: function () {
                return 'fileUploaded';
            },
            uploadMultiple: true,
            init: function () {
                this.on('processing', function () {
                    this.options.url = '/file-reviewer/company-positions/upload-document';
                });
                this.on('success', function () {
                    refreshUploadedFiles(relationId, fileGroup, id, fileType);
                    let $presentField = $('#' + fileGroup + '-files-' + row +'-present');
                    if ($presentField){
                        $presentField.prop('checked', true);
                    }
                    let $fileUpload = $('#btn-'+fileGroup+ "-" + row );
                    $fileUpload.text('Modify');
                    $fileUpload.css({
                        'background-color': '#0AC292',
                        'border-color': '#0AC292',
                    });
                });
                this.on('sending', function (file, xhr, formData) {
                    if (!formData.has('fileName')) {
                        formData.append('fileName', field);
                    }
                    if (!formData.has('reviewId')) {
                        formData.append('reviewId', reviewId);
                    }
                    if (!formData.has('fileId')) {
                        formData.append('fileId', id);
                    }
                    if (!formData.has('fileGroup')){
                        formData.append('fileGroup', fileGroup);
                    }
                    if (!formData.has('row')){
                        formData.append('row', row);
                    }
                });

                this.on('errormultiple', function (files, response) {
                });

                this.on('maxfilesexceeded', function (file) {
                });

                this.on('resetFiles', function () {
                    if (this.files.length !== 0) {
                        for (i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                    }
                    $('#maxUploadTemp').text(this.options.maxFiles);
                });
            },
        });

        $('#upload-temp-modal').on('show.bs.modal', function (event) {
            button = $(event.relatedTarget); // Button that triggered the modal
            name = button.data('field'); //name of the file
            field = name.replace(/[\s\’\'\/\(\)]/g, ''); //formatted name with no special chars
            id = button.data('id'); // _id
            reviewId = button.data('review-id'); // _id
            relationId = button.data('relation-id');
            row = button.data('row'); // row index
            fileGroup = button.data('file-group');
            fileType =  button.data('file-type');
            $('#upload-modal-temp-label').text(name);
            $.get('/file-reviewer/company-file-review/uploaded-files',
                {reviewId: relationId, fileGroup: fileGroup, fileId: id, getTempFiles: true, type: fileType,  row: row },
                function (data) {
                    let template = Handlebars.templates.uploadedfiles;
                    let d = {
                        id: relationId,
                        files: data.files ? data.files : [],
                        group: fileGroup,
                        rowId: id,
                    };
                    let html = template(d);

                    $('#uploadedTempFiles').html(html);
                }
            );
            var modal = $(this);
            const objDZ = Dropzone.forElement('#uploadModalTempForm');
            objDZ.emit('resetFiles');
        });

        $('#upload-temp-modal').on('hide.bs.modal', function (event) {
            var objDZ = Dropzone.forElement('#uploadModalTempForm');
            objDZ.removeAllFiles(true);
            $('#uploadedTempFiles').html('');
        });
    });

    function deleteFile(id, fileGroup, rowId, fileId, blobName) {
        $.ajax({
            type: 'DELETE',
            url: '/file-reviewer/company-file-review/uploaded-files' ,
            data: {
                reviewId: relationId,
                group: fileGroup,
                row: row,
                rowId: rowId,
                fileId: fileId,
                blobName: blobName,
                type: fileType,
                deleteTempFiles: true
            },
            success: function (res) {
                if (res.result) {
                    refreshUploadedFiles(relationId, fileGroup, rowId, fileType);
                    const objDZ = Dropzone.forElement('#uploadModalTempForm');
                    const index = objDZ.files.findIndex((file) => file.blobName === blobName);
                    if (index > -1){
                        objDZ.files[index].pop();
                    }

                }
            },
            dataType: 'json',
        });
        return false;
    }

    function downloadFile(relationId,section,  rowId, fileId) {
        let url = '';
        if (relationId){
            url = "/file-reviewer/reviews/" + reviewId + "/relations/" + relationId + "/download/"+ fileType  +"/" + rowId + "/" + fileId  ;
        }
        else{
            url = "/file-reviewer/reviews/" + reviewId + "/download/"+ fileType  +"/" + row + "/" + fileId  ;
        }

        window.open(url,"_blank");
        return false;
    }

    function refreshUploadedFiles(relationId, fileGroup, rowId, fileType) {
        $.get(
                '/file-reviewer/company-file-review/uploaded-files' ,
                {reviewId: relationId, fileGroup: fileGroup, getTempFiles: true, type: fileType,  fileId: rowId, row: row},
                function (data) {
                    let template = Handlebars.templates.uploadedfiles;
                    let d = {
                        id: relationId,
                        files: data.files ? data.files : [],
                        group: fileGroup,
                        rowId: rowId,
                    };
                    let html = template(d);
                    $('#uploadedTempFiles').html(html);

                    if (data.length === 0) {
                        let $presentField = $('#' + fileGroup + '-files-' + row +'-present');
                        if ($presentField){
                            $presentField.prop('checked', false);
                        }
                        let $fileUpload = $('#btn-'+fileGroup+ "-" + row );
                        $fileUpload.text('Upload');
                        $fileUpload.css({
                            'background-color': '#0081b4',
                            'border-color': '#0081b4',
                        });
                    }
                }
        );
    }
</script>

const { getBooleanFormat } = require('../../utils/schemaUtils');

const substanceModuleLabelsSchema = {
  active: {
    type: Boolean,
    label: 'Enable Substance Module',
    get: getBooleanFormat
  },
  hasITADate: {
    type: Boolean,
    label: 'Substance Module, Has ITA Date?',
    get: getBooleanFormat
  },
  approvedITAStartDate: {
    type: Date,
    label: 'Substance Module, ITA Start Date'
  },
  approvedITAEndDate: {
    type: Date,
    label: 'Substance Module,ITA End Date'
  },
  applicationITADate: {
    type: Date,
    label: 'Substance Module, Application ITA Date'
  },
  approval: {
    approved: {
      type: Boolean,
      label: 'Substance Module, Is Approve?',
      get: getBooleanFormat
    },
    date: {
      type: Date,
      label: 'Substance Module, Approved Date'
    },
    by: {
      type: String,
      label: 'Substance Module, Approved By'
    }
  },
}

const accountingRecordsModuleLabelsSchema = {
  active: {
    type: Boolean,
    label: 'Enable Accounting Records Module',
    get: getBooleanFormat
  },
  selfServiceCompleteAnnualReturnAmount: {
    type: Number,
    label: 'Accounting Records Module, Self Service Complete Option Amount'
  },
  selfServicePrepareAnnualReturnAmount: {
    type: Number,
    label: 'Accounting Records Module, Self Service Prepare Option Amount'
  },
  tridentServiceCompleteAnnualReturnAmount: {
    type: Number,
    label: 'Accounting Records Module, Trident Service Prescribed Option Amount'
  },
  tridentServiceDropAccountingRecordsAmount: {
    type: Number,
    label: 'Accounting Records Module, Trident Service Drop Option Amount'
  },
  firstFinancialPeriodStart: {
    type: Date,
    label: 'Accounting Records Module, First Financial Period Start'
  },
  firstFinancialPeriodEnd: {
    type: Date,
    label: 'Accounting Records Module, First Financial Period End'
  },
}

const dirboModuleLabelsSchema = {
  active: {
    type: Boolean,
    label: 'Enable DIR/BO Module',
    get: getBooleanFormat
  },
}


const CompanyMapperSchema = {
  name: {
    type: String,
    label: 'Entity Name'
  },
  address: {
    type: String,
    label: 'Address'
  },
  code: {
    type: String,
    label: 'Entity Code'
  },
  incorporationcode: {
    type: String,
    label: 'Incorporation Code'
  },
  incorporationdate: {
    type: Date,
    label: 'Incorporation Date'
  },
  masterclientcode: {
    type: String,
    label: 'Master Client Code'
  },
  riskgroup: {
    type: String,
    label: 'Risk Group'
  },
  filereviews: {
    type: [],
    label: "FileReview ID's",
    isArray: true,
    childType: String
  },
  company_type: {
    type: String,
    label: 'Company Type'
  },
  referral_office: {
    type: String,
    label: 'Referral Office'
  },
  amount: {
    type: Number,
    label: "Amount",
  },
  isDeleted: {
    type: Boolean,
    label: 'Is Deleted?',
    get: getBooleanFormat
  },
  deletedAt: {
    type: Date,
    label: 'Deleted At'
  },

  hasITADate: {
    type: Boolean,
    label: 'Has ITA Date?',
    get: getBooleanFormat
  },
  approvedITAStartDate: {
    type: Date,
    label: 'ITA Start Date'
  },
  approvedITAEndDate: {
    type: Date,
    label: 'ITA End Date'
  },
  applicationITADate: {
    type: Date,
    label: 'Application ITA Date'
  },
  paymentYears: {
    type: [],
    label: "Payment Years",
    isArray: true,
    childType: String
  },
  createdAt: {
    type: Date,
    label: 'Created At'
  },
  createdBy: {
    type: String,
    label: 'Created By'
  },
  updatedAt: {
    type: Date,
    label: 'Updated At'
  },
  modifiedBy: {
    type: String,
    label: 'Modified By'
  },
  vpMasterFileCode: {
    type: String,
    label: 'VP Master File Code'
  },
  entityStatus: {
    type: String,
    label: 'Entity Status'
  },
  entitySubStatus: {
    type: String,
    label: 'Entity Sub-Status'
  },
  entityStatusLabel: {
    type: String,
    label: 'Entity Status Label'
  },
  productionOffice: {
    type: String,
    label: 'Production Office'
  },
  mccCpMasterFileCode: {
    type: String,
    label: 'MCC Copy Master File Code'
  }, 
  substanceModule: substanceModuleLabelsSchema,
  accountingRecordsModule: accountingRecordsModuleLabelsSchema,
  dirboModule: dirboModuleLabelsSchema
};


module.exports = {
  CompanyMapperSchema,
};
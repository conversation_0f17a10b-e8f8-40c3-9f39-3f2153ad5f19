<main class="">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h1>{{title}}</h1>
                        <form method='GET' id="rfiForm">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="filter_company">Company</label>
                                            <input class='form-control' type='text' name='filter_company' id='filter_company'
                                                   value="{{filters.filter_company}}" />
                                        </div>
                                        <div class="col-md-6">
                                            <label for="filter_masterclient">Masterclient</label>
                                            <input class='form-control' type='text' name='filter_masterclient'
                                                   id='filter_masterclient' value="{{filters.filter_masterclient}}" />
                                        </div>
                                    </div>
                                    <div class="row my-1">
                                        <div class="col-md-6">
                                            <label for="filter_submitted_range_start">Submitted after:</label>
                                            <input class='form-control' type='date' name='filter_submitted_range_start'
                                                   id='filter_submitted_range_start' value="{{filters.filter_submitted_range_start}}" />
                                        </div>
                                        <div class="col-md-6">
                                            <label for="filter_submitted_range_end">Submitted before:</label>
                                            <input class='form-control' type='date' name='filter_submitted_range_end'
                                                   id='filter_submitted_range_end' value="{{filters.filter_submitted_range_end}}" />
                                        </div>
                                    </div>
                                    <div class="row my-1">
                                        <div class="col-md-6">
                                            <label for="filter_incorporated_range_start">Company incorporated after:</label>
                                            <input class='form-control' type='date' name='filter_incorporated_range_start'
                                                   id='filter_incorporated_range_start' value="{{filters.filter_incorporated_range_start}}" />
                                        </div>
                                        <div class="col-md-6">
                                            <label for="filter_incorporated_range_end">Company incorporated before:</label>
                                            <input class='form-control' type='date' name='filter_incorporated_range_end'
                                                   id='filter_incorporated_range_end' value="{{filters.filter_incorporated_range_end}}" />
                                        </div>
                                    </div>
                                    <div class="row my-1">
                                        <div class="col-md-6">
                                            <label for="filter_financial_period_range_start">Financial period end after:</label>
                                            <input class='form-control' type='date' name='filter_financial_period_range_start'
                                                   id='filter_financial_period_range_start' value="{{filters.filter_financial_period_range_start}}" />
                                        </div>
                                        <div class="col-md-6">
                                            <label for="filter_financial_period_range_end">Financial period end before:</label>
                                            <input class='form-control' type='date' name='filter_financial_period_range_end'
                                                   id='filter_financial_period_range_end' value="{{filters.filter_financial_period_range_end}}" />
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-2">
                                    <label for="filter_referral">Referral Office</label>
                                    <input class='form-control' type='text' name='filter_referral' id='filter_referral'
                                           value="{{filters.filter_referral}}" />

                                        <label class="my-1" for="filter_referral ">Relevant Activities</label>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="relevant_activities" id="none" value="none" {{#if
                                                    filters.relevantActivities.none }} checked {{/if}} />
                                            <label class="custom-control-label" for="none">None</label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="relevant_activities" id="banking_business"
                                                   value="banking_business" {{#if
                                                    filters.relevantActivities.banking_business }} checked {{/if}} />
                                            <label class="custom-control-label" for="banking_business">Banking
                                                Business</label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="relevant_activities" id="insurance_business"
                                                   value="insurance_business" {{#if
                                                    filters.relevantActivities.insurance_business }}checked {{/if}} />
                                            <label class="custom-control-label" for="insurance_business">Insurance
                                                Business</label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="relevant_activities" id="fund_management_business"
                                                   value="fund_management_business" {{#if
                                                    filters.relevantActivities.fund_management_business }}checked
                                            {{/if}} />
                                            <label class="custom-control-label" for="fund_management_business">Fund
                                                Management Business</label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="relevant_activities" id="finance_leasing_business"
                                                   value="finance_leasing_business" {{#if
                                                    filters.relevantActivities.finance_leasing_business }}checked
                                            {{/if}} />
                                            <label class="custom-control-label"
                                                   for="finance_leasing_business">Finance/Leasing Business</label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="relevant_activities" id="headquarters_business"
                                                   value="headquarters_business" {{#if
                                                    filters.relevantActivities.headquarters_business }}checked
                                            {{/if}} />
                                            <label class="custom-control-label"
                                                   for="headquarters_business">Headquarters
                                                Business</label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="relevant_activities" id="shipping_business"
                                                   value="shipping_business" {{#if
                                                    filters.relevantActivities.shipping_business }}checked {{/if}} />
                                            <label class="custom-control-label" for="shipping_business">Shipping
                                                Business</label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="relevant_activities" id="holding_business"
                                                   value="holding_business" {{#if
                                                    filters.relevantActivities.holding_business }}checked {{/if}} />
                                            <label class="custom-control-label" for="holding_business">Holding
                                                Business</label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="relevant_activities" id="intellectual_property_business"
                                                   value="intellectual_property_business" {{#if
                                                    filters.relevantActivities.intellectual_property_business }}checked
                                            {{/if}} />
                                            <label class="custom-control-label"
                                                   for="intellectual_property_business">Intellectual Property
                                                Business</label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="relevant_activities" id="service_centre_business"
                                                   value="service_centre_business" {{#if
                                                    filters.relevantActivities.service_centre_business }}checked
                                            {{/if}} />
                                            <label class="custom-control-label"
                                                   for="service_centre_business">Service Centre
                                                Business</label>
                                        </div>
                                </div>
                                <div class="col-md-2">
                                        <label for="filter_other">Other</label>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input"
                                                   name="resident_outside_bvi" id="resident_outside_bvi" value="Yes"
                                                {{#if filters.resident_outside_bvi }} checked {{/if}} />
                                            <label class="custom-control-label" for="resident_outside_bvi">Tax
                                                residency claim outside BVI</label>
                                        </div>
                                        <br>

                                        <label>RFI</label>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" name="rfi_filter[]" id="show_completed" value="show_completed"
                                                {{#ifContains 'show_completed' filters.rfi_filter }}checked {{/ifContains}} />
                                            <label class="custom-control-label" for="show_completed">Show completed
                                            </label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" name="rfi_filter[]" id="show_not_completed" value="show_not_completed"
                                                {{#ifContains 'show_not_completed' filters.rfi_filter }}checked {{/ifContains}} />
                                            <label class="custom-control-label" for="show_not_completed">Show not completed
                                            </label>
                                        </div>

                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" name="rfi_filter[]" id="show_not_rfi_started" value="show_not_rfi_started"
                                                {{#ifContains 'show_past_deadline' filters.rfi_filter }}
                                                   disabled
                                                {{else}}
                                                    {{#ifContains 'show_not_rfi_started' filters.rfi_filter }}checked {{/ifContains}}
                                                {{/ifContains}}
                                                 />
                                            <label class="custom-control-label" for="show_not_rfi_started">Show not RFI started
                                            </label>
                                        </div>

                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" name="rfi_filter[]" id="show_past_deadline" value="show_past_deadline"
                                                {{#ifContains 'show_past_deadline' filters.rfi_filter }}checked {{/ifContains}} />
                                            <label class="custom-control-label" for="show_past_deadline">Show past deadline
                                            </label>
                                        </div>
                                </div>

                                <div class="col-md-2" style="padding-top:30px">
                                    <button type="submit" form="rfiForm"  class='btn btn-primary waves-effect ' >Search </button>
                                    <button  type="button" form="rfiForm" id="clearFormBtn" class='btn btn-secondary waves-effect ' >Reset filters </button>
                                </div>

                            </div>




                        </form>
                        <br /><br />
                        <table id="scroll-horizontal-datatable" class="table w-100 nowrap">

                            <thead>
                            <tr>
                                <th>Entity Name</th>
                                <th>Entity Number</th>
                                <th>Master Client Code</th>
                                <th>Financial Period End Date</th>
                                <th>Status</th>
                                <th>Export Date</th>
                                <th>RFI</th>
                                <th>RFI deadline</th>
                                <th>RFI Completed</th>
                                <th>Last Reminder</th>
                                <th>Request Information</th>
                                <th>View Information</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{#each result}}
                                <tr>
                                    <td>{{entity_name}}</td>
                                    <td>{{code}}</td>
                                    <td>{{masterclientcode}}</td>
                                    <td data-sort="{{formatDate financial_period_ends 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate financial_period_ends ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td>{{status}}</td>
                                    <td data-sort="{{formatDate exported_at 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate exported_at ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td data-sort="{{formatDate rfi_at 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate rfi_at ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td {{#if rfi_deadline_before_today}} class="text-danger" {{/if}}
                                        data-sort="{{#unless rfi_completed_at}} {{formatDate rfi_deadline_at 'YYYY-MM-DD HH:mm'}}  {{/unless}}"
                                        >
                                        {{#unless rfi_completed_at}}
                                            {{formatDate rfi_deadline_at ../STANDARD_DATE_FORMAT}}
                                        {{/unless}}
                                    </td>
                                    <td data-sort="{{formatDate rfi_completed_at 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate rfi_completed_at ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td  data-sort="{{formatDate last_reminder.reminder_date 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate last_reminder.reminder_date ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td>
                                        {{#ifEquals status 'PAID'}}
                                                <button class="btn btn-primary waves-effect waves-light border-white"
                                                        data-toggle="modal"
                                                        data-target="#rfiModal"
                                                        data-entry-id="{{_id}}">
                                                    Request Information
                                                </button>
                                            {{/ifEquals}}

                                            {{#ifEquals status 'INFORMATION REQUEST'}}
                                                <input type="button" class="btn btn-danger  w-100  waves-effect waves-light border-white"
                                                       onclick="cancelRequestInformation('{{_id}}')" value="Cancel RFI">
                                            {{/ifEquals}}
                                    </td>
                                    <td>

                                        {{#if show_info_details}}
                                            <button class="btn btn-primary waves-effect waves-light border-white"
                                                    data-toggle="modal"
                                                    data-target="#showInformationModal"
                                                    data-entry-id="{{_id}}"

                                            > Show Info
                                            </button>
                                        {{/if}}
                                    </td>
                                </tr>
                            {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <div class="row">
                        <div class="col-12 text-sm-center form-inline">
                            <div class="ml-3 mr-2  mb-3">
                                <a href='/substance/'
                                   class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{>substance/show-information-modal}}
    {{>substance/request-information-modal}}
</main>
<script type="text/javascript">

    $(document).ready(function () {

        let showLimitAlert = "{{showLimitAlert}}" === 'true';
        if (showLimitAlert) {
            toastr["warning"]('Maximum number of records reached. Please refine your search to reduce the size of query.', 'Limit reached!', {
                "timeOut": 100000,
            });
        }

        $("#scroll-horizontal-datatable").DataTable({
            scrollX: !0,
            select: true,
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>" }
            },
            drawCallback: function () { $(".dataTables_paginate > .pagination").addClass("pagination-rounded") }
        });
    });

    $("#clearFormBtn").on('click', () =>{
        $("#rfiForm")[0].reset();
        $("#rfiForm input").val('');
        $('#rfiForm input[type=checkbox]').prop('checked',false);
    })

    $("#show_past_deadline").on('change', ()=>{
        const val = $("#show_past_deadline").is(':checked');
        $("#show_not_rfi_started").prop("disabled", val);
        if (val){
            $("#show_not_rfi_started").prop("checked", false).trigger('change');
        }
    });

    function cancelRequestInformation(id) {
        Swal.fire({
            title: 'Cancel Request',
            text: "This will cancel the current request information, are you sure?",
            input: 'textarea',
            inputLabel: 'Message',
            inputPlaceholder: 'Write the reason to cancel...',
            inputAttributes: {
                'aria-label': 'Write the reason to cancel...',
                'maxlength': 2000
            },
            backdrop: true,
            showCancelButton: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#6658DD",
            confirmButtonText: 'Yes',
            reverseButtons: true,
            showLoaderOnConfirm: true,
            inputValidator: (reason) => {
                if(!reason || reason === ''){
                    return 'Please provide the reason for canceling the current information request'
                }
            },
            preConfirm(reason) {
                return fetch('/substance/' +id+ '/cancel-request-information', {
                    method: 'PUT',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({reason: reason})
                }).then(response => {
                    try {
                        return response.json()
                    } catch (e) {
                        throw new Error(response.statusText)
                    }
                }).catch(error => {
                    console.log(error);
                    return {status: 500, error: error}

                });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.value) {
                swal.showLoading();
                if (result.value.status === 200) {
                    Swal.fire('Success', result.value.message, 'success').then(() => {
                        location.reload();
                    });
                } else if (result.value.status === 400) {
                    Swal.fire('Error', result.value.error, 'error');
                } else {
                    Swal.fire('Error', 'There was an error cancelling the current request information', 'error');
                }
            }
        });
    }

</script>

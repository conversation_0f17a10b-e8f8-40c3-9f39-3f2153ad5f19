{{!-- EDIT PERCENTAGE MODAL --}}
<form id="modalPercentageForm">
<div class="modal fade" id="editPercentageModal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="percentageTitle" style="text-transform: capitalize;"></h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        </div>
            <div class="modal-body p-3 text-justify">
            <label for="modalInputPercentage">Percentage</label>
            <div class="col-8 input-group mb-3">
            <input 
            name="modalInputPercentage"
            id="modalInputPercentage" 
            type="number" 
            min="0"
            max="100" 
            class="form-control" />
            <div class="input-group-append">
            <span class="input-group-text">%</span>
            </div>
            </div>
      </div>
      <div class="modal-footer justify-content-between">
        <button type="button" class="btn btn-danger" data-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn solid royal-blue" id="editPercentageButton">Edit</button>
      </div>
    </div>
  </div>
</div>
</form>
<script type="text/javascript">
    $editButton = $('#editPercentageButton');
    let companyPercentageId;
    let percentage;
    let relationFileId;
    let fullName;
    let percentageValue;
    let typeRelation;
    let typeGroup;
    const myForm = $('#modalPercentageForm')[0];
    $('#editPercentageModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        companyPercentageId = button.data('review-id');
        relationFileId = button.data('relation-id');
        typeRelation = button.data('type');
        fullName = button.data('name');
        typeGroup = button.data('group');
        percentageValue = button.data('percentage');
        $('#percentageTitle').text(fullName + '- Edit percentage');
        $('#modalInputPercentage').val(percentageValue);
    });
    $editButton.click(function () {
        percentage = $('#modalInputPercentage').val();
        if (!myForm.checkValidity()) {
        if (myForm.reportValidity) {
          myForm.reportValidity();
          return;
        }
        }
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/open-file-review/'+ companyPercentageId + '/edit-percentage',
            data: { companyId: companyPercentageId, relationId: relationFileId,
            type: typeRelation, group: typeGroup, percentage: percentage },
            success: () => {
                Swal.fire('Success', 'The percentage has been edited successfully', 'success').then(() => {
                    location.href = '/file-reviewer/open-file-review/'+ companyPercentageId + '/beneficial-owners';
            });
            },
            error: (err) => {
                Swal.fire('Error', 'There was an error editing the percentage', 'error').then(() => {
                    location.href = '/file-reviewer/open-file-review/'+ companyPercentageId + '/beneficial-owners';
            });
            },
        });
    });
</script>
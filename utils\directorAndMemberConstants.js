exports.TYPE_OF_DIRECTOR_DIR = "Director"
exports.TYPE_OF_DIRECTOR_BO = "Owner/Controller"
exports.TYPE_OF_MEMBER = "Member"

exports.DIRECTOR_REQUIRED_FIELDS = {
    "individual": [
        {
            field: "DirOfficerType",
            name: "Director Type"
        },
        {
            field: "DirName",
            name: "Name"
        },
        {
            field: "DirFromDate",
            name: "Appointment Date"
        },
        {
            field: "ServiceAddress",
            name: "Service Address"
        },
        {
            field: "ResidentialOrRegisteredAddress",
            name: "Residential Address"
        },
        {
            field: "DateOfBirthOrIncorp",
            name: "Date of Birth"
        },
        {
            field: "PlaceOfBirthOrIncorp",
            name: "Place of Birth"
        },
        {
            field: "Nationality",
            name: "Nationality"
        }
    ],
    "corporate": [
        {
            field: "DirOfficerType",
            name: "Director Type"
        },
        {
            field: "DirName",
            name: "Name"
        },
        {
            field: "MFIncropNr",
            name: "Corporate Number"
        },
        {
            field: "DirFromDate",
            name: "Appointment Date"
        },
        {
            field: "MFROAddress",
            name: "Address"
        },
        {
            field: "MFIncorpDate",
            name: "Incorporation Date"
        },
        {
            field: "MFIncorpCountry",
            name: "Incorporation Place"
        }
    ],
}

// TODO: make sure the fields match the fields in the mem_Shareholders table #17252
// Member required fields based on member type
exports.MEMBER_REQUIRED_FIELDS = {
    // Individual Member
    "Individual": [
        {
            field: "MFDateOfBirth",
            name: "Date of Birth"
        },
        {
            field: "MFBirthCountry",
            name: "Place of Birth"
        },
        {
            field: "MFNationality",
            name: "Nationality"
        },
        {
            field: "MFRAAddress",
            name: "Address"
        }
    ],
    // Corporate Member
    "Corporate": [
        {
            field: "MFIncropNr",
            name: "Corporate Number"
        },
        {
            field: "MFIncorpCountry",
            name: "Incorporation Place"
        },
        {
            field: "MFIncorpDate",
            name: "Incorporation Date"
        },
        {
            field: "MFROAddress",
            name: "Address"
        }
    ],
    // Mutual Fund
    "Mutual Fund": [
        {
            field: "BusRegNr",
            name: "Licence Number"
        },
        {
            field: "BusRegType",
            name: "Licence Type"
        },
        {
            field: "BusRegStartDate",
            name: "Date Entered"
        }
    ],
    // Stock entity
    "Stock": [
        {
            field: "STXName",
            name: "Stock Exchange Name"
        },
        {
            field: "STXTicker",
            name: "Ticker Symbol"
        },
        {
            field: "STXJurisdiction",
            name: "Jurisdiction of Stock Exchange"
        },
        {
            field: "STXRegulator",
            name: "Name of Stock Exchange Regulator"
        },
        {
            field: "STXListingDate",
            name: "Date of Listing"
        }
    ],
}

exports.DIRBO_STATUS = {
    INITIAL: "INITIAL",
    PENDING: "PENDING UPDATE REQUEST",
    RECEIVED: "VP DATA RECEIVED",
    CONFIRMED: "CONFIRMED",
    REFRESHED: "REFRESHED"
}

exports.DIRMEMBER_STATUS = {
    INITIAL: "INITIAL",
    PENDING: "PENDING UPDATE REQUEST",
    RECEIVED: "VP DATA RECEIVED",
    CONFIRMED: "CONFIRMED",
    REFRESHED: "REFRESHED"
}
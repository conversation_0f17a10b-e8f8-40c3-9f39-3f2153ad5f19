<div class="modal hide fade" tabindex="-1" role="dialog" id="financial-period-modal" style="display: none;"
  aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="mediumModal">Change Financial Period</h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
      </div>
      <div class="modal-body">
        <div class="row" id="newPeriodDatesRow">
          <div class="col-6">
            <label for="financialStartDateControl">Start Date:</label>
            <input type="text" class="form-control datepicker mr-2" placeholder="MM/DD/YYYY" name="financialStartDate"
              id="financialStartDateControl">
          </div>
          <div class="col-6">
            <label for="financialEndDateControl">End Date:</label>
            <input type="text" class="form-control datepicker" placeholder="MM/DD/YYYY" name="financialEndDate"
              id="financialEndDateControl">
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <input type="button" class="btn btn-secondary waves-effect waves-light" data-dismiss="modal" value="Cancel">

        <input type="button" class="btn btn-primary waves-effect waves-light ml-2" id="confirmFinancialPeriodButton"
          onclick="saveFinancialPeriod()" value="Confirm">
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>
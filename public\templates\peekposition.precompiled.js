(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['peekposition'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":4,"column":4},"end":{"line":8,"column":11}}})) != null ? stack1 : "");
},"2":function(container,depth0,helpers,partials,data) {
    return "        <span class=\"badge badge-success\">Complete</span>\r\n";
},"4":function(container,depth0,helpers,partials,data) {
    return "        <span class=\"badge badge-warning text-dark\">Incomplete</span>\r\n";
},"6":function(container,depth0,helpers,partials,data) {
    return " ";
},"8":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"identification") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":39,"column":4},"end":{"line":43,"column":11}}})) != null ? stack1 : "");
},"10":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":62,"column":4},"end":{"line":66,"column":11}}})) != null ? stack1 : "");
},"12":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"mailingAddress") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":91,"column":4},"end":{"line":95,"column":11}}})) != null ? stack1 : "");
},"14":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"taxResidence") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":120,"column":4},"end":{"line":124,"column":11}}})) != null ? stack1 : "");
},"16":function(container,depth0,helpers,partials,data) {
    return "Confirmed ";
},"18":function(container,depth0,helpers,partials,data) {
    return " Not Confirmed ";
},"20":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":139,"column":4},"end":{"line":143,"column":11}}})) != null ? stack1 : "");
},"22":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":174,"column":4},"end":{"line":178,"column":11}}})) != null ? stack1 : "");
},"24":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"worldCheck") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(25, data, 0),"inverse":container.program(27, data, 0),"data":data,"loc":{"start":{"line":205,"column":8},"end":{"line":209,"column":15}}})) != null ? stack1 : "");
},"25":function(container,depth0,helpers,partials,data) {
    return "            <span class=\"badge badge-success\">Complete</span>\r\n";
},"27":function(container,depth0,helpers,partials,data) {
    return "            <span class=\"badge badge-warning text-dark\">Incomplete</span>\r\n";
},"29":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            <tr>\r\n                <td style=\"text-transform: capitalize;\">"
    + alias4(((helper = (helper = lookupProperty(helpers,"external") || (depth0 != null ? lookupProperty(depth0,"external") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"external","hash":{},"data":data,"loc":{"start":{"line":229,"column":56},"end":{"line":229,"column":68}}}) : helper)))
    + "</td>\r\n                <td style=\"text-transform: capitalize;\">"
    + alias4(((helper = (helper = lookupProperty(helpers,"fileGroup") || (depth0 != null ? lookupProperty(depth0,"fileGroup") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileGroup","hash":{},"data":data,"loc":{"start":{"line":230,"column":56},"end":{"line":230,"column":69}}}) : helper)))
    + "</td>\r\n                <td class=\"text-center\" style=\"text-transform: capitalize;\">\r\n                    <div class=\"custom-control custom-checkbox\">\r\n                        <input type=\"checkbox\"\r\n                               disabled\r\n                               class=\"custom-control-input\"\r\n                               id=\"standardFilePresent-"
    + alias4(((helper = (helper = lookupProperty(helpers,"key") || (data && lookupProperty(data,"key"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"key","hash":{},"data":data,"loc":{"start":{"line":236,"column":55},"end":{"line":236,"column":65}}}) : helper)))
    + "\"\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"present") : depth0),{"name":"if","hash":{},"fn":container.program(30, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":237,"column":28},"end":{"line":239,"column":35}}})) != null ? stack1 : "")
    + "                        />\r\n                        <label\r\n                                class=\"custom-control-label\"\r\n                                for=\"standardFilePresent-"
    + alias4(((helper = (helper = lookupProperty(helpers,"key") || (data && lookupProperty(data,"key"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"key","hash":{},"data":data,"loc":{"start":{"line":243,"column":57},"end":{"line":243,"column":67}}}) : helper)))
    + "\"\r\n                        ></label>\r\n                    </div>\r\n                </td>\r\n                <td style=\"text-transform: capitalize;\">"
    + alias4(((helper = (helper = lookupProperty(helpers,"explanation") || (depth0 != null ? lookupProperty(depth0,"explanation") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"explanation","hash":{},"data":data,"loc":{"start":{"line":247,"column":56},"end":{"line":247,"column":73}}}) : helper)))
    + "</td>\r\n                <td class=\"text-center align-middle\">\r\n                    <button class=\"btn solid royal-blue download-button\"\r\n                            id=\"standardFileDownload-"
    + alias4(((helper = (helper = lookupProperty(helpers,"key") || (data && lookupProperty(data,"key"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"key","hash":{},"data":data,"loc":{"start":{"line":250,"column":53},"end":{"line":250,"column":63}}}) : helper)))
    + "\"\r\n                            type=\"button\"\r\n                            data-toggle=\"modal\"\r\n                            data-target=\"#downloadFileModal\"\r\n                            data-review-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"reviewId") : depths[1]), depth0))
    + "\"\r\n                            data-relation-id=\""
    + alias4(alias5(((stack1 = (depths[1] != null ? lookupProperty(depths[1],"position") : depths[1])) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\"\r\n                            data-file-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":256,"column":42},"end":{"line":256,"column":50}}}) : helper)))
    + "\"\r\n                            data-file-group=\""
    + alias4(alias5(((stack1 = (depths[1] != null ? lookupProperty(depths[1],"position") : depths[1])) != null ? lookupProperty(stack1,"type") : stack1), depth0))
    + "\"\r\n                        "
    + ((stack1 = lookupProperty(helpers,"unless").call(alias1,(depth0 != null ? lookupProperty(depth0,"present") : depth0),{"name":"unless","hash":{},"fn":container.program(32, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":258,"column":24},"end":{"line":258,"column":64}}})) != null ? stack1 : "")
    + "\r\n                    >Download\r\n                    </button>\r\n                </td>\r\n            </tr>\r\n";
},"30":function(container,depth0,helpers,partials,data) {
    return "                               checked\r\n";
},"32":function(container,depth0,helpers,partials,data) {
    return " disabled ";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3=container.lambda, alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<p>\r\n    DETAILS\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":3,"column":4},"end":{"line":9,"column":15}}})) != null ? stack1 : "")
    + "</p>\r\n<div class=\"row\">\r\n    <div class=\"col-2\">Full Name:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"fullName") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">First Name:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"firstName") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row pt-2\">\r\n    <div class=\"col-2\">Middle Name:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"middleName") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Last Name:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"lastName") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row pt-2\">\r\n    <div class=\"col-2\">Occupation:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"occupation") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Date of Birth:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"birthDate") : stack1),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(6, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":27,"column":40},"end":{"line":27,"column":112}}})) != null ? stack1 : "")
    + "</div>\r\n</div>\r\n<div class=\"row pt-2\">\r\n    <div class=\"col-2\">Nationality:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"nationality") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Country of Birth:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"countryBirth") : stack1), depth0))
    + "</div>\r\n</div>\r\n<hr class=\"mt-3\"/>\r\n<p>\r\n    IDENTIFICATION\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(8, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":38,"column":4},"end":{"line":44,"column":15}}})) != null ? stack1 : "")
    + "</p>\r\n<div class=\"row\">\r\n    <div class=\"col-2\">Type of Identification:</div>\r\n    <div class=\"col-4 font-weight-bold\" style=\"text-transform: capitalize;\">\r\n        "
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"identification") : stack1)) != null ? lookupProperty(stack1,"identificationType") : stack1), depth0))
    + "\r\n    </div>\r\n    <div class=\"col-2\">Country of Issue:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"identification") : stack1)) != null ? lookupProperty(stack1,"issueCountry") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row pt-2\">\r\n    <div class=\"col-2\">Expiry Date:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"identification") : stack1)) != null ? lookupProperty(stack1,"expiryDate") : stack1),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(6, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":56,"column":40},"end":{"line":56,"column":120}}})) != null ? stack1 : "")
    + "</div>\r\n</div>\r\n<hr class=\"mt-3\"/>\r\n<p>\r\n    PRINCIPAL ADDRESS\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(10, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":61,"column":4},"end":{"line":67,"column":15}}})) != null ? stack1 : "")
    + "</p>\r\n<div class=\"row\">\r\n    <div class=\"col-2\">Address - 1st Line:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"primaryAddress") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Address - 2nd Line:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"secondaryAddress") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row pt-2\">\r\n    <div class=\"col-2\">Country:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"country") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">State:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"state") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row pt-2\">\r\n    <div class=\"col-2\">City:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"city") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Postal Code:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"postalCode") : stack1), depth0))
    + "</div>\r\n</div>\r\n<hr class=\"mt-3\"/>\r\n<p>\r\n    MAILING ADDRESS\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(12, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":90,"column":4},"end":{"line":96,"column":15}}})) != null ? stack1 : "")
    + "</p>\r\n<div class=\"row\">\r\n    <div class=\"col-2\">Address - 1st Line:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"mailingAddress") : stack1)) != null ? lookupProperty(stack1,"primaryAddress") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Address - 2nd Line:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"mailingAddress") : stack1)) != null ? lookupProperty(stack1,"secondaryAddress") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row pt-2\">\r\n    <div class=\"col-2\">Country:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"mailingAddress") : stack1)) != null ? lookupProperty(stack1,"country") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">State:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"mailingAddress") : stack1)) != null ? lookupProperty(stack1,"state") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row pt-2\">\r\n    <div class=\"col-2\">City:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"mailingAddress") : stack1)) != null ? lookupProperty(stack1,"city") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Postal Code:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"mailingAddress") : stack1)) != null ? lookupProperty(stack1,"postalCode") : stack1), depth0))
    + "</div>\r\n</div>\r\n<hr class=\"mt-3\"/>\r\n<p>\r\n    TAX ADVICE\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(14, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":119,"column":4},"end":{"line":125,"column":15}}})) != null ? stack1 : "")
    + "</p>\r\n<div class=\"row\">\r\n    <div class=\"col-6\">Confirmation Regarding Legal / Tax Advice:</div>\r\n    <div class=\"col-6 font-weight-bold\">\r\n        "
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"taxResidence") : stack1)) != null ? lookupProperty(stack1,"confirmation") : stack1),{"name":"if","hash":{},"fn":container.program(16, data, 0, blockParams, depths),"inverse":container.program(18, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":130,"column":8},"end":{"line":130,"column":90}}})) != null ? stack1 : "")
    + "\r\n    </div>\r\n    <div class=\"col-6\">Tax Residence:</div>\r\n    <div class=\"col-6 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"taxResidence") : stack1)) != null ? lookupProperty(stack1,"taxResidence") : stack1), depth0))
    + "</div>\r\n</div>\r\n<hr class=\"mt-3\"/>\r\n<p>\r\n    ADVISOR DETAILS\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(20, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":138,"column":4},"end":{"line":144,"column":15}}})) != null ? stack1 : "")
    + "</p>\r\n<div class=\"row\">\r\n    <div class=\"col-2\">First Name:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"firstName") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Middle Name:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"middleName") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row\">\r\n    <div class=\"col-2\">Last Name:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"lastName") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Name of Firm:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"firmName") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row\">\r\n    <div class=\"col-2\">Phone:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"phone") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">E-mail:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"email") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row\">\r\n    <div class=\"col-2\">Nationality:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"nationality") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Country of Incorporation:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"incorporationCountry") : stack1), depth0))
    + "</div>\r\n</div>\r\n<hr class=\"mt-3\"/>\r\n<p>\r\n    PRINCIPAL ADVISOR ADDRESS\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(22, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":173,"column":4},"end":{"line":179,"column":15}}})) != null ? stack1 : "")
    + "</p>\r\n<div class=\"row\">\r\n    <div class=\"col-2\">Address - 1st Line:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"primaryAddress") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Address - 2nd Line:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"secondaryAddress") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row pt-2\">\r\n    <div class=\"col-2\">Country:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"country") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">State:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"state") : stack1), depth0))
    + "</div>\r\n</div>\r\n<div class=\"row pt-2\">\r\n    <div class=\"col-2\">City:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"city") : stack1), depth0))
    + "</div>\r\n    <div class=\"col-2\">Postal Code:</div>\r\n    <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"postalCode") : stack1), depth0))
    + "</div>\r\n</div>\r\n\r\n<hr class=\"mt-3\"/>\r\n    <p>\r\n        <!-- WORLD CHECK -->\r\n        WORLD CHECK\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(24, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":204,"column":8},"end":{"line":210,"column":19}}})) != null ? stack1 : "")
    + "    </p>\r\n\r\n<div>\r\n    <hr class=\"mt-3\"/>\r\n    <p>FILES</p>\r\n    <table class=\"table\">\r\n        <thead>\r\n        <tr>\r\n            <th >Name</th>\r\n            <th style=\"width: 10%\">Group</th>\r\n            <th style=\"width: 10%\">Present</th>\r\n            <th >Explanation</th>\r\n            <th style=\"width: 10%\">Download</th>\r\n        </tr>\r\n        </thead>\r\n        <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(alias1,(depth0 != null ? lookupProperty(depth0,"positionFiles") : depth0),{"name":"each","hash":{},"fn":container.program(29, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":227,"column":8},"end":{"line":263,"column":17}}})) != null ? stack1 : "")
    + "        </tbody>\r\n    </table>\r\n</div>\r\n";
},"useData":true,"useDepths":true});
})();
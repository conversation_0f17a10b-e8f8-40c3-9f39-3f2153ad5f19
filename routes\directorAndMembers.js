const express = require('express');
const router = express.Router();
const directorAndMembersController =  require('../controllers/directorAndMembersController');

router.get('/', ensureAuthenticatedDirectorManager, directorAndMembersController.getDashboard);
router.get('/search', ensureAuthenticatedDirectorProductionOffices, directorAndMembersController.getSearch);
router.post("/search", ensureAuthenticatedDirectorProductionOffices,  directorAndMembersController.getSearch);
router.post("/export-search-xls", ensureAuthenticatedDirectorProductionOffices, directorAndMembersController.exportSearchXls)
//Import Directors

router.get("/import-data", ensureAuthenticatedImportDataManager, directorAndMembersController.getImportDataHistoryView);
router.get("/import-data/download/:filename", ensureAuthenticatedImportDataManager, directorAndMembersController.downloadArchiveVPDirectorFile);

function ensureAuthenticatedDirectorManager(req, res, next) {
    if (req.session.is_authenticated) {
        if (req.session.authentication.isDirBoImportManager === true || req.session.productionOfficeGroups?.length > 0) {
            return next();
        } else {
            res.redirect("/not-authorized");
        }
    } else {
        res.redirect("/login");
    }
}

function ensureAuthenticatedDirectorProductionOffices(req, res, next) {
    if (req.session.is_authenticated) {
        if (req.session.productionOfficeGroups?.length > 0) {
            return next();
        } else {
            res.redirect("/not-authorized");
        }
    } else {
        res.redirect("/login");
    }
}

function ensureAuthenticatedImportDataManager(req, res, next) {
    if (req.session.is_authenticated) {
        if (req.session.authentication.isDirBoImportManager === true) {
            return next();
        } else {
            res.redirect("/not-authorized");
        }
    } else {
        res.redirect("/login");
    }
}

module.exports = router;
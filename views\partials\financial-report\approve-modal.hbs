<div class="modal hide fade confirmation-modal-md" tabindex="-1" role="dialog" 
  id="approveExemptCompanyModal" style="display: none;" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="approveExemptCompanyTitle">Confirmation</h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
      </div>
      <div class="modal-body">
          <span>Have you reviewed all the provided documents and is everything in order?</span>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
          Close
        </button>
        
        <button id="approveCompany" type="button" class="btn btn-primary waves-effect waves-light" data-approved="true">
          Approve
        </button>
        <button id="approveCompanySpinner" style="display: none;" class="btn btn-primary waves-effect waves-light" type="button"
          disabled>
          <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
          Approve...
        </button>
        <button id="returnToClient" type="button" class="btn btn-primary waves-effect waves-light" data-approved="false">
          Return to Client
        </button>
        <button id="returnToClientSpinner" style="display: none;" class="btn btn-primary waves-effect waves-light" type="button"
          disabled>
          <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
          Return to Client...
        </button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>
<script type="text/javascript">
  const $approveCompanySpinner = $("#approveCompanySpinner");
  const $returnToClientSpinner = $("#returnToClientSpinner");
  let reportId = ''

  $('#approveExemptCompanyModal').on('shown.bs.modal', function (event) {
    const button = $(event.relatedTarget)
    reportId = button.data('report-id')
    $returnToClientSpinner.hide()
    $('#returnToClient').show()
  });


  $('#approveExemptCompanyModal').on('hide.bs.modal', function () {
    reportId = ''
  });

 $('#approveCompany, #returnToClient').on('click', async function (e) {
    e.preventDefault()
    $(this).hide()
    const $spinner = $(this).data('approved')? $approveCompanySpinner : $returnToClientSpinner
      $spinner.show()
      const dataObject = {
        isApproved: $(this).data('approved')
      }
      const id = reportId

      if (!dataObject.isApproved) {
        $('#approveExemptCompanyModal').modal('hide');
        const result = await Swal.fire({
            type: 'info',
            title: 'Please provide a note for the client:',
            showCancelButton: true,
            confirmButtonText: 'Confirm',
            reverseButtons: true,
            confirmButtonColor: "#4938D7",
            showLoaderOnConfirm: true,
            backdrop: true,
            input: "textarea",
            inputPlaceholder: "Type your message here...",
            inputAttributes: {
              "aria-label": "Type your message here"
            },
            onBeforeOpen: () => {
              setTimeout(() => {
                const textarea = Swal.getInput();
                if (textarea) {
                  textarea.focus();
                }
              }, 100);
            },
            onOpen: () => {  
              Swal.enableInput()
            },
            allowOutsideClick: () => !Swal.isLoading(),
            inputValidator: (value) => {
                if(!value){
                    return 'Please enter a valid email'
                } 
            },
            preConfirm: async (value) => {
                try {
                    dataObject.reason = value
                    const response = await $.ajax({
                        type: 'POST',
                        url: '/financial-report-management/'+ id+'/approve',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        },
                        data: JSON.stringify(dataObject)
                    });
                    if (response.status === 200) {
                        return response;
                    } else {
                        Swal.fire('Error', response.error ? response.error : 'An error occurred while sending the report back to the client', 'error');
                        return false;
                    }
                } catch (error) {
                    console.log("error ", error);
                    Swal.fire('Error', error.responseJSON?.error ? error.responseJSON?.error :  'An error occurred while a sending the report back to the client', 'error');
                    return false;
                }
            }
        });

        if (result?.value?.status === 200) {
            Swal.fire('Success', 'The report has been reopened and sent back for client to review', 'success').then(() => {
              $('#approveExemptCompanyModal').modal('hide');
              location.reload();
            });
        } else {
          $('#approveExemptCompanyModal').modal('show');
        }
      } else {
        $.ajax({
            type: 'POST',
            url: '/financial-report-management/'+ reportId+'/approve',
            data: dataObject,
            success: (data) => {
              $spinner.hide();
              if (data.status !== 200){
                Swal.fire('Error', 'There was an error approving the report', 'error').then(()=>{
                  $('#approveExemptCompanyModal').modal('hide');
                });
              }
              else{
                  Swal.fire('Success', 'The report has been approved succesfully', 'success').then(()=>{
                      $('#approveExemptCompanyModal').modal('hide');
                      location.reload();
                  });                  
              }
            },
            error: (err) => {
              $spinner.hide();
              Swal.fire('Error', 'There was an error approving the report', 'error').then(()=>{
                $('#approveExemptCompanyModal').modal('hide');
              });
            },
        });
      }
      
  })

</script>
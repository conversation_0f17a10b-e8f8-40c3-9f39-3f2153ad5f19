(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['informationmodalcontent'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"row ml-2\">\r\n        <div class=\"col-12\">\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"financialReports") : stack1),{"name":"each","hash":{},"fn":container.program(2, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":11,"column":12},"end":{"line":19,"column":21}}})) != null ? stack1 : "")
    + "        </div>\r\n    </div>\r\n";
},"2":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <span>\r\n                  <b>Start Date - End Date:</b> <br>\r\n                    "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"financialPeriod") : depth0)) != null ? lookupProperty(stack1,"start") : stack1),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":14,"column":20},"end":{"line":14,"column":87}}})) != null ? stack1 : "")
    + " - "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"financialPeriod") : depth0)) != null ? lookupProperty(stack1,"end") : stack1),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":14,"column":90},"end":{"line":14,"column":155}}})) != null ? stack1 : "")
    + " <br>\r\n                    <b>submitted At:</b> "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"submittedAt") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":15,"column":41},"end":{"line":15,"column":98}}})) != null ? stack1 : "")
    + " | Status: <b>"
    + container.escapeExpression(((helper = (helper = lookupProperty(helpers,"status") || (depth0 != null ? lookupProperty(depth0,"status") : depth0)) != null ? helper : alias2),(typeof helper === "function" ? helper.call(alias1,{"name":"status","hash":{},"data":data,"loc":{"start":{"line":15,"column":112},"end":{"line":15,"column":122}}}) : helper)))
    + "</b>\r\n                  \r\n                </span>\r\n                <br>\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    return " ";
},"5":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"row ml-2\">\r\n        <div class=\"col-12\">\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"companyData") : stack1)) != null ? lookupProperty(stack1,"accountingRecordsModule") : stack1)) != null ? lookupProperty(stack1,"reportedHistory") : stack1),{"name":"each","hash":{},"fn":container.program(6, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":35,"column":12},"end":{"line":42,"column":21}}})) != null ? stack1 : "")
    + "        </div>\r\n    </div> \r\n";
},"6":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <span>\r\n                  <b></b> <br>\r\n                    <b>Reported At:</b>"
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"reportedAt") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":38,"column":39},"end":{"line":38,"column":95}}})) != null ? stack1 : "")
    + " | "
    + container.escapeExpression(((helper = (helper = lookupProperty(helpers,"reportedBy") || (depth0 != null ? lookupProperty(depth0,"reportedBy") : depth0)) != null ? helper : alias2),(typeof helper === "function" ? helper.call(alias1,{"name":"reportedBy","hash":{},"data":data,"loc":{"start":{"line":38,"column":98},"end":{"line":38,"column":112}}}) : helper)))
    + " <br>\r\n                    "
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"isReported") : depth0),true,{"name":"ifEquals","hash":{},"fn":container.program(7, data, 0),"inverse":container.program(9, data, 0),"data":data,"loc":{"start":{"line":39,"column":20},"end":{"line":39,"column":92}}})) != null ? stack1 : "")
    + "\r\n                </span>\r\n                <br>\r\n";
},"7":function(container,depth0,helpers,partials,data) {
    return " Reported ";
},"9":function(container,depth0,helpers,partials,data) {
    return " Unreported";
},"11":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"row ml-2\">\r\n        <div class=\"col-12\">\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"companyData") : stack1)) != null ? lookupProperty(stack1,"accountingRecordsModule") : stack1)) != null ? lookupProperty(stack1,"deadlineReminders") : stack1),{"name":"each","hash":{},"fn":container.program(12, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":61,"column":12},"end":{"line":68,"column":21}}})) != null ? stack1 : "")
    + "        </div>\r\n    </div> \r\n";
},"12":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <span>\r\n                  <b></b> <br>\r\n                    <b>Reminder Date:</b>"
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"reminderDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":64,"column":41},"end":{"line":64,"column":99}}})) != null ? stack1 : "")
    + " | <b>Reminder Deadline:</b>"
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"reminderDeadline") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":64,"column":127},"end":{"line":64,"column":189}}})) != null ? stack1 : "")
    + "  | <b>Day: </b>"
    + alias4(((helper = (helper = lookupProperty(helpers,"reportedBy") || (depth0 != null ? lookupProperty(depth0,"reportedBy") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"reportedBy","hash":{},"data":data,"loc":{"start":{"line":64,"column":205},"end":{"line":64,"column":219}}}) : helper)))
    + " <br>\r\n                    <b>description:</b> "
    + alias4(((helper = (helper = lookupProperty(helpers,"description") || (depth0 != null ? lookupProperty(depth0,"description") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"description","hash":{},"data":data,"loc":{"start":{"line":65,"column":40},"end":{"line":65,"column":55}}}) : helper)))
    + "\r\n                </span>\r\n                <br>\r\n";
},"14":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"row ml-2\">\r\n        <div class=\"col-12\">\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"emails") : stack1),{"name":"each","hash":{},"fn":container.program(15, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":87,"column":12},"end":{"line":94,"column":21}}})) != null ? stack1 : "")
    + "        </div>\r\n    </div> \r\n";
},"15":function(container,depth0,helpers,partials,data) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <span>\r\n                  <b></b> <br>\r\n                    <b>Subject:</b>"
    + alias4(((helper = (helper = lookupProperty(helpers,"subject") || (depth0 != null ? lookupProperty(depth0,"subject") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"subject","hash":{},"data":data,"loc":{"start":{"line":90,"column":35},"end":{"line":90,"column":46}}}) : helper)))
    + " | <b>Email Subject:</b>"
    + alias4(((helper = (helper = lookupProperty(helpers,"emailSubject") || (depth0 != null ? lookupProperty(depth0,"emailSubject") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"emailSubject","hash":{},"data":data,"loc":{"start":{"line":90,"column":70},"end":{"line":90,"column":86}}}) : helper)))
    + " | <b>Status: </b>"
    + alias4(((helper = (helper = lookupProperty(helpers,"status") || (depth0 != null ? lookupProperty(depth0,"status") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"status","hash":{},"data":data,"loc":{"start":{"line":90,"column":104},"end":{"line":90,"column":114}}}) : helper)))
    + " <br>\r\n                    <b>Content:</b> "
    + alias4(((helper = (helper = lookupProperty(helpers,"content") || (depth0 != null ? lookupProperty(depth0,"content") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"content","hash":{},"data":data,"loc":{"start":{"line":91,"column":36},"end":{"line":91,"column":47}}}) : helper)))
    + "\r\n                </span>\r\n                <br>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "\r\n<div id=\"financialPeriods\" class=\"mb-2\">\r\n    <div class=\"row\">\r\n        <div class=\"col-12\">\r\n            <h4><b>Financial Reports </b></h4>\r\n        </div>\r\n    </div>\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"financialReports") : stack1),{"name":"if","hash":{},"fn":container.program(1, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":8,"column":4},"end":{"line":22,"column":11}}})) != null ? stack1 : "")
    + "</div>\r\n<br>\r\n<hr>\r\n<div id=\"reportHistory\">\r\n    <div class=\"row mb-2\">\r\n        <div class=\"col-12\">\r\n            <h4><b>Report/Unreport History</b></h4>\r\n        </div>\r\n    </div>\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,((stack1 = ((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"companyData") : stack1)) != null ? lookupProperty(stack1,"accountingRecordsModule") : stack1)) != null ? lookupProperty(stack1,"reportedHistory") : stack1),{"name":"if","hash":{},"fn":container.program(5, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":32,"column":4},"end":{"line":45,"column":11}}})) != null ? stack1 : "")
    + "\r\n\r\n\r\n</div>\r\n<br>\r\n<hr>\r\n<div id=\"reminders\">\r\n    <div class=\"row mb-2\">\r\n        <div class=\"col-12\">\r\n            <h4><b>Deadline Reminders</b></h4>\r\n        </div>\r\n    </div>\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,((stack1 = ((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"companyData") : stack1)) != null ? lookupProperty(stack1,"accountingRecordsModule") : stack1)) != null ? lookupProperty(stack1,"deadlineReminders") : stack1),{"name":"if","hash":{},"fn":container.program(11, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":58,"column":4},"end":{"line":71,"column":11}}})) != null ? stack1 : "")
    + "\r\n\r\n\r\n</div>\r\n<br>\r\n<hr>\r\n<div id=\"Emails\">\r\n    <div class=\"row mb-2\">\r\n        <div class=\"col-12\">\r\n            <h4><b>MCC Emails</b></h4>\r\n        </div>\r\n    </div>\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"emails") : stack1),{"name":"if","hash":{},"fn":container.program(14, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":84,"column":4},"end":{"line":97,"column":11}}})) != null ? stack1 : "")
    + "\r\n\r\n\r\n</div>\r\n\r\n\r\n";
},"useData":true});
})();
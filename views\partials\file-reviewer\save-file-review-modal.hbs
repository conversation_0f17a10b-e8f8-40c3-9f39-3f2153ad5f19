<!-- NOFITY MODAL -->
<div class="modal fade" id="saveModal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 id="modal-title" class="modal-title"></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group" id="remarkGroup">
          <label for="internalTridentRemark">Internal Trident Remark</label>
          <textarea
            class="form-control"
            name="internalTridentRemark"
            id="internalTridentRemark"
            form="fileReviewForm"
          ></textarea>
        </div>
        <span id="saveGroup" class="font-weight-bold"
          >Are you sure you want to save and continue later?</span
        >
      </div>
      <div class="modal-footer justify-content-between">
        <div class="col-6">
          <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
            Close
          </button>
        </div>
        <div class="d-flex col-6 justify-content-end">
          <button
            id="onHoldButton"
            type="button"
            form="fileReviewForm"
            class="btn solid royal-blue modal-action-btn"
          >
            Put on hold
          </button>
          <button
            id="saveButton"
            type="button"
            form="fileReviewForm"
            class="btn solid royal-blue modal-action-btn"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  let id = '';
  let page;
  $('#saveModal').on('show.bs.modal', function (event) {
    let button = $(event.relatedTarget);
    page = button.data('page');
    if (button.attr('id') === 'save-file-review-button') {
      $('#modal-title').text('Save');
      $('#remarkGroup').hide();
      $('#onHoldButton').hide();
      $('#saveGroup').show();
      $('#saveButton').show();
    } else if (button.attr('id') === 'on-hold-file-review-button') {
      $('#modal-title').text('Put On Hold');
      $('#remarkGroup').show();
      $('#onHoldButton').show();
      $('#saveGroup').hide();
      $('#saveButton').hide();
    }
    id = button.data('id');
  });

  $('.modal-action-btn').click(function () {
    let btnId = this.id;
    if(page === 1) {
      let form = $('#fileReviewForm').serialize();
      form = form.concat('&btnId=' + btnId + '&page=' + page);
      $.ajax({
      type: 'POST',
      url: '/file-reviewer/save-file-review/'+id,
      data: form,
      success: function (res) {
        location.href = '/file-reviewer/file-review-list';
      },
      error: function (err) {
        if (err.status === 400) {
          Swal.fire('Error', 'You have too many File Reviews on hold', 'error');
        }
      },
    });
    } else if (page === 2) {
      
    }
  });
</script>
<!-- NOFITY MODAL END -->

<div id="worldCheckDetails">
    <div class="row">
        <div class="col-5">
            <h4>World Check Details</h4>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            {{>file-reviewer/shared/relation-file-table tableId="worldCheckTable" group=group
                    files=(ternary newRelation worldCheck worldCheck.files) name="worldCheck" relationId=relation._id}}
        </div>
    </div>
</div>
<div class="row pt-3">
    <div class="col-12 d-flex justify-content-end">
        <div class="custom-control custom-checkbox">
            <input
                    type="checkbox"
                    class="custom-control-input completeCheck"
                    name="worldCheck[correct]"
                    id="correctWorldCheck"
                    {{#if relationInformation.complete }} checked {{/if}}
            />
            <label class="custom-control-label" for="correctWorldCheck">Complete Information</label>
        </div>
    </div>
</div>
const { BlobServiceClient, StorageSharedKeyCredential } = require("@azure/storage-blob");
const { DefaultAzureCredential } = require("@azure/identity");

/**
 * Renames a blob in Azure Storage
 * @param {string} accountName - Azure Storage account name
 * @param {string} accountKey - Azure Storage account key (optional)
 * @param {string} container - Azure Storage container name
 * @param {string} oldName - Old blob name
 * @param {string} newName - New blob name
 * @returns void
 */
exports.renameBlob = async function (accountName, accountKey, container, oldName, newName) {
  try {
    let credentials;

    if (!accountKey) {
      credentials = new DefaultAzureCredential();
    } else {
      // Only for local development and testing
      credentials = new StorageSharedKeyCredential(accountName, accountKey);
    }
    const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net`, credentials);
    const containerClient = blobServiceClient.getContainerClient(container);

    const newBlobClient = containerClient.getBlobClient(newName);
    const sourceBlobClient = containerClient.getBlobClient(oldName);

    const poller = await newBlobClient.beginCopyFromURL(sourceBlobClient.url);
    await poller.pollUntilDone();
    await sourceBlobClient.delete();

  } catch (error) {
    console.log("error: ", error);
  }
}

/**
 * Get a container client from Azure Storage
 * @param {string} container - Azure Storage container name
 * @param {string} account - Azure Storage account name
 * @param {string} key - Azure Storage account key (optional)
 * @returns Azure Storage container client
 */
exports.getContainerClient = function (container, account, key) {
  let credentials;

  if (!key) {
    credentials = new DefaultAzureCredential();
  } else {
    // Only for local development and testing
    credentials = new StorageSharedKeyCredential(account, key);
  }
  const blobServiceClient = new BlobServiceClient(`https://${account}.blob.core.windows.net`, credentials);
  return blobServiceClient.getContainerClient(container);
}
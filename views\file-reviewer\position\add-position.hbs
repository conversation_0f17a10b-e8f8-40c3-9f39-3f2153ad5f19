<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            New Position to
                            <span class="font-weight-bold"
                            >{{org.details.organizationName }}</span
                            >
                        </h3>
                    </div>
                    <div class="card-body">
                        <form
                                id="positionForm"
                                method="POST"
                                action=""
                                autocomplete="off">
                            <label for="positionType">Select Type of Position: </label>
                            <div class="form-row pl-1">
                                <div class="custom-control custom-checkbox form-check-inline">
                                    <input class="custom-control-input" type="checkbox" id="positionType1" value="founder" name="positionType[]">
                                    <label class="custom-control-label" for="positionType1">Founder</label>
                                </div>
                                <div class="custom-control custom-checkbox form-check-inline">
                                    <input class="custom-control-input" type="checkbox" id="positionType2" value="trustee" name="positionType[]">
                                    <label class="custom-control-label" for="positionType2">Trustee</label>
                                </div>
                                <div class="custom-control custom-checkbox form-check-inline">
                                    <input class="custom-control-input" type="checkbox" id="positionType3" value="council member" name="positionType[]">
                                    <label class="custom-control-label" for="positionType3">Council Member</label>
                                </div>
                                <div class="custom-control custom-checkbox form-check-inline">
                                    <input class="custom-control-input" type="checkbox" id="positionType4" value="protector" name="positionType[]">
                                    <label class="custom-control-label" for="positionType4">Protector</label>
                                </div>
                            </div>
                            {{>file-reviewer/review-relations/natural-form-component newRelation="true"
                                    relation=position relationInformation=positionInformation}}
                        </form>
                        <!-- END FORM -->
                    </div>
                    <!-- END CARD BODY -->
                    <div class="row mt-4 justify-content-between">
                        <div class="col-2">
                            <a
                                    href="/file-reviewer/open-file-review/{{ id }}/beneficial-owners"
                                    class="btn btn-secondary width-lg waves-effect waves-light"
                            >
                                Back
                            </a>
                        </div>
                        <div class="col-2 d-flex justify-content-end">
                            <button
                                    id="submitPositionBtn"
                                    type="submit"
                                    form="positionForm"
                                    class="btn solid royal-blue px-4"
                                    data-id="{{ id }}"
                                    data-org="{{ org._id }}"
                            >
                                Submit
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>file-reviewer/upload-temp-modal}}
<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script>
    let existingSelected = '';
    $('#empty-option').prop('selected', true);
    $('#empty-relation-option').prop('selected', true);
</script>

<script>
    $('#correctDetails').change(function () {
        let checkedState = this.checked;
        $('#correctIdentification').prop('checked', checkedState);
    });
    $('#correctIdentification').change(function () {
        let checkedState = this.checked;
        $('#correctDetails').prop('checked', checkedState);
    });
</script>
<!-- VALIDATION -->
<script>
    $('input[name="details[fullName]"]').on('input', function () {
        $('input[name="details[fullName]"]').removeClass('is-invalid');
    });

    $('input[name="positionType[]"]').on('change', function () {
        if ($('input[name="positionType[]"]:checked').length > 0) {
            $('input[name="positionType[]"]').removeClass('is-invalid')
        }
    });

    $('#positionForm').submit(function (event) {
        const submitBtn = $("#submitPositionBtn");
        submitBtn.prop('disabled', true);
        event.preventDefault();

        let reviewId = $('button[type=submit]').data('id');
        let orgId = $('button[type=submit]').data('org');
        let fullname = $('input[name="details[fullName]"]');
        let positionType = $('input[name="positionType[]"]');
        if ($('input[name="positionType[]"]:checked').length === 0) {
            positionType.addClass("is-invalid");
            positionType.focus();
            setTimeout(function(){submitBtn.prop('disabled', false); }, 0);
            return false;
        }

        if (!fullname.val()) {
            fullname.addClass('is-invalid');
            fullname.focus();
            setTimeout(function(){submitBtn.prop('disabled', false); }, 0);
            return false;
        } else {
            $.ajax({
                url: '/file-reviewer/reviews/' + reviewId + '/organizations/' + orgId + '/create-position',
                type: 'POST',
                data: $(this).serialize(),
                timeout: 3000,
                success: function () {
                    location.href = '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
                },
                error: function () {
                    Swal.fire('Error', 'There was an error creating the position', 'error').then(() => {
                        location.href = '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
                    });
                },
            });
        }

    });
</script>

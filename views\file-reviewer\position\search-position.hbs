<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            <span class="font-weight-bold">Add position to organization:</span>
                        </h3>
                    </div>
                    <div class="card-body">
                        <form id="searchRelationForm" method="POST" autocomplete="off" class="form"
                              action="/file-reviewer/reviews/{{ reviewId }}/organizations/{{ organizationId }}/positions">
                            <p>Your query should contain at least three characters.</p>
                            <div class="form-row align-items-center">
                                <div class="form-group col-md-3">
                                    <label for="name">Full name</label>
                                    <input type="text" class="form-control" name="name" id="name"
                                           value="{{filters.name}}">
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="birthDate">Date of Birth</label>
                                    <input id="birthDate" name="birthDate" type="date" class="form-control"
                                           value="{{filters.birthDate}}"/>
                                </div>
                                <div class="form-group col-md-3">
                                    <label for="nationality">Nationality</label>
                                    {{>file-reviewer/shared/select-country selectId="nationality" value=filters.nationality}}
                                </div>
                                <div class="col-auto mt-2">
                                    <button type="submit" class="btn solid royal-blue">Search</button>
                                </div>
                            </div>
                        </form>

                        <br>
                        <div class="row">
                            <div class="col-md-12">
                                <table id="position-datatable" class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Full Name</th>
                                            <th>First Name</th>
                                            <th>Middle Name</th>
                                            <th>Last Name</th>
                                            <th>Date of Birth</th>
                                            <th>Country of Birth</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    {{#each positions}}
                                        <tr>
                                            <td>{{details.fullName}}</td>
                                            <td>{{details.firstName}}</td>
                                            <td>{{details.middleName}}</td>
                                            <td>{{details.lastName}}</td>
                                            <td>{{#formatDate details.birthDate "YYYY-MM-DD"}} {{/formatDate }}</td>
                                            <td>{{details.countryBirth}}</td>

                                            <td>
                                                {{#ifCond positionGroups.length '<' 4}}
                                                    <button
                                                            type="button"
                                                            class="btn solid royal-blue"
                                                            data-toggle="modal"
                                                            data-target="#pickPositionModal"
                                                            id="btn-{{_id}}"
                                                            data-position-id="{{ _id }}"
                                                            data-organization-id="{{../organizationId}}"
                                                            data-review-id="{{../reviewId}}"
                                                            data-position-groups="{{positionGroups}}"
                                                    >
                                                        Pick
                                                    </button>
                                                {{/ifCond}}

                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="7" class="text-center font-italic">There are no relations</td>

                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-md-12">
                                <p>
                                    Your search results contains all Clients from Trident and all relations that have been
                                    added manually. If your search result does not include the entity you are looking for,
                                    you can create one manually by clicking on the New button.
                                </p>
                            </div>
                        </div>

                    </div>
                    <div class="row mt-4 justify-content-between">
                        <div class="col-2">
                            <a
                                    href="/file-reviewer/open-file-review/{{ reviewId }}/beneficial-owners"
                                    class="btn btn-secondary width-lg waves-effect waves-light"
                            >
                                Back
                            </a>
                        </div>
                        {{#if canCreateRelation}}
                            <div class="col-2 d-flex justify-content-end">
                                <a href="/file-reviewer/reviews/{{ reviewId }}/organizations/{{organizationId}}/create-position"
                                   class="btn solid royal-blue px-4">
                                    New
                                </a>
                            </div>
                        {{/if}}

                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>file-reviewer/review-relations/modals/create-relation-modal}}
{{>file-reviewer/position/pick-position-modal}}

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/showrelationtypeform.precompiled.js"></script>
<script>
    let existingSelected = "";
    let $relationType = $('#relationType');
    let typeSelected = $('#relationType option:selected').val();

    $(document).ready(function () {
        $("#position-datatable").DataTable({
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>",
                },
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });

    });

    $relationType.on('change', function () {
        typeSelected = $('#relationType option:selected').val();
        existingSelected = '';

        $('#empty-relation-option').prop('selected', true);
        const url = window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/'));

        window.location.href = window.location.origin + url + "/" + typeSelected;

    });
</script>


<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            Add Position:
                            <span class="font-weight-bold">{{ review.companyName }} - {{ organization.details.organizationName }}</span>
                        </h3>
                    </div>
                    <div class="card-body">
                        <form id="positionForm" method="POST"
                              action="/file-reviewer/open-file-review/{{ review._id }}/relations/{{organization._id}}/positions/{{ positionReferenceId }}">

                            <div>
                                <label for="positionType">Type of Position:</label>
                                <input type="text" name="positionType" id="positionType" readonly value="{{positionType}}">
                            </div>
                            {{>file-reviewer/review-relations/natural-form-component relation=position relationInformation=positionInformation}}
                        </form>
                    </div>

                    <div class="row mt-4 justify-content-between">
                        <div class="col-2">
                            <a
                                    href="/file-reviewer/open-file-review/{{ review._id }}/beneficial-owners"
                                    class="btn btn-secondary width-lg waves-effect waves-light"
                            >
                                Back
                            </a>
                        </div>
                        <div class="col-2 d-flex justify-content-end">
                            <button type="submit" id="submitPositionBtn" form="positionForm" class="btn solid royal-blue px-4"
                                    data-review-id="{{ review._id }}" data-org-id="{{ organization._id }}"
                                    data-position-reference="{{ positionReferenceId }}" data-position-id="{{ position._id }}">
                                Edit
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

{{>file-reviewer/upload-temp-modal}}
<script>
    $('#correctDetails').change(function () {
        let checkedState = this.checked;
        $('#correctIdentification').prop('checked', checkedState);
    });
    $('#correctIdentification').change(function () {
        let checkedState = this.checked;
        $('#correctDetails').prop('checked', checkedState);
    });
</script>
<!-- VALIDATION -->
<script>
    $('input[name="details[fullName]"]').on('input', function () {
        $('input[name="details[fullName]"]').removeClass('is-invalid');
    });


    $('#positionForm').submit(function (event) {
        const submitBtn = $("#submitPositionBtn");
        submitBtn.prop('disabled', true);
        event.preventDefault();

        let reviewId = $('button[type=submit]').data('review-id');
        let org = $('button[type=submit]').data('org-id');
        let positionId = $('button[type=submit]').data('position-id');
        let positionReferenceId = $('button[type=submit]').data('position-reference');
        let fullname = $('input[name="details[fullName]"]');

        if (!fullname.val()) {
            fullname.addClass('is-invalid');
            fullname.focus();
            setTimeout(function(){submitBtn.prop('disabled', false); }, 0);
            return false;
        } else {
            $.ajax({
                url: '/file-reviewer/open-file-review/' + reviewId + '/relations/' + org + '/positions/' + positionReferenceId + '/update',
                type: 'PUT',
                timeout: 3000,
                data: $(this).serialize(),
                success: function () {
                    location.href = '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
                },
                error: function () {
                    Swal.fire('Error', 'There was an error updating the position', 'error').then(() => {
                        location.href = '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
                    });
                },
            });
        }
    });
</script>

const excel = require("node-excel-export");
const EntryModel = require("../models/entry").EntryModel;
const ArchivedEntryModel = require("../models/entry").ArchivedEntryModel;
const moment = require("moment");
const CompanyModel = require("../models/company").schema;

exports.exportSearchXls = async function (req, res, next) {
  try {

    let entries = await searchToExport(req);

    const styles = {
      headerTable: {
        fill: {
          fgColor: {
            rgb: "ffffff",
          },
        },
        border: {
          top: { style: "thin", color: "000000" },
          bottom: { style: "thin", color: "000000" },
          left: { style: "thin", color: "000000" },
          right: { style: "thin", color: "000000" },
        },
        font: {
          color: {
            rgb: "000000",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
    };

    const specification = {
      Email: {
        displayName: "Email",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Entity_Name: {
        displayName: "Entity Name",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Entity_Number: {
        displayName: "Entity Number",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Master_Client_Code: {
        displayName: "Master Client Code",
        headerStyle: styles.headerTable,
        width: 160,
      },
      Company_Number: {
        displayName: "Company Number",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Status: {
        displayName: "Status",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Created: {
        displayName: "Created",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Submitted: {
        displayName: "Submitted",
        headerStyle: styles.headerTable,
        width: 120,
      },
      reOpenDate: {
        displayName: "Re-Open",
        headerStyle: styles.headerTable,
        width: 120,
      },
      reSubmittedDate: {
        displayName: "Re-Submitted",
        headerStyle: styles.headerTable,
        width: 120,
      },
      rfi: {
        displayName: "RFI",
        headerStyle: styles.headerTable,
        width: 120,
      },
      rfiCompleted: {
        displayName: "RFI completed",
        headerStyle: styles.headerTable,
        width: 120,
      },
      incorporationDate: {
        displayName: "Incorporation Date",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Date_Paid: {
        displayName: "Date Paid",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Payment_Ref: {
        displayName: "Payment Ref",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Financial_Period_Enddate: {
        displayName: "Financial Period Enddate",
        headerStyle: styles.headerTable,
        width: 180,
      },
      Referral_Office: {
        displayName: "Referral Office",
        headerStyle: styles.headerTable,
        width: 120,
      },
      None: {
        displayName: "None",
        headerStyle: styles.headerTable,
        width: 120,
      },
      BB: {
        displayName: "BB",
        headerStyle: styles.headerTable,
        width: 120,
      },
      IB: {
        displayName: "IB",
        headerStyle: styles.headerTable,
        width: 120,
      },
      FMB: {
        displayName: "FMB",
        headerStyle: styles.headerTable,
        width: 120,
      },
      FLB: {
        displayName: "FLB",
        headerStyle: styles.headerTable,
        width: 120,
      },
      HQ: {
        displayName: "HQ",
        headerStyle: styles.headerTable,
        width: 120,
      },
      SB: {
        displayName: "SB",
        headerStyle: styles.headerTable,
        width: 120,
      },
      HB: {
        displayName: "HB",
        headerStyle: styles.headerTable,
        width: 120,
      },
      IP: {
        displayName: "IP",
        headerStyle: styles.headerTable,
        width: 120,
      },
      SC: {
        displayName: "SC",
        headerStyle: styles.headerTable,
        width: 120,
      },
    };
    const dataset = [{}];
    for (let i = 0; i < entries.length; i++) {
      dataset.push({
        Email: entries[i].email,
        Entity_Name: entries[i].entity_name,
        Entity_Number: entries[i].code,
        Master_Client_Code: entries[i].masterclientcode,
        Company_Number: entries[i].incorporationcode,
        Status: entries[i].status,
        Created: entries[i].createdAt ? moment(entries[i].createdAt).format("YYYY-MM-DD") : "",
        Submitted: entries[i].submitted_at ? moment(entries[i].submitted_at).format("YYYY-MM-DD") : "",
        reOpenDate:  entries[i].reOpenDate,
        reSubmittedDate: entries[i].reSubmittedDate,
        rfi: entries[i].rfi,
        rfiCompleted:  entries[i].rfiCompleted,
        incorporationDate:  entries[i].incorporationdate ?  moment(entries[i].incorporationdate).format("YYYY-MM-DD") :  "",
        Date_Paid: entries[i].date_paid
          ? moment(entries[i].date_paid).format("YYYY-MM-DD")
          : "",
        Payment_Ref: entries[i].payment_reference,
        Financial_Period_Enddate: entries[i].financial_period_ends
          ? moment(entries[i].financial_period_ends).format("YYYY-MM-DD")
          : "",
        Referral_Office: entries[i].referral_office,
        None: entries[i].relevant_activities
          ? entries[i].relevant_activities.none.selected
            ? "Yes"
            : "No"
          : "No",
        BB: entries[i].relevant_activities
          ? entries[i].relevant_activities.banking_business.selected
            ? "Yes"
            : "No"
          : "No",
        IB: entries[i].relevant_activities
          ? entries[i].relevant_activities.insurance_business.selected
            ? "Yes"
            : "No"
          : "No",
        FMB: entries[i].relevant_activities
          ? entries[i].relevant_activities.fund_management_business.selected
            ? "Yes"
            : "No"
          : "No",
        FLB: entries[i].relevant_activities
          ? entries[i].relevant_activities.finance_leasing_business.selected
            ? "Yes"
            : "No"
          : "No",
        HQ: entries[i].relevant_activities
          ? entries[i].relevant_activities.headquarters_business.selected
            ? "Yes"
            : "No"
          : "No",
        SB: entries[i].relevant_activities
          ? entries[i].relevant_activities.shipping_business.selected
            ? "Yes"
            : "No"
          : "No",
        HB: entries[i].relevant_activities
          ? entries[i].relevant_activities.holding_business.selected
            ? "Yes"
            : "No"
          : "No",
        IP: entries[i].relevant_activities
          ? entries[i].relevant_activities.intellectual_property_business
            .selected
            ? "Yes"
            : "No"
          : "No",
        SC: entries[i].relevant_activities
          ? entries[i].relevant_activities.service_centre_business.selected
            ? "Yes"
            : "No"
          : "No",
      });
    }

    const report = excel.buildExport([
      {
        name: "Submission",
        specification: specification,
        data: dataset,
      },
    ]);

    res.attachment("Search Report.xlsx");
    res.send(report);
  } catch (error) {
    console.log(error);
    next(error);
  }
};

async function searchToExport(req, res, next) {
  try {

    let submissionfilter = {};
    let companyfilter = {};

    if (
      req.body.filter_masterclient &&
      req.body.filter_masterclient.length > 2
    ) {
      submissionfilter["company_data.masterclientcode"] = {
        $regex: req.body.filter_masterclient,
        $options: "i",
      };
      companyfilter["masterclientcode"] = {
        $regex: req.body.filter_masterclient,
        $options: "i",
      };
    }

    if (req.body.filter_company && req.body.filter_company.length > 2) {
      companyfilter["name"] = {
        $regex: req.body.filter_company,
        $options: "i",
      };
      submissionfilter["company_data.name"] = {
        $regex: req.body.filter_company,
        $options: "i",
      };
    }

    if (req.body.filter_referral && req.body.filter_referral.length > 2) {
      companyfilter["referral_office"] = {
        $regex: req.body.filter_referral,
        $options: "i",
      };
      submissionfilter["company_data.referral_office"] = {
        $regex: req.body.filter_referral,
        $options: "i",
      };
    }

    if (req.body.resident_outside_bvi && req.body.resident_outside_bvi == "true") {
      req.body.relevantActivities = {};
      submissionfilter["tax_residency.resident_in_BVI"] = false;
    }

    if (req.body.relevant_activities && req.body.relevant_activities.length) {
      req.body.relevantActivities = {};
      if (Array.isArray(req.body.relevant_activities)) {
        for (let relevantActivity of req.body.relevant_activities) {
          submissionfilter[
            `relevant_activities.${relevantActivity}.selected`
            ] = true;
          req.body.relevantActivities[relevantActivity] = true;
        }
      } else {
        submissionfilter[
          `relevant_activities.${req.body.relevant_activities}.selected`
          ] = true;
        req.body.relevantActivities[req.body.relevant_activities] = true;
      }
    }

    if (req.body.filter_submitted_range_start) {
      submissionfilter["submitted_at"] = {
        $gte: req.body.filter_submitted_range_start,
        $lte: req.body.filter_submitted_range_end ? req.body.filter_submitted_range_end : new Date(),
      };
    } else if (req.body.filter_submitted_range_end) {
      submissionfilter["submitted_at"] = { $lte: req.body.filter_submitted_range_end };
    }

    if (req.body.filter_incorporated_range_start) {
      companyfilter["incorporationdate"] = {
        $gte: req.body.filter_incorporated_range_start,
      };
      submissionfilter["company_data.incorporationdate"] = {
        $gte: req.body.filter_incorporated_range_start,
      };
    }
    if (req.body.filter_incorporated_range_end) {
      companyfilter["incorporationdate"] = {
        ...companyfilter["incorporationdate"],
        $lte: req.body.filter_incorporated_range_end
      };
      submissionfilter["company_data.incorporationdate"] = {
        ...submissionfilter["company_data.incorporationdate"],
        $lte: req.body.filter_incorporated_range_end
      };
    }

    if (req.body.filter_financial_period_range_start) {
      submissionfilter["entity_details.financial_period_ends"] = {
        $gte: req.body.filter_financial_period_range_start,
      };
    }
    if (req.body.filter_financial_period_range_end) {
      submissionfilter["entity_details.financial_period_ends"] = {
        ...submissionfilter["entity_details.financial_period_ends"],
        $lte: req.body.filter_financial_period_range_end
      };
    }

    if (
      !companyfilter["name"] &&
      !companyfilter["masterclientcode"] &&
      !companyfilter["referral_office"]
    ) {
      //dummy filter to exclude all companies without submissions
      companyfilter["code"] = "-1";
    }

    if (
      submissionfilter["submitted_at"] ||
      (req.body.relevant_activities && req.body.relevant_activities.length)
    ) {
      companyfilter["code"] = "-1";
    }
    let limit = 100;
    if (Object.keys(submissionfilter).length) {
      limit = process.env.MAX_QUERY_SIZE ? Number(process.env.MAX_QUERY_SIZE) : 5000 ;
    }
    let search_result = [];
    if (req.body.filter_not_started === 'true') {

      let companies = await  CompanyModel.aggregate([
        { $match: companyfilter },
        {
          $lookup: {
            from: "archivedentries",
            localField: "code",
            foreignField: "company",
            as: "archivedentries",
          },
        },
        {
          $lookup: {
            from: "entries",
            localField: "code",
            foreignField: "company",
            as: "entries",
          },
        },
        {
          $project: {
            name: 1,
            code: 1,
            masterclientcode: 1,
            incorporationcode: 1,
            referral_office: 1,
            count: { $add: [{ $size: "$entries" }, { $size: "$archivedentries" }] },
          },
        },
        { $match: { count: 0 } },
      ]);

      for (let idx = 0; idx < companies.length; idx++) {
        let singleResult = {
          entity_name: companies[idx].name,
          code: companies[idx].code,
          masterclientcode: companies[idx].masterclientcode,
          incorporationcode: companies[idx].incorporationcode,
          incorporationdate: companies[idx].incorporationdate,
          status: "NOT STARTED",
          referral_office: companies[idx].referral_office,
        };
        search_result.push(singleResult);
      }
    } else {
      let entries = await EntryModel.find(submissionfilter, {
        _id: 1,
        created_by: 1,
        company_data: 1,
        company: 1,
        status: 1,
        createdAt: 1,
        submitted_at: 1,
        entity_details: 1,
        relevant_activities: 1,
        payment: 1,
        requested_information: 1,
        client_returned_information: 1,
        reopened: 1
      }).sort({ createdAt: -1 }).limit(limit).exec();

      const archivedEntries = await ArchivedEntryModel.find(submissionfilter, {
        _id: 1,
        created_by: 1,
        company_data: 1,
        company: 1,
        status: 1,
        createdAt: 1,
        submitted_at: 1,
        entity_details: 1,
        relevant_activities: 1,
        payment: 1,
        requested_information: 1,
        client_returned_information: 1,
        reopened: 1
      }).sort({ createdAt: -1 }).limit(limit).exec();

      entries = [
        ...entries,
        ...archivedEntries
      ];

      let companies = await CompanyModel.aggregate([
        { $match: companyfilter },
        {
          $lookup: {
            from: "entries",
            localField: "code",
            foreignField: "company",
            as: "entries",
          },
        },
        {
          $lookup: {
            from: "archivedentries",
            localField: "code",
            foreignField: "company",
            as: "archivedentries",
          },
        },
        {
          $project: {
            name: 1,
            code: 1,
            masterclientcode: 1,
            incorporationcode: 1,
            referral_office: 1,
            count: { $add: [{ $size: "$entries" }, { $size: "$archivedentries" }] },
          },
        },
        { $match: { count: 0 } },
      ]);

      for (let idx = 0; idx < entries.length; idx++) {
        const requestedInformationList = entries[idx].requested_information?.details || [];
        const returnedInformationList = entries[idx].client_returned_information?.details || [];

        const lastReopen = entries[idx].reopened && entries[idx].reopened.details.length
          ? entries[idx].reopened.details[entries[idx].reopened.details.length - 1] : null;
        const lastRFI = requestedInformationList && requestedInformationList.length
          ? requestedInformationList[requestedInformationList.length - 1] : null;
        const lastRFIResponse = returnedInformationList && returnedInformationList.length
          ? returnedInformationList[returnedInformationList.length - 1] : null;

        let singleResult = {
          _id: entries[idx]._id,
          company: entries[idx].company,
          email: entries[idx].created_by,
          entity_name: entries[idx].company_data.name,
          code: entries[idx].company_data.code,
          masterclientcode: entries[idx].company_data.masterclientcode,
          incorporationcode: entries[idx].company_data.incorporationcode,
          incorporationdate: entries[idx].company_data.incorporationdate,
          status: entries[idx].status,
          createdAt: entries[idx].createdAt,
          submitted_at: entries[idx].submitted_at,
          reOpenDate:  lastReopen && lastReopen.date_reopened ?
            moment(lastReopen.date_reopened).format("YYYY-MM-DD") : "",
          reSubmittedDate: lastReopen && lastReopen.resubmitted_at ?
            moment(lastReopen.resubmitted_at).format("YYYY-MM-DD") : "",
          rfi: lastRFI && lastRFI.status !== 'CANCELLED' && lastRFI.requested_at  ?
            moment(lastRFI.requested_at ).format("YYYY-MM-DD") : "",
          rfiCompleted:  lastRFI && lastRFI.status !== 'CANCELLED' && lastRFIResponse && lastRFIResponse.request_id === lastRFI.id
            ? moment(lastRFIResponse.returned_at).format("YYYY-MM-DD") : "",
          financial_period_ends: entries[idx].entity_details
            ? entries[idx].entity_details.financial_period_ends
            : null,
          referral_office: entries[idx].company_data.referral_office,
          relevant_activities: entries[idx].relevant_activities,
          date_paid: entries[idx].payment
            ? entries[idx].payment.payment_received_at
            : null,
          payment_reference: entries[idx].payment
            ? entries[idx].payment.batchpayment_code
            : null,
        };
        search_result.push(singleResult);
      }

      for (let idx = 0; idx < companies.length; idx++) {
        let singleResult = {
          entity_name: companies[idx].name,
          code: companies[idx].code,
          masterclientcode: companies[idx].masterclientcode,
          incorporationcode: companies[idx].incorporationcode,
          incorporationdate: companies[idx].incorporationdate,
          status: "NOT STARTED",
          referral_office: companies[idx].referral_office,
        };
        search_result.push(singleResult);
      }

    }

    return search_result;
  } catch (error) {
    console.log(error);
    next(error);
  }
}

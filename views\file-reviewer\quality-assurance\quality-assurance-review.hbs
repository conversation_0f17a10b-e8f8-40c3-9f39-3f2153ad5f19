<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            Company Name:<span class="font-weight-bold pl-1">{{ file.companyName }}</span>
                        </h3>
                        <h5>
                            The file review officer confirmed the type of company is <span class="mb-2">{{file.companyType}}</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        {{#if pep}}
                            <div class="alert alert-warning" role="alert">
                                <span class="font-weight-bold">Be aware</span>: in this company structure there is at
                                least one PEP present!
                            </div>
                        {{/if}}
                        <div id="internal-comments" class="pb-2">
                            <h4 class="font-weight-bold mb-2">Internal Comments</h4>
                            {{#each file.comments}}
                                <div class="row">
                                    <div class="col-12 {{#ifEquals role 'CO'}} {{#unless
                                            to}} text-danger{{/unless}}{{/ifEquals}}">
                                        <span>({{#formatDate date "DD-MM-YYYY" }}{{/formatDate}})</span>
                                        <span class="font-weight-bold">{{username }}</span>
                                        <span>
                                            {{#ifCond role "&&" to}}
                                                - Sent from {{ from }} to {{to}}
                                            {{/ifCond}}
                                            : {{ comment }}
                                        </span>
                                    </div>
                                </div>
                            {{/each}}
                        </div>
                        <form id="fileReviewForm" method="post"
                              action="/file-reviewer/quality-assurance-review/{{ id }}">
                            <!-- STANDARD FILES TABLE -->
                            <div class="table-responsive pt-4">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th style="width: 30%;">Files Required</th>
                                        <th style="width: 10%;" class="text-center">Present</th>
                                        <th style="width: 20%;">Explanation</th>
                                        <th style="width: 10%;" class="text-center">Download</th>
                                        <th style="width: 5%;" class="text-center">Validate</th>
                                        <th style="width: 5%;" class="text-center">
                                            <div class="custom-control custom-checkbox pt-2">
                                                    <input  type="checkbox" name="allValidateCheck"
                                                        class="custom-control-input validateCheck"
                                                        id="allValidateCheck"
                                                        form="fileReviewForm"
                                                    />
                                                    <label class="custom-control-label" for="allValidateCheck"></label>
                                            </div>
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each standardFileQuestions }}
                                        <tr>
                                            <td>{{ internal }}</td>
                                            <td class="text-center">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox"
                                                           disabled
                                                           class="custom-control-input"
                                                           id="standardFilePresent-{{ @key }}"
                                                           form="fileReviewForm"
                                                        {{#if present}}
                                                           checked
                                                        {{/if}}
                                                    />
                                                    <label
                                                            class="custom-control-label"
                                                            for="standardFilePresent-{{ @key }}"
                                                    ></label>
                                                </div>
                                            </td>
                                            <td>
                                                <textarea readonly
                                                          id="standardFileComment-{{ @key }}"
                                                          class="form-control"
                                                          rows="1"
                                                >{{ explanation }}</textarea>
                                            </td>
                                            <td class="text-center">
                                                <button
                                                        type="button"
                                                        class="btn solid royal-blue download-button"
                                                        id="standardFileDownload-{{ @key }}"
                                                        data-review-id="{{../id }}"
                                                        data-file-id="{{ id }}"
                                                        data-file-group="{{ fileGroup }}"
                                                        data-toggle="modal"
                                                        data-target="#downloadFileModal"
                                                        {{#unless present}}disabled{{/unless}}
                                                >
                                                    Download
                                                </button>
                                            </td>
                                            <td class="text-center">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox" name="files[{{ id }}][validated]"
                                                           class="custom-control-input validation-checkbox validateCheck" id="standard-file-validated-{{ @key }}"
                                                           form="fileReviewForm"
                                                           {{#if validated }}
                                                           checked 
                                                           {{/if}}
                                                        
                                                    />
                                                    <label
                                                            class="custom-control-label"
                                                            for="standard-file-validated-{{ @key }}"
                                                    ></label>
                                                </div>
                                            </td>
                                            <td></td>
                                        </tr>
                                    {{/each}}
                                    <td>Company Activity</td>
                                    <td class="text-center">
                                      <div class="custom-control custom-checkbox">
                                        <input type="checkbox" name="companyActivity[present]" disabled
                                        class="custom-control-input" id="company-activity-present"
                                        form="fileReviewForm"
                                        {{#if companyActivityReview.present}}
                                        checked
                                        {{/if}}
                                        />
                                        <label
                                          class="custom-control-label"
                                          for="company-activity-present"
                                        ></label>
                                      </div>
                                    </td>
                                    <td>
                                      <textarea readonly
                                        name="companyActivity[explanation]"
                                        id="company-activity-explanation"
                                        class="form-control"
                                        form="fileReviewForm"
                                        rows="1"
                                      >{{ companyActivityReview.explanation }}</textarea>
                                    </td>
                                  <td>
                                    <select name="companyActivity[select]" id="company-activity" class="custom-select" style="width:260px" disabled>
                                      <option value="none">Select...</option>
                                      <option value="banking-business">Banking Business</option>
                                      <option value="insurance-business">Insurance Business</option>
                                      <option value="fund-management-business">Fund management business</option>
                                      <option value="finance-and-leasing-business">Finance and leasing business</option>
                                      <option value="headquarters-business">Headquarters business</option>
                                      <option value="shipping-business">Shipping business</option>
                                      <option value="holding-business">Holding business</option>
                                      <option value="intellectual-property-business">Intellectual property business</option>
                                      <option value="distribution-and-service-centre-business">Distribution and service centre business</option>
                                      <option value="other">Other</option>
                                    </select>
                                  </td>
                                  <td class="text-center">
                                        <div class="custom-control custom-checkbox">
                                        <input type="checkbox" name="companyActivity[validated]"
                                        class="custom-control-input validateCheck" id="company-activity-validated"
                                        form="fileReviewForm"
                                        {{#if companyActivityReview.validated }}
                                        checked 
                                        {{/if}}
                                        />
                                        <label
                                        class="custom-control-label"
                                        for="company-activity-validated"
                                        ></label>
                                        </div>
                                        </td>
                                    <td></td>
                                    </tbody>
                                </table>
                            </div>
                            <!-- STANDARD FILES END -->
                            <!-- RECORD KEEPING DETAILS -->
                            <hr class="mt-3"/>
                            <div class="mb-3">
                                <h4>Record Keeping Details</h4>
                                {{#if recordKeepingQuestions }}
                                <!-- RECORD HOLDER AND PRIMARY ADDRESS -->
                                <div class="row mt-2">
                                    <div class="col-2 py-1">
                                        <label for="records-holder">Record Holder</label>
                                    </div>
                                    <div class="col-4 py-1">
                                        <input id="records-holder" name="records[recordHolder]" type="text" class="form-control"
                                                value="{{recordKeepingQuestions.recordHolder}}" readonly/>
                                    </div>

                                    <div class="col-2 py-1">
                                        <label for="records-primary-address">Address - 1st Line</label>
                                    </div>
                                    <div class="col-4 py-1">
                                        <input id="records-primary-address" name="records[primaryAddress]" type="text" class="form-control"
                                                value="{{recordKeepingQuestions.primaryAddress}}" readonly/>
                                    </div>
                                </div>
                                <!-- SECONDARY ADDRESS AND COUNTRY -->
                                <div class="row mt-2">
                                    <div class="col-2 py-1">
                                        <label for="records-secondary-address">Address - 2nd Line</label>
                                    </div>
                                    <div class="col-4 py-1">
                                        <input id="records-secondary-address" name="records[secondaryAddress]" type="text" class="form-control"
                                                value="{{recordKeepingQuestions.secondaryAddress}}" readonly/>
                                    </div>

                                    <div class="col-2">
                                        <label for="records[operationCountry]">Country of record keeping</label>
                                    </div>
                                    <div class="col-4">
                                    {{>file-reviewer/shared/select-country selectId="records[operationCountry]" isDisabled="True" value=recordKeepingQuestions.operationCountry}}
                                    </div>
                                </div>
                                <!-- STATE AND CITY -->
                                <div class="row mt-2">
                                    <div class="col-2 py-1">
                                        <label for="records-state">State</label>
                                    </div>
                                    <div class="col-4 py-1">
                                        <input id="records-state" name="records[state]" type="text" class="form-control"
                                                value="{{recordKeepingQuestions.state}}" readonly/>
                                    </div>

                                    <div class="col-2 py-1">
                                        <label for="records-city">City</label>
                                    </div>
                                    <div class="col-4 py-1">
                                        <input id="records-city" name="records[city]" type="text" class="form-control"
                                                value="{{recordKeepingQuestions.city}}" readonly/>
                                    </div>
                                </div>
                                    <!-- POSTAL CODE AND EMAIL -->
                                    <div class="row mt-2">
                                        <div class="col-2 py-1">
                                            <label for="records-postal-code">Postal Code</label>
                                        </div>
                                        <div class="col-4 py-1">
                                            <input id="records-postal-code" name="records[postalCode]" type="text" class="form-control"
                                                value="{{recordKeepingQuestions.postalCode}}" readonly/>
                                        </div>

                                        <div class="col-2 py-1">
                                            <label for="records-email">E-mail</label>
                                        </div>
                                        <div class="col-4 py-1">
                                            <input id="records-email" name="records[email]" type="text" class="form-control"
                                                value="{{recordKeepingQuestions.email}}" readonly/>
                                        </div>
                                    </div>
                                {{/if}}
                            </div>
                            <!-- FREE QUESTION CHECK -->
                            {{#each freeQuestions }}
                                <div id="previousFreeQuestionsWrap">
                                    <div class="row" id="oldFreeQuestion-{{ @key }}">
                                        <div class="col-1 d-flex align-items-center">
                                            <button class="btn btn-danger btn-xs delete-old-fq" data-row="{{ @key }}">
                                                <i class="dripicons-cross"></i>
                                            </button>
                                        </div>
                                        <div class="col-11">
                                            <div class="row">
                                                <div class="col-6 form-group">
                                                    <label for="oldExternalQuestion-{{ @key }}">External Free
                                                        Question</label>
                                                    <textarea
                                                            class="form-control"
                                                            id="oldExternalQuestion-{{ @key }}"
                                                            form="fileReviewForm"
                                                            rows="1"
                                                    >{{ external }}</textarea>
                                                </div>
                                                <div class="col-6 form-group">
                                                    <label for="oldInternalQuestion-{{ @key }}">Internal Trident
                                                        Comment</label>
                                                    <textarea
                                                            class="form-control"
                                                            id="oldInternalQuestion-{{ @key }}"
                                                            form="fileReviewForm"
                                                            rows="1"
                                                    >{{internal}}</textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {{/each}}
                            <div id="newFreeQuestionWrap">
                            </div>
                            <div class="row mt-4">
                                <div class="col-12 d-flex justify-content-end">
                                    <div class="custom-control custom-checkbox">
                                        <button type="button" class="btn btn-xs solid royal-blue py-1 px-2"
                                                id="freeQuestionButton" data-id="{{ id }}" disabled><i
                                                class="dripicons-plus"></i></button>
                                        <span class="font-weight-bold">Add free questions</span>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <!-- CARD BODY END -->
                    <!-- CARD FOOTER NAV -->
                    <div class="row mt-2 justify-content-between">
                        <div class="col-md-2">
                            <a href="/file-reviewer/quality-assurance-list/"
                               class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>
                        </div>
                        <div class="col-md-2 d-flex justify-content-around">
                            <input id="next-button" name="next-button" type="submit" form="fileReviewForm"
                                   class="btn solid royal-blue px-3" value="Continue to Next Page"/>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    {{>file-reviewer/save-file-review-modal}}
    {{>file-reviewer/upload-file-modal}}
    {{>file-reviewer/quality-assurance/send-quality-review-modal}}
    {{>file-reviewer/download-file-modal}}
</main>

<script type="text/javascript" src="/templates/freequestion.precompiled.js"></script>
<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script>
    let newFreeQuestions = 0;
    let activityValue = "{{companyActivity.activity}}";
    // for the question check toggle
    $(".question-check").on('change', function () {
        let group = this.id.replace("Check", "");
        let label = $("label[for='" + this.id + "']");
        // change label
        if ($(this).is(':checked')) {
            label.text('Yes');
        } else {
            label.text('No');
        }
        // expand table
        if (group === "businessOwner") {
            if ($(this).is(':checked')) {
                $('#' + group + 'Info').show(150);
                $('#shareholderCheckWrapper').show(150);
            } else {
                $('#' + group + 'Info').hide(150);
                $('#shareholderCheckWrapper').hide(150);
                $('#shareholderInfo').hide(150);
            }
        } else if (group === "trustee") {
            if ($(this).is(':checked')) {
                $('#no' + group + 'Info').hide(150);
                $('#' + group + 'Info').show(150);
            } else {
                $('#' + group + 'Info').hide(150);
                $('#no' + group + 'Info').show(150);
            }
        } else {
            if ($(this).is(':checked')) {
                $('#' + group + 'Info').show(150);
            } else {
                $('#' + group + 'Info').hide(150);
            }
        }
    })

    $('#freeQuestionButton').on('click', function () {
        let template = Handlebars.templates.freequestion;
        let d = {
            row: newFreeQuestions
        };
        let html = template(d);
        $('#newFreeQuestionWrap').append(html);
        newFreeQuestions++;
    });

    $('.delete-old-fq').on('click', function () {
        let row = $(this).data('row');
        $('#oldFreeQuestion-' + row).remove();
    });
    if (activityValue === '') {
        $('#company-activity').val("none");
    } else {
        $('#company-activity option[value="{{companyActivity.activity}}"]').attr('selected', 'selected');
        $('#company-activity').val("{{companyActivity.activity}}");
    }

    $('#allValidateCheck').on('change', function (){
        const checked = $(this).is(':checked');
        if(checked){
            $('.validateCheck').prop('checked', true);
        }
        else {
            $('.validateCheck').prop('checked', false);
        }
    });
</script>

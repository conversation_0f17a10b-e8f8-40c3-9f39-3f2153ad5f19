<main class="">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card">
                    <form method="POST" id="submitForm">
                        <div class="card-body">
                            <h1>{{title}}</h1>
                            <div id="tableImport">

                            </div>
                            <table class="table w-100 nowrap" id="scroll-horizontal-datatable">
                            </table>
                            <div class="row" id="uploadRow">
                                <div class="col-md-12">
                                    <p>
                                        Maximum of 1 file, XLSX only. File must not
                                        be password
                                        protected.
                                    </p>
                                    <div id="uploadFile" class="dropzone">
                                        <div class="fallback">
                                            <input name="fileUploaded" type="file" multiple />
                                        </div>
                                        <div class="dz-message needsclick">
                                            <i class="h1 text-muted dripicons-cloud-upload"></i>
                                            <h3>Drop files here or click to upload.</h3>
                                            <span class="text-muted">Files will be automatically uploaded</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="row">
                                <div class="mb-3 ml-3">
                                    <a href='/substance/'
                                        class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                                    <button type="button" class="btn btn-primary my-1 ml-3" id="loadDataBtn"
                                        onclick="loadBulkPaymentTable()" style="display: none;">Load Data</button>
                                    <button class="btn btn-primary  my-1 ml-3" type="button"  id="saveDataBtn"
                                            onclick="saveBulkPaymentData()" style="display: none;">
                                        <span class="spinner-border spinner-border-sm"  id="spinnerSave" aria-hidden="true" style="display: none;"></span>
                                        Save Data
                                    </button>

                                    <button type="button" class="btn btn-secondary my-1 ml-3" id="clearDataBtn"
                                            style="display: none;">Start new import</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/substance/importpaymentstable.precompiled.js"></script>
<script type="text/javascript">
    let importedData;
    Dropzone.autoDiscover = false;
    $(function () {
        let field = '';
        const myDropZone = new Dropzone('#uploadFile', {
            url: './import-payments/load-file',
            acceptedFiles: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            uploadMultiple: false,
            autoProcessQueue: true,
            parallelUploads: 1,
            maxFiles: 1,
            maxFilesize: 5,
            paramName: function () {
                return 'fileUploaded';
            },
            init: function () {
                this.on('processing', function () {
                    this.options.url = './import-payments/load-file';
                    $(".dz-message").hide();
                });
                this.on("success", function (file, response) {
                    if (response.error) {
                        toastr["warning"](response.error);
                        if (this.files.length !== 0) {
                        for (let i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                        this.files.length = 0;
                        }
                    } else {
                        $('#loadDataBtn').show();
                        importedData = response;
                    }
                });
                this.on("sending", function (file, xhr, formData) {
                    $("#btnSubmit").prop('disabled', true);
                    if (!formData.has('filetype')) {
                        formData.append("filetype", field);
                    }
                });

                this.on('maxfilesexceeded', function (file) { });

                this.on('resetFiles', function () {
                    if (this.files.length !== 0) {
                        for (let i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                        this.files.length = 0;
                    }
                    $('#maxUpload').text(this.options.maxFiles);
                });
            },
        });
    });

    function loadBulkPaymentTable() {
        $('#loadDataBtn').hide();
        $('#clearDataBtn').show();
        $('#uploadRow').hide();
        let template = Handlebars.templates.importpaymentstable;
        let d = {
            entries: importedData.entries || [],
        };
        let html = template(d);
        $('#tableImport').html(html);
        $('#saveDataBtn').show();
        table = $("#load-import-payments-table").DataTable({ "pageLength": 50,
            "order": [[0, "asc"]],
            scrollX: !0,
            language: {
              paginate: {
                previous: "<i class='mdi mdi-chevron-left'>",
                  next: "<i class='mdi mdi-chevron-right'>"
              }
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
            }
        });
    }

    function saveBulkPaymentData() {
        $('#saveDataBtn').prop('disabled', true);
        $('#clearDataBtn').prop('disabled', true);
        $("#spinnerSave").show();
        $.ajax({
            type: "PUT",
            url: "./import-payments",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({ importedData }),
            success: function (data) {
                if (data.success) {
                    $("#spinnerSave").hide();
                    if (data.errorUpdating?.length > 0){

                        Swal.fire('Error', `Total of ${data.entriesUpdated} records were paid successfully. <b>There was an error updating ${data.errorUpdating.length} records, You can see the errors detail in the table.`, 'error');

                        let template = Handlebars.templates.importpaymentstable;
                        let d = {entries: data.errorUpdating };
                        importedData =d;
                        let html = template(d);
                        $('#tableImport').html(html);
                        $("#clearDataBtn").prop('disabled', false);
                    }else{
                        toastr.success(`Data saved successfully. ${data.entriesUpdated} records were paid.`);
                        window.setTimeout(function () {
                            document.location.reload();
                        }, 2000)
                    }
                } else {
                  console.log("error: ", data);
                    toastr["warning"]('Sorry, there was an error saving the data.');
                    $('#saveDataBtn').prop('disabled', false);
                    $("#clearDataBtn").prop('disabled', false);
                    $("#spinnerSave").hide();
                }
            }
        });
    }

    $("#clearDataBtn").on('click', function (event) {
        $('#clearDataBtn').prop('disabled', true);
        $("#spinnerSave").hide();
        event.preventDefault();
        importedData = [];
        $('#saveDataBtn').prop('disabled', false).hide();
        $('#tableImport').html('');
        var objDZ = Dropzone.forElement("#uploadFile");
        objDZ.emit("resetFiles");
        $('.dz-preview').empty();
        $(".dz-message").show();
        $('#uploadRow').show();
        $('#clearDataBtn').prop('disabled', false).hide();

    });

</script>

<div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h1>My Roles</h1>
                        <form method="POST" id="generalForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="row" style="row-gap: 15px;">
                                        <div class="col-md-4">
                                            <div class="custom-control custom-checkbox">
                                              <input class="custom-control-input" type="checkbox" id="cbClientManagementSuperUser" name="cbClientManagementSuperUser" value="1" {{#if authentication.isClientManagementSuperUser}}checked{{/if}}>                                              
                                              <label class="custom-control-label" for="cbClientManagementSuperUser">Client Management Super User</label>                                              
                                            </div>
                                            <div class="custom-control custom-checkbox">                                              
                                              <input class="custom-control-input" type="checkbox" id="cbCSubstanceManager" name="cbCSubstanceManager" value="1" {{#if authentication.isSubsManagers}}checked{{/if}}>
                                              <label class="custom-control-label" for="cbCSubstanceManager">Substance Manager</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">                                              
                                              <input class="custom-control-input" type="checkbox" id="cbSubstanceSuperUser" name="cbSubstanceSuperUser" value="1" {{#if authentication.isSubsSuperUser}}checked{{/if}}>
                                              <label class="custom-control-label" for="cbSubstanceSuperUser">Substance Super User</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">                                              
                                              <input class="custom-control-input" type="checkbox" id="cbAnnouncementManager" name="cbAnnouncementManager" value="1" {{#if authentication.isAnnouncementManager}}checked{{/if}}>
                                              <label class="custom-control-label" for="cbAnnouncementManager">Announcement Manager</label>
                                          </div>
                                        </div>
                                        <div class="col-md-4">
                                            <input type="SUBMIT" text="Save" class="btn btn-light btn-sm waves-effect">
                                        </div>                                        
                                    </div>
                                </div> 
                            </div>

                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>




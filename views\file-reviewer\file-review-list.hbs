<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h2>{{ title }}</h2>
                    </div>
                    <!-- CONTENT -->
                    <div class="card-body">
                        <!-- AUTOMATICALY ASSIGNED TABLE -->
                        <div id="automatic-assign-table" class="row">
                            <h5 class="pl-1 pb-1">MY ASSIGNATIONS</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 25%;">Company Name</th>
                                        <th scope="col" style="width: 20%;">Company ID</th>
                                        <th scope="col" style="width: 20%;">Assigned at</th>
                                        <th scope="col" style="width: 15%;">Status</th>
                                        <th scope="col" style="width: 20%;"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each assignedAuto}}
                                        <tr>
                                            <td id="auto-company-name-{{ _id }}">{{ companyName }}</td>
                                            <td id="auto-company-code-{{ _id }}">{{ companyCode }}</td>
                                            <td id="auto-date-assigned-{{ _id }}">
                                                {{#formatDate fileReview.dateAssigned "DD-MM-YYYY"}} {{/formatDate}}
                                            </td>
                                            <td id="auto-status-{{ _id }}" class="text-uppercase">{{ status.code }}</td>
                                            <td class="pl-2 py-1 text-right">
                                                {{#ifEquals status.code "ASSIGNED"}}
                                                    <a href="confirm-type/{{ _id }}" class="btn solid royal-blue">
                                                        Open
                                                    </a>
                                                {{/ifEquals}}
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="5" class="text-center font-italic">There are no files
                                                assigned
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                        <!-- AUTOMATIC ASSIGN TABLE END -->
                        <!-- ASSIGNED BY OFFICER TABLE -->
                        <div id="officer-assign-table" class="row mt-3">
                            <h5 class="pl-1 pb-1">MY ASSIGNATIONS - ASSIGNED BY OFFICER</h5>
                            <div class="table-responsive">
                                <table class="table w-100 nowrap table-striped font-size">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 25%;">Company Name</th>
                                        <th scope="col" style="width: 20%;">Company ID</th>
                                        <th scope="col" style="width: 20%;">Assigned at</th>
                                        <th scope="col" style="width: 15%;">Status</th>
                                        <th scope="col" style="width: 20%;"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each assignedByOfficer}}
                                        <tr>
                                            <td>{{ companyName }}</td>
                                            <td>{{ companyCode }}</td>
                                            <td>
                                                {{#formatDate fileReview.dateAssigned "DD-MM-YYYY"}} {{/formatDate}}
                                            </td>
                                            <td class="text-uppercase">
                                                {{ status.code }} - {{ fileReview.assignedBy.role }}
                                            </td>
                                            <td class="pl-2 py-1 text-right">
                                                {{#ifEquals status.code "ASSIGNED"}}
                                                    <a href="confirm-type/{{ _id }}" class="btn solid royal-blue">
                                                        Open
                                                    </a>
                                                {{/ifEquals}}
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="5" class="text-center font-italic">There are no files
                                                assigned
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                        <!-- ASSIGNED BY OFFICER TABLE END -->
                        <!-- ON HOLD TABLE -->
                        <div id="on-hold-table" class="row mt-3">
                            <h5 class="pl-1 pb-1">ON HOLD / SENT TO CLIENT</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 25%;">Company Name</th>
                                        <th scope="col" style="width: 12%;">Company ID</th>
                                        <th scope="col" style="width: 20%;">
                                            Internal Trident Remark
                                        </th>
                                        <th scope="col" style="width: 13%;">Assigned at</th>
                                        <th scope="col" style="width: 13%;">Status</th>
                                        <th scope="col" style="width: 12%;">ID Status <small><i
                                                class="fa fa-info-circle"
                                                data-toggle="tooltip"
                                                data-placement="top"
                                                title="Red = No relations have yet completed the ID verification.
                                               Orange = At least one relation has completed the ID verification.
                                               Green = All relations have completed the ID verification."
                                                aria-hidden="true"></i></small>
                                        </th>
                                        <th scope="col" style="width: 5%;"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each onHold}}
                                        <tr>
                                            <td>{{ companyName }}</td>
                                            <td>{{ companyCode }}</td>
                                            <td>{{ remark }}</td>
                                            <td>
                                                {{#formatDate fileReview.dateAssigned "DD-MM-YYYY"}} {{/formatDate}}
                                            </td>
                                            <td class="text-uppercase">{{ status.code }}</td>
                                            <td class="text-uppercase">
                                                {{#if electronicRequests}}
                                                    {{#ifEquals electronicRequests "COMPLETED"}}
                                                        <i class="fa fa-bell text-success" aria-hidden="true"></i>
                                                    {{/ifEquals}}
                                                    {{#ifEquals electronicRequests "PENDING AT LEAST ONE"}}
                                                        <i class="fa fa-bell text-warning" aria-hidden="true"></i>
                                                    {{/ifEquals}}
                                                    {{#ifEquals electronicRequests "PENDING ALL"}}
                                                        <i class="fa fa-bell text-danger" aria-hidden="true"></i>
                                                    {{/ifEquals}}
                                                {{/if}}
                                            </td>
                                            <td class="pl-2 py-1 text-right">

                                                <a href="confirm-type/{{ _id }}" class="btn solid royal-blue ">
                                                    Open
                                                </a>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="6" class="text-center font-italic">
                                                There are no on hold files
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                        <!-- ON HOLD TABLE END -->
                        <!-- ALL TABLE -->

                        <div id="all-table" class="row mt-3">
                            <h5 class="ml-1 mb-1">ALL</h5>
                            <div class="col-12">
                                <form method="POST" id="submitForm">
                                    <div class="row mt-3">
                                        <label for="filter_filereview">File Review</label>
                                    </div>
                                    <div class="row mb-3">
                                        <input class='form-control col-md-4' type='text' name='filter_filereview'
                                               data-toggle="tooltip" data-placement="left"
                                               title="Enter at least 3 characters"
                                               id='filter_filereview'/>
                                        <input type='SUBMIT' class='btn btn-light btn-sm waves-effect col-md-1 ml-2'
                                               value='Search'/>
                                    </div>
                                </form>
                            </div>
                            <div class="table-responsive">
                                <table id="scroll-horizontal-datatable" class="table table-striped w-100 nowrap">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 25%;">Company Name</th>
                                        <th scope="col" style="width: 15%;">Company ID</th>
                                        <th scope="col" style="width: 20%;">Assigned to</th>
                                        <th scope="col" style="width: 15%;">Assigned at</th>
                                        <th scope="col" style="width: 15%;">Status</th>
                                        <th scope="col" style="width: 10%;"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each allFiles}}
                                        <tr>
                                            <td>{{ companyName }}</td>
                                            <td>{{ companyCode }}</td>
                                            <td id="all-file-reviewer-assigned-{{ _id }}">{{ fileReview.name }}</td>
                                            <td id="all-date-assigned-{{ _id }}">
                                                {{#formatDate fileReview.dateAssigned "DD-MM-YYYY"}} {{/formatDate}}
                                            </td>
                                            <td class="text-uppercase" id="all-status-{{ _id }}">
                                                {{ status.code }}
                                            </td>
                                            <td class="pl-2 py-1 text-right align-right">
                                                {{#ifEquals status.code 'IN PROGRESS'}}
                                                    <button
                                                            type="button"
                                                            id="notifyButton-{{ _id }}"
                                                            class="btn solid royal-blue notifyButton"
                                                            data-toggle="modal"
                                                            data-target="#sendNotificationModal"
                                                            data-companycode="{{ companyCode }}"
                                                            data-companyname="{{ companyName }}"
                                                            data-frname="{{ fileReview.name }}"
                                                            data-fremail="{{ fileReview.username }}"
                                                    >
                                                        Notify
                                                    </button>
                                                {{/ifEquals}}
                                                {{#ifEquals status.code 'ASSIGNED'}}
                                                    <a href="confirm-type/{{ _id }}" class="btn solid royal-blue ">
                                                        Open
                                                    </a>
                                                {{/ifEquals}}
                                                {{#ifEquals status.code 'SUBMITTED'}}
                                                    <button
                                                            type="button"
                                                            id="action-{{ _id }}"
                                                            class="pick-file btn solid royal-blue w-100"
                                                        {{#ifCond ../cantPick '||' (ifNotIn riskGroup ../riskGroup)}}
                                                            disabled {{/ifCond}}>
                                                        Pick
                                                    </button>
                                                {{/ifEquals}}
                                                {{#ifEquals status.code 'NOT STARTED'}}
                                                    <button
                                                            type="button"
                                                            id="action-{{ _id }}"
                                                            class="pick-file btn solid royal-blue "
                                                        {{#if ../cantPick }} hidden {{/if}}>
                                                        Pick
                                                    </button>
                                                {{/ifEquals}}

                                                {{#ifEquals status.code 'UNASSIGNED'}}
                                                    <button
                                                            type="button"
                                                            id="action-{{ _id }}"
                                                            class="pick-file btn solid royal-blue "
                                                        {{#if ../cantPick }} hidden {{/if}}>
                                                        Pick
                                                    </button>
                                                {{/ifEquals}}

                                                {{#unless fileReview.username}}
                                                    {{#ifEquals status.code 'SEND TO FILE REVIEW OFFICER BY QA'}}
                                                        <button
                                                                type="button"
                                                                id="action-{{ _id }}"
                                                                class="pick-file btn solid royal-blue "
                                                            {{#if ../cantPick }} hidden {{/if}}>
                                                            Pick
                                                        </button>
                                                    {{/ifEquals}}

                                                    {{#ifEquals status.code 'SEND TO FILE REVIEW OFFICER BY CO'}}
                                                        <button
                                                                type="button"
                                                                id="action-{{ _id }}"
                                                                class="pick-file btn solid royal-blue "
                                                            {{#if ../cantPick }} hidden {{/if}}>
                                                            Pick
                                                        </button>
                                                    {{/ifEquals}}
                                                {{/unless}}

                                                {{#ifEquals status.code 'ON HOLD'}}
                                                    <a href="confirm-type/{{ _id }}" class="btn solid royal-blue">
                                                        Open
                                                    </a>
                                                {{/ifEquals}}
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="6" class="text-center font-italic">
                                                There are no files
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                        <!-- ALL TABLE END -->
                    </div>
                    <!-- CONTENT END -->
                    <div class="row mt-2">
                        <div class="col-md-2">
                            <a href="/file-reviewer/dashboard"
                               class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{>file-reviewer/send-notification-modal}}
</main>
<script type="text/javascript">
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body',
        boundary: 'window'
    });

    $(document).ready(function () {
        $("#scroll-horizontal-datatable").DataTable({
            scrollX: !0,
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>",
                },
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
    });

    $(".pick-file").on("click", function () {
        const id = this.id.split("-")[1];
        $.ajax({
            url: "/file-reviewer/pick-file-review",
            method: "POST",
            data: {
                id: id,
            },
            success: function (res) {
                location.reload();
            },
            error: function (err) {
                if (err.status === 400) {
                    Swal.fire('Error', 'Already exists a filereview created for this company', 'error');
                } else if (err.status === 500) {
                    Swal.fire('Error', 'There was an internal error', 'error');
                }
            },
        });
    });
</script>

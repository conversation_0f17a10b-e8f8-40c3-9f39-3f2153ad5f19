(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['changelogslist'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <tr>\r\n        <td>\r\n            "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"createdAt") : depth0),"MM/DD/YYYY HH:mm:ss",{"name":"formatDate","hash":{},"fn":container.program(2, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":4,"column":12},"end":{"line":4,"column":76}}})) != null ? stack1 : "")
    + "\r\n        </td>\r\n        <td>\r\n            "
    + alias4(((helper = (helper = lookupProperty(helpers,"modifiedBy") || (depth0 != null ? lookupProperty(depth0,"modifiedBy") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"modifiedBy","hash":{},"data":data,"loc":{"start":{"line":7,"column":12},"end":{"line":7,"column":26}}}) : helper)))
    + "\r\n        </td>\r\n        <td>\r\n            "
    + alias4(((helper = (helper = lookupProperty(helpers,"modifiedValuesLength") || (depth0 != null ? lookupProperty(depth0,"modifiedValuesLength") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"modifiedValuesLength","hash":{},"data":data,"loc":{"start":{"line":10,"column":12},"end":{"line":10,"column":36}}}) : helper)))
    + "\r\n        </td>\r\n        <td>\r\n            <button id=\"btn-"
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":13,"column":28},"end":{"line":13,"column":35}}}) : helper)))
    + "\" type=\"button\" class=\"btn btn-sm btn-secondary text-white  text-center displayCompanyLogDetails\"\r\n                 onclick=\"getLogDetails('"
    + alias4(container.lambda(((stack1 = (depths[1] != null ? lookupProperty(depths[1],"data") : depths[1])) != null ? lookupProperty(stack1,"documentId") : stack1), depth0))
    + "','"
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":14,"column":66},"end":{"line":14,"column":73}}}) : helper)))
    + "')\">\r\n                <i class=\"fa fa-search mr-2\" aria-hidden=\"true\"></i> View log\r\n            </button>\r\n        </td>\r\n    </tr>\r\n";
},"2":function(container,depth0,helpers,partials,data) {
    return " ";
},"4":function(container,depth0,helpers,partials,data) {
    return "    <tr>\r\n        <td colspan=\"4\">\r\n            Logs not found\r\n        </td>\r\n        <td></td>\r\n        <td></td>\r\n        <td></td>\r\n    </tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"changeLogs") : stack1),{"name":"each","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.program(4, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":28,"column":9}}})) != null ? stack1 : "");
},"useData":true,"useDepths":true});
})();
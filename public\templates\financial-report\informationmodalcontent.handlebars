
<div id="financialPeriods" class="mb-2">
    <div class="row">
        <div class="col-12">
            <h4><b>Financial Reports </b></h4>
        </div>
    </div>
    {{#if data.financialReports}}
    <div class="row ml-2">
        <div class="col-12">
            {{#each data.financialReports}}
                <span>
                  <b>Start Date - End Date:</b> <br>
                    {{#formatDate financialPeriod.start "MM/DD/YYYY"}} {{/formatDate }} - {{#formatDate financialPeriod.end "MM/DD/YYYY"}} {{/formatDate }} <br>
                    <b>submitted At:</b> {{#formatDate submittedAt "MM/DD/YYYY"}} {{/formatDate }} | Status: <b>{{status}}</b>
                  
                </span>
                <br>
            {{/each}}
        </div>
    </div>
    {{/if}}
</div>
<br>
<hr>
<div id="reportHistory">
    <div class="row mb-2">
        <div class="col-12">
            <h4><b>Report/Unreport History</b></h4>
        </div>
    </div>
    {{#if data.companyData.accountingRecordsModule.reportedHistory}}
    <div class="row ml-2">
        <div class="col-12">
            {{#each data.companyData.accountingRecordsModule.reportedHistory}}
                <span>
                  <b></b> <br>
                    <b>Reported At:</b>{{#formatDate reportedAt "MM/DD/YYYY"}} {{/formatDate }} | {{reportedBy}} <br>
                    {{#ifEquals isReported true}} Reported {{else}} Unreported{{/ifEquals }}
                </span>
                <br>
            {{/each}}
        </div>
    </div> 
    {{/if}}



</div>
<br>
<hr>
<div id="reminders">
    <div class="row mb-2">
        <div class="col-12">
            <h4><b>Deadline Reminders</b></h4>
        </div>
    </div>
    {{#if data.companyData.accountingRecordsModule.deadlineReminders}}
    <div class="row ml-2">
        <div class="col-12">
            {{#each data.companyData.accountingRecordsModule.deadlineReminders}}
                <span>
                  <b></b> <br>
                    <b>Reminder Date:</b>{{#formatDate reminderDate "MM/DD/YYYY"}} {{/formatDate }} | <b>Reminder Deadline:</b>{{#formatDate reminderDeadline "MM/DD/YYYY"}} {{/formatDate }}  | <b>Day: </b>{{reportedBy}} <br>
                    <b>description:</b> {{description}}
                </span>
                <br>
            {{/each}}
        </div>
    </div> 
    {{/if}}



</div>
<br>
<hr>
<div id="Emails">
    <div class="row mb-2">
        <div class="col-12">
            <h4><b>MCC Emails</b></h4>
        </div>
    </div>
    {{#if data.emails}}
    <div class="row ml-2">
        <div class="col-12">
            {{#each data.emails}}
                <span>
                  <b></b> <br>
                    <b>Subject:</b>{{subject}} | <b>Email Subject:</b>{{emailSubject}} | <b>Status: </b>{{status}} <br>
                    <b>Content:</b> {{content}}
                </span>
                <br>
            {{/each}}
        </div>
    </div> 
    {{/if}}



</div>



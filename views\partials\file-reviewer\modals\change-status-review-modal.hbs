{{!-- CONFIRM CLIENT MODAL --}}
<div class="modal fade" id="changeStatusModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <div class="row">
                    <div class="col-12" id="messageModal">

                    </div>
                </div>
                <br>
                <div class="row">
                    <div class="col-12">
                        <label for="modalInputComment"></label>
                        <textarea id="modalInputComment" rows="3" class="w-100 form-control"
                                  placeholder="Add a comment"></textarea>
                    </div>
                </div>

            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="button" class="btn solid royal-blue" id="sendButton">
                    Confirm
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    let statusReviewId;
    let statusToUpdate;
    let statusOfficer;

    $('#changeStatusModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        statusToUpdate = button.data('status');
        statusReviewId = button.data('id');
        statusOfficer = button.data('officer');

        const modalMessage = {
            "sendOfficer": 'You are about to submit the File Review back to a <b>File Reviewer Officer </b>.',
            "sendClient": 'You are about to submit the File Review back to <b>Client.</b> <br> ¿Are you sure?',
            "sendCompliance": 'You are about to submit the File Review to a <b>Compliance Officer</b>.',
            "sendQuality": 'You are about to submit the File Review to a <b>Quality Assurance Officer</b>.'
        };

        $('#messageModal').html(modalMessage[statusToUpdate]);

    });


    $("#sendButton").on('click', function (e) {
        e.preventDefault();
        $(this).prop('disabled', true);
        const submitComment = $('#modalInputComment').val();
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/reviews/' + statusReviewId + '/update-status',
            data: {
                status: statusToUpdate,
                officer: statusOfficer,
                comment: submitComment
            },
            success: () => {
                $(this).prop('disabled', false);
                Swal.fire('Success', 'The review has been submitted successfully.', 'success').then(() => {
                    if (statusOfficer === "fileReviewer"){
                        location.href = '/file-reviewer/file-review-list';
                    }
                    else if(statusOfficer === "qualityAssurance"){
                        location.href = '/file-reviewer/quality-assurance-list';
                    }
                    else if (statusOfficer === "compliance"){
                        location.href = '/file-reviewer/compliances';
                    }
                });
            },
            error: (err) => {
                $(this).prop('disabled', false);
                $('#confirmClientModal').modal('hide');
                Swal.fire('Error', 'There was an error submitting your review.', 'error');
            },
        });
    });
</script>

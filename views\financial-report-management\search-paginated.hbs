<main class="">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h1>{{title}}</h1>

                        <!-- FILTERS -->
                        <form method='GET' id="accountingSearchForm" action="?page={{result.pageNumber}}&pageSize={{result.pageSize}}">
                            <div class="row gx-1">
                                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                                    <label for="filterIncorporationNumber">Incorporation Number</label>
                                    <input class='form-control filter' type='text' name='incorporationNumber' id='filterIncorporationNumber'
                                        value="{{filters.incorporationNumber}}" />
                                </div>

                                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                                    <label for="filterCompanyName">Entity Name</label>
                                    <input class='form-control filter' type='text' name='companyName' id='filterCompanyName'
                                        value="{{filters.companyName}}" />
                                </div>

                                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                                    <label for="filterMasterclient">Master Client Code</label>
                                    <input class='form-control filter' type='text' name='masterclientcode' id='filterMasterclient'
                                        value="{{filters.masterclientcode}}" />
                                </div>

                                <div class="col-12  col-md-6 col-lg-4 col-xl-2 px-2">
                                    <label for="filterReferralOffice">Referral Office</label>
                                    <input class='form-control filter' type='text' name='referralOffice' id='filterReferralOffice'
                                        value="{{filters.referralOffice}}" />
                                </div>

                                <div class="col-12 col-md-6 col-lg-2">
                                    <label for="filterSubmittedDateRange">
                                        Submitted date
                                        <i class="fa fa-info-circle fa-lg text-info " data-toggle="tooltip" data-placement="top"
                                            title="Search within a submission date range"></i>
                                    </label>
                                    <input class='form-control filter' type='text' name='submittedDateRange' id='filterSubmittedDateRange'
                                        autocomplete="off" {{#if filters.submittedDateRange}}
                                        value="{{filters.submittedDateRange.start}} - {{filters.submittedDateRange.end}}" {{else}} value="" {{/if}} />
                                </div>
                                
                                <div class="col-12 col-md-6 col-lg-2">
                                    <label for="filterIncorporatedDateRange">
                                        Incorporation date
                                        <i class="fa fa-info-circle fa-lg text-info " data-toggle="tooltip" data-placement="top"
                                            title="Search within a company incorporation date range"></i>
                                
                                    </label>
                                    <input class='form-control filter' type='text' name='incorporatedDateRange' id='filterIncorporatedDateRange'
                                        autocomplete="off" {{#if filters.incorporatedDateRange}}
                                        value="{{filters.incorporatedDateRange.start}} - {{filters.incorporatedDateRange.end}}" {{else}} value=""
                                        {{/if}} />
                                </div>
                                
                                
                                <div class="col-12 col-md-6 col-lg-2">
                                    <label for="filterFinancialPeriodEndDateRange">
                                        Financial period end
                                        <i class="fa fa-info-circle fa-lg text-info " data-toggle="tooltip" data-placement="top"
                                            title="Search within a end of financial period date range"></i>
                                    </label>
                                    <input class='form-control filter' type='text' name='financialPeriodEndDateRange' autocomplete="off"
                                        id='filterFinancialPeriodEndDateRange' {{#if filters.financialPeriodEndDateRange}}
                                        value="{{filters.financialPeriodEndDateRange.start}} - {{filters.financialPeriodEndDateRange.end}}" {{else}}
                                        value="" {{/if}} />
                                </div>

    
                            </div>

                            <div class="row my-1">


                                <div class="col-12 col-md-6 col-lg-2 hidden">
                                    <label for="filterPaymentDateRange">
                                        Date paid
                                        <i class="fa fa-info-circle fa-lg text-info " data-toggle="tooltip" data-placement="top"
                                            title="Search within a payment date range"></i>
                                    </label>
                                    <input class='form-control filter' type='text' name='paymentDateRange' autocomplete="off"
                                        id='filterPaymentDateRange'
                                        {{#if filters.paymentDateRange}}
                                        value="{{filters.paymentDateRange.start}} - {{filters.paymentDateRange.end}}"
                                        {{else}}
                                        value=""
                                        {{/if}}
                                        />
                                </div>

                                <div class="col-12 col-md-6 col-lg-4 col-xl-2  p-0">

                                    <div class="col-12  px-2">
                                        <label for="filterAlreadySubmitted">Show submitted?</label> <br>
                                        <select id="filterAlreadySubmitted" class="form-control filter w-100" name="alreadySubmitted">
                                            <option value="" 
                                                {{#ifEquals filters.alreadySubmitted ''}} selected {{/ifEquals}}
                                            >
                                                ALL
                                            </option>
                                            <option value="YES" {{#ifEquals filters.alreadySubmitted 'YES'}} selected {{/ifEquals}}>YES</option>
                                            <option value="NO" {{#ifEquals filters.alreadySubmitted  'NO'}} selected {{/ifEquals}}>NO</option>
                                        </select>
                                    </div>

                                    <div class="col-12  px-2">
                                        <label for="filterAccountingModule">Accounting Module</label> <br>
                                        <select id="filterAccountingModule" class="form-control filter w-100" name="moduleState">
                                            <option value="" {{#ifEquals filters.moduleState 'ALL' }} selected {{/ifEquals}}>
                                            ALL
                                            </option>
                                            <option value="ACTIVE" {{#ifEquals filters.moduleState 'ACTIVE' }} selected {{/ifEquals}}>ACTIVE</option>
                                            <option value="INACTIVE" {{#ifEquals filters.moduleState 'INACTIVE' }} selected {{/ifEquals}}>INACTIVE
                                            </option>
                                        </select>
                                    </div>
                                </div>



                                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                                    <label for="filterAllowReopen">Allow re-open?</label> <br>
                                    <select id="filterAllowReopen" class="form-control filter w-100" name="allowReopen">
                                        <option value="" {{#ifEquals filters.allowReopen '' }} selected {{/ifEquals}}>ALL
                                        </option>
                                        <option value="YES" {{#ifEquals filters.allowReopen 'YES' }} selected {{/ifEquals}}>YES</option>
                                        <option value="NO" {{#ifEquals filters.allowReopen 'NO' }} selected {{/ifEquals}}>NO</option>
                                    </select>
                                </div>

                              

                                <div class="col-12 col-md-6 px-2">
                                    <label for="filterStatus">Status</label> <br>
                                    <div class="d-lg-flex  justify-content-between">
                                        <div class="checkbox-list mr-2">
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusSAVED" class="custom-control-input filter" name="status[]" value="SAVED"
                                                    {{#ifContains 'SAVED' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusSAVED" class="custom-control-label">SAVED</label>
                                            </div>
                                        
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusIN_PROGRESS" class="custom-control-input filter" name="status[]"
                                                    value="IN PROGRESS" {{#ifContains 'IN PROGRESS' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusIN_PROGRESS" class="custom-control-label">IN PROGRESS</label>
                                            </div>
                                        
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusCONFIRMED" class="custom-control-input filter" name="status[]"
                                                    value="CONFIRMED" {{#ifContains 'CONFIRMED' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusCONFIRMED" class="custom-control-label">CONFIRMED</label>
                                            </div>
                                        
                                            <div class="custom-control custom-checkbox hidden">
                                                <input type="checkbox" id="filterStatusPAID" class="custom-control-input filter" name="status[]" value="PAID"
                                                    {{#ifContains 'PAID' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusPAID" class="custom-control-label">PAID</label>
                                            </div>
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusCOMPLETED" class="custom-control-input filter" name="status[]" value="COMPLETED"
                                                    {{#ifContains 'COMPLETED' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusCOMPLETED" class="custom-control-label">COMPLETED</label>
                                            </div>
                                        </div>
                                        
                                        <div class="checkbox-list mr-2">
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusUNDER_REVIEW" class="custom-control-input filter" name="status[]" value="UNDER REVIEW"
                                                    {{#ifContains 'UNDER REVIEW' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusUNDER_REVIEW" class="custom-control-label">UNDER REVIEW</label>
                                            </div>

                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusHELP_REQUEST" class="custom-control-input filter" name="status[]"
                                                    value="HELP REQUEST" {{#ifContains 'HELP REQUEST' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusHELP_REQUEST" class="custom-control-label">HELP REQUEST</label>
                                            </div>
                                        
                                        
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusHELP_IN_PROGRESS" class="custom-control-input filter" name="status[]"
                                                    value="HELP IN PROGRESS" {{#ifContains 'HELP IN PROGRESS' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusHELP_IN_PROGRESS" class="custom-control-label">HELP IN PROGRESS</label>
                                            </div>
                                        
                                                                    
                                        
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusHELP_COMPLETED" class="custom-control-input filter" name="status[]"
                                                    value="HELP COMPLETED" {{#ifContains 'HELP COMPLETED' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusHELP_COMPLETED" class="custom-control-label">HELP COMPLETED</label>
                                            </div>

                                            
                                        
                                        </div>

                                        <div class="checkbox-list">
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusREOPEN" class="custom-control-input filter" name="status[]" value="RE-OPEN"
                                                    {{#ifContains 'RE-OPEN' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusREOPEN" class="custom-control-label">RE-OPEN</label>
                                            </div>

                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusINFORMATION_REQUEST" class="custom-control-input filter" name="status[]"
                                                    value="INFORMATION REQUEST" {{#ifContains 'INFORMATION REQUEST' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusINFORMATION_REQUEST" class="custom-control-label">INFORMATION REQUEST</label>
                                            </div>
                                            
                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusIN_PENALTY" class="custom-control-input filter" name="status[]" value="IN PENALTY"
                                                    {{#ifContains 'IN PENALTY' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusIN_PENALTY" class="custom-control-label">IN PENALTY</label>
                                            </div>

                                            <div class="custom-control custom-checkbox ">
                                                <input type="checkbox" id="filterStatusDELETED" class="custom-control-input filter" name="status[]" value="DELETED"
                                                    {{#ifContains 'DELETED' filters.status }} checked {{/ifContains}}>
                                                <label for="filterStatusDELETED" class="custom-control-label">DELETED</label>
                                            </div>

                                            
                                        </div>
                                    </div>
    
                                </div>
                            </div>

                            <div class="row my-1">
                                <div class="col-12  px-2">
                                    <label for="filterServiceType">Service Type</label> <br>
                                
                                    <div class="checkbox-list">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" id="filterServiceTypeComplete" class="custom-control-input filter"
                                                name="serviceType[]" value="self-service-complete" {{#ifContains 'self-service-complete'
                                                filters.serviceType }} checked {{/ifContains}}>
                                            <label for="filterServiceTypeComplete" class="custom-control-label">Complete the Company's Annual Return in
                                                the
                                                prescribed format</label>
                                        </div>
                                
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" id="filterServiceTypePrepare" class="custom-control-input filter"
                                                name="serviceType[]" value="self-service-prepare" {{#ifContains 'self-service-prepare'
                                                filters.serviceType }} checked {{/ifContains}}>
                                            <label for="filterServiceTypePrepare" class="custom-control-label">Prepare the Company's Annual Return using
                                                the
                                                Trident Accounting Portal</label>
                                        </div>
                                
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" id="filterServiceTypeCompleteTrident" class="custom-control-input filter"
                                                name="serviceType[]" value="trident-service-complete" {{#ifContains 'trident-service-complete'
                                                filters.serviceType }} checked {{/ifContains}}>
                                            <label for="filterServiceTypeCompleteTrident" class="custom-control-label">Have TridentTrust complete the
                                                company's annual return in the prescribed format</label>
                                        </div>
                                
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" id="filterServiceTypeDrop" class="custom-control-input filter" name="serviceType[]"
                                                value="trident-service-drop" {{#ifContains 'trident-service-drop' filters.serviceType }} checked
                                                {{/ifContains}}>
                                            <label for="filterServiceTypeDrop" class="custom-control-label">Drop Accounting Records for
                                                assistance</label>
                                        </div>

                                        <div class="custom-control custom-checkbox mb-1">
                                            <input type="checkbox" class="custom-control-input filter" name="isExemptCompany" id="isExemptCompany" value="YES"
                                                {{#if filters.isExemptCompany }} checked {{/if}} />
                                            <label class="custom-control-label" for="isExemptCompany">Show only exempt company?</label>
                                        </div>
                                     </div>

                                </div>
                            </div>
                            
                            <div class="row mt-2">
                                <div class="col-12">
                                    <button type="submit" form="accountingSearchForm" class='btn btn-primary waves-effect mr-2'>Search </button>
                                    <button type="button" form="accountingSearchForm" id="clearFormBtn" class='btn btn-secondary waves-effect '>Reset filters </button>
                                    <div class="btn-group" role="group" >
                                        <button type="button" id="selectAllTableBtn" class='btn btn-secondary waves-effect '>Select All On Page</button>
                                        <button type="button" id="unselectAllTableBtn" class='btn btn-secondary waves-effect ' title='unselect all'>
                                            <i class="far fa-square"></i>
                                        </button>
                                    </div>

                                </div>
                            </div>

                        </form>
                        <br /><br />

                        <!-- TABLE RESULTS -->
                        {{>shared/table-pagination pagination=result formName="accountingSearchForm" tableId="financialReportsTable" searching="true"}}
                        <table id="financialReportsTable" class="table w-100 nowrap">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Email</th>
                                    <th>Entity Name</th>
                                    <th>Registration Code</th>
                                    <th>Master Client Code</th>
                                    <th>Incorporation Number</th>
                                    <th>Incorporation Date</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Submitted</th>
                                    <th>FP Start Date</th>
                                    <th>FP End Date</th>
                                    <th>Referral Office</th>
                                    <th>Exempt Company</th>
                                    <th>Accounting Module</th>
                                    {{#if permissions.resetToSavedActions}}
                                    <th>Reset Submission</th>
                                    {{/if}}
                                    {{#if permissions.changePeriod}}
                                    <th>Change Financial Period</th>
                                    {{/if}}
                                    {{#if permissions.requestInfoActions}}
                                    <th>Request Information</th>
                                    {{/if}}
                                    {{#if permissions.downloadSummaryPDF}}
                                    <th>Summary</th>
                                    {{/if}}

                                    {{#if permissions.requestInfoActions}}
                                    <th>View Information</th>
                                    {{/if}}

                                    {{#if permissions.downloadAttachmentsActions}}
                                    <th>Download support attachments</th>
                                    {{/if}}

                                </tr>
                            </thead>
                            <tbody>
                                {{#each result.data}}
                                <tr data-allow-select="{{enableResetToSaved}}">
                                    <td>{{_id}}</td>
                                    <td>{{email}}</td>
                                    <td>{{companyName}}</td>
                                    <td>{{code}}</td>
                                    <td>{{masterClientCode}}</td>
                                    <td>{{incorporationCode}}</td>
                                    <td>
                                        {{formatDate incorporationDate ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td>{{status}}</td>
                                    <td>
                                        {{formatDate createdAt ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td >
                                        {{formatDate submittedAt ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td>
                                        {{formatDate financialPeriodStart ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td >
                                        {{formatDate financialPeriodEnd ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td>{{referralOffice}}</td>
                                    <td>{{#if isExemptCompany}}Yes{{else}}No{{/if}}</td>
                                    <td>{{#if accountingModule}}Yes{{else}}No{{/if}}</td>
                                    {{#if ../permissions.resetToSavedActions}}
                                        <td>
                                            {{#if enableResetToSaved}}
                                                <button type="button" class="btn btn-primary waves-effect waves-light border-white rounded"
                                                    data-toggle="modal"
                                                    data-target="#rtsModal"
                                                    data-report-id="{{_id}}"
                                                    data-start-date="{{formatDate financialPeriodStart ../STANDARD_DATE_FORMAT}}"
                                                    data-end-date="{{formatDate financialPeriodEnd ../STANDARD_DATE_FORMAT}}"
                                                >
                                                Edit
                                                </button>
                                            {{/if}}
                                        </td>
                                    {{/if}}
                                    {{#if ../permissions.changePeriod}}
                                        <td>
                                            {{#if enableChangePeriod}}
                                            <button type="button" class="btn btn-primary waves-effect waves-light border-white rounded"
                                                data-toggle="modal"
                                                data-target="#financialPeriodChangeModal"
                                                data-report-id="{{_id}}"
                                                data-start-date="{{formatDate financialPeriodStart ../STANDARD_DATE_FORMAT}}"
                                                data-end-date="{{formatDate financialPeriodEnd ../STANDARD_DATE_FORMAT}}"
                                                data-incorporation-date="{{formatDate incorporationDate ../STANDARD_DATE_FORMAT}}"
                                                data-first-report="{{isFirstReport}}"
                                            >
                                            Change
                                            </button>
                                            {{/if}}
                                            
                                        </td>
                                    {{/if}}

                                    {{#if ../permissions.requestInfoActions}}
                                    <td>
                                        {{#if enableInfoRequest}}
                                            <button class="btn btn-primary waves-effect waves-light border-white rounded" 
                                                data-toggle="modal" 
                                                data-target="#financialReportRfiModal"
                                                data-report-id="{{_id}}">
                                                Request Information
                                            </button>
                                        {{/if}}
                                    
                                        {{#if enableRequestCancel}}
                                            <button class="btn btn-danger px-3 waves-effect waves-light border-white rounded cancelRfiBtn" 
                                                data-report-id="{{_id}}">
                                                Cancel RFI
                                            </button>
                                        {{/if}}
                                    </td>
                                    {{/if}}
                                    
                                    {{#if ../permissions.downloadSummaryPDF}}
                                    <td>
                                        {{#if enablePdfDownload}}
                                            <a href="/financial-report-management/{{_id}}/report.pdf" target="_blank"
                                               class="btn btn-primary  waves-effect waves-light  text-white border-white rounded">Download</a>
                                        {{/if}}
                                    </td>
                                    {{/if}}

                                    {{#if ../permissions.requestInfoActions}}
                                    <td>                                    
                                        {{#if showInfoDetails}}
                                            <button class="btn btn-primary waves-effect waves-light border-white rounded"
                                                    data-toggle="modal"
                                                    data-target="#showInformationModal"
                                                    data-report-id="{{_id}}"

                                            > Show Info
                                            </button>
                                        {{/if}}
                                    </td>
                                    {{/if}}


                                    {{#if ../permissions.downloadAttachmentsActions}}
                                        <td style="vertical-align: middle;">
                                    
                                            {{#if showAttachmentsDownload}}
                                                <a href="/financial-report-management/{{_id}}/download-files" target="_blank"
                                                    class="btn btn-primary waves-effect waves-light  text-white border-white rounded">Download</a>
                                            {{/if}}
                                            
                                        </td>
                                    {{/if}}
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                       
                        
                        <br>
                        <!--FOOTER BUTTONS-->
                        <div class="row">
                            <div class="col-12 mb-3 d-flex flex-row">
                                <div class="mr-2 ">
                                    <a href='/financial-report-management/' class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                                </div>

                                {{#if permissions.exportFiles}}
                                <div class=" mr-2">
                                    <button class="btn btn-primary width-lg waves-effect waves-light" id="btn-freport-export-xls">Export xls</button>
                                </div>
                                {{/if}}

                                {{#if permissions.resetToSavedActions}}
                                <div data-toggle="tooltip" data-placement="top"  title="Re-open all selected table entries">
                                    <button id="resetToSaveMultipleBtn" class="btn btn-primary width-lg "  style="display: none;"
                                        data-toggle="modal" data-target="#rtsBulkModal"
                                    >
                                        Reset to saved
                                    </button>
                                </div>
                                {{/if}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{>financial-report/change-financial-period-modal}}
    {{>financial-report/reset-to-saved-modal}}
    {{>financial-report/reset-to-saved-bulk-modal}}
    {{>financial-report/show-information-modal}}
    {{>financial-report/request-information-modal}}
</main>
<script type='text/javascript' src='/javascripts/form-advanced.init.js'></script>

<script type="text/javascript">
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body',
        boundary: 'window'
    });
    let selectedId;
    let selectedCompany;
    let $financialReportsTable;

    $(document).ready(function () {

        $("#filterSubmittedDateRange, #filterIncorporatedDateRange, #filterFinancialPeriodEndDateRange, #filterPaymentDateRange").flatpickr({
            mode: "range",
            dateFormat: "m/d/Y",
            autoclose: true,
            monthSelectorType: "dropdown",
            allowInput: true,
            locale: {
                rangeSeparator: ' - '
            },

        })

        $financialReportsTable = $("#financialReportsTable").DataTable({
            dom: "lrtip",
            columnDefs: [{ "visible": false, "targets": [0] }],
            scrollX: !0,
            select: { style: "multi" },
            paging:false,
            info: false,
            sort: false
        });
        


        $financialReportsTable.on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                const selectedRowData = $financialReportsTable.row(indexes).node();;
                const allowSelectRow = $(selectedRowData).data('allow-select')

                if(allowSelectRow !== true){
                    $(selectedRowData).removeClass('selected');
                    e.preventDefault();
                    return false;
                    
                }

                if ($financialReportsTable.rows('.selected').data().length) {
                    $("#resetToSaveMultipleBtn").show();
                } else {
                    $('#resetToSaveMultipleBtn').hide();
                }
            }
        });
        $financialReportsTable.on('deselect', function (e, dt, type, indexes) {
            if (type === 'row') {
                if ($financialReportsTable.rows('.selected').data().length) {
                    $("#resetToSaveMultipleBtn").show(); 
                } else {
                    $('#resetToSaveMultipleBtn').hide();
                }
            }
        });


        $("#selectAllTableBtn").on('click', function () {
            let showButton = false; 
            $financialReportsTable.rows({ page: 'current' }).nodes().each((node) =>{
                if($(node).data('allow-select') === true){
                    showButton = true;
                    $(node).addClass('selected');
                }
            })

            if(showButton){
                $("#resetToSaveMultipleBtn").show();
            }
            
        })

        $("#unselectAllTableBtn").on('click', function () {
            $financialReportsTable.rows({ page: 'current' }).deselect();
            $("#resetToSaveMultipleBtn").hide();
        })

    });

    $("#isExemptCompany").on('change', function(e){
        const val = $("input[name='isExemptCompany']:checked").val();

        if(val === "YES"){
            $("input[name='serviceType[]']").prop('disabled', true);
            $("input[name='serviceType[]']").prop('checked', false);
        }else{
             $("input[name='serviceType[]']").prop('disabled', false);
        }
    })

    $("#clearFormBtn").on('click', () => {
        $("#accountingSearchForm")[0].reset();
        $("#accountingSearchForm input[type=text]").val('');
        $('#accountingSearchForm input[type=checkbox]').prop('checked', false);
        $("#accountingSearchForm select").val('');
        $("#filterStatusSAVED").prop('checked', true);
        $("#filterStatusIN_PROGRESS").prop('checked', true);
        $("#filterStatusCONFIRMED").prop('checked', true);
        $("#filterStatusPAID").prop('checked', true);
        $("#filterStatusREOPEN").prop('checked', true);
        $("#filterStatusHELP_REQUEST").prop('checked', true);
        $("#filterStatusHELP_IN_PROGRESS").prop('checked', true);
        $("#filterStatusHELP_REQUEST").prop('checked', true);
        $("#filterStatusHELP_COMPLETED").prop('checked', true);
        $("#filterStatusINFORMATION_REQUEST").prop('checked', true);
        $("#filterStatusUNDER_REVIEW").prop('checked', true);
        $("#filterStatusIN_PENALTY").prop('checked', true);
        $("#filterStatusCOMPLETED").prop('checked', true);
    })


    $(".cancelRfiBtn").on('click',  async function (e) {
        $(".cancelRfiBtn").prop('disabled', true);
        e.preventDefault();

        const id = $(this).data('report-id');

        const result = await Swal.fire({
            title: 'Cancel Request',
            text: "This will cancel the current request information, are you sure?",
            input: 'textarea',
            inputLabel: 'Message',
            inputPlaceholder: 'Write the reason to cancel...',
            inputAttributes: {
                'aria-label': 'Write the reason to cancel...',
                'maxlength': 2000
            },
            backdrop: true,
            showCancelButton: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#6658DD",
            confirmButtonText: 'Yes',
            reverseButtons: true,
            showLoaderOnConfirm: true,
            inputValidator: (reason) => {
                if (!reason || reason === '') {
                    return 'Please provide the reason for canceling the current information request'
                }
            },
            allowOutsideClick: () => !Swal.isLoading(),
            preConfirm: async (value) =>  {
                try {
                    const response = await $.ajax({
                        type: 'PUT',
                        url: `/financial-report-management/${id}/cancel-request-information`,
                        dataType: 'json',
                        data: { reason: value }
                    });

                    if (response.status === 200) {
                        return response;
                    } else {
                        Swal.fire('Error', response.error ? response.error : 'There was an error cancelling the current request information', 'error');
                        return false;
                    }
                } catch (error) {
                    Swal.fire('Error', 'There was an error cancelling the current request information', 'error');
                    return false;
                }
            },
        })

        if (result?.value?.status === 200) {
            Swal.fire('Success', result.value?.message, 'success').then(() => {
                location.reload();
            });
        } else {
            $(".cancelRfiBtn").prop("disabled", false);
        }
    })



    $('#btn-freport-export-xls').click(function () {
        const page = Number($("#pageNumberInput").data('page-number'));
        const size = $('#pageSizeControl').val();
        let dataToSend = $('#accountingSearchForm').serialize();

        dataToSend = dataToSend.replace(/page=[^&]*/, 'page=' + encodeURIComponent(page));
        dataToSend = dataToSend.replace(/pageSize=[^&]*/, 'pageSize=' + encodeURIComponent(size));
  
        
        $.ajax({
            url: "./export-search-xls",
            data: dataToSend,
            method: 'POST',
            xhrFields: {
                responseType: 'blob' // to avoid binary data being mangled on charset conversion
            },
            success: function (blob, status, xhr) {
                // check for a filename
                var filename = "";
                var disposition = xhr.getResponseHeader('Content-Disposition');
                if (disposition && disposition.indexOf('attachment') !== -1) {
                    var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    var matches = filenameRegex.exec(disposition);
                    if (matches != null && matches[1]) filename = matches[1].replace(/['"]/g, '');
                }

                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(blob, filename);
                } else {
                    var URL = window.URL || window.webkitURL;
                    var downloadUrl = URL.createObjectURL(blob);

                    if (filename) {
                        var a = document.createElement("a");
                        if (typeof a.download === 'undefined') {
                            window.location.href = downloadUrl;
                        } else {
                            a.href = downloadUrl;
                            a.download = filename;
                            document.body.appendChild(a);
                            a.click();
                        }
                    } else {
                        window.location.href = downloadUrl;
                    }

                    setTimeout(function () { URL.revokeObjectURL(downloadUrl); }, 100); // cleanup
                }
            }
        })
        
    });



</script>

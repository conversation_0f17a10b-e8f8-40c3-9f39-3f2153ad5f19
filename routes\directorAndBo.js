const express = require('express');
const router = express.Router();
const directorAndBoController =  require('../controllers/directorAndBoController');

router.get('/', ensureAuthenticatedDirectorManager, directorAndBoController.getDashboard);
router.get('/search', ensureAuthenticatedDirectorProductionOffices, directorAndBoController.getSearch);
router.post("/search", ensureAuthenticatedDirectorProductionOffices,  directorAndBoController.getSearch);
router.post("/export-search-xls", ensureAuthenticatedDirectorProductionOffices, directorAndBoController.exportSearchXls)
//Import Directors


router.get("/import-directors", ensureAuthenticatedImportDirectorManager, directorAndBoController.getImportDirectorHistoryView);

router.get("/import-directors/new", ensureAuthenticatedImportDirectorManager, directorAndBoController.getNewImportDirectorDataView);
router.post("/import-directors/load-file", ensureAuthenticatedImportDirector<PERSON>anager, directorAndBoController.uploadImportDirectorFile);
router.get("/import-directors/download/:filename", ensureAuthenticatedImportDirectorManager, directorAndBoController.downloadArchiveVPDirectorFile);

function ensureAuthenticatedDirectorManager(req, res, next) {
    if (req.session.is_authenticated) {
        if (req.session.authentication.isDirBoImportManager === true || req.session.productionOfficeGroups?.length > 0) {
            return next();
        } else {
            res.redirect("/not-authorized");
        }
    } else {
        res.redirect("/login");
    }
}

function ensureAuthenticatedDirectorProductionOffices(req, res, next) {
    if (req.session.is_authenticated) {
        if (req.session.productionOfficeGroups?.length > 0) {
            return next();
        } else {
            res.redirect("/not-authorized");
        }
    } else {
        res.redirect("/login");
    }
}

function ensureAuthenticatedImportDirectorManager(req, res, next) {
    if (req.session.is_authenticated) {
        if (req.session.authentication.isDirBoImportManager === true) {
            return next();
        } else {
            res.redirect("/not-authorized");
        }
    } else {
        res.redirect("/login");
    }
}

module.exports = router;
<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title">
                            <h2>{{ title }}</h2>
                        </div>
                        <br>
                        <!-- ASSIGNED BY CO -->
                        <div id="officer-assign-table" class="row mt-2 p-1">
                            <h5 class="pl-1 pb-1">ASSIGNATIONS</h5>
                            <div class="table-responsive">
                                <table class="table w-100 nowrap table-striped font-size">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 30%;">Company Name</th>
                                        <th scope="col" style="width: 20%;">Company ID</th>
                                        <th scope="col" style="width: 15%;">Assigned at</th>
                                        <th scope="col" style="width: 20%;">Status</th>
                                        <th scope="col" style="width: 10%;"> </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each assignedReviews}}
                                        <tr>
                                            <td>{{ companyName }}</td>
                                            <td>{{ companyCode }}</td>
                                            <td>
                                                {{#formatDate fileReview.dateAssigned "DD-MM-YYYY"}} {{/formatDate}}
                                            </td>
                                            <td class="text-uppercase">
                                                {{ status.code }}
                                            </td>
                                            <td class="pl-2 py-1 text-center align-middle">
                                                <a href="/file-reviewer/quality-assurance-review/{{ _id }}"
                                                   class="btn solid royal-blue w-100">
                                                    Open
                                                </a>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="5" class="text-center font-italic">There are no files assigned</td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- FILTERS -->
                        <div class="col-md-12">
                            <form method="POST" id="submitForm">
                                <div class="row mt-3">
                                    <h5>SEARCH:</h5>
                                </div>
                                <div class="row pt-1">
                                    <form method="POST" id="searchForm">
                                        <div class="col-md-3 pl-0">
                                            <select
                                                    name="filter_filereview"
                                                    id='filter_filereview'
                                                    class="custom-select"
                                                    onchange=""
                                            >
                                                <option id="all" value="all" selected>Select...</option>
                                                <option id="completed" value="completed">All files present</option>
                                                <option id="client-interaction" value="client-interaction">Files/information required
                                                </option>
                                                <label for="filter_filereview"></label>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <input class='form-control' type='text' name='search_filter'
                                                   placeholder="Enter at least 3 characters"
                                                   id='search_filter'/>
                                            <label for="search_filter"></label>
                                        </div>
                                        <div class="col-md-3">
                                            <input type='SUBMIT' class='btn btn-light btn-sm waves-effect '
                                                   value='Search'/>
                                        </div>
                                    </form>
                                </div>
                            </form>
                        </div>

                        <br>
                        <!-- AUTOMATICALY ASSIGNED TABLE -->
                        <div id="automatic-assign-table" class="row p-1">
                            <div class="table">
                                <table id="scroll-horizontal-datatable" class="table table-striped w-100 nowrap">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 15%;">Company Name</th>
                                        <th scope="col" style="width: 15%;">Company ID</th>
                                        <th scope="col" style="width: 5%;">MCC</th>
                                        <th scope="col" style="width: 15%;">Reviewed at</th>
                                        <th scope="col" style="width: 10%;">Status</th>
                                        <th scope="col" style="width: 20%;">File Review Officer</th>
                                        <th scope="col" style="width: 5%;">Action</th>
                                        <th scope="col" style="width: 10%;">Validated</th>
                                        <th scope="col" style="width: 5%;">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" name="approvedCheck"
                                                       class="custom-control-input" id="approvedCheck"
                                                       form="qualityAssuranceForm"
                                                />
                                                <label
                                                        class="custom-control-label"
                                                        for="approvedCheck"
                                                ></label>
                                            </div>
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each fileReviews}}
                                        <tr>
                                            <td id="auto-company-name-{{ _id }}">{{ companyName }}</td>
                                            <td id="auto-company-code-{{ _id }}">{{ companyCode }}</td>
                                            <td id="auto-mcc-code-{{ _id }}">{{ masterClientCode }}</td>
                                            <td id="auto-date-assigned-{{ _id }}">
                                                {{#if status.statusDate }}  
                                                    {{#formatDate status.statusDate "YYYY-MM-DD"}}{{/formatDate}}
                                                {{else }}  
                                                 {{#formatDate fileReview.dateAssigned "YYYY-MM-DD"}} {{/formatDate}}
                                                {{/if}}                                
                                            </td>
                                            <td id="auto-status-{{ _id }}" class="text-uppercase">{{ status.code }}</td>
                                            <td id="auto-officer-name-{{ _id }}">{{ fileReview.name }}</td>
                                            <td class="pl-2 py-1 text-center align-middle">
                                                <a href="/file-reviewer/quality-assurance-review/{{ _id }}"
                                                   class="btn solid royal-blue w-100">
                                                    Open
                                                </a>
                                            </td>
                                            <td id="auto-approved-{{ _id }}">
                                                {{#if approved}}
                                                    Yes
                                                {{else}}
                                                    No
                                                {{/if}}</td>
                                            <td>
                                                {{#if check }}
                                                    {{#unless approved}}
                                                        <div class="custom-control custom-checkbox">
                                                            <input type="checkbox" name="{{ _id }}"
                                                                   class="custom-control-input" id="{{ _id }}"
                                                                   form="qualityAssuranceForm"
                                                                   {{#if approved}}checked{{/if}}
                                                            />
                                                            <label
                                                                    class="custom-control-label"
                                                                    for="{{ _id }}"
                                                            ></label>
                                                        </div>
                                                    {{/unless}}
                                                {{/if}}
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>

                        <!-- CONTENT END -->
                        <div class="row mt-2 justify-content-between ">
                            <div class="col-md-2">
                                <a href="/file-reviewer/dashboard"
                                   class="btn btn-secondary width-lg waves-effect waves-light">
                                    Back
                                </a>
                            </div>

                            <div class="col-md-2">
                                <button type="button" id="submit-button" class="btn solid royal-blue w-100">
                                    Validate
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<script type="text/javascript">
    if ( window.history.replaceState ) {
        window.history.replaceState( null, null, window.location.href );
    }

    var listToUpdate = [];
    $(document).ready(function () {
        $("#scroll-horizontal-datatable").DataTable({
            scrollX: !0,
            autoWidth: true,
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>",
                },
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });

        const tableRows = $('#scroll-horizontal-datatable').DataTable();
        for (let i = 0; i < tableRows.rows().data().length; i++) {
            if ((tableRows.rows().data()[i][8] === "") && tableRows.rows().data()[i][7] === "No") {
                $('#submit-button').css('display', 'none')
            }
        }
    });

    $('#approvedCheck').change(function () {
        $('input[type="checkbox"]').click();
    })

    $('table').on('change', ':checkbox', function () {
        const approved = this.checked;
        const reviewId = this.id;
        const cellApproved = "#auto-approved-" + reviewId;
        if (listToUpdate.length > 0) {
            if (listToUpdate.find(element => element.reviewId === this.id) !== undefined) {
                listToUpdate = listToUpdate.map((x) => {
                    if (x.reviewId !== this.id) {
                        return x
                    }
                });
                listToUpdate = listToUpdate.filter(caro => caro !== undefined);
            } else {
                listToUpdate.push({
                    approved: this.checked,
                    reviewId: this.id,
                    cellApproved: "#auto-approved-" + reviewId
                })
            }
        } else {
            listToUpdate.push({
                approved: this.checked,
                reviewId: this.id,
                cellApproved: "#auto-approved-" + reviewId
            })
        }
    });

    $('#submit-button').on('click', function () {
        for (let k = 0; k < listToUpdate.length; k++) {
            $.ajax({
                url: '/file-reviewer/update-file-review',
                type: 'POST',
                timeout: 1500,
                data: {
                    fileReviewId: listToUpdate[k].reviewId,
                    fieldsToUpdate: {
                        approved: listToUpdate[k].approved
                    }
                },
                success: function () {
                },
                error: function () {
                },
            });
            if (k === listToUpdate.length - 1) {
                setTimeout(() => {
                    location.reload();
                }, 1500)

            }
        }

    })
</script>

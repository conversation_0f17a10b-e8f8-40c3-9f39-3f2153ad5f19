<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            Company Name: <span class="font-weight-bold">{{ review.companyName }}</span>
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="owners-table mt-3">
                            <h5>BENEFICIAL OWNERS</h5>
                            <hr>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th style="width: 30%">Name</th>
                                        <th style="width: 25%">Country</th>
                                        <th style="width: 20%">Type</th>
                                        <th style="width: 10%"></th>
                                        <th style="width: 10%"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each beneficialOwners}}
                                        <tr>
                                            {{!-- MAY NEED REFACTORING --}}
                                            <!-- NAME -->
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.fullName}}
                                                {{else}}
                                                    {{details.organizationName}}
                                                {{/ifEquals}}
                                            </td>
                                            <!-- COUNTRY -->
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.countryBirth}}
                                                {{else}}
                                                    {{details.incorporationCountry}}
                                                {{/ifEquals}}
                                            </td>
                                            <td style="text-transform: capitalize;">{{ type }}</td>
                                            <td class="text-right">
                                                {{#ifCond type '!==' "organization"}}
                                                    {{#if positions}}
                                                        <button
                                                                type="button"
                                                                class="btn solid royal-blue"
                                                                data-toggle="modal"
                                                                data-target="#openPositionListModal"
                                                                data-open-mode="readOnly"
                                                                data-review-id="{{../id}}"
                                                                data-organization-id="{{_id}}"
                                                        >
                                                            Positions
                                                        </button>
                                                    {{/if}}
                                                {{/ifCond}}
                                            </td>
                                            <td class="text-right">
                                                <button
                                                        type="button"
                                                        data-type="{{ type }}"
                                                        data-group="beneficial"
                                                        data-review-id="{{../id}}"
                                                        data-relation-id="{{ _id }}"
                                                        data-open-mode="readOnly"
                                                        data-name="{{#ifEquals type 'natural' }}
                                                            {{details.fullName}} {{else}}
                                                            {{details.organizationName}} {{/ifEquals}}"
                                                        data-toggle="modal"
                                                        data-target="#openRelationModal"
                                                        class="btn solid royal-blue"
                                                >
                                                    Open
                                                </button>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="5" class="text-center font-italic">
                                                There are no beneficial owners
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- SHAREHOLDERS TABLE -->
                        <div id="shareholders-table-wrap" class="mt-3">
                            <h5>SHAREHOLDERS</h5>
                            <hr>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th class="w-20">Name</th>
                                        <th class="w-20">Country</th>
                                        <th class="w-20">Percentage</th>
                                        <th class="w-20">Type</th>
                                        <th class="w-20"></th>
                                        <th style="width: 10%"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each shareholders}}
                                        <tr>
                                            {{!-- MAY NEED REFACTORING --}}
                                            <!-- NAME -->
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.fullName}}
                                                {{else}}
                                                    {{details.organizationName}}
                                                {{/ifEquals}}
                                            </td>
                                            <!-- COUNTRY -->
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.countryBirth}}
                                                {{else}}
                                                    {{details.incorporationCountry}}
                                                {{/ifEquals}}
                                            </td>
                                            <!-- PERCENTAGE -->
                                            <td style="text-transform: capitalize;">
                                                {{#if additional.percentage}}
                                                    {{additional.percentage}} %
                                                {{/if}}
                                            </td>
                                            <td style="text-transform: capitalize;">{{ type }}</td>
                                            <td class="text-right">
                                                {{#ifCond type '!==' "organization"}}
                                                    {{#if positions}}
                                                        <button
                                                                type="button"
                                                                class="btn solid royal-blue"
                                                                data-toggle="modal"
                                                                data-target="#openPositionListModal"
                                                                data-open-mode="readOnly"
                                                                data-review-id="{{../id}}"
                                                                data-organization-id="{{_id}}"
                                                        >
                                                            Positions
                                                        </button>
                                                    {{/if}}
                                                {{/ifCond}}
                                            </td>
                                            <td class="text-right">
                                                <button
                                                        type="button"
                                                        data-type="{{ type }}"
                                                        data-group="shareholder"
                                                        data-review-id="{{../id}}"
                                                        data-relation-id="{{ _id }}"
                                                        data-open-mode="readOnly"
                                                        data-name="{{#ifEquals type 'natural' }}
                                                            {{details.fullName}} {{else}}
                                                            {{details.organizationName}} {{/ifEquals}}"
                                                        data-toggle="modal"
                                                        data-target="#openRelationModal"
                                                        class="btn solid royal-blue"
                                                >
                                                    Open
                                                </button>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="8" class="text-center font-italic">
                                                There are no shareholders
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!-- DIRECTORS TABLE -->
                        <div id="positions-table-wrap" class="mt-3">
                            <h5>DIRECTORS</h5>
                            <hr>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th style="width: 30%">Name</th>
                                        <th style="width: 25%">Country</th>
                                        <th style="width: 20%">Type</th>
                                        <th style="width: 10%"></th>
                                        <th style="width: 10%"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each directors}}
                                        <tr>
                                            {{!-- MAY NEED REFACTORING --}}
                                            <!-- NAME -->
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.fullName}}
                                                {{else}}
                                                    {{details.organizationName}}
                                                {{/ifEquals}}
                                            </td>
                                            <!-- COUNTRY -->
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.countryBirth}}
                                                {{else}}
                                                    {{details.incorporationCountry}}
                                                {{/ifEquals}}
                                            </td>
                                            <!-- PERCENTAGE -->
                                            <td style="text-transform: capitalize;">{{ type }}</td>
                                            <td class="text-right">
                                                {{#ifCond type '!==' "organization"}}
                                                    {{#if positions}}
                                                        <button
                                                                type="button"
                                                                class="btn solid royal-blue"
                                                                data-toggle="modal"
                                                                data-target="#openPositionListModal"
                                                                data-open-mode="readOnly"
                                                                data-review-id="{{../id}}"
                                                                data-organization-id="{{_id}}"
                                                        >
                                                            Positions
                                                        </button>
                                                    {{/if}}
                                                {{/ifCond}}
                                            </td>
                                            <td class="text-right">
                                                <button
                                                        type="button"
                                                        data-type="{{ type }}"
                                                        data-group="director"
                                                        data-review-id="{{../id}}"
                                                        data-relation-id="{{ _id }}"
                                                        data-open-mode="readOnly"
                                                        data-name="{{#ifEquals type 'natural' }}
                                                            {{details.fullName}} {{else}}
                                                            {{details.organizationName}} {{/ifEquals}}"
                                                        data-toggle="modal"
                                                        data-target="#openRelationModal"
                                                        class="btn solid royal-blue"
                                                >
                                                    Open
                                                </button>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="8" class="text-center font-italic">
                                                There are no directors
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <!-- CONTENT END -->
                    <div class="row mt-2 justify-content-between">
                        <div class="col-md-2">
                            <a href="/file-reviewer/compliances/{{ id }}"
                               class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>

                        </div>
                        <div class="col-md-10 d-flex justify-content-end">
                            {{#if canValidate}}
                                {{#ifEquals review.compliance.assignedBy "FR"}}
                                    <button
                                            type="button"
                                            id="send-file-reviewer-button"
                                            class="btn solid royal-blue px-2 ml-3"
                                            data-toggle="modal"
                                            data-target="#changeStatusModal"
                                            data-status="sendOfficer"
                                            data-officer="compliance"
                                            data-id="{{ id }}"
                                            data-page="1"
                                    >
                                        Send To File Reviewer
                                    </button>
                                {{/ifEquals}}

                                {{#ifEquals review.compliance.assignedBy "QA"}}
                                    <button
                                            type="button"
                                            id="send-file-reviewer-button"
                                            class="btn solid royal-blue px-2 ml-3"
                                            data-toggle="modal"
                                            data-target="#changeStatusModal"
                                            data-status="sendQuality"
                                            data-id="{{ id }}"
                                            data-officer="compliance"
                                            data-page="1"
                                    >
                                        Send To QA Officer
                                    </button>
                                {{/ifEquals}}

                                {{#ifEquals review.compliance.assignedBy "CO"}}
                                    <button
                                            type="button"
                                            id="send-file-reviewer-button"
                                            class="btn solid royal-blue px-2 ml-3"
                                            data-toggle="modal"
                                            data-target="#changeStatusModal"
                                            data-status="sendOfficer"
                                            data-officer="compliance"
                                            data-id="{{ id }}"
                                            data-page="1"
                                    >
                                        Send To File Reviewer
                                    </button>
                                    <button
                                            type="button"
                                            id="send-file-reviewer-button"
                                            class="btn solid royal-blue px-2 ml-3"
                                            data-toggle="modal"
                                            data-target="#changeStatusModal"
                                            data-status="sendQuality"
                                            data-id="{{ id }}"
                                            data-officer="compliance"
                                            data-page="1"
                                    >
                                        Send To QA Officer
                                    </button>
                                    <button
                                            type="button"
                                            id="saveQualityButton"
                                            class="btn solid royal-blue px-2 ml-3"
                                            data-id="{{ id }}"
                                            data-officer="compliance"
                                            data-status="validateCompliance"
                                            data-toggle="modal"
                                            data-target="#confirmReviewModal"
                                    >
                                        COMPLIANCE VALIDATE
                                    </button>
                                {{/ifEquals}}
                            {{else}}
                                <button
                                        type="button"
                                        id="add-comment-button"
                                        class="btn solid royal-blue px-2 ml-3"
                                        data-toggle="modal"
                                        data-target="#commentsModal"
                                        data-officer="CO"
                                        data-id="{{ id }}"
                                >
                                    Add Comment
                                </button>
                            {{/if}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

{{>file-reviewer/download-file-modal}}
{{>file-reviewer/quality-assurance/quality-assurance-relations-modal}}
{{>file-reviewer/position/open-position-list-modal}}
{{>file-reviewer/position/open-quality-position-modal}}
{{>file-reviewer/modals/change-status-review-modal}}
{{>file-reviewer/modals/confirm-review-modal}}
{{>file-reviewer/modals/add-comment-modal}}

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>

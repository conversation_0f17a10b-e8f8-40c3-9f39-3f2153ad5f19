const EntryModel = require("../models/entry").EntryModel;
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const xlsx = require('xlsx');
const moment = require('moment');

dotenv.config();


try {
  mongoose.connect(process.env.MONGODB, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    useFindAndModify: false,
  });
  const db = mongoose.connection;
  db.on('error', console.error.bind(console, 'MongoDB connection error:'));
  console.log("Connected to MongoDB");
  generateReport().then(r => console.log("FINISH, total files: ", r.totalFiles)).catch(e => console.log(e));

} catch (e) {
  console.log(e);
}


async function generateReport() {
  try {

    const files = await EntryModel.aggregate([
      {
        $project: {
          evidence_non_residency: { $size: '$tax_residency.evidence_non_residency' },
          evidence_provisional_treatment_non_residency: { $size: '$tax_residency.evidence_provisional_treatment_non_residency' },
          evidence_none_activities: { $size: '$relevant_activities.evidence_none_activities' },
          support_attachments: { $size: '$supporting_details.support_attachments' },
          // Relevant activities files
          "banking_business.evidence_equipment": { $size: '$banking_business.evidence_equipment' },
          "banking_business.outsourcing_evidence": { $size: '$banking_business.outsourcing_evidence' },
          "banking_business.support_documents": { $size: '$banking_business.support_documents' },
          "banking_business.tangible_assets_explanation_files": { $size: '$banking_business.tangible_assets_explanation_files' },
          "banking_business.intangible_assets_decisions_files": { $size: '$banking_business.intangible_assets_decisions_files' },
          "banking_business.intangible_assets_nature_files": { $size: '$banking_business.intangible_assets_nature_files' },
          "banking_business.intangible_assets_trading_nature_files": { $size: '$banking_business.intangible_assets_trading_nature_files' },
          "banking_business.other_ciga_business_files": { $size: '$banking_business.other_ciga_business_files' },
          "banking_business.other_ciga_decisions_files": { $size: '$banking_business.other_ciga_decisions_files' },
          "banking_business.other_ciga_evidence_files": { $size: '$banking_business.other_ciga_evidence_files' },
          "banking_business.other_ciga_files": { $size: '$banking_business.other_ciga_files' },
          "banking_business.high_risk_ip_evidence": { $size: '$banking_business.high_risk_ip_evidence' },
          "insurance_business.evidence_equipment": { $size: '$insurance_business.evidence_equipment' },
          "insurance_business.outsourcing_evidence": { $size: '$insurance_business.outsourcing_evidence' },
          "insurance_business.support_documents": { $size: '$insurance_business.support_documents' },
          "insurance_business.tangible_assets_explanation_files": { $size: '$insurance_business.tangible_assets_explanation_files' },
          "insurance_business.intangible_assets_decisions_files": { $size: '$insurance_business.intangible_assets_decisions_files' },
          "insurance_business.intangible_assets_nature_files": { $size: '$insurance_business.intangible_assets_nature_files' },
          "insurance_business.intangible_assets_trading_nature_files": { $size: '$insurance_business.intangible_assets_trading_nature_files' },
          "insurance_business.other_ciga_business_files": { $size: '$insurance_business.other_ciga_business_files' },
          "insurance_business.other_ciga_decisions_files": { $size: '$insurance_business.other_ciga_decisions_files' },
          "insurance_business.other_ciga_evidence_files": { $size: '$insurance_business.other_ciga_evidence_files' },
          "insurance_business.other_ciga_files": { $size: '$insurance_business.other_ciga_files' },
          "insurance_business.high_risk_ip_evidence": { $size: '$insurance_business.high_risk_ip_evidence' },
          "fund_management_business.evidence_equipment": { $size: '$fund_management_business.evidence_equipment' },
          "fund_management_business.outsourcing_evidence": { $size: '$fund_management_business.outsourcing_evidence' },
          "fund_management_business.support_documents": { $size: '$fund_management_business.support_documents' },
          "fund_management_business.tangible_assets_explanation_files": { $size: '$fund_management_business.tangible_assets_explanation_files' },
          "fund_management_business.intangible_assets_decisions_files": { $size: '$fund_management_business.intangible_assets_decisions_files' },
          "fund_management_business.intangible_assets_nature_files": { $size: '$fund_management_business.intangible_assets_nature_files' },
          "fund_management_business.intangible_assets_trading_nature_files": { $size: '$fund_management_business.intangible_assets_trading_nature_files' },
          "fund_management_business.other_ciga_business_files": { $size: '$fund_management_business.other_ciga_business_files' },
          "fund_management_business.other_ciga_decisions_files": { $size: '$fund_management_business.other_ciga_decisions_files' },
          "fund_management_business.other_ciga_evidence_files": { $size: '$fund_management_business.other_ciga_evidence_files' },
          "fund_management_business.other_ciga_files": { $size: '$fund_management_business.other_ciga_files' },
          "fund_management_business.high_risk_ip_evidence": { $size: '$fund_management_business.high_risk_ip_evidence' },
          "finance_leasing_business.evidence_equipment": { $size: '$finance_leasing_business.evidence_equipment' },
          "finance_leasing_business.outsourcing_evidence": { $size: '$finance_leasing_business.outsourcing_evidence' },
          "finance_leasing_business.support_documents": { $size: '$finance_leasing_business.support_documents' },
          "finance_leasing_business.tangible_assets_explanation_files": { $size: '$finance_leasing_business.tangible_assets_explanation_files' },
          "finance_leasing_business.intangible_assets_decisions_files": { $size: '$finance_leasing_business.intangible_assets_decisions_files' },
          "finance_leasing_business.intangible_assets_nature_files": { $size: '$finance_leasing_business.intangible_assets_nature_files' },
          "finance_leasing_business.intangible_assets_trading_nature_files": { $size: '$finance_leasing_business.intangible_assets_trading_nature_files' },
          "finance_leasing_business.other_ciga_business_files": { $size: '$finance_leasing_business.other_ciga_business_files' },
          "finance_leasing_business.other_ciga_decisions_files": { $size: '$finance_leasing_business.other_ciga_decisions_files' },
          "finance_leasing_business.other_ciga_evidence_files": { $size: '$finance_leasing_business.other_ciga_evidence_files' },
          "finance_leasing_business.other_ciga_files": { $size: '$finance_leasing_business.other_ciga_files' },
          "finance_leasing_business.high_risk_ip_evidence": { $size: '$finance_leasing_business.high_risk_ip_evidence' },
          "headquarters_business.evidence_equipment": { $size: '$headquarters_business.evidence_equipment' },
          "headquarters_business.outsourcing_evidence": { $size: '$headquarters_business.outsourcing_evidence' },
          "headquarters_business.support_documents": { $size: '$headquarters_business.support_documents' },
          "headquarters_business.tangible_assets_explanation_files": { $size: '$headquarters_business.tangible_assets_explanation_files' },
          "headquarters_business.intangible_assets_decisions_files": { $size: '$headquarters_business.intangible_assets_decisions_files' },
          "headquarters_business.intangible_assets_nature_files": { $size: '$headquarters_business.intangible_assets_nature_files' },
          "headquarters_business.intangible_assets_trading_nature_files": { $size: '$headquarters_business.intangible_assets_trading_nature_files' },
          "headquarters_business.other_ciga_business_files": { $size: '$headquarters_business.other_ciga_business_files' },
          "headquarters_business.other_ciga_decisions_files": { $size: '$headquarters_business.other_ciga_decisions_files' },
          "headquarters_business.other_ciga_evidence_files": { $size: '$headquarters_business.other_ciga_evidence_files' },
          "headquarters_business.other_ciga_files": { $size: '$headquarters_business.other_ciga_files' },
          "headquarters_business.high_risk_ip_evidence": { $size: '$headquarters_business.high_risk_ip_evidence' },
          "shipping_business.evidence_equipment": { $size: '$shipping_business.evidence_equipment' },
          "shipping_business.outsourcing_evidence": { $size: '$shipping_business.outsourcing_evidence' },
          "shipping_business.support_documents": { $size: '$shipping_business.support_documents' },
          "shipping_business.tangible_assets_explanation_files": { $size: '$shipping_business.tangible_assets_explanation_files' },
          "shipping_business.intangible_assets_decisions_files": { $size: '$shipping_business.intangible_assets_decisions_files' },
          "shipping_business.intangible_assets_nature_files": { $size: '$shipping_business.intangible_assets_nature_files' },
          "shipping_business.intangible_assets_trading_nature_files": { $size: '$shipping_business.intangible_assets_trading_nature_files' },
          "shipping_business.other_ciga_business_files": { $size: '$shipping_business.other_ciga_business_files' },
          "shipping_business.other_ciga_decisions_files": { $size: '$shipping_business.other_ciga_decisions_files' },
          "shipping_business.other_ciga_evidence_files": { $size: '$shipping_business.other_ciga_evidence_files' },
          "shipping_business.other_ciga_files": { $size: '$shipping_business.other_ciga_files' },
          "shipping_business.high_risk_ip_evidence": { $size: '$shipping_business.high_risk_ip_evidence' },
          "holding_business.evidence_equipment": { $size: '$holding_business.evidence_equipment' },
          "holding_business.outsourcing_evidence": { $size: '$holding_business.outsourcing_evidence' },
          "holding_business.support_documents": { $size: '$holding_business.support_documents' },
          "holding_business.tangible_assets_explanation_files": { $size: '$holding_business.tangible_assets_explanation_files' },
          "holding_business.intangible_assets_decisions_files": { $size: '$holding_business.intangible_assets_decisions_files' },
          "holding_business.intangible_assets_nature_files": { $size: '$holding_business.intangible_assets_nature_files' },
          "holding_business.intangible_assets_trading_nature_files": { $size: '$holding_business.intangible_assets_trading_nature_files' },
          "holding_business.other_ciga_business_files": { $size: '$holding_business.other_ciga_business_files' },
          "holding_business.other_ciga_decisions_files": { $size: '$holding_business.other_ciga_decisions_files' },
          "holding_business.other_ciga_evidence_files": { $size: '$holding_business.other_ciga_evidence_files' },
          "holding_business.other_ciga_files": { $size: '$holding_business.other_ciga_files' },
          "holding_business.high_risk_ip_evidence": { $size: '$holding_business.high_risk_ip_evidence' },
          "intellectual_property_business.evidence_equipment": { $size: '$intellectual_property_business.evidence_equipment' },
          "intellectual_property_business.outsourcing_evidence": { $size: '$intellectual_property_business.outsourcing_evidence' },
          "intellectual_property_business.support_documents": { $size: '$intellectual_property_business.support_documents' },
          "intellectual_property_business.tangible_assets_explanation_files": { $size: '$intellectual_property_business.tangible_assets_explanation_files' },
          "intellectual_property_business.intangible_assets_decisions_files": { $size: '$intellectual_property_business.intangible_assets_decisions_files' },
          "intellectual_property_business.intangible_assets_nature_files": { $size: '$intellectual_property_business.intangible_assets_nature_files' },
          "intellectual_property_business.intangible_assets_trading_nature_files": { $size: '$intellectual_property_business.intangible_assets_trading_nature_files' },
          "intellectual_property_business.other_ciga_business_files": { $size: '$intellectual_property_business.other_ciga_business_files' },
          "intellectual_property_business.other_ciga_decisions_files": { $size: '$intellectual_property_business.other_ciga_decisions_files' },
          "intellectual_property_business.other_ciga_evidence_files": { $size: '$intellectual_property_business.other_ciga_evidence_files' },
          "intellectual_property_business.other_ciga_files": { $size: '$intellectual_property_business.other_ciga_files' },
          "intellectual_property_business.high_risk_ip_evidence": { $size: '$intellectual_property_business.high_risk_ip_evidence' },
          "service_centre_business.evidence_equipment": { $size: '$service_centre_business.evidence_equipment' },
          "service_centre_business.outsourcing_evidence": { $size: '$service_centre_business.outsourcing_evidence' },
          "service_centre_business.support_documents": { $size: '$service_centre_business.support_documents' },
          "service_centre_business.tangible_assets_explanation_files": { $size: '$service_centre_business.tangible_assets_explanation_files' },
          "service_centre_business.intangible_assets_decisions_files": { $size: '$service_centre_business.intangible_assets_decisions_files' },
          "service_centre_business.intangible_assets_nature_files": { $size: '$service_centre_business.intangible_assets_nature_files' },
          "service_centre_business.intangible_assets_trading_nature_files": { $size: '$service_centre_business.intangible_assets_trading_nature_files' },
          "service_centre_business.other_ciga_business_files": { $size: '$service_centre_business.other_ciga_business_files' },
          "service_centre_business.other_ciga_decisions_files": { $size: '$service_centre_business.other_ciga_decisions_files' },
          "service_centre_business.other_ciga_evidence_files": { $size: '$service_centre_business.other_ciga_evidence_files' },
          "service_centre_business.other_ciga_files": { $size: '$service_centre_business.other_ciga_files' },
          "service_centre_business.high_risk_ip_evidence": { $size: '$service_centre_business.high_risk_ip_evidence' },
        }
      },
      {
        $project: {
          evidence_non_residency: 1,
          evidence_provisional_treatment_non_residency: 1,
          evidence_none_activities: 1,
          support_attachments: 1,
          evidence_equipment: {
            $sum: [
              '$banking_business.evidence_equipment',
              '$insurance_business.evidence_equipment',
              '$fund_management_business.evidence_equipment',
              '$finance_leasing_business.evidence_equipment',
              '$headquarters_business.evidence_equipment',
              '$shipping_business.evidence_equipment',
              '$holding_business.evidence_equipment',
              '$intellectual_property_business.evidence_equipment',
              '$service_centre_business.evidence_equipment',
            ]
          },
          outsourcing_evidence: {
            $sum: [
              '$banking_business.outsourcing_evidence',
              '$insurance_business.outsourcing_evidence',
              '$fund_management_business.outsourcing_evidence',
              '$finance_leasing_business.outsourcing_evidence',
              '$headquarters_business.outsourcing_evidence',
              '$shipping_business.outsourcing_evidence',
              '$holding_business.outsourcing_evidence',
              '$intellectual_property_business.outsourcing_evidence',
              '$service_centre_business.outsourcing_evidence',
            ]
          },
          support_documents: {
            $sum: [
              '$banking_business.support_documents',
              '$insurance_business.support_documents',
              '$fund_management_business.support_documents',
              '$finance_leasing_business.support_documents',
              '$headquarters_business.support_documents',
              '$shipping_business.support_documents',
              '$holding_business.support_documents',
              '$intellectual_property_business.support_documents',
              '$service_centre_business.support_documents',
            ]
          },
          tangible_assets_explanation_files: {
            $sum: [
              '$banking_business.tangible_assets_explanation_files',
              '$insurance_business.tangible_assets_explanation_files',
              '$fund_management_business.tangible_assets_explanation_files',
              '$finance_leasing_business.tangible_assets_explanation_files',
              '$headquarters_business.tangible_assets_explanation_files',
              '$shipping_business.tangible_assets_explanation_files',
              '$holding_business.tangible_assets_explanation_files',
              '$intellectual_property_business.tangible_assets_explanation_files',
              '$service_centre_business.tangible_assets_explanation_files',
            ]
          },
          intangible_assets_decisions_files: {
            $sum: [
              '$banking_business.intangible_assets_decisions_files',
              '$insurance_business.intangible_assets_decisions_files',
              '$fund_management_business.intangible_assets_decisions_files',
              '$finance_leasing_business.intangible_assets_decisions_files',
              '$headquarters_business.intangible_assets_decisions_files',
              '$shipping_business.intangible_assets_decisions_files',
              '$holding_business.intangible_assets_decisions_files',
              '$intellectual_property_business.intangible_assets_decisions_files',
              '$service_centre_business.intangible_assets_decisions_files',
            ]
          },
          intangible_assets_nature_files: {
            $sum: [
              '$banking_business.intangible_assets_nature_files',
              '$insurance_business.intangible_assets_nature_files',
              '$fund_management_business.intangible_assets_nature_files',
              '$finance_leasing_business.intangible_assets_nature_files',
              '$headquarters_business.intangible_assets_nature_files',
              '$shipping_business.intangible_assets_nature_files',
              '$holding_business.intangible_assets_nature_files',
              '$intellectual_property_business.intangible_assets_nature_files',
              '$service_centre_business.intangible_assets_nature_files',
            ]
          },
          intangible_assets_trading_nature_files: {
            $sum: [
              '$banking_business.intangible_assets_trading_nature_files',
              '$insurance_business.intangible_assets_trading_nature_files',
              '$fund_management_business.intangible_assets_trading_nature_files',
              '$finance_leasing_business.intangible_assets_trading_nature_files',
              '$headquarters_business.intangible_assets_trading_nature_files',
              '$shipping_business.intangible_assets_trading_nature_files',
              '$holding_business.intangible_assets_trading_nature_files',
              '$intellectual_property_business.intangible_assets_trading_nature_files',
              '$service_centre_business.intangible_assets_trading_nature_files',
            ]
          },
          other_ciga_business_files: {
            $sum: [
              '$banking_business.other_ciga_business_files',
              '$insurance_business.other_ciga_business_files',
              '$fund_management_business.other_ciga_business_files',
              '$finance_leasing_business.other_ciga_business_files',
              '$headquarters_business.other_ciga_business_files',
              '$shipping_business.other_ciga_business_files',
              '$holding_business.other_ciga_business_files',
              '$intellectual_property_business.other_ciga_business_files',
              '$service_centre_business.other_ciga_business_files',
            ]
          },
          other_ciga_decisions_files: {
            $sum: [
              '$banking_business.other_ciga_decisions_files',
              '$insurance_business.other_ciga_decisions_files',
              '$fund_management_business.other_ciga_decisions_files',
              '$finance_leasing_business.other_ciga_decisions_files',
              '$headquarters_business.other_ciga_decisions_files',
              '$shipping_business.other_ciga_decisions_files',
              '$holding_business.other_ciga_decisions_files',
              '$intellectual_property_business.other_ciga_decisions_files',
              '$service_centre_business.other_ciga_decisions_files',
            ]
          },
          other_ciga_evidence_files: {
            $sum: [
              '$banking_business.other_ciga_evidence_files',
              '$insurance_business.other_ciga_evidence_files',
              '$fund_management_business.other_ciga_evidence_files',
              '$finance_leasing_business.other_ciga_evidence_files',
              '$headquarters_business.other_ciga_evidence_files',
              '$shipping_business.other_ciga_evidence_files',
              '$holding_business.other_ciga_evidence_files',
              '$intellectual_property_business.other_ciga_evidence_files',
              '$service_centre_business.other_ciga_evidence_files',
            ]
          },
          other_ciga_files: {
            $sum: [
              '$banking_business.other_ciga_files',
              '$insurance_business.other_ciga_files',
              '$fund_management_business.other_ciga_files',
              '$finance_leasing_business.other_ciga_files',
              '$headquarters_business.other_ciga_files',
              '$shipping_business.other_ciga_files',
              '$holding_business.other_ciga_files',
              '$intellectual_property_business.other_ciga_files',
              '$service_centre_business.other_ciga_files',
            ]
          },
          high_risk_ip_evidence: {
            $sum: [
              '$banking_business.high_risk_ip_evidence',
              '$insurance_business.high_risk_ip_evidence',
              '$fund_management_business.high_risk_ip_evidence',
              '$finance_leasing_business.high_risk_ip_evidence',
              '$headquarters_business.high_risk_ip_evidence',
              '$shipping_business.high_risk_ip_evidence',
              '$holding_business.high_risk_ip_evidence',
              '$intellectual_property_business.high_risk_ip_evidence',
              '$service_centre_business.high_risk_ip_evidence',
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          evidence_non_residency: { $sum: '$evidence_non_residency' },
          evidence_provisional_treatment_non_residency: { $sum: '$evidence_provisional_treatment_non_residency' },
          evidence_none_activities: { $sum: '$evidence_none_activities' },
          support_attachments: { $sum: '$support_attachments' },
          evidence_equipment: { $sum: '$evidence_equipment' },
          outsourcing_evidence: { $sum: '$outsourcing_evidence' },
          support_documents: { $sum: '$support_documents' },
          tangible_assets_explanation_files: { $sum: '$tangible_assets_explanation_files' },
          intangible_assets_decisions_files: { $sum: '$intangible_assets_decisions_files' },
          intangible_assets_nature_files: { $sum: '$intangible_assets_nature_files' },
          intangible_assets_trading_nature_files: { $sum: '$intangible_assets_trading_nature_files' },
          other_ciga_business_files: { $sum: '$other_ciga_business_files' },
          other_ciga_decisions_files: { $sum: '$other_ciga_decisions_files' },
          other_ciga_evidence_files: { $sum: '$other_ciga_evidence_files' },
          other_ciga_files: { $sum: '$other_ciga_files' },
          high_risk_ip_evidence: { $sum: '$high_risk_ip_evidence' },
        }
      }
    ]);

    let filesExported = [[
      "",
      "evidence_non_residency",
      "evidence_provisional_treatment_non_residency",
      "evidence_none_activities",
      "support_attachments",
      "evidence_equipment",
      "outsourcing_evidence",
      "support_documents",
      "tangible_assets_explanation_files",
      "intangible_assets_decisions_files",
      "intangible_assets_nature_files",
      "intangible_assets_trading_nature_files",
      "other_ciga_business_files",
      "other_ciga_decisions_files",
      "other_ciga_evidence_files",
      "other_ciga_files",
      "high_risk_ip_evidence",
    ]];

    filesExported.push([
      "Is file exported in export button?",
      "Yes",
      "Yes",
      "No",
      "Only for none activities",
      "Yes",
      "Yes",
      "No",
      "No",
      "No",
      "No",
      "No",
      "No",
      "No",
      "No",
      "No",
      "Yes",
    ]);

    filesExported.push([
      "Is file exported in Download Support Attachments button?",
      "No",
      "No",
      "No",
      "Yes",
      "No",
      "No",
      "Yes",
      "No",
      "No",
      "No",
      "No",
      "No",
      "No",
      "No",
      "No",
      "No",
    ]);

    filesExported.push([
      "Count",
      files[0].evidence_non_residency,
      files[0].evidence_provisional_treatment_non_residency,
      files[0].evidence_none_activities,
      files[0].support_attachments,
      files[0].evidence_equipment,
      files[0].outsourcing_evidence,
      files[0].support_documents,
      files[0].tangible_assets_explanation_files,
      files[0].intangible_assets_decisions_files,
      files[0].intangible_assets_nature_files,
      files[0].intangible_assets_trading_nature_files,
      files[0].other_ciga_business_files,
      files[0].other_ciga_decisions_files,
      files[0].other_ciga_evidence_files,
      files[0].other_ciga_files,
      files[0].high_risk_ip_evidence,
    ]);

    const totalFiles = files[0].evidence_non_residency +
      files[0].evidence_provisional_treatment_non_residency +
      files[0].evidence_none_activities +
      files[0].support_attachments +
      files[0].evidence_equipment +
      files[0].outsourcing_evidence +
      files[0].support_documents +
      files[0].tangible_assets_explanation_files +
      files[0].intangible_assets_decisions_files +
      files[0].intangible_assets_nature_files +
      files[0].intangible_assets_trading_nature_files +
      files[0].other_ciga_business_files +
      files[0].other_ciga_decisions_files +
      files[0].other_ciga_evidence_files +
      files[0].other_ciga_files +
      files[0].high_risk_ip_evidence;

    
    const filename = 'substance_files_report_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(filesExported);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'File report');
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalFiles": totalFiles };
  } catch (e) {
    console.log(e);
    return { "success": false };
  }
}



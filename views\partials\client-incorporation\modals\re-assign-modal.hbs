{{!-- CONFIRM MODAL --}}
<div class="modal fade" id="reAssignApplicationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog  modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Re-assign reviewer</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <div class="row">
                    <div class="col-md-12">
                        <p>
                            You are about to change the review officer. Are you sure?
                        </p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <form id="reAssignForm">
                            <label for="current-user">Current review officer:</label>
                            <input class="form-control" type="text" id="current-user" name="currentReviewer"
                                   readonly/>
                            <br>
                            <label for="new-review-officer">New review officer:</label>
                            <input class="form-control" type="email" name="newReviewer" required id="new-review-officer"/>
                        </form>

                    </div>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="submit" class="btn solid royal-blue" form="reAssignForm" id="submitReAssignForm">
                    Submit
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    const valid_email = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    let incorpId;
    $('#reAssignApplicationModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        incorpId = button.data('incorporation-id');
        let name = button.data('name');
        $("#current-user").val(name);
    });

    $('#reAssignApplicationModal').on('hide.bs.modal', function (event) {
        $("#reAssignForm").trigger("reset");
    });

    $('#new-review-officer').on('keyup', function () {
        const val = $(this).val();
        const bad = !val.match(valid_email);
        bad ? $('#new-review-officer').toggleClass('is-invalid', true) : $('#new-review-officer').toggleClass('is-invalid', false);
    });

    $("#reAssignForm").on('submit', function (event) {
        event.preventDefault();
        $(this).prop('disabled', true);

        $('#reAssignForm input[required]:visible').trigger('keyup');
        if ($(".is-invalid:visible").length === 0) {
            $.ajax({
                type: "POST",
                url: "/client-incorporation/" + incorpId +  "/update-officer",
                data: $(this).serialize(),
                success: () => {
                    Swal.fire('Success','Officer updated successfully.', 'success').then(() => {
                        location.reload();
                    });
                },
                error: (err) => {
                    Swal.fire('Error', 'There was an error updating the information.', 'error').then(() => {
                        $('#reAssignApplicationModal').modal('hide');
                    });
                },
            });
        } else {
            setTimeout(function () {
                $("#reAssignApplicationModal").prop('disabled', false);
            }, 1);
        }
    });

</script>

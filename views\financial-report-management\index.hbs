<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box1">
                    <h1>{{title}}</h1>
                    <p>Welcome {{user.name}}</p>
                    <div class="card-box">
                        <div class="row">
                            
                            {{#if showAccountingSearchModule}}
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Financial returns</h5>
                                        <a href="/financial-report-management/search" class="btn btn-light btn-sm waves-effect">
                                            Search Submissions
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {{/if}}

                            {{#if showQueueOfficerModule}}
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Accounting Officer</h5>
                                        <a href="/financial-report-management/requests" class="btn btn-light btn-sm waves-effect">Manage Requests</a>
                                    </div>
                                </div>
                            </div>
                            {{/if}}

                            {{#if showAccountingImportModule}}
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">AFR Data Import</h5>
                                        <a href="/financial-report-management/import-files" class="btn btn-light btn-sm waves-effect">
                                            Mass-upload
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {{/if}}

                            {{#if showAccountingCompanyModule}}
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Company Compliance</h5>
                                        <a href="/financial-report-management/company-dashboard" class="btn btn-light btn-sm waves-effect">
                                            Manage Reporting
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {{/if}}
                        </div>


                        <div class="row mt-2">
                            <div class="col-md-2">
                                <a href="/" class="btn solid royal-blue w-100">
                                    Back
                                </a>
                            </div>
                        </div>
                    </div>

                    <br>
                </div>
            </div>
        </div>
    </div>
</main>

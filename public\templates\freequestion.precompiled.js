(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['freequestion'] = template({"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<div class=\"row\" id=\"freeQuestions-"
    + alias4(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":1,"column":35},"end":{"line":1,"column":44}}}) : helper)))
    + "\">\r\n  <div class=\"col-1 d-flex pt-2 align-items-center justify-content-end\">\r\n    <button class=\"btn btn-danger btn-xs delete-new-fq\" data-row=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":3,"column":66},"end":{"line":3,"column":75}}}) : helper)))
    + "\">\r\n      <i class=\"dripicons-cross\"></i>\r\n    </button>\r\n  </div>\r\n  <div class=\"col-11\">\r\n    <div class=\"row\">\r\n      <div class=\"col-6 form-group\">\r\n        <label for=\"freeQuestions-"
    + alias4(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":10,"column":34},"end":{"line":10,"column":43}}}) : helper)))
    + "-external\">External Free Question</label>\r\n        <textarea\r\n          class=\"form-control\"\r\n          name=\"freeQuestions["
    + alias4(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":13,"column":30},"end":{"line":13,"column":39}}}) : helper)))
    + "][external]\"\r\n          id=\"freeQuestions-"
    + alias4(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":14,"column":28},"end":{"line":14,"column":37}}}) : helper)))
    + "-external\"\r\n          form=\"fileReviewForm\"\r\n          rows=\"1\"\r\n        ></textarea>\r\n      </div>\r\n      <div class=\"col-6 form-group\">\r\n        <label for=\"freeQuestions-"
    + alias4(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":20,"column":34},"end":{"line":20,"column":43}}}) : helper)))
    + "-internal\">Internal Trident Comment</label>\r\n        <textarea\r\n          class=\"form-control\"\r\n          name=\"freeQuestions["
    + alias4(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":23,"column":30},"end":{"line":23,"column":39}}}) : helper)))
    + "][internal]\"\r\n          id=\"freeQuestions-"
    + alias4(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":24,"column":28},"end":{"line":24,"column":37}}}) : helper)))
    + "-internal\"\r\n          form=\"fileReviewForm\"\r\n          rows=\"1\"\r\n        ></textarea>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<script type=\"text/javascript\">\r\n  $('.delete-new-fq').on('click', function () {\r\n    let row = $(this).data('row');\r\n    $('#freeQuestions-' + row).remove();\r\n  });\r\n</script>\r\n";
},"useData":true});
})();
<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title">
                            <h2>{{ title }}</h2>
                        </div>
                        <br>
                        <!-- FILTERS -->
                        <div class="col-md-12">
                            <form method="POST" id="submitForm">
                                <div class="row mt-3">
                                    <h5>SEARCH:</h5>
                                </div>
                                <div class="row pt-1 ">
                                    <form method="POST" id="searchForm">
                                        <div class="col-md-3 pl-0">
                                            <input class='form-control' type='text' name='search_filter'
                                                   placeholder="Enter at least 3 characters"
                                                   id='search_filter'/>
                                            <label for="search_filter"></label>
                                        </div>
                                        <div class="col-md-3">
                                            <input type='SUBMIT' class='btn btn-light btn-sm waves-effect '
                                                   value='Search'/>
                                        </div>
                                    </form>
                                </div>
                            </form>
                        </div>

                        <br>

                        <div id="automatic-assign-table" class="row p-1">
                            <div class="table">
                                <table id="scroll-horizontal-datatable" class="table table-striped w-100 nowrap">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 15%;">Company Name</th>
                                        <th scope="col" style="width: 15%;">Company ID</th>
                                        <th scope="col" style="width: 5%;">MCC</th>
                                        <th scope="col" style="width: 15%;">Reviewed at</th>
                                        <th scope="col" style="width: 10%;">Status</th>
                                        <th scope="col" style="width: 20%;">File Review Officer</th>
                                        <th scope="col" style="width: 5%;">Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each fileReviews}}
                                        <tr>
                                            <td id="auto-company-name-{{ _id }}">{{ companyName }}</td>
                                            <td id="auto-company-code-{{ _id }}">{{ companyCode }}</td>
                                            <td id="auto-mcc-code-{{ _id }}">{{ masterClientCode }}</td>
                                            <td id="auto-date-assigned-{{ _id }}">
                                                {{#if status.statusDate }}
                                                    {{#formatDate status.statusDate "YYYY-MM-DD"}}{{/formatDate}}
                                                {{else }}
                                                 {{#formatDate fileReview.dateAssigned "YYYY-MM-DD"}} {{/formatDate}}
                                                {{/if}}
                                            </td>
                                            <td id="auto-status-{{ _id }}" class="text-uppercase">{{ status.code }}</td>
                                            <td id="auto-officer-name-{{ _id }}">{{ fileReview.name }}</td>
                                            <td class="pl-2 py-1 text-center align-middle">
                                                <a href="/file-reviewer/file-observer-review/{{ _id }}"
                                                   class="btn solid royal-blue w-100">
                                                    Open
                                                </a>
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>

                        <!-- CONTENT END -->
                        <div class="row mt-2 justify-content-between ">
                            <div class="col-md-2">
                                <a href="/file-reviewer/dashboard"
                                   class="btn btn-secondary width-lg waves-effect waves-light">
                                    Back
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<script type="text/javascript">
    if ( window.history.replaceState ) {
        window.history.replaceState( null, null, window.location.href );
    }

    var listToUpdate = [];
    $(document).ready(function () {
        $("#scroll-horizontal-datatable").DataTable({
            scrollX: !0,
            autoWidth: true,
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>",
                },
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });

    });

</script>

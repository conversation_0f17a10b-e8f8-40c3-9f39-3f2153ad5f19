<div id="naturalForm">
    <!-- NATURAL DETAILS -->
    <div id="naturalDetails">
        <div class="row mt-3">
            <div class="col-2">
                <label for="details-full-name">Full Name*</label>
            </div>
            <div class="col-4">
                <input id="details-full-name" name="details[fullName]" type="text" class="form-control"
                       value="{{relation.details.fullName}}" required/>
            </div>
            <div class="col-2">
                <label for="details-first-name">First Name</label>
            </div>
            <div class="col-4">
                <input id="details-first-name" name="details[firstName]" type="text" class="form-control"
                       value="{{relation.details.firstName}}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="details-middle-name">Middle Name/Initial</label>
            </div>
            <div class="col-4">
                <input id="details-middle-name" name="details[middleName]" type="text" class="form-control"
                       value="{{relation.details.middleName}}"/>
            </div>
            <div class="col-2">
                <label for="details-last-name">Last Name</label>
            </div>
            <div class="col-4">
                <input id="details-last-name"  name="details[lastName]" type="text" class="form-control"
                       value="{{relation.details.lastName}}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="details-occupation">Occupation</label>
            </div>
            <div class="col-4">
                <input id="details-occupation" name="details[occupation]" type="text" class="form-control"
                       value="{{relation.details.occupation}}"/>
            </div>
            <div class="col-2">
                <label for="details-birth-date">Date Of Birth</label>
            </div>
            <div class="col-4">
                <input id="details-birth-date" name="details[birthDate]" type="date" class="form-control"
                       value="{{#formatDate relation.details.birthDate "YYYY-MM-DD"}} {{/formatDate }}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="details-nationality">Nationality</label>
            </div>
            <div class="col-4">
<!--                <input id="details-nationality" name="details[nationality]" type="text" class="form-control"-->
<!--                       value="{{relation.details.nationality}}"/>-->
                {{>file-reviewer/shared/select-country selectId="details[nationality]" value=relation.details.nationality}}
            </div>
            <div class="col-2">
                <label for="details[countryBirth]">Country of Birth</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[countryBirth]" value=relation.details.countryBirth}}
            </div>
        </div>
        <div class="row pt-3">
            <div class="col-12 d-flex justify-content-end">
                <div class="custom-control custom-checkbox">
                    <input
                            type="checkbox"
                            class="custom-control-input completeCheck"
                            name="details[correct]"
                            id="details-correct-check"
                            {{#if relationInformation.details.complete}} checked {{/if}}
                    />
                    <label class="custom-control-label" for="details-correct-check">Complete Information</label>
                </div>
            </div>
        </div>
    </div>
    <hr class="mt-2" />

    <!-- IDENTIFICATION DETAILS -->
    <div id="identificationDetails">
        <h4>Identification</h4>
        <div class="row mt-2">
            <div class="col-5">

            </div>
            <div class="col-12">
                <label>Do you want to sent the ID PAL request to the client? *</label>
                <div class="custom-control custom-radio custom-control-inline ml-lg-3">
                    <input type="radio" class="custom-control-input" id="natural-is-electronic-id-yes"
                           name="electronicIdInfo[isElectronicId]" required {{#if relation.electronicIdInfo.isElectronicId }} checked {{/if}} value="YES"/>
                    <label class="custom-control-label" for="natural-is-electronic-id-yes">Yes</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="natural-is-electronic-id-no"
                           name="electronicIdInfo[isElectronicId]" required {{#unless relation.electronicIdInfo.isElectronicId }}
                           checked {{/unless}} value="NO"/>
                    <label class="custom-control-label" for="natural-is-electronic-id-no">No</label>
                </div>
            </div>
        </div>

        <div  id="electronic-id-files" {{#unless relation.electronicIdInfo.isElectronicId }} style="display: none" {{/unless}}>
            <div class="row mt-2" >
                <div class="col-2">
                    <label>Send to*</label>
                </div>
                <div class="col-4">
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="natural-send-to-default"
                               name="electronicIdInfo[emailType]"
                            {{#ifEquals relation.electronicIdInfo.emailType 'default'}} checked {{/ifEquals}} value="default"/>
                        <label class="custom-control-label" for="natural-send-to-default">Default e-mail</label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="natural-send-to-free-email"
                               name="electronicIdInfo[emailType]" {{#ifEquals relation.electronicIdInfo.emailType 'free email' }}
                               checked {{/ifEquals}} value="free email"/>
                        <label class="custom-control-label" for="natural-send-to-free-email">Free e-mail</label>
                    </div>
                </div>
            </div>
            <div id="default-email-id" class="row mt-2"
                {{#ifCond relation.electronicIdInfo.emailType '!==' 'default' }} style="display: none" {{/ifCond}}>
                <div class="col-2">
                    <label for="identification-email-default">E-mail*</label>
                </div>
                <div class="col-4">
                    <input id="identification-email-default" name="electronicIdInfo[defaultEmail]" type="email" class="form-control" readonly
                           value="{{user.username}}"/>
                </div>
            </div>

            <div id="free-email-id" class="row mt-2" {{#ifCond relation.electronicIdInfo.emailType '!==' 'free email' }}  style="display: none"  {{/ifCond}}>
                <div class="col-2">
                    <label for="identification-email">E-mail*</label>
                </div>
                <div class="col-4">
                    <input id="identification-email" name="electronicIdInfo[email]" type="email" class="form-control"
                           value="{{relation.electronicIdInfo.email}}"/>
                </div>
            </div>

            {{#if relation.electronicIdInfo.uuid}}
                <div class="row">
                    <div class="col-12 d-flex">
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input"
                                    name="electronicIdInfo[allowNewRequest]"
                                    id="electronicIdInfo[allowNewRequest]"
                                {{#if relation.electronicIdInfo.allowNewRequest}} checked {{/if}}
                            />
                            <label class="custom-control-label" for="electronicIdInfo[allowNewRequest]">Start new request</label>
                        </div>
                    </div>
                </div>
            {{/if}}

        </div>


        <div id="manual-id-info" {{#if relation.electronicIdInfo.isElectronicId }} style="display: none" {{/if}}>
            <div class="row mt-2">
                <div class="col-2">
                    <label for="identification-id-type">Type of Identification</label>
                </div>
                <div class="col-4">
                    <select name="identification[identificationType]" id="identification-id-type" class="custom-select" >
                        <option value="" selected disabled>Select</option>
                        <option value="passport">Passport</option>
                        <option value="id">ID Card</option>
                        <option value="driver">Driver's license/Other</option>
                    </select>
                </div>
                <div class="col-2">
                    <label for="identification[issueCountry]">Country of Issue</label>
                </div>
                <div class="col-4">
                    {{>file-reviewer/shared/select-country selectId="identification[issueCountry]" value=relation.identification.issueCountry}}
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-2">
                    <label for="identification-expiry-date">Expiry Date</label>
                </div>
                <div class="col-4">
                    <input id="identification-expiry-date" name="identification[expiryDate]" type="date" class="form-control"
                           value="{{#formatDate relation.identification.expiryDate "YYYY-MM-DD"}} {{/formatDate }}"/>
                </div>
                <div class="col-2">
                    <label for="identification-valid">Valid Identification</label>
                </div>
                <div class="col-4">
                    <div class="custom-control custom-switch my-auto">
                        <input
                                type="checkbox"
                                class="custom-control-input"
                                id="identification-valid"
                                name="identification[valid]"
                            {{#if relation.identification.valid}} checked {{/if}}
                        />
                        <label class="custom-control-label" for="identification-valid"></label>
                    </div>
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-2">
                    <label>Certified legible copy of passport</label>
                </div>
                <div class="col-4">
                    <button
                            type="button"
                            class="btn solid royal-blue upload-button"
                            id="btn-identification-0"
                            data-toggle="modal"
                            data-target="#upload-temp-modal"
                            data-id="{{relation.identification.files.0.id }}"
                            data-row="0"
                            data-review-id="{{id}}"
                            data-relation-id="{{relation._id }}"
                            data-file-type="natural"
                        {{#if newRelation}}
                            data-field="{{relation.naturalFiles.identification.0.internal}}"
                        {{else}}
                            data-field="{{relation.identification.files.0.internal}}"
                        {{/if}}

                            data-file-group="identification"
                            {{#if relation.identification.files.0.uploadFiles}}style="background-color:#0AC292;border-color: #0AC292;"{{/if}}
                    >
                        {{#if relation.identification.files.0.uploadFiles}}Modify{{else}}Upload{{/if}}
                    </button>
                </div>
                <div class="col-2">
                    <label data-toggle="tooltip" data-placement="right" title="Utility bill - not a telephone bill">Certified legible copy of Proof of Address</label>
                </div>
                <div class="col-4">
                    <button
                            type="button"
                            class="btn solid royal-blue upload-button"
                            id="btn-identification-1"
                            data-toggle="modal"
                            data-target="#upload-temp-modal"
                            data-id="{{ relation.identification.files.1.id }}"
                            data-row="1"
                            data-review-id="{{ id}}"
                            data-relation-id="{{relation._id }}"
                            data-file-type="natural"
                        {{#if newRelation}}
                            data-field="{{relation.naturalFiles.identification.1.internal}}"
                        {{else}}
                            data-field="{{relation.identification.files.1.internal}}"
                        {{/if}}
                            data-file-group="identification"
                            {{#if relation.identification.files.1.uploadFiles}}style="background-color:#0AC292;border-color: #0AC292;"{{/if}}
                    >
                        {{#if relation.identification.files.1.uploadFiles}}Modify{{else}}Upload{{/if}}
                    </button>
                </div>
            </div>
        </div>

        <div class="row pt-3">
            <div class="col-12 d-flex justify-content-end">
                <div class="custom-control custom-checkbox">
                    <input
                            type="checkbox"
                            class="custom-control-input completeCheck"
                            name="identification[correct]"
                            id="correctIdentification"
                            {{#if relationInformation.identification.complete}} checked {{/if}}
                    />
                    <label class="custom-control-label" for="correctIdentification">Complete Information</label>
                </div>
            </div>
        </div>
    </div>
    <hr class="mt-2" />

    <!-- PRINCIPAL ADDRESS DETAILS -->
    <div id="principalAddressDetails">
        <h4>Principal Address</h4>
        {{>file-reviewer/review-relations/sections/address-details-form group="natural"
                principalAddress=relation.principalAddress formType="principalAddress"
                relationInformation=relationInformation.principalAddress}}
    </div>
    <hr class="mt-2" />

    <!-- MAILING ADDRESS DETAILS -->
    <div id="mailingAddressDetails">
        <h4>Mailing Address</h4>
        {{>file-reviewer/review-relations/sections/address-details-form group="natural"
                principalAddress=relation.mailingAddress formType="mailingAddress"
                relationInformation=relationInformation.mailingAddress}}
    </div>
    <hr class="mt-2" />

    <!-- COUNTRY OF TAX ADVICE-->
    <div id="countryTaxResidence">
        <h4>Tax Advice</h4>
        <div class="row mt-4">
            <div class="col-5">
                <label for="tax-residence-confirmation">Confirmation Regarding Legal / Tax Advice</label>
            </div>
            <div class="col-7 d-flex">
                <div class="custom-control custom-switch my-auto">
                    <input
                        type="checkbox"
                        class="custom-control-input"
                        id="tax-residence-confirmation-check"
                        name="taxResidence[confirmation]"
                        {{#if relation.taxResidence.confirmation}} checked {{/if}}
                    />
                    <label class="custom-control-label" for="tax-residence-confirmation-check"></label>
                </div>
            </div>
            <div class="col-5">
            <label for="natural-details-tax-residence">Tax Residence</label>
            </div>
            <div class="col-3">
                {{>file-reviewer/shared/select-country selectId="taxResidence[taxResidence]"
                        value=relation.taxResidence.taxResidence}}
            </div>
        </div>
        <div class="row pt-3">
            <div class="col-12 d-flex justify-content-end">
                <div class="custom-control custom-checkbox">
                    <input
                        type="checkbox"
                        class="custom-control-input completeCheck"
                        name="taxResidence[correct]"
                        id="correctTaxResidence"
                        {{#if relationInformation.taxResidence.complete}} checked {{/if}}
                    />
                    <label class="custom-control-label" for="correctTaxResidence">Complete Information</label>
                </div>
            </div>
        </div>
        <hr class="mt-2" />
    </div>

    <!-- ADVISOR DETAILS -->
    <div id="advisorDetails">
        <div class="row">
        <h4 data-toggle="tooltip" data-placement="right" title="Not compulsory information - not all clients have an advisor">Advisor Details</h4>
        </div>
        <div class="row mt-4">
            <div class="col-2">
                <label for="advisor-first-name">First Name</label>
            </div>
            <div class="col-4">
                <input id="advisor-first-name" name="advisorDetails[firstName]" type="text" class="form-control"
                       value="{{relation.advisorDetails.firstName}}"/>
            </div>
            <div class="col-2">
                <label for="advisor-middle-name">Middle Name</label>
            </div>
            <div class="col-4">
                <input id="advisor-middle-name" name="advisorDetails[middleName]" type="text" class="form-control"
                       value="{{relation.advisorDetails.middleName}}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="advisor-last-name">Last Name</label>
            </div>
            <div class="col-4">
                <input id="advisor-last-name" name="advisorDetails[lastName]" type="text" class="form-control"
                       value="{{relation.advisorDetails.lastName}}"/>
            </div>
            <div class="col-2">
                <label for="advisor-firm-name">Name of Firm</label>
            </div>
            <div class="col-4">
                <input id="advisor-firm-name" name="advisorDetails[firmName]" type="text" class="form-control"
                       value="{{relation.advisorDetails.firmName}}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="advisor-phone">Phone</label>
            </div>
            <div class="col-4">
                <input id="advisor-phone" name="advisorDetails[phone]" type="text" class="form-control"
                       value="{{relation.advisorDetails.phone}}"/>
            </div>
            <div class="col-2">
                <label for="advisor-email">E-mail</label>
            </div>
            <div class="col-4">
                <input id="advisor-email" name="advisorDetails[email]" type="email" class="form-control"
                       value="{{relation.advisorDetails.email}}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="advisorDetails[nationality]">Nationality</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="advisorDetails[nationality]"
                        value=relation.advisorDetails.nationality}}
            </div>
            <div class="col-2">
                <label for="advisorDetails[incorporationCountry]">Country of Incorporation</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="advisorDetails[incorporationCountry]"
                        value=relation.advisorDetails.incorporationCountry}}
            </div>
        </div>
        <div class="row pt-3">
            <div class="col-12 d-flex justify-content-end">
                <div class="custom-control custom-checkbox">
                    <input
                            type="checkbox"
                            class="custom-control-input completeCheck"
                            name="advisorDetails[correct]"
                            id="correctAdvisor"
                        {{#if relationInformation.advisorDetails.complete}} checked {{/if}}
                    />
                    <label class="custom-control-label" for="correctAdvisor">Complete Information</label>
                </div>
            </div>
        </div>
    </div>
    <hr class="mt-2" />

    <!-- PRINCIPAL RESIDENTIAL DETAILS -->
    <div id="residentialAddressDetails">
        <div class="row">
        <h4 data-toggle="tooltip" data-placement="right" title="Not compulsory information if no advisor">Principal Advisor Address</h4>
        </div>
        {{>file-reviewer/review-relations/sections/address-details-form group="natural"
                principalAddress=relation.residentialAddress formType="residentialAddress"
                relationInformation=relationInformation.residentialAddress}}
    </div>
    <hr class="mt-2" />

    <!--WORLD CHECK DETAILS -->
    <div  id="worldCheckSection" >
        {{>file-reviewer/review-relations/sections/world-check-details-form group="natural"
                worldCheck=(ternary newRelation relation.naturalFiles.worldCheck relation.worldCheck)
                relationInformation=relationInformation.worldCheck }}
    </div>
    <hr class="mt-2" />


    <!-- PEP DETAILS -->
    {{#if showPep}}
        <div id="pepDetails">
            <div class="row mt-4">
                <div class="col-5">
                    <h4>Is this a PEP?</h4>
                </div>
                <div class="col-7 d-flex justify-content-end">
                    <div class="custom-control custom-radio custom-control-inline">
                        <input
                                type="radio"
                                class="custom-control-input"
                                id="pep-confirmation-check-yes"
                                name="pepDetails[confirmation]"
                            {{#if relation.pep }} checked {{/if}}
                                value="YES"
                        />
                        <label class="custom-control-label" for="pep-confirmation-check-yes">Yes</label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline">
                        <input
                                type="radio"
                                class="custom-control-input"
                                id="pep-confirmation-check-no"
                                name="pepDetails[confirmation]"
                            {{#unless relation.pep }} checked {{/unless}}
                                value="NO"
                        />
                        <label class="custom-control-label" for="pep-confirmation-check-no">No</label>
                    </div>
                </div>
            </div>
            <div id="pepInfo"  {{#unless relation.pep }} style="display: none;" {{/unless}}>
                <div class="row mt-2">
                    <div class="col-2">
                        <label>Professional reference letter addressed to TBVI</label>
                    </div>
                    <div class="col-4">
                        <button
                                type="button"
                                class="btn solid royal-blue upload-button"
                                id="btn-pepDetails-0"
                                data-toggle="modal"
                                data-target="#upload-temp-modal"
                                data-id="{{ relation.pepDetails.files.0.id }}"
                                data-row="0"
                                data-review-id="{{ id}}"
                                data-relation-id="{{relation._id }}"
                                data-file-type="natural"
                                {{#if newRelation}}
                                    data-field="{{relation.naturalFiles.pep.0.internal}}"
                                {{else}}
                                    data-field="{{relation.pepDetails.files.0.internal}}"
                                {{/if}}
                                data-file-group="pepDetails"
                                {{#if relation.pepDetails.files.0.uploadFiles}}style="background-color:#0AC292;border-color: #0AC292;"{{/if}}
                        >
                            {{#if relation.pepDetails.files.0.uploadFiles}}Modify{{else}}Upload{{/if}}
                        </button>
                    </div>
                    <div class="col-2">
                        <label>Bank reference letter addressed to TBVI</label>
                    </div>
                    <div class="col-4">
                        <button
                                type="button"
                                class="btn solid royal-blue upload-button"
                                id="btn-pepDetails-1"
                                data-toggle="modal"
                                data-target="#upload-temp-modal"
                                data-id="{{ relation.pepDetails.files.1.id }}"
                                data-row="1"
                                data-review-id="{{ id}}"
                                data-relation-id="{{relation._id }}"
                                data-file-type="natural"
                                {{#if newRelation}}
                                    data-field="{{relation.naturalFiles.pep.1.internal}}"
                                {{else}}
                                    data-field="{{relation.pepDetails.files.1.internal}}"
                                {{/if}}
                                data-file-group="pepDetails"
                                {{#if relation.pepDetails.files.1.uploadFiles}}style="background-color:#0AC292;border-color: #0AC292;"{{/if}}
                        >
                            {{#if relation.pepDetails.files.1.uploadFiles}}Modify{{else}}Upload{{/if}}
                        </button>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-2">
                        <label>Curricular Vitae</label>
                    </div>
                    <div class="col-4">
                        <button
                                type="button"
                                class="btn solid royal-blue upload-button"
                                id="btn-pepDetails-2"
                                data-toggle="modal"
                                data-target="#upload-temp-modal"
                                data-id="{{ relation.pepDetails.files.2.id }}"
                                data-row="2"
                                data-review-id="{{ id}}"
                                data-relation-id="{{relation._id }}"
                                data-file-type="natural"
                                {{#if newRelation}}
                                    data-field="{{relation.naturalFiles.pep.2.internal}}"
                                {{else}}
                                    data-field="{{relation.pepDetails.files.2.internal}}"
                                {{/if}}
                                data-file-group="pepDetails"
                                {{#if relation.pepDetails.files.2.uploadFiles}}style="background-color:#0AC292;border-color: #0AC292;"{{/if}}
                        >
                            {{#if relation.pepDetails.files.2.uploadFiles}}Modify{{else}}Upload{{/if}}
                        </button>
                    </div>
                    <div class="col-2">
                        <label for="pep-information">PEP Information</label>
                    </div>
                    <div class="col-4">
                    <textarea
                            name="pepDetails[information]"
                            id="pep-information"
                            class="form-control"
                            rows="1"
                    >{{relation.pepDetails.information}}</textarea>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-2">
                        <label>Addtional news check completed?</label>
                    </div>
                    <div class="col-4">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input
                                    type="radio"
                                    class="custom-control-input"
                                    id="add-news-check-yes"
                                    name="pepDetails[confirmAdditionalComments]"
                                {{#if relation.pepDetails.confirmAdditionalComments}} checked {{/if}}
                                    value="YES"
                            />
                            <label class="custom-control-label" for="add-news-check-yes">Yes</label>
                        </div>
                        <div class="custom-control custom-radio custom-control-inline">
                            <input
                                    type="radio"
                                    class="custom-control-input"
                                    id="add-news-check-no"
                                    name="pepDetails[confirmAdditionalComments]"
                                {{#unless relation.pepDetails.confirmAdditionalComments}} checked {{/unless}}
                                    value="NO"
                            />
                            <label class="custom-control-label" for="add-news-check-no">No</label>
                        </div>
                    </div>

                    <div class="col-2">
                        <div id="lbl-additionalComments" {{#unless relation.pepDetails.confirmAdditionalComments }} style="display: none;" {{/unless}}>
                        <label for="pep-add-news">Additional news comments:</label>
                        </div>
                    </div>
                    <div class="col-4">
                        <div id="pep-add-news" {{#unless relation.pepDetails.confirmAdditionalComments }} style="display: none;" {{/unless}}>
                    <textarea
                            name="pepDetails[additionalComments]"

                            class="form-control"
                            rows="1"
                    >{{relation.pepDetails.additionalComments}}</textarea>
                    </div>
                    </div>
                </div>

                <div class="row pt-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input completeCheck" name="pepDetails[correct]" id="correctPEP"
                                {{#if relationInformation.pepDetails.complete}} checked{{/if}}/>
                            <label class="custom-control-label" for="correctPEP">Complete Information</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {{/if}}

</div>

<script>
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body',
        boundary: 'window'
    });
    $('#pep-confirmation-check-yes').change(function () {
        $('#pepInfo').toggle(200);
    });
    $('#pep-confirmation-check-no').change(function () {
        $('#pepInfo').hide();
    });

    $('#identification-id-type').val("{{relation.identification.identificationType}}");

    $('#add-news-check-yes').change(function () {
        $('#lbl-additionalComments').toggle(200);
        $('#pep-add-news').toggle(200);

    });
    $('#add-news-check-no').change(function () {
        $('#lbl-additionalComments').hide();
        $('#pep-add-news').hide();

    });


    $('input[name="electronicIdInfo[isElectronicId]"]').on('change', function () {
        const value = $(this).val() === 'YES';
        if (value){
            $('#naturalForm #electronic-id-files').show(200);
            $('#naturalForm #manual-id-info').hide();
            $('#naturalForm input[name="electronicIdInfo[emailType]"]').prop('required', true);
            $("#sendNewIdPalRequest").show();
        }
        else{
            $('#naturalForm #electronic-id-files').hide();
            $('#naturalForm #manual-id-info').show(200);
            $('#naturalForm input[name="electronicIdInfo[emailType]"]').prop('required', false);
            $('#naturalForm input[name="electronicIdInfo[email]"]').prop('required', false);
            $("#sendNewIdPalRequest").hide();
        }
    });

    $('input[name="electronicIdInfo[emailType]"]').on('change', function () {
        const isDefault = $(this).val() === 'default';
        if (isDefault){
            $('#naturalForm #free-email-id').hide();
            $('#naturalForm #default-email-id').show(200);
            $('#naturalForm input[name="electronicIdInfo[email]"]').prop('required', false);
        }
        else{
            $('#naturalForm #default-email-id').hide();
            $('#naturalForm #free-email-id').show(200);
            $('#naturalForm input[name="electronicIdInfo[email]"]').prop('required', true);
        }
    });

    $('input[name="electronicIdInfo[allowNewRequest]"]').on('change', function () {
        const value = $(this).is(':checked');
        $("#sendNewIdPalRequest").prop('disabled',value !== true);
    });
</script>

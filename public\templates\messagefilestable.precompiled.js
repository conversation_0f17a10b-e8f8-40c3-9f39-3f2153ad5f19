(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['messagefilestable'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"table-responsive\">\r\n        <table class=\"table table-striped mb-0\">\r\n            <thead>\r\n            <tr>\r\n                <th style=\"width: 60%\">Uploaded Files</th>\r\n                <th style=\"width: 20%\">Download</th>\r\n                <th style=\"width: 20%\">Delete</th>\r\n            </tr>\r\n            </thead>\r\n            <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"files") : depth0),{"name":"each","hash":{},"fn":container.program(2, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":12,"column":12},"end":{"line":32,"column":21}}})) != null ? stack1 : "")
    + "            </tbody>\r\n        </table>\r\n    </div> <!-- end .padding -->\r\n";
},"2":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.lambda, alias3=container.escapeExpression, alias4=container.hooks.helperMissing, alias5="function", lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <tr>\r\n                    <td>\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"originalName") : depth0),{"name":"if","hash":{},"fn":container.program(3, data, 0, blockParams, depths),"inverse":container.program(5, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":15,"column":24},"end":{"line":19,"column":31}}})) != null ? stack1 : "")
    + "</td>\r\n                    <td>\r\n                        <a type=\"button\" class=\"btn btn solid royal-blue\" target=\"_blank\"\r\n                           href=\"/client-management/announcements/"
    + alias3(alias2((depths[1] != null ? lookupProperty(depths[1],"id") : depths[1]), depth0))
    + "/files/"
    + alias3(((helper = (helper = lookupProperty(helpers,"fileId") || (depth0 != null ? lookupProperty(depth0,"fileId") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias1,{"name":"fileId","hash":{},"data":data,"loc":{"start":{"line":22,"column":82},"end":{"line":22,"column":92}}}) : helper)))
    + "\">\r\n                            Download</a>\r\n                    </td>\r\n                    <td>\r\n                        <button class=\"demo-delete-row btn btn-danger btn-xs btn-icon\"\r\n                                onclick=\"deleteMessageFile('"
    + alias3(alias2((depths[1] != null ? lookupProperty(depths[1],"id") : depths[1]), depth0))
    + "','"
    + alias3(((helper = (helper = lookupProperty(helpers,"fileId") || (depth0 != null ? lookupProperty(depth0,"fileId") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias1,{"name":"fileId","hash":{},"data":data,"loc":{"start":{"line":27,"column":72},"end":{"line":27,"column":82}}}) : helper)))
    + "')\">\r\n                            <i class=\"fa fa-times\"></i>\r\n                        </button>\r\n                    </td>\r\n                </tr>\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    var helper, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                            "
    + container.escapeExpression(((helper = (helper = lookupProperty(helpers,"originalName") || (depth0 != null ? lookupProperty(depth0,"originalName") : depth0)) != null ? helper : container.hooks.helperMissing),(typeof helper === "function" ? helper.call(depth0 != null ? depth0 : (container.nullContext || {}),{"name":"originalName","hash":{},"data":data,"loc":{"start":{"line":16,"column":28},"end":{"line":16,"column":44}}}) : helper)))
    + "\r\n";
},"5":function(container,depth0,helpers,partials,data) {
    var helper, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                            "
    + container.escapeExpression(((helper = (helper = lookupProperty(helpers,"originalname") || (depth0 != null ? lookupProperty(depth0,"originalname") : depth0)) != null ? helper : container.hooks.helperMissing),(typeof helper === "function" ? helper.call(depth0 != null ? depth0 : (container.nullContext || {}),{"name":"originalname","hash":{},"data":data,"loc":{"start":{"line":18,"column":28},"end":{"line":18,"column":44}}}) : helper)))
    + "\r\n                        ";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"files") : depth0),{"name":"if","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":36,"column":7}}})) != null ? stack1 : "");
},"useData":true,"useDepths":true});
})();
const express = require('express');
const router = express.Router();
const fileReviewerController = require('../controllers/file-reviews/fileReviewerController');
const relationController = require('../controllers/relations/relationController');
const uploadController = require('../controllers/uploadController');
const downloadController = require('../controllers/downloadController');
const positionController = require('../controllers/relations/positionController');
const qualityAssuranceController = require('../controllers/file-reviews/qualityAssuranceController');
const fileObserverController = require('../controllers/file-reviews/fileObserverController');
const complianceController = require('../controllers/file-reviews/complianceController');
const statisticsController = require('../controllers/file-reviews/statisticsController');


// GET file reviewer dashboard
router.get('/dashboard', ensureAuthenticated, fileReviewerController.getDashboard);
// POST file reviewer dashboard for filter
router.post('/file-review-list', ensureAuthenticatedFileReviewer, fileReviewerController.searchFileReview);
// GET file review list
router.get('/file-review-list', ensureAuthenticatedFileReviewer, fileReviewerController.getFileReviewList);

router.get('/reviews/:reviewId', ensureAuthenticatedFileReviewer, fileReviewerController.getReview);

router.delete('/reviews/:reviewId', ensureAuthenticatedFileReviewer, fileReviewerController.deleteReview);
//POST quality assurance approved filereview
router.post('/update-file-review', ensureAuthenticated, fileReviewerController.updateFileReview);

// POST AJAX pick submitted file review
router.post('/pick-file-review', ensureAuthenticatedFileReviewer, fileReviewerController.pickFileReview);

// POST AJAX send email notification
router.post('/send-file-review-notification', ensureAuthenticatedFileReviewer, fileReviewerController.sendFileReviewNotification);

// POST save comment
router.post('/reviews/:reviewId/add-comment', ensureAuthenticated, fileReviewerController.addComment);

// POST request electronic ids
router.post('/reviews/:reviewId/request-electronic-id', ensureAuthenticated, fileReviewerController.requestElectronicInfo);

//POST AJAX request files from client
router.post('/reviews/:reviewId/request-client-files', ensureAuthenticatedFileReviewer, fileReviewerController.requestClientFiles);

//POST AJAX request files from client
router.post('/reviews/:reviewId/cancel-request-client-files', ensureAuthenticatedFileReviewer, fileReviewerController.cancelRequestClientFiles);

// POST request electronic ids
router.post('/reviews/:reviewId/relations/:relationId/cancel-electronic-id', ensureAuthenticated, relationController.cancelElectronicInfoRequest);

//POST AJAX save submitted file review
router.post('/save-file-review/:companyId', ensureAuthenticatedFileReviewer, fileReviewerController.saveFileReview);



//POST UPLOAD FILE
router.post('/company-file-review/upload-document/:id', ensureAuthenticatedFileReviewer,
    uploadController.uploadFile.fields([{name: 'fileUploaded', maxCount: 5}]),
    uploadController.saveUpload
);
//GET UPLOADED FILES
router.get('/company-file-review/uploaded-files', ensureAuthenticated, fileReviewerController.getUploadedFiles);

//DELETE UPLOADED FILE
router.delete('/company-file-review/uploaded-files', ensureAuthenticatedFileReviewer, uploadController.deleteFile);

//POST UPDATE PROVIDED FIELD IN FILES
router.post('/company-file-review/update-file-provided-field', ensureAuthenticated, fileReviewerController.updateFileProvidedField);

// GET confirm-type of file to open
router.get('/confirm-type/:companyId', ensureAuthenticatedFileReviewer, fileReviewerController.verifyFileType);

// POST confirm-type redirects to GET open-file-review
router.post('/confirm-type/:companyId', ensureAuthenticatedFileReviewer, fileReviewerController.changeFileType);

// GET open file
router.get('/open-file-review/:companyId', ensureAuthenticatedFileReviewer, fileReviewerController.openFileReview);
// POST open beneficial owners table
router.post('/open-file-review/:companyId', ensureAuthenticatedFileReviewer, fileReviewerController.saveStandardPage);

// GET show owners table
router.get('/open-file-review/:companyId/beneficial-owners', ensureAuthenticatedFileReviewer, fileReviewerController.getRelations);

//POST temp store files
router.post('/company-positions/upload-document', ensureAuthenticatedFileReviewer,
    uploadController.uploadFile.fields([{name: 'fileUploaded', maxCount: 5}]),
    relationController.storeFiles
);

//POST submit file review
router.post('/submit-file-review', ensureAuthenticatedFileReviewer, fileReviewerController.submitFileReview);

//POST validate file review 
router.post('/open-file-review/:companyId/validate-review', ensureAuthenticatedFileReviewer, fileReviewerController.validateFileReview);

//POST edit percentage 
router.post('/open-file-review/:companyId/edit-percentage', ensureAuthenticatedFileReviewer, fileReviewerController.editPercentage);

//POST edit percentage
router.post('/reviews/:reviewId/update-status', ensureAuthenticated, fileReviewerController.changeReviewStatus);
// // GET peek relation (read-only modal)
// router.get('/peek-relation/:peekIndex',ensureAuthenticated,relationController.getRelationById);

// START RELATION ROUTES

// GET Search relation view
router.get('/reviews/:reviewId/relations/search/:type', ensureAuthenticatedFileReviewer, relationController.openSearchRelationView);

// POST Search relations by filters
router.post('/reviews/:reviewId/relations/search/:type', ensureAuthenticatedFileReviewer, relationController.getRelationsByFilters);

// GET add new relation to file review
router.get('/open-file-review/:companyId/relations/:relationGroups/add-relation', ensureAuthenticatedFileReviewer,
    relationController.openNewRelation);

// POST  save new relation
router.post('/open-file-review/:companyId/relations/:relationGroups/add-relation', ensureAuthenticatedFileReviewer,
    relationController.saveRelation);

// GET existing relation to edit review
router.get('/open-file-review/:companyId/:relationGroup/edit-relation/:type/:relationId', ensureAuthenticatedFileReviewer,
    relationController.openRelation);

// POST  save existing relation
router.post('/open-file-review/:companyId/:relationGroup/edit-relation/:type/:relationId', ensureAuthenticatedFileReviewer,
    relationController.saveRelation);

// GET template files to file review
router.get('/get-template-files', ensureAuthenticated,
    relationController.getTemplateFile);

// GET relations by group
router.get('/get-relation-template', ensureAuthenticatedFileReviewer,
    relationController.showRelationViewByGroup);

// DELETE existing relation
router.delete('/open-file-review/:companyId/relations/:relationId', ensureAuthenticatedFileReviewer,
    relationController.deleteRelation);

// GET existing relation
router.get('/peek-relation/:companyId', ensureAuthenticated, relationController.peekRelation);

// POST pick existing relation to client
router.post('/reviews/:reviewId/pick-relation/:type/:relationId', ensureAuthenticatedFileReviewer, relationController.assignPickedRelation);

// POST pick existing relation to client
router.get('/reviews/:reviewId/relations/:relationId/groups', ensureAuthenticated, relationController.getRelationGroupsByReview);

// POST existing relation
router.post('/reviews/:reviewId/relations/:relationId/send-electronic-invitation', ensureAuthenticatedFileReviewer,
  relationController.startNewElectronicIdRequest);



// END RELATION ROUTES //

// START POSITION ROUTES //

//GET search org position
router.get('/reviews/:reviewId/organizations/:organizationId/positions', ensureAuthenticatedFileReviewer, positionController.openSearchPosition);
//POST search org position by filters
router.post('/reviews/:reviewId/organizations/:organizationId/positions', ensureAuthenticatedFileReviewer, positionController.getPositionsByFilters);

//POST add org position
router.post('/reviews/:reviewId/organizations/:organizationId/positions/:relationId', ensureAuthenticatedFileReviewer, positionController.assignPickedPosition);

//GET add org position
router.get('/reviews/:reviewId/organizations/:organizationId/create-position', ensureAuthenticatedFileReviewer, positionController.openPosition);
//POST add org position
router.post('/reviews/:reviewId/organizations/:organizationId/create-position', ensureAuthenticatedFileReviewer, positionController.addPosition);

// GET peek position (read-only modal)
router.get('/reviews/:reviewId/relations/:relationId/positions/:positionId', ensureAuthenticated,
    positionController.peekPosition);

// GET edit org position view
router.get('/open-file-review/:reviewId/relations/:relationId/positions/:positionId/update', ensureAuthenticatedFileReviewer,
    positionController.openEditPosition);
// PUT edit org position
router.put('/open-file-review/:reviewId/relations/:relationId/positions/:positionId/update', ensureAuthenticatedFileReviewer,
    positionController.editPosition);

// DELETE edit org position
router.delete('/open-file-review/:reviewId/relations/:relationId/positions/:positionId', ensureAuthenticatedFileReviewer,
    positionController.deletePosition);

// //POST delete org position
// router.post('/delete-position/:orgId/:positionId', ensureAuthenticated, positionController.deletePositon);

// END POSITION ROUTES //

// START QUALITY ASSURANCE ROUTES //

// GET quality assurance list
router.get('/quality-assurance-list', ensureAuthenticatedQa, qualityAssuranceController.getQualityAssuranceList);

// POST quality assurance list
router.post('/quality-assurance-list', ensureAuthenticatedQa, qualityAssuranceController.getQualityAssuranceList);

// GET quality assurance review
router.get('/quality-assurance-review/:companyId', ensureAuthenticatedQa, qualityAssuranceController.openQualityReview);

// POST quality assurance review
router.post('/quality-assurance-review/:companyId', ensureAuthenticatedQa,
    qualityAssuranceController.saveStandardQualityReview);

//POST submit file review officer
router.post('/quality-assurance-review/:companyId/submit-review', ensureAuthenticatedQa, qualityAssuranceController.submitQualityAssuranceReview);

//POST validate file review officer
router.post('/quality-assurance-review/:companyId/validate-review', ensureAuthenticatedQa, qualityAssuranceController.validateQualityReview);

//POST submit file review officer
router.post('/quality-assurance-review/:companyId/relations/:relationId', ensureAuthenticatedQa, qualityAssuranceController.submitQualityReviewRelation);

// POST submit quality position information
router.post('/quality-assurance-review/:reviewId/organizations/:organizationId/positions/:positionId',
    ensureAuthenticatedQa, qualityAssuranceController.submitQualityPosition);

// GET show owners table
router.get('/quality-assurance-review/:companyId/relations', ensureAuthenticatedQa,
    qualityAssuranceController.getRelations);

// GET DOWNLOAD STANDARD FILES
router.get("/reviews/:reviewId/download/:type/:rowId/:fileId", ensureAuthenticated,
    downloadController.downloadFile);

// GET DOWNLOAD RELATION FILES
router.get("/reviews/:reviewId/relations/:relationId/download/:type/:rowId/:fileId", ensureAuthenticated,
    downloadController.downloadFile);

router.get(
    '/quality-assurance-review/:companyId/download-file/:type/:fileId',
    ensureAuthenticatedQa,
    downloadController.downloadReviewFiles
);

// GET DOWNLOAD RELATION FILES
router.get(
    '/quality-assurance-review/:companyId/download-file/:type/relation/:relationId/:fileId',
    ensureAuthenticatedQa, downloadController.downloadReviewFiles
);

router.get('/quality-assurance-review/:companyId/search-files',
    ensureAuthenticatedQa, qualityAssuranceController.getFilesByReview
);

// END QUALITY ASSURANCE ROUTES //

// START FILE OBSERVER ROUTES //

// GET quality assurance list
router.get('/file-observer/export', ensureAuthenticatedFileObserver, fileObserverController.exportReviews);

// GET file observer list
router.get('/file-observer-list', ensureAuthenticatedFileObserver, fileObserverController.getFileObserverList);

// POST file observer list
router.post('/file-observer-list', ensureAuthenticatedFileObserver, fileObserverController.getFileObserverList);

// GET file observer review
router.get('/file-observer-review/:companyId', ensureAuthenticatedFileObserver, fileObserverController.openFileReview);

// GET show owners table
router.get('/file-observer-review/:companyId/relations', ensureAuthenticatedFileObserver,
fileObserverController.getRelations);

// GET DOWNLOAD STANDARD FILES
router.get("/reviews/:reviewId/download/:type/:rowId/:fileId", ensureAuthenticated,
    downloadController.downloadFile);

// GET DOWNLOAD RELATION FILES
router.get("/reviews/:reviewId/relations/:relationId/download/:type/:rowId/:fileId", ensureAuthenticated,
    downloadController.downloadFile);

router.get(
    '/file-observer-review/:companyId/download-file/:type/:fileId',
    ensureAuthenticatedFileObserver,
    downloadController.downloadReviewFiles
);

// GET DOWNLOAD RELATION FILES
router.get(
    '/quality-assurance-review/:companyId/download-file/:type/relation/:relationId/:fileId',
    ensureAuthenticatedFileObserver, downloadController.downloadReviewFiles
);

router.get('/quality-assurance-review/:companyId/search-files',
ensureAuthenticatedFileObserver, qualityAssuranceController.getFilesByReview
);

// END FILE OBSERVER ROUTES //

// START COMPLIANCE ROUSTES //

// GET quality assurance list
router.get('/compliances', ensureAuthenticatedCompliance, complianceController.getComplianceList);

router.post('/compliances', ensureAuthenticatedCompliance, complianceController.getReviewsByFilters);

router.post('/compliances/pick', ensureAuthenticatedCompliance, complianceController.pickComplianceReview);

// GET compliance review
router.get('/compliances/:reviewId', ensureAuthenticatedCompliance, complianceController.openComplianceReview);

// GET compliance relations by review
router.get('/compliances/:reviewId/relations', ensureAuthenticatedCompliance, complianceController.getRelations);

// GET compliance relations by review
router.post('/compliances/:reviewId/validate', ensureAuthenticatedCompliance, complianceController.submitComplianceReview);

// POST clear officer assignation
router.post('/compliances/:reviewId/unassign-officer', ensureAuthenticatedCompliance, complianceController.unassignOfficer);
// END COMPLIANCE ROUTES //

// START STATISTICS ROUTES //
router.get('/statistics', ensureAuthenticated, statisticsController.getDashboard);

router.post('/statistics/export-xls', ensureAuthenticated, statisticsController.exportStatisticsXls);

// END STATISTICS ROUTES //


module.exports = router;

function ensureAuthenticated(req, res, next) {
    if (req.session.is_authenticated ) {
        if (req.session.authentication.isFileReviewer || 
            req.session.authentication.isCompliance ||
            req.session.authentication.isQualityAssurance ||
            req.session.authentication.isFileObserver
        ) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

function ensureAuthenticatedFileReviewer(req, res, next) {

    if (req.session.is_authenticated ) {
        if (req.session.authentication.isFileReviewer) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

function ensureAuthenticatedQa(req, res, next) {

    if (req.session.is_authenticated ) {
        if (req.session.authentication.isQualityAssurance) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

function ensureAuthenticatedFileObserver(req, res, next) {

    if (req.session.is_authenticated ) {
        if (req.session.authentication.isFileObserver) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

function ensureAuthenticatedCompliance(req, res, next) {
    if (req.session.is_authenticated ) {
        if (req.session.authentication.isCompliance) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

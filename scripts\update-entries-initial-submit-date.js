const EntryModel = require("../models/entry").EntryModel;
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
const fs = require('fs');

dotenv.config();


const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const data = new Uint8Array(fs.readFileSync('scripts/update-initial-date.xlsx'));
    const workbook = xlsx.read(data, {
        type: "array",
        cellText: false,
        cellDates: true,
        sheetStubs: true,
    });
    
    const result = await updateEntriesInitialSubmitDate(workbook);

    console.log('Script executed successfully', result);
  } catch (error) {
    console.error('Error executing the script:', error);
  } finally {
    mongoose.disconnect();
  }
}

function getEntriesRows(workbook) {
  try {
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const companies = [];

      for (let cell in worksheet) {
          const cellAsString = cell.toString();
          const rowNumber = Number(cellAsString.replace(/\D/g, ''));
          const dataStartRow = 2;
          const companyIndex = rowNumber - dataStartRow;
          const rowName = cellAsString.replace(/[0-9]/g, '');

          if (companyIndex >= 0 && worksheet[cell].v) {
            if (!companies[companyIndex]) {
                companies.push({
                    id: '',
                    submittedAt: '',
                    currentInitialDate: '',
                    code: '',
                    newInitialDate: '',
                    skipUpdate: false
                })
            }

            if (rowName === "A") {
                companies[companyIndex].id = worksheet[cell].v;
            }
            if (rowName === "B") {
                const date = worksheet[cell].v;
                companies[companyIndex].submittedAt = date ? new Date(moment(date).format('YYYY-MM-DDTHH:mm:ssZ')) : undefined;
            }
            if (rowName === "C") {
              const date = worksheet[cell].v;
              companies[companyIndex].currentInitialDate = date ? new Date(moment(date).format('YYYY-MM-DDTHH:mm:ssZ')) : undefined;
            } 
            if (rowName === "D") {
              companies[companyIndex].code =  worksheet[cell].v;
            }
            if (rowName === "E") {
              const date = worksheet[cell].v;
              companies[companyIndex].newInitialDate = date ? new Date(moment(date).format('YYYY-MM-DDTHH:mm:ssZ')) : undefined;
            } 
            if (rowName === "F") {
              companies[companyIndex].skipUpdate = worksheet[cell].v?.toString() !== '0' ? true : false ;
            } 
          }
      }
      return companies;
  } catch (e) {
      console.log("Error processing xlsx data: ", e);
      return []
  }

}


async function updateEntriesInitialSubmitDate(workbook) {
  try {

    let updateLog = [['Entry ID', 'Company', 'Update date', 'Old date', 'New date', 'Action']];

    const entriesToUpdate = getEntriesRows(workbook)

    console.log("entriesToUpdate length ", entriesToUpdate.length);


    if (entriesToUpdate.length > 0) {
      for (let i = 0; i < entriesToUpdate.length; i++) {
        console.log('processing ' + (i + 1) + '  from ' + entriesToUpdate.length)

        const entry = entriesToUpdate[i];
       
        try {
          if(entry.skipUpdate === true){
            updateLog.push([entry.id?.toString(), entry.code, new Date(), entry.currentInitialDate, entry.newInitialDate, 'SKIPPED']);
            continue;
          }

          const result = await EntryModel.updateOne({ _id: entry.id }, {
            $set: {
              initial_submit_date: entry.newInitialDate,
            }
          });

          if (result.nModified > 0) {
            updateLog.push([entry.id, entry.code, new Date(), entry.currentInitialDate, entry.newInitialDate, 'SUCCESS']);
          } else {
            updateLog.push([entry.id, entry.code, new Date(), entry.currentInitialDate, entry.newInitialDate, 'ERROR: NOT FOUND']);
          }
          
        } catch (error) {
          console.error('Error:', error.message);
          updateLog.push([entry.id, entry.code, new Date(),entry.currentInitialDate, entry.newInitialDate, 'ERROR UPDATING']);
        }
      }
    }

    console.log("Entries updated ", updateLog.length -1);

    const filename = 'update_entries_initial_submit_date_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'entries ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalRows": updateLog.length -1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}


runScript();
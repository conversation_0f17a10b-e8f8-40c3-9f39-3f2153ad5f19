<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            <span class="font-weight-bold">Add entity to file review</span>
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group row d-flex">
                            <label for="relationType" class="col-sm-4 col-form-label">
                                Search existing Natural Person or Organization
                            </label>
                            <div class="col-sm-3">
                                <select class="custom-select" id="relationType" name="relationType">
                                    <option id="natural-option" value="natural" {{#ifEquals type "natural"}}
                                            selected {{/ifEquals}}>Natural
                                    </option>
                                    <option id="corporate-option" value="organization" {{#ifEquals type "organization"}}
                                            selected {{/ifEquals}}>Organization
                                    </option>
                                </select>
                            </div>
                        </div>
                        <br>
                        <form id="searchRelationForm" method="POST" autocomplete="off" class="form"
                              action="/file-reviewer/reviews/{{ id }}/relations/search/{{type}}">
                            <p>Your query should contain at least three characters.</p>
                            <div class="form-row align-items-center">
                                {{#ifEquals type "natural"}}
                                    <div class="form-group col-md-3">
                                        <label for="name">Full name</label>
                                        <input type="text" class="form-control" name="name" id="name"
                                               value="{{filters.name}}">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="birthDate">Date of Birth</label>
                                        <input id="birthDate" name="birthDate" type="date" class="form-control"
                                               value="{{filters.birthDate}}"/>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="nationality">Nationality</label>
                                        {{>file-reviewer/shared/select-country selectId="nationality" value=filters.nationality}}
                                    </div>
                                {{else}}
                                    <div class="form-group col-md-3">
                                        <label for="organizationName">Organization name</label>
                                        <input type="text" class="form-control" name="organizationName"
                                               id="organizationName"
                                               value="{{filters.organizationName}}">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="incorporationNumber">Incorporation Number</label>
                                        <input type="text" class="form-control" name="incorporationNumber"
                                               id="incorporationNumber"
                                               value="{{filters.incorporationNumber}}">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="businessNumber">Business Registration Number</label>
                                        <input type="text" class="form-control" name="businessNumber"
                                               id="businessNumber"
                                               value="{{filters.businessNumber}}">
                                    </div>
                                {{/ifEquals}}
                                <div class="col-auto mt-2">
                                    <button type="submit" class="btn solid royal-blue">Search</button>
                                </div>
                            </div>
                        </form>

                        <br>
                        <div class="row">
                            <div class="col-md-12">
                                <table id="relation-datatable" class="table table-striped">
                                    <thead>
                                    {{#ifEquals type "natural"}}
                                        <tr>
                                            <th>Full Name</th>
                                            <th>First Name</th>
                                            <th>Middle Name</th>
                                            <th>Last Name</th>
                                            <th>Date of Birth</th>
                                            <th>Country of Birth</th>
                                            <th>Action</th>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <th>Organization Name</th>
                                            <th>Incorporation Number</th>
                                            <th>Business Registration Number</th>
                                            <th>Date of incorporation</th>
                                            <th>Action</th>
                                        </tr>
                                    {{/ifEquals}}
                                    </thead>
                                    <tbody>
                                    {{#each relations}}
                                        <tr>

                                            {{#ifEquals type "natural"}}
                                                <td>{{details.fullName}}</td>
                                                <td>{{details.firstName}}</td>
                                                <td>{{details.middleName}}</td>
                                                <td>{{details.lastName}}</td>
                                                <td>{{#formatDate details.birthDate "YYYY-MM-DD"}} {{/formatDate }}</td>
                                                <td>{{details.countryBirth}}</td>

                                            {{else}}
                                                {{#ifEquals type "other-bo-director"}}
                                                    <td>{{details.fullName}}</td>
                                                    <td>{{details.firstName}}</td>
                                                    <td>{{details.middleName}}</td>
                                                    <td>{{details.lastName}}</td>
                                                    <td>{{#formatDate details.birthDate
                                                                      "YYYY-MM-DD"}} {{/formatDate }}</td>
                                                    <td>{{details.countryBirth}}</td>
                                                {{else}}
                                                    <td>{{details.organizationName}}</td>
                                                    <td>{{details.incorporationNumber}}</td>
                                                    <td>{{details.businessNumber}}</td>
                                                    <td>{{#formatDate details.incorporationDate
                                                                      "YYYY-MM-DD"}} {{/formatDate }}</td>
                                                {{/ifEquals}}

                                            {{/ifEquals}}


                                            <td>
                                                {{#ifCond positionGroups.length '<' 3}}
                                                    <button
                                                            type="button"
                                                            class="btn solid royal-blue"
                                                            data-toggle="modal"
                                                            data-target="#pickRelationModal"
                                                            id="btn-{{_id}}"
                                                            data-relation-id="{{ _id }}"
                                                            data-review-id="{{../id}}"
                                                            data-type="{{type}}"
                                                            data-position-groups="{{positionGroups}}"
                                                    >
                                                        Pick
                                                    </button>
                                                {{/ifCond}}

                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            {{#ifEquals type "natural"}}
                                                <td colspan="7" class="text-center font-italic">There are no relations
                                                </td>
                                            {{else}}
                                                <td colspan="5" class="text-center font-italic">There are no relations
                                                </td>
                                            {{/ifEquals}}

                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-md-12">
                                <p>
                                    Your search results contains all Clients from Trident and all relations that have
                                    been
                                    added manually. If your search result does not include the entity you are looking
                                    for,
                                    you can create one manually by clicking on the New button.
                                </p>
                            </div>
                        </div>

                    </div>
                    <div class="row mt-4 justify-content-between">
                        <div class="col-2">
                            <a
                                    href="/file-reviewer/open-file-review/{{ id }}/beneficial-owners"
                                    class="btn btn-secondary width-lg waves-effect waves-light"
                            >
                                Back
                            </a>
                        </div>
                        {{#if canCreateRelation}}
                            <div class="col-2 d-flex justify-content-end">
                                <button
                                        type="submit"
                                        data-toggle="modal"
                                        data-target="#newRelationModal"
                                        class="btn solid royal-blue px-4"
                                        data-review-id="{{ id }}"
                                >
                                    New
                                </button>
                            </div>
                        {{/if}}

                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>file-reviewer/review-relations/modals/create-relation-modal}}
{{>file-reviewer/review-relations/modals/pick-relation-modal}}

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/showrelationtypeform.precompiled.js"></script>
<script>
    let existingSelected = "";
    let $relationType = $('#relationType');
    let typeSelected = $('#relationType option:selected').val();

    $(document).ready(function () {
        $("#relation-datatable").DataTable({
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>",
                },
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });

    });


    $relationType.on('change', function () {
        typeSelected = $('#relationType option:selected').val();
        existingSelected = '';

        $('#empty-relation-option').prop('selected', true);
        const url = window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/'));

        window.location.href = window.location.origin + url + "/" + typeSelected;

    });
</script>


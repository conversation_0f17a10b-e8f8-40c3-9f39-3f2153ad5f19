const CompanyModel = require("../models/company").schema;
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config();

const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const result = await updateAccountingCompliantStatus();

    console.log('Script executed successfully', result);
  } catch (error) {
    console.error('Error in the script:', error);
  } finally {
    mongoose.disconnect();
  }
}


async function updateAccountingCompliantStatus() {
  let updateLog = [['ID', 'Company Code', 'Update date', 'Action']];
  try {

    
    const lookupFinancialReports = {
      from: "financialreportsbspls",
      localField: "code",
      foreignField: "companyData.code",
      as: "financialReports",
    };

    const searchProjection = {
        _id: 1,
        code: 1,
        name:1, 
        masterclientcode: 1,
        incorporationdate: 1,
        accountingRecordsModule: 1,
        compliantStatus: 1,
        financialReports: {
            "$map": {
                "input": "$financialReports",
                "as": "f",
                "in": {
                    "_id": "$$f._id",
                    "status": "$$f.status",
                    "financialPeriod": "$$f.financialPeriod",
                    "submittedAt": "$$f.submittedAt",
                }
            } 
        }
    };

    // Get submissions with accounting records module active
    let accountingCompaniesResults = await CompanyModel.aggregate([
        { $match: { "accountingRecordsModule.active": true }},
        { $lookup: lookupFinancialReports },
        { $project: searchProjection },
    ]);


    console.log(`Total companies ${accountingCompaniesResults.length}`);

    for (let index = 0; index < accountingCompaniesResults.length; index++) {
      const company = accountingCompaniesResults[index];
      try {
      

      //console.log(`Start processing (${index}) - company ${company.code}`);
      let accountingRecordsModule = company.accountingRecordsModule ?? {};

      let debug = company.code == '**********';
      
      // get the current deadline for the company
      const deadline = getAccountingDeadline(accountingRecordsModule, company.financialReports, debug);
      //console.log(`company ${company.code} - deadline: ${deadline}`)
      if (deadline == null || deadline.newAccountingDeadline === null) {
          //console.log(`Cannot get accounting deadline for company ${company.code}, confirmed FP end date is empty or invalid: ${accountingRecordsModule?.firstFinancialPeriodEnd}`);
          updateLog.push([company._id?.toString(), company.code, company.accountingRecordsModule.currentDeadline, 'Cannot get accounting deadline for company']);
          continue;
      }

      // update currentDeadline if not exists in company 
      
      //if (!accountingRecordsModule?.currentDeadline || 
      //    (accountingRecordsModule.currentDeadline && moment(accountingRecordsModule.currentDeadline).utc().format('YYYY-MM-DD') !== deadline.newAccountingDeadline.format('YYYY-MM-DD'))) {

          //console.log(`UPDATE DEADLINE: current deadline ${moment(accountingRecordsModule.currentDeadline).utc().format('YYYY-MM-DD')} - new dealine: ${deadline.format('YYYY-MM-DD') }`)



          //let result = await CompanyModel.findByIdAndUpdate(company._id, {
          //  $set: { "accountingRecordsModule.currentDeadline": deadline.newAccountingDeadline, "accountingRecordsModule.currentFilingDeadline": deadline.newFilingDeadline}          
          //});
          //console.log(result)
          //if (result) {
            updateLog.push([company._id?.toString(), company.code, company.accountingRecordsModule.currentDeadline, 'UPDATED to ' + deadline.newAccountingDeadline.toDate().toISOString(), company.accountingRecordsModule.currentFilingDeadline, 'UPDATED to ' + deadline.newFilingDeadline]);          
          //} 
          //else {
          //  updateLog.push([company._id?.toString(), company.code, new Date(), 'ERROR: NOT FOUND']);
          //}   

      //} else {
      //  updateLog.push([company._id?.toString(), company.code, company.accountingRecordsModule.currentDeadline, 'Deadline already correct']);
      //}    
          
      } catch  (error) {
        console.log(error)
        updateLog.push([company._id?.toString(), company.code, company.accountingRecordsModule.currentDeadline, 'ERROR ' + error.message]);
      }
    }
  } catch (error) {
    console.error('Error:', error.message);
    updateLog.push(["XXX", "XXX", new Date(), 'ERROR UPDATING' + error.message]);
  }


  // create entities bo
  console.log("companies updated ", updateLog.length - 1);

  const filename = 'set_company_new_deadline_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
  const logWorkbook = xlsx.utils.book_new();
  const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

  xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'companies ' + moment.utc().format('YYYY-MM-DD'));
  xlsx.writeFile(logWorkbook, filename);

  return { "success": true, "totalRows": updateLog.length - 1 };
}


function getAccountingDeadline(accountingModule, financialReports, debug){

  if (!accountingModule || !accountingModule?.firstFinancialPeriodEnd){
      return null;
  }

  let returnObject = {
    newAccountingDeadline: null,
    newFilingDeadline: null
  };

  const availableReports = financialReports.filter((r) => r.status !== "DELETED");

  let confirmedPeriodStart = accountingModule.firstFinancialPeriodStart;
  let confirmedPeriodEnd = accountingModule.firstFinancialPeriodEnd;
  let currentAccountingDeadline = null;
  

  const PENDING_STATUS = ["SAVED", "IN PROGRESS", "IN PENALTY"];

  if (debug) console.log(availableReports);

  // get current deadline saved in accountingModule
  if (accountingModule?.currentDeadline && accountingModule?.currentDeadline !== null){
      currentAccountingDeadline = moment(accountingModule.currentDeadline).utc();

      //console.log(`current deadline - ${accountingModule.currentDeadline ? moment(accountingModule.currentDeadline).utc() : 'none'}`);
  }

  // if not reports created then deadline is confirmed end period + 18 months
  if (availableReports.length === 0){      
  
      if (moment(confirmedPeriodStart).isBefore(moment("2024-01-01").utc().startOf('day'))) {        
        returnObject.newAccountingDeadline = moment(confirmedPeriodEnd).utc().add(18, 'months');
        returnObject.newFilingDeadline = moment(confirmedPeriodEnd).utc().add(18, 'months');
      } else {
        returnObject.newAccountingDeadline = moment(confirmedPeriodEnd).utc().add(9, 'months');
        returnObject.newFilingDeadline = moment(confirmedPeriodEnd).utc().add(9, 'months');
      }
      

      if (debug) console.log(returnObject);
      //TODO if confirmedPeriod ends on or before 30/09/2024, deadline will be 30/06/2025, otherwise + 9 months
  }
  // if company has only 1 report created
  else if (availableReports.length === 1){
     

      const existingReport = availableReports[0];
      if (debug) console.log(existingReport);
      const pendingSubmitReport = PENDING_STATUS.includes(existingReport.status);

      // if report is still pending to confirm submit then deadline is report end period + 9 months
      if (pendingSubmitReport){         
          if (debug) console.log(moment(existingReport.financialPeriod.start).utc().toString() + "  " + (moment(existingReport.financialPeriod.start).utc().isBefore(moment("2024-01-01").utc().startOf('day'))))
          if (moment(existingReport.financialPeriod.start).utc().isBefore(moment("2024-01-01").utc().startOf('day'))) {            
            returnObject.newAccountingDeadline = moment(existingReport.financialPeriod.end).utc().add(18, 'months');
            returnObject.newFilingDeadline = moment(existingReport.financialPeriod.end).utc().add(18, 'months');
          } else {
            returnObject.newAccountingDeadline = moment(existingReport.financialPeriod.end).utc().add(9, 'months');
            returnObject.newFilingDeadline = moment(existingReport.financialPeriod.end).utc().add(9, 'months');
          }          
          
          if (debug) console.log(returnObject);
      }
      // if is confirmed submission then deadline must be report end period + 1 year + 9 months
      else{
          const nextFPStartDate = moment(existingReport.financialPeriod.end).utc().add('1', 'days');
          const nextFPEndDate = moment(existingReport.financialPeriod.end).add(1, 'years');

          if (debug) console.log(nextFPEndDate);
         
          if (moment(nextFPStartDate).utc().isBefore(moment("2024-01-01").utc().startOf('day'))) {
            returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months');
            returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months');
          } else {
            returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months');
            returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months');
          }
          
          if (debug) console.log(returnObject);
      }
  }
  // if company has more than 1 reports created
  else{
      // get the last valid submitted report
      const submittedReports = availableReports.filter((r) => r.submittedAt && !PENDING_STATUS.includes(r.status));
      submittedReports.sort((a, b) => b.financialPeriod?.end - a.financialPeriod?.end);
      let lastFinancialReport = submittedReports[0];

      if (debug) console.log(lastFinancialReport);

      // the deadline should be the last submitted report end period + 1 year + 9 months      
      const nextFPStartDate = moment(lastFinancialReport.financialPeriod.end).utc().add('1', 'days');
      const nextFPEndDate = moment(lastFinancialReport.financialPeriod.end).add(1, 'years');
      //newAccountingDeadline = moment(nextFPEndDate).utc().add(9, 'months');
      if (debug) console.log(nextFPEndDate);

      if (moment(nextFPStartDate).utc().isBefore(moment("2024-01-01").utc().startOf('day'))) {
        returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months');
        returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months');
      } else {
        returnObject.newAccountingDeadline = moment(nextFPEndDate).utc().add(18, 'months');
        returnObject.newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months');
      }  
      if (debug) console.log(returnObject);
  }
  
  // compare if is the same date, then return the current saved date
  if (currentAccountingDeadline && currentAccountingDeadline.format('YYYY-MM-DD') === returnObject.newAccountingDeadline.format('YYYY-MM-DD')){
      returnObject.newAccountingDeadline =  currentAccountingDeadline
  }

  return returnObject;
}




runScript();
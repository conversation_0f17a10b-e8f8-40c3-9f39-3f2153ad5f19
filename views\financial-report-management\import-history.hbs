<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h2>{{ title }}</h2>
                    </div>
                    <!-- CONTENT -->
                    <div class="card-body">
                        <div class="row">
                            <h5 class="pb-1">HISTORY:</h5>
                            <div class="table-responsive">
                                <table id="import-directors-datatable"
                                    class="table table-striped w-100 nowrap">
                                    <thead>
                                        <tr>
                                            <th style="width: 50%;">FILE</th>
                                            <th style="width: 10%;">STATUS</th>
                                            <th style="width: 30%;">CREATED AT</th>
                                            <th style="width: 30%;">DOWNLOAD FILE</th>
                                            <th style="width: 30%;">DOWNLOAD RESULT FILE</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {{#each importFiles}}
                                            <tr class="text-left">
                                                <td>{{name}}</td>
                                                <td>{{status}}</td>
                                                <td>
                                                     {{createdAt}}
                                                </td>
                                                <td>
                                                    <a href="/financial-report-management/import-files/download/{{name}}?pathname={{importFilePath}}" target="_blank"
                                                        class="btn btn-primary waves-effect waves-light">
                                                        Download
                                                    </a>
                                                </td>
                                                 <td>
                                                    {{#ifCond status '===' 'FINISHED'}}
                                                    <a href="/financial-report-management/import-files/download/{{name}}?pathname={{resultFilePath}}" target="_blank"
                                                        class="btn btn-primary waves-effect waves-light">
                                                        Download
                                                    </a>
                                                    {{/ifCond}}
                                                </td>
                                                    
                                            </tr>
                                        {{else}}
                                            <tr>
                                                <td colspan="5" class="text-center font-italic">There are no files imported
                                                </td>
                                            </tr>
                                        {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                    </div>
                    <!-- CONTENT END -->
                    <div class="row mt-2">
                        <div class="col-md-12 d-flex justify-content-between">
                            <a href="/financial-report-management" class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>
                             <a href="/financial-report-management/import-files/new" class="btn  solid royal-blue">New Import</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>


<script type="text/javascript">

    let table;
    $(document).ready(function () {

        table = $("#import-directors-datatable").DataTable({
            "pageLength": 50,
            "order": [[1, "des"]],
            scrollX: !0,
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>"
                }
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
            }
        });
    });
</script>

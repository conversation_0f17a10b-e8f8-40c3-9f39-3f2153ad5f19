<div id="limitedPartnershipForm">
    <!-- CORPORATE DETAILS -->
    <div id="limitedPartnershipDetails">
        <div class="row mt-3">
            <div class="col-2">
                <label for="limitedPartnership-details-organization-name">Organization Name*</label>
            </div>
            <div class="col-4">
                <input type="text" class="form-control"  id="limitedPartnership-details-organization-name"  name="details[organizationName]"
                       value="{{relation.details.organizationName}}" required/>
            </div>
            <div class="col-2">
                <label for="limitedPartnership-details-incorporation-number">Incorporation / Formation Number</label>
            </div>
            <div class="col-4">
                <input id="limitedPartnership-details-incorporation-number" class="form-control" type="text" name="details[incorporationNumber]"
                       value="{{relation.details.incorporationNumber}}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="limitedPartnership-details-tax-residence">Tax Residence</label>
            </div>
            <div class="col-4">
<!--                <input id="limitedPartnership-details-tax-residence" class="form-control" type="text" name="details[taxResidence]"-->
<!--                       value="{{relation.details.taxResidence}}"/>-->
                {{>file-reviewer/shared/select-country selectId="details[taxResidence]"
                        value=relation.details.taxResidence}}
            </div>
            <div class="col-2">
                <label for="limitedPartnership-details-registration-number"
                >Business Registration Number (if applicable)</label
                >
            </div>
            <div class="col-4">
                <input class="form-control" type="text"  id="limitedPartnership-details-registration-number" name="details[businessNumber]"
                       value="{{relation.details.businessNumber}}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="limitedPartnership-details-incorporation-date">Date of Incorporation</label>
            </div>
            <div class="col-4">
                <input class="form-control" type="date" id="limitedPartnership-details-incorporation-date" name="details[incorporationDate]"
                       value="{{#formatDate relation.details.incorporationDate "YYYY-MM-DD"}} {{/formatDate }}"/>
            </div>
            <div class="col-2">
                <label for="details[incorporationCountry]">Country of Incorporation</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[incorporationCountry]"
                        value=relation.details.incorporationCountry}}
            </div>
        </div>

        <!-- DETAILS TABLE -->
        {{>file-reviewer/shared/relation-file-table tableId="detailsTable"  name="details" group="limitedPartnership"
                files=(ternary newRelation relation.limitedFiles.details relation.details.files)
                relationId=relation._id}}

        <!-- DETAILS PARTNER TABLE -->
        {{>file-reviewer/shared/certificate-partner-table group="limited"
                partnerFiles=(ternary newRelation relation.limitedFiles.detailsPartner relation.detailsPartner.files)
                relationId=relation._id}}

        <div class="row pt-3">
            <div class="col-12 d-flex justify-content-end">
                <div class="custom-control custom-checkbox">
                    <input
                            type="checkbox"
                            class="custom-control-input completeCheck"
                            name="details[correct]"
                            id="limitedPartnership-details-correct"
                        {{#if relationInformation.details.complete}} checked {{/if}}
                    />
                    <label class="custom-control-label" for="limitedPartnership-details-correct"
                    >Complete Information</label
                    >
                </div>
            </div>
        </div>
    </div>
    <hr class="mt-2"/>

    <!-- PRINCIPAL ADDRESS DETAILS -->
    <div id="principalAddressDetails">
        <h4>Principal Address</h4>
        {{>file-reviewer/review-relations/sections/address-details-form group="limitedPartnership"
                principalAddress=relation.principalAddress formType="principalAddress"
                relationInformation=relationInformation.principalAddress}}
    </div>
    <hr class="mt-2" />

    <!-- MAILING ADDRESS DETAILS -->
    <div id="mailingAddressDetails">
        <h4>Mailing Address</h4>
        {{>file-reviewer/review-relations/sections/address-details-form group="limitedPartnership"
                principalAddress=relation.mailingAddress formType="mailingAddress"
                relationInformation=relationInformation.mailingAddress}}
    </div>
    <hr class="mt-2" />

    <!-- LISTED COMPANY DETAILS -->
    <div class="listedCompanyDetails">
        {{>file-reviewer/review-relations/sections/listed-company-details-form group="limitedPartnership"
                listedCompany=relation.listedCompanyDetails}}
    </div>
    <hr class="mt-2" />

    <!-- LIMITED COMPANY DETAILS -->
    <div class="limitedCompanyDetails">
        {{>file-reviewer/review-relations/sections/limited-company-details-form group="limitedPartnership" showTable="true"
                limitedCompany=(ternary newRelation relation.limitedFiles.limitedCompany relation.limitedCompanyDetails)}}
    </div>
    <hr class="mt-2" />

    <!-- MUTUAL FUND DETAILS -->
    <div class="mutualFundDetails">
        {{>file-reviewer/review-relations/sections/mutual-fund-details-form group="limitedPartnership"
                mutualFund=(ternary newRelation relation.limitedFiles.mutualFund relation.mutualFundDetails)
                relationId=relation._id}}
    </div>
    <hr class="mt-2" />

    <!--WORLD CHECK DETAILS -->
    <div  id="worldCheckSection" >
        {{>file-reviewer/review-relations/sections/world-check-details-form group="limitedPartnership"
                worldCheck=(ternary newRelation relation.limitedFiles.worldCheck relation.worldCheck)
                relationInformation=relationInformation.worldCheck }}
    </div>

</div>

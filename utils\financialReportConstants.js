exports.ACCOUNTING_SERVICE_TYPES = {
    SELF_SERVICE_COMPLETE: "self-service-complete",
    SELF_SERVICE_PREPARE: "self-service-prepare",
    TRIDENT_SERVICE_COMPLETE: "trident-service-complete",
    TRIDENT_SERVICE_DROP: "trident-service-drop"
}

exports.REPORT_STATUS = {
    SAVED: "SAVED",
    IN_PROGRESS: "IN PROGRESS",
    UNDER_REVIEW: "UNDER REVIEW",
    CONFIRMED: "CONFIRMED",
    PAID: "PAID",
    RE_OPEN: "RE-OPEN",
    DELETED: "DELETED",
    REQUEST_HELP: "HELP REQUEST",
    HELP_PROGRESS: "HELP IN PROGRESS",
    REQUEST_INFO: "INFORMATION REQUEST",
    HELP_COMPLETED: "HELP COMPLETED",
    IN_PENALTY: "IN PENALTY"
}


exports.REPORT_REQUEST_INFO_STATUS = {
    REQUESTED: "REQUESTED",
    CANCELLED: "CANCELLED",
    RETURNED: "RETURNED",
}

exports.REPORT_FORM_PAGES = {
    INCOME_EXPENSES_PAGE: 1,
    ASSETS_LIABILITIES_PAGE: 2
}

exports.AR_DEFAULT_SORTING_ITEMS = [
    {
        name: "Creation date",
        code: "CRE-DATE",
        dbField: "createdAt"
    },
    {
        name: "Entity name",
        code: "ENT-NAME",
        dbField: "companyData.name"
    }, 
    {
        name: "Email",
        code: "EMAIL",
        dbField: "createdBy"
    },
    {
        name: "FP end date",
        code: "FP-END",
        dbField: "financialPeriod.end"
    },
    {
        name: "Master client code",
        code: "MCC",
        dbField: "masterClientCode"
    },
    {
        name: "Status",
        code: "STATUS",
        dbField: "status"
    },
    {
        name: "Submitted date",
        code: "SUBM-DATE",
        dbField: "submittedAt"
    }
]


exports.AR_COMPANY_DEFAULT_SORTING_ITEMS = [
    {
        name: "Company code",
        code: "ENT-CODE",
        dbField: "code"
    },
    {
        name: "Company name",
        code: "ENT-NAME",
        dbField: "name"
    },
    {
        name: "Referral office",
        code: "REF-OFFICE",
        dbField: "referral_office"
    },
    {
        name: "Master client code",
        code: "MCC",
        dbField: "masterclientcode"
    },
    {
        name: "Incorporation code",
        code: "INC-CODE",
        dbField: "incorporationcode"
    },
    {
        name: "Incorporation date",
        code: "INC-DATE",
        dbField: "incorporationdate"
    },
    {
        name: "Initial FP end date",
        code: "IFP-DATE",
        dbField: "accountingRecordsModule.firstFinancialPeriodEnd"
    }, 
    {
        name: "Deadline date",
        code: "DEADLINE",
        dbField: "accountingRecordsModule.currentDeadline"
    }
]


exports.FORM_COMPLETE_INCOME_EXPENSES_INPUT_NUMBER_VALIDATION_ERRORS = [
    { "input": "revenue", "validation": "num-positive", "error": "Provide a positive value to revenue" },
    { "input": "costOfSales", "validation": "num-positive", "error": "Provide a positive value to cost of sales" },
    { "input": "operatingExpenses", "validation": "num-positive", "error": "Provide a positive value to operating expenses" },
    { "input": "totalOtherExpenses", "validation": "num-positive", "error": "Provide a positive value to other expenses" },
    { "input": "incomeTax", "validation": "num-positive", "error": "Provide a positive value to income tax expenses" }
]

exports.FORM_COMPLETE_ASSETS_LBT_INPUT_NUMBER_VALIDATION_ERRORS = [
    { "input": "cashAmount", "validation": "num-positive", "error": "Provide a valid value to cash and cash equivalents" },
    { "input": "loansAndReceivables", "validation": "num-positive", "error": "Provide a valid value to loans and receivables" },
    { "input": "investmentsAssetsAmount", "validation": "num-positive", "error": "Provide a valid value to investments and other financial assets" },
    { "input": "fixedAssetsAmount", "validation": "num-positive", "error": "Provide a valid value to fixed assets" },
    { "input": "intangibleAssetsAmount", "validation": "num-positive", "error": "Provide a valid value to intangible assets" },
    { "input": "totalOtherAssets", "validation": "num-positive", "error": "Provide a positive value to other assets" },
    { "input": "accountsPayable", "validation": "num-positive", "error": "Provide a valid value to accounts payable " },
    { "input": "longTermDebts", "validation": "num-positive", "error": "Provide a valid value to long-term debts" },
    { "input": "totalOtherLiabilities", "validation": "num-positive", "error": "Provide a positive value to other liabilities" },
]

exports.ACCOUNTING_SERVICE_TYPES_LABELS = {
    "self-service-complete": "1.2.1 Self Service: Complete the Company's Annual Return in the prescribed format",
    "self-service-prepare": "1.2.2 Self Service: Prepare the Company's Annual Return using the Trident Accounting Portal",
    "trident-service-complete": "1.2.3 Trident Service: Reformatting existing annual accounts, to the prescribed format, and submission (starting at $350)",
    "trident-service-drop": "1.2.4 Trident Service: Preparation of the accounts including a statement of Income and Expenses, and submission of the annual return(starting at $600)"
}

<main class="">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card">
                    <form method="POST" id="submitForm">
                        <div class="card-body">
                            <h1>{{title}}</h1>
                            <br /><br />
                            <table id="scroll-horizontal-datatable" class="table w-100 nowrap">
                                <thead>
                                    <tr>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Active</th>
                                        <th>Clear Two-factor authentication</th>
                                        <th>Block / Unlock user</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {{#each result}}
                                    <tr>
                                        <td>{{username}}</td>
                                        <td>{{email}}</td>
                                        <td>{{#if active}}Yes{{else}}No{{/if}}</td>
                                        <td>
                                            {{#if mfa_type}}
                                            <input type="button" class="btn btn-primary waves-effect waves-light"
                                                id="reset2faButton" value="Clear" onclick="clear2faUser('{{id}}', '{{email}}')">
                                            {{else}}
                                            {{#if secret_2fa}}
                                            <input type="button" class="btn btn-primary waves-effect waves-light"
                                                id="reset2faButton" value="Clear" onclick="clear2faUser('{{id}}', '{{email}}')">
                                            {{/if}}
                                            {{/if}}
                                        </td>
                                        <td>
                                            {{#if locked}}
                                            <input type="button" class="btn btn-primary waves-effect waves-light"
                                                id="unlockButton" value="Unlock" onclick="unlockUser('{{id}}', '{{email}}')">
                                            {{else}}
                                            <input type="button" class="btn btn-primary waves-effect waves-light"
                                                id="unlockButton" value="Block" onclick="BlockUser('{{id}}', '{{email}}')">
                                            {{/if}}
                                        </td>
                                    </tr>
                                    {{/each}}
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-12">
                            <div class="row">
                                <div class="mb-3 ml-3">
                                    <a href='/client-management/'
                                        class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript">
    $(document).ready(function () {

        table = $("#scroll-horizontal-datatable").DataTable({ "pageLength": 50, "order": [[1, "asc"]], scrollX: !0, language: { paginate: { previous: "<i class='mdi mdi-chevron-left'>", next: "<i class='mdi mdi-chevron-right'>" } }, drawCallback: function () { $(".dataTables_paginate > .pagination").addClass("pagination-rounded") } });
    });


    function unlockUser(id, email) {
        $.ajax({
            type: "POST",
            url: "./search-users/unlock",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({id, email}),
            success: function (data) {
                if (data.success) {
                    toastr.success('User updated successfully');
                    window.setTimeout(function () {
                        document.location.reload();
                    }, 200)
                } else {
                    toastr["warning"]('Sorry, there was an error updating the user.');
                }
            }
        });
    }

    function BlockUser(id, email) {
            $.ajax({
                type: "POST",
                url: "./search-users/block",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify({id, email}),
                success: function (data) {
                    if (data.success) {
                        toastr.success('User updated successfully');
                        window.setTimeout(function () {
                            document.location.reload();
                        }, 200)
                    } else {
                        toastr["warning"]('Sorry, there was an error updating the user.');
                    }
            }
        });
    }

    
    function clear2faUser(id, email) {
        $.ajax({
            type: "POST",
            url: "./search-users/clear-2fa",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({id, email}),
            success: function (data) {
                if (data.success) {
                    toastr.success('User updated successfully');
                    window.setTimeout(function () {
                        document.location.reload();
                    }, 200)
                } else {
                    toastr["warning"]('Sorry, there was an error updating the user.');
                }
            }
        });
    }

</script>
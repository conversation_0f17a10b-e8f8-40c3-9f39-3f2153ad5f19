<form id="qualityPositionForm" method="POST" autocomplete="off" action="#">
    <!--PERSONAL DETAILS -->
    <div>
        <p>
            DETAILS
            {{#ifCond position.lockedByFileReview '==' reviewId}}
                {{#if positionInformation.details.complete}}
                    <span class="badge badge-success">Complete</span>
                {{else}}
                    <span class="badge badge-warning text-dark">Incomplete</span>
                {{/if}}
            {{/ifCond}}
        </p>
        <div class="row">
            <div class="col-2">Full Name:</div>
            <div class="col-4 font-weight-bold">{{ position.details.fullName }}</div>
            <div class="col-2">First Name:</div>
            <div class="col-4 font-weight-bold">{{ position.details.firstName }}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">Middle Name:</div>
            <div class="col-4 font-weight-bold">{{ position.details.middleName }}</div>
            <div class="col-2">Last Name:</div>
            <div class="col-4 font-weight-bold">{{ position.details.lastName }}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">Occupation:</div>
            <div class="col-4 font-weight-bold">{{ position.details.occupation }}</div>
            <div class="col-2">Date of Birth:</div>
            <div class="col-4 font-weight-bold">{{#formatDate position.details.birthDate
                                                              "MM/DD/YYYY"}} {{/formatDate }}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">Nationality:</div>
            <div class="col-4 font-weight-bold">{{ position.details.nationality }}</div>
            <div class="col-2">Country of Birth:</div>
            <div class="col-4 font-weight-bold">{{ position.details.countryBirth }}</div>
        </div>
        <div class="row">
            <div class="col-12 d-flex justify-content-end">
                {{#ifCond position.lockedByFileReview '==' reviewId}}
                    <div class="custom-control custom-checkbox">
                        <input
                                type="checkbox"
                                class="custom-control-input"
                                name="details[validated]"
                                id="details[validated]-{{ positionInformation.details.validated}}"
                            {{#if positionInformation.details.validated}} checked {{/if}}
                            {{#if onlyRead}} disabled {{/if}}
                        />
                        <label class="custom-control-label"
                               for="details[validated]-{{ positionInformation.details.validated}}"
                        >Validate</label
                        >
                    </div>
                {{else}}
                    <i class="fa fa-lock"></i>
                {{/ifCond}}
            </div>
        </div>
        <hr class="mt-3"/>
    </div>

    <!--IDENTIFICATION DETAILS -->
    <div>
        <p>
            IDENTIFICATION
            {{#ifCond position.lockedByFileReview '==' reviewId}}
                {{#if positionInformation.identification.complete}}
                    <span class="badge badge-success">Complete</span>
                {{else}}
                    <span class="badge badge-warning text-dark">Incomplete</span>
                {{/if}}
            {{/ifCond}}
        </p>
        <div class="row">
            <div class="col-2">Type of Identification:</div>
            <div class="col-4 font-weight-bold" style="text-transform: capitalize;">
                {{  position.identification.identificationType }}
            </div>
            <div class="col-2">Country of Issue:</div>
            <div class="col-4 font-weight-bold">{{  position.identification.issueCountry }}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">Expiry Date:</div>
            <div class="col-4 font-weight-bold">{{#formatDate position.identification.expiryDate
                                                              "MM/DD/YYYY"}} {{/formatDate }}</div>
        </div>
        <!-- CHECK VALIDATE -->
        <div class="row">
            <div class="col-12 d-flex justify-content-end">
                {{#ifCond position.lockedByFileReview '==' reviewId}}
                    <div class="custom-control custom-checkbox">
                        <input
                                type="checkbox"
                                class="custom-control-input"
                                name="identification[validated]"
                                id="identification[validated]-{{ positionInformation.identification.validated}}"
                            {{#if positionInformation.identification.validated}} checked {{/if}}
                            {{#if onlyRead}} disabled {{/if}}
                        />
                        <label class="custom-control-label"
                               for="identification[validated]-{{ positionInformation.identification.validated}}"
                        >Validate</label
                        >
                    </div>
                {{else}}
                    <i class="fa fa-lock"></i>
                {{/ifCond}}
            </div>
        </div>
        <hr class="mt-3"/>
    </div>

    <!--PRINCIPAL ADDRESS -->
    <div>
        <p>
            PRINCIPAL ADDRESS
            {{#ifCond position.lockedByFileReview '==' reviewId}}
                {{#if positionInformation.principalAddress.complete}}
                    <span class="badge badge-success">Complete</span>
                {{else}}
                    <span class="badge badge-warning text-dark">Incomplete</span>
                {{/if}}
            {{/ifCond}}
        </p>
        <div class="row">
            <div class="col-2">Address - 1st Line:</div>
            <div class="col-4 font-weight-bold">{{  position.principalAddress.primaryAddress }}</div>
            <div class="col-2">Address - 2nd Line:</div>
            <div class="col-4 font-weight-bold">{{ position.principalAddress.secondaryAddress}}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">Country:</div>
            <div class="col-4 font-weight-bold">{{ position.principalAddress.country }}</div>
            <div class="col-2">State:</div>
            <div class="col-4 font-weight-bold">{{ position.principalAddress.state }}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">City:</div>
            <div class="col-4 font-weight-bold">{{ position.principalAddress.city }}</div>
            <div class="col-2">Postal Code:</div>
            <div class="col-4 font-weight-bold">{{ position.principalAddress.postalCode}}</div>
        </div>
        <!-- CHECK VALIDATE -->
        <div class="row">
            <div class="col-12 d-flex justify-content-end">
                {{#ifCond position.lockedByFileReview '==' reviewId}}
                    <div class="custom-control custom-checkbox">
                        <input
                                type="checkbox"
                                class="custom-control-input"
                                name="principalAddress[validated]"
                                id="principalAddress[validated]-{{ positionInformation.principalAddress.validated}}"
                            {{#if positionInformation.principalAddress.validated}} checked {{/if}}
                            {{#if onlyRead}} disabled {{/if}}
                        />
                        <label class="custom-control-label"
                               for="principalAddress[validated]-{{ positionInformation.principalAddress.validated}}"
                        >Validate</label
                        >
                    </div>
                {{else}}
                    <i class="fa fa-lock"></i>
                {{/ifCond}}
            </div>
        </div>
        <hr class="mt-3"/>
    </div>

    <!--MAILING ADDRESS-->
    <div>
        <p>
            MAILING ADDRESS
            {{#ifCond position.lockedByFileReview '==' reviewId}}
                {{#if positionInformation.mailingAddress.complete}}
                    <span class="badge badge-success">Complete</span>
                {{else}}
                    <span class="badge badge-warning text-dark">Incomplete</span>
                {{/if}}
            {{/ifCond}}
        </p>
        <div class="row">
            <div class="col-2">Address - 1st Line:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.primaryAddress }}</div>
            <div class="col-2">Address - 2nd Line:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.secondaryAddress }}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">Country:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.country }}</div>
            <div class="col-2">State:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.state }}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">City:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.city }}</div>
            <div class="col-2">Postal Code:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.postalCode}}</div>
        </div>
        <!-- CHECK VALIDATE -->
        <div class="row">
            <div class="col-12 d-flex justify-content-end">
                {{#ifCond position.lockedByFileReview '==' reviewId}}
                    <div class="custom-control custom-checkbox">
                        <input
                                type="checkbox"
                                class="custom-control-input"
                                name="mailingAddress[validated]"
                                id="mailingAddress[validated]-{{ positionInformation.mailingAddress.validated}}"
                            {{#if positionInformation.mailingAddress.validated}} checked {{/if}}
                            {{#if onlyRead}} disabled {{/if}}
                        />
                        <label class="custom-control-label"
                               for="mailingAddress[validated]-{{ positionInformation.mailingAddress.validated}}"
                        >Validate</label
                        >
                    </div>
                {{else}}
                    <i class="fa fa-lock"></i>
                {{/ifCond}}
            </div>
        </div>
        <hr class="mt-3"/>
    </div>

    <!--TAX ADVICE -->
    <div>
        <p>
            TAX ADVICE
            {{#ifCond position.lockedByFileReview '==' reviewId}}
                {{#if positionInformation.taxResidence.complete}}
                    <span class="badge badge-success">Complete</span>
                {{else}}
                    <span class="badge badge-warning text-dark">Incomplete</span>
                {{/if}}
            {{/ifCond}}
        </p>
        <div class="row">
            <div class="col-6">Confirmation Regarding Legal / Tax Advice:</div>
            <div class="col-6 font-weight-bold">
                {{#if position.taxResidence.confirmation}}Confirmed {{else}} Not Confirmed {{/if}}
            </div>
            <div class="col-6">Tax Residence:</div>
            <div class="col-6 font-weight-bold">{{ position.taxResidence.taxResidence }}</div>
        </div>
        <!-- CHECK VALIDATE -->
        <div class="row">
            <div class="col-12 d-flex justify-content-end">
                {{#ifCond position.lockedByFileReview '==' reviewId}}
                    <div class="custom-control custom-checkbox">
                        <input
                                type="checkbox"
                                class="custom-control-input"
                                name="taxResidence[validated]"
                                id="taxResidence[validated]-{{ positionInformation.taxResidence.validated}}"
                            {{#if positionInformation.taxResidence.validated}} checked {{/if}}
                            {{#if onlyRead}} disabled {{/if}}
                        />
                        <label class="custom-control-label"
                               for="taxResidence[validated]-{{ positionInformation.taxResidence.validated}}"
                        >Validate</label
                        >
                    </div>
                {{else}}
                    <i class="fa fa-lock"></i>
                {{/ifCond}}
            </div>
        </div>
        <hr class="mt-3"/>
    </div>

    <!--ADVISOR DETAILS-->
    <div>
        <p>
            ADVISOR DETAILS
            {{#ifCond position.lockedByFileReview '==' reviewId}}
                {{#if positionInformation.advisorDetails.complete}}
                    <span class="badge badge-success">Complete</span>
                {{else}}
                    <span class="badge badge-warning text-dark">Incomplete</span>
                {{/if}}
            {{/ifCond}}
        </p>
        <div class="row">
            <div class="col-2">First Name:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.firstName }}</div>
            <div class="col-2">Middle Name:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.middleName }}</div>
        </div>
        <div class="row">
            <div class="col-2">Last Name:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.lastName }}</div>
            <div class="col-2">Name of Firm:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.firmName }}</div>
        </div>
        <div class="row">
            <div class="col-2">Phone:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.phone }}</div>
            <div class="col-2">E-mail:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.email }}</div>
        </div>
        <div class="row">
            <div class="col-2">Nationality:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.nationality }}</div>
            <div class="col-2">Country of Incorporation:</div>
            <div class="col-4 font-weight-bold">{{ position.advisorDetails.incorporationCountry }}</div>
        </div>
        <!-- CHECK VALIDATE -->
        <div class="row">
            <div class="col-12 d-flex justify-content-end">
                {{#ifCond position.lockedByFileReview '==' reviewId}}
                    <div class="custom-control custom-checkbox">
                        <input
                                type="checkbox"
                                class="custom-control-input"
                                name="advisorDetails[validated]"
                                id="advisorDetails[validated]-{{ positionInformation.advisorDetails.validated}}"
                            {{#if positionInformation.advisorDetails.validated}} checked {{/if}}
                            {{#if onlyRead}} disabled {{/if}}
                        />
                        <label class="custom-control-label"
                               for="advisorDetails[validated]-{{ positionInformation.advisorDetails.validated}}"
                        >Validate</label
                        >
                    </div>
                {{else}}
                    <i class="fa fa-lock"></i>
                {{/ifCond}}
            </div>
        </div>
        <hr class="mt-3"/>
    </div>

    <!--PRINCIPAL ADVISOR ADDRESS -->
    <div>
        <p>
            PRINCIPAL ADVISOR ADDRESS
            {{#ifCond position.lockedByFileReview '==' reviewId}}
                {{#if positionInformation.residentialAddress.complete}}
                    <span class="badge badge-success">Complete</span>
                {{else}}
                    <span class="badge badge-warning text-dark">Incomplete</span>
                {{/if}}
            {{/ifCond}}
        </p>
        <div class="row">
            <div class="col-2">Address - 1st Line:</div>
            <div class="col-4 font-weight-bold">{{ position.residentialAddress.primaryAddress }}</div>
            <div class="col-2">Address - 2nd Line:</div>
            <div class="col-4 font-weight-bold">{{ position.residentialAddress.secondaryAddress }}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">Country:</div>
            <div class="col-4 font-weight-bold">{{ position.residentialAddress.country }}</div>
            <div class="col-2">State:</div>
            <div class="col-4 font-weight-bold">{{ position.residentialAddress.state }}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">City:</div>
            <div class="col-4 font-weight-bold">{{ position.residentialAddress.city }}</div>
            <div class="col-2">Postal Code:</div>
            <div class="col-4 font-weight-bold">{{ position.residentialAddress.postalCode }}</div>
        </div>
        <!-- CHECK VALIDATE -->
        <div class="row">
            <div class="col-12 d-flex justify-content-end">
                {{#ifCond position.lockedByFileReview '==' reviewId}}
                    <div class="custom-control custom-checkbox">
                        <input
                                type="checkbox"
                                class="custom-control-input"
                                name="residentialAddress[validated]"
                                id="residentialAddress[validated]-{{ positionInformation.residentialAddress.validated}}"
                            {{#if positionInformation.residentialAddress.validated}} checked {{/if}}
                            {{#if onlyRead}} disabled {{/if}}
                        />
                        <label class="custom-control-label"
                               for="residentialAddress[validated]-{{ positionInformation.residentialAddress.validated}}"
                        >Validate</label
                        >
                    </div>
                {{else}}
                    <i class="fa fa-lock"></i>
                {{/ifCond}}
            </div>
        </div>
        <hr class="mt-3"/>
    </div>
    <!-- WORLD CHECK -->
    <div>
        <p>
            WORLD CHECK
            {{#ifCond position.lockedByFileReview '==' reviewId}}
                {{#if positionInformation.worldCheck.complete}}
                    <span class="badge badge-success">Complete</span>
                {{else}}
                    <span class="badge badge-warning text-dark">Incomplete</span>
                {{/if}}
            {{/ifCond}}
        </p>
        <!-- CHECK VALIDATE -->
        <div class="row">
            <div class="col-12 d-flex justify-content-end">
                {{#ifCond position.lockedByFileReview '==' reviewId}}
                    <div class="custom-control custom-checkbox">
                        <input
                                type="checkbox"
                                class="custom-control-input"
                                name="worldCheck[validated]"
                                id="worldCheck[validated]-{{ positionInformation.worldCheck.validated}}"
                            {{#if positionInformation.worldCheck.validated}} checked {{/if}}
                            {{#if onlyRead}} disabled {{/if}}
                        />
                        <label class="custom-control-label"
                               for="worldCheck[validated]-{{ positionInformation.worldCheck.validated}}"
                        >Validate</label
                        >
                    </div>
                {{else}}
                    <i class="fa fa-lock"></i>
                {{/ifCond}}
            </div>
        </div>
    </div>

    <div>
        <hr class="mt-3"/>
        <p>FILES</p>
        <table class="table">
            <thead>
            <tr>
                <th>Name</th>
                <th style="width: 10%">Group</th>
                <th style="width: 10%">Present</th>
                <th>Explanation</th>
                <th style="width: 10%">Download</th>
                <th style="width: 10%">Validate</th>
            </tr>
            </thead>
            <tbody>
            {{#each positionFiles}}
                <tr>
                    <td style="text-transform: capitalize;">{{external}}</td>
                    <td style="text-transform: capitalize;">{{fileGroup}}</td>
                    <td class="text-center" style="text-transform: capitalize;">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox"
                                   disabled
                                   class="custom-control-input"
                                   id="standardFilePresent-{{ @key }}"
                                {{#if present}}
                                   checked
                                {{/if}}
                            />
                            <label
                                    class="custom-control-label"
                                    for="standardFilePresent-{{ @key }}"
                            ></label>
                        </div>
                    </td>
                    <td style="text-transform: capitalize;">{{ explanation }}</td>
                    <td class="text-center align-middle">
                        <button class="btn solid royal-blue download-button"
                                id="standardFileDownload-{{ @key }}"
                                type="button"
                                data-toggle="modal"
                                data-target="#downloadFileModal"
                                data-review-id="{{../reviewId }}"
                                data-relation-id="{{../position._id}}"
                                data-file-id="{{ id }}"
                                data-file-group="{{../position.type}}"
                            {{#unless present}} disabled {{/unless}}
                        >Download
                        </button>
                    </td>
                    <td class="text-center align-middle">
                        {{#ifCond ../position.lockedByFileReview '==' ../reviewId}}
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" name="files[{{ fileGroup }}][{{id}}]"
                                       class="custom-control-input"
                                       id="standard-file-validated-{{ fileGroup }}-{{id}}"
                                    {{#if validated}}
                                       checked
                                    {{/if}}
                                    {{#if ../onlyRead}} disabled {{/if}}
                                />
                                <label
                                        class="custom-control-label"
                                        for="standard-file-validated-{{ fileGroup }}-{{id}}"
                                ></label>
                            </div>
                        {{else}}
                            <i class="fa fa-lock"></i>
                        {{/ifCond}}
                    </td>
                </tr>
            {{/each}}
            </tbody>
        </table>
    </div>
</form>




<main class="">
    <div class="row">
        <div class="col-12">
            <div class="card-title ml-4">
                <h2>{{ title }}</h2>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card-box">

                <!-- CONTENT -->
                <div class="card-body">
                    <div class="row">
                        <h5 class="pb-1">PREVIOUS ANNOUNCEMENTS:</h5>
                        <div class="table-responsive">
                            <table id="messages-datatable"
                                class="table table-striped table-sm table-small-font w-100 nowrap ">
                                <thead>
                                    <tr>
                                        <th scope="col">ID</th>
                                        <th scope="col" style="width: 20%">Scheduled Date</th>
                                        <th scope="col" style="width: 30%">Subject</th>
                                        <th scope="col" style="width: 20%">MCC</th>
                                        <th scope="col" style="width: 15%">Status</th>
                                        <th scope="col" style="width: 15%" class="text-center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {{#each announcements}}
                                    <tr>
                                        <td>{{ _id }}</td>
                                        <td name="scheduledAtCell" data-sort="{{formatDate scheduledAt 'YYYY-MM-DD HH:mm'}}" 
                                            onchange="formatLocalDate('{{#formatDate scheduledAt "MM/DD/YYYY HH:mm:ss"}} {{/formatDate}}', '{{_id}}' )" id="scheduled-at-{{_id}}"
                                            >
    
                                        </td>
                                        <td class="limited-col-text">
                                            {{subject}}
                                        </td>
                                        <td>
                                            {{#if sendToAll}}
                                            ALL
                                            {{else}}
                                            {{masterClientCodes}}
                                            {{/if}}
                                        </td>
                                        <td>
                                            {{ status }}
                                        </td>
                                        <td class="pl-2 py-1 d-flex text-center align-middle ">
                                            {{#if ../newAnnouncementAlowed}}
                                                {{#ifEquals status 'SCHEDULED'}}
                                                <a href="/client-management/announcements/{{_id}}"
                                                    class="btn  solid royal-blue w-50 mr-1">
                                                    Edit
                                                </a>

                                                <button type="button" data-message-id="{{_id}}"
                                                    class="btn btn-danger w-50 showDeleteAnnouncementPopup">
                                                    Delete
                                                </button>
                                                {{/ifEquals}}
                                            {{/if}}
    
                                        </td>
                                    </tr>
                                    {{else}}
                                    <tr>
                                        <td colspan="6" class="text-center font-italic">There are no new
                                            announcements
                                        </td>
                                    </tr>
                                    {{/each}}
                                </tbody>
                            </table>
                        </div>
                        <!-- RESPONSIVE TABLE END -->
                    </div>
                </div>
                <!-- CONTENT END -->
                <div class="row mt-2">
                    <div class="col-md-12 d-flex justify-content-between">
                        <a href="/client-management" class="btn btn-secondary width-lg waves-effect waves-light">
                            Back
                        </a>

                        {{#if newAnnouncementAlowed}}
                            <a href="/client-management/announcements/create" class="btn  solid royal-blue">New Announcement</a>
                        {{/if}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>client-management/modals/show-announcement-details}}
<script type="text/javascript">
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body'
    });


    function formatLocalDate(date, id) {
        const newDate = moment(moment.utc(date, "MM/DD/YYYY HH:mm:ss").toDate());
        $('#scheduled-at-' + id).html(newDate.format("MM/DD/YYYY HH:mm:ss"));
    }
    $('td[name="scheduledAtCell"]').each(function () {
        $(this).trigger('change');
    });

    let currentSelectedAnnouncementId;

    $(document).ready(function () {
        let table = $("#messages-datatable").DataTable({
            "columnDefs": [{
                "visible": false, "targets": [0]
            }, {
                orderable: false, targets: [ 0,2, 3, 5]
            }
            ],
            order: [1, 'desc'],
            "scrollX": !0,
            select: {
                style: 'single',
                selector: 'tr>td:nth-child(1), tr>td:nth-child(2), tr>td:nth-child(3), tr>td:nth-child(4)'
            },
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>",
                },
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });

        table.on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                let selectedRowData = table.rows(indexes).data();
                let currentSelectedAnnouncementId = selectedRowData[0][0];
                loadDetails(currentSelectedAnnouncementId);
            }
        });
    });


    $(".showDeleteAnnouncementPopup").on('click', function () {
        const id = $(this).data('message-id');
        deleteAnnouncement(id);
    });

    function loadDetails(id) {
        $.ajax({
            type: "GET",
            url: "/client-management/announcements/" + id + "/details",
            success: async function (data) {
                if (data) {
                    if (data.status !== 200 && data.error.length > 0) {
                        toastr["warning"](data.error);
                        window.setTimeout(function () {
                            document.location.reload();
                        }, 1500);
                    } else {
                        try {
                            const selectedMessage = data.announcement;
                            $("#subjectField").val(selectedMessage.subject);
                            $("#contentField").text(selectedMessage.content);
                            $("#scheduleDate").val(selectedMessage.scheduledAt);
                            if (selectedMessage.sendToAll === true) {
                                $('#send-to-all-yes').prop('checked', true);
                                $('#send-to-all-no').prop('checked', false);
                            } else {
                                $('#send-to-all-no').prop('checked', true);
                                $('#send-to-all-yes').prop('checked', false);
                            }

                            $("#mccField").text(selectedMessage.masterClientCodes);

                            if (selectedMessage.files && selectedMessage.files.length > 0) {
                                for (let i = 0; i < selectedMessage.files.length; i++) {
                                    let file = selectedMessage.files[i];
                                    let fileRow = '<li>' +
                                        '<a target="_blank" href="/client-management/announcements/' + selectedMessage.id + '/files/' +
                                        file.fileId + '">' + file.originalName + '</a></li>';

                                    $("#messageFilesBox").append(fileRow)
                                }
                            }
                            else {
                                $("#messageFilesBox").append('Not files found')
                            }

                            $("#showAnnouncementDetails").modal();
                        }
                        catch {
                        }
                    }
                }
            },
            dataType: "json"
        });
    }



    function deleteAnnouncement(messageId) {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will delete the announcement.",
            type: 'warning',
            backdrop: true,
            showCancelButton: true,
            cancelButtonColor: "#6c757d",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes, do it!',
            reverseButtons: true,
            showLoaderOnConfirm: true,
            preConfirm(inputValue) {
                return fetch('/client-management/announcements/' + messageId, { method: 'DELETE' })
                    .then(response => {
                        try {
                            return response.json()
                        } catch (e) {
                            throw new Error(response.statusText)
                        }

                    })
                    .catch(error => {
                        return { status: 500, error: error }

                    });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.value) {
                swal.showLoading();
                if (result.value.status === 200) {
                    Swal.fire('Success', 'Announcement has been deleted successfully.', 'success').then(() => {
                        location.reload()
                    });
                } else if (result.value.status === 400) {
                    Swal.fire('Error', result.value.error, 'error');
                } else {
                    Swal.fire('Error', 'There was an error deleting the announcement.', 'error');
                }
            }

        })
    }


</script>

<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            Company Name: <span class="font-weight-bold">{{ file.companyName }}</span>
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <a href="/file-reviewer/reviews/{{ id }}/relations/search/natural"
                                   class="btn solid royal-blue">
                                    <i class="fa fa-plus pr-2"></i>Add New Relation
                                </a>
                            </div>
                        </div>
                        <div class="owners-table mt-3">
                            <h5>BENEFICIAL OWNERS</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th style="width: 18%;">Name</th>
                                        <th style="width: 13%;">Country</th>
                                        <th style="width: 12%;"></th>
                                        <th style="width: 12%;">Type</th>
                                        <th style="width: 15%;">ID Status</th>
                                        <th style="width: 12%;"></th>
                                        <th style="width: 3%;"></th>
                                        <th style="width: 5%;"></th>
                                        <th style="width: 5%;"></th>
                                        <th style="width: 5%;"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each beneficialOwners}}
                                        <tr>
                                            {{!-- MAY NEED REFACTORING --}}
                                            <!-- NAME -->
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.fullName}}
                                                {{else}}
                                                    {{details.organizationName}}
                                                {{/ifEquals}}
                                            </td>
                                            <!-- COUNTRY -->
                                            <td style="text-transform: capitalize;" colspan="2">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.countryBirth}}
                                                {{else}}
                                                    {{details.incorporationCountry}}
                                                {{/ifEquals}}
                                            </td>
                                            <td style="text-transform: capitalize;">{{ type }}</td>
                                            <td style="text-transform: capitalize;">
                                                {{#ifCond type '===' 'natural'}}
                                                    {{#if electronicIdInfo.isElectronicId}}
                                                        {{electronicIdInfo.status}}
                                                    {{/if}}
                                                {{/ifCond}}
                                            </td>
                                            <td class="align-middle text-center p-1">
                                                {{#ifCond type '!==' 'natural'}}
                                                    {{#ifCond lockedByFileReview '==' ../id}}
                                                        <a class="btn btn-xs btn-outline-secondary"
                                                           href="/file-reviewer/reviews/{{ ../id }}/organizations/{{ _id }}/positions"
                                                        >Add Position</a>
                                                    {{/ifCond}}
                                                {{/ifCond}}
                                                {{#ifCond type '===' 'natural'}}
                                                    {{#if electronicIdInfo.isElectronicId}}
                                                        {{#ifCond electronicIdInfo.status '!==' 'COMPLETED'}}
                                                            <button
                                                                    type="button"
                                                                    data-review-id="{{../id}}"
                                                                    data-relation-id="{{ _id }}"
                                                                    style="min-width: 100px;"
                                                                    class="btn btn-xs btn-danger showCancelElectronicIdPopup"
                                                            >
                                                                Cancel
                                                            </button>
                                                        {{/ifCond}}
                                                    {{/if}}
                                                {{/ifCond}}
                                            </td>
                                            <td class="p-1 d-flex align-middle justify-content-between">

                                                {{#ifCond lockedByFileReview '!=' ../id}}
                                                    <i class="fa fa-lock pt-2"></i>
                                                {{/ifCond}}
                                            </td>
                                            <td class="p-1 align-middle">
                                                <button
                                                        type="button"
                                                        data-type="{{ type }}"
                                                        data-group="beneficial"
                                                        data-review-id="{{../id}}"
                                                        data-relation-id="{{ _id }}"
                                                        data-name="{{#ifEquals type
                                                                               'natural' }} {{details.fullName}} {{else}} {{details.organizationName}} {{/ifEquals}}"
                                                        data-toggle="modal"
                                                        data-target="#openRelationModal"
                                                        class="btn btn-xs solid royal-blue mr-1"
                                                >
                                                    Open
                                                </button>

                                            </td>
                                            <td class="p-1 align-middle">
                                                {{#ifCond lockedByFileReview '==' ../id}}
                                                    <a href="/file-reviewer/open-file-review/{{../id }}/beneficial/edit-relation/{{type}}/{{ _id }}"
                                                       class="btn btn-outline-secondary royal-blue"
                                                    ><i class="fa fa-pen"></i></a>
                                                {{else}}
                                                    <button
                                                            type="button"
                                                            class="btn btn-outline-secondary"
                                                            id="locked-{{ _id }}"
                                                            data-locked-id="{{ lockedByFileReview  }}"
                                                            data-toggle="modal"
                                                            data-target="#lockedReviewModal"
                                                    >
                                                        <i class="fa fa-info-circle"></i>
                                                    </button>
                                                {{/ifCond}}
                                            </td>
                                            <td class="p-1 align-middle">
                                                <button
                                                        type="button"
                                                        class="delete-beneficial btn btn-danger"
                                                        id="delete-beneficial-{{ _id }}"
                                                        data-relation-id="{{ _id }}"
                                                        data-review-id="{{ ../id  }}"
                                                        data-group="beneficial"
                                                        data-type="{{ type }}"
                                                        data-toggle="modal"
                                                        data-target="#deleteRelationModal"
                                                >
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="10" class="text-center font-italic">
                                                There are no beneficial owners
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div id="shareholders-table-wrap" class="mt-3">
                            <h5>SHAREHOLDERS</h5>
                            {{#if incorrectShareholderPercentage}}
                                <p><small>Please note: The total percentage of shares does not equal 100%</small></p>
                            {{/if}}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th style="width: 18%;">Name</th>
                                        <th style="width: 13%;">Country</th>
                                        <th style="width: 12%;">Percentage</th>
                                        <th style="width: 12%;">Type</th>
                                        <th style="width: 15%;">ID Status</th>
                                        <th style="width: 12%;"></th>
                                        <th style="width: 3%;"></th>
                                        <th style="width: 5%;"></th>
                                        <th style="width: 5%;"></th>
                                        <th style="width: 5%;"></th>

                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each shareholders}}
                                        <tr>
                                            {{!-- MAY NEED REFACTORING --}}
                                            <!-- NAME -->
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.fullName}}
                                                {{else}}
                                                    {{details.organizationName}}
                                                {{/ifEquals}}
                                            </td>
                                            <!-- COUNTRY -->
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.countryBirth}}
                                                {{else}}
                                                    {{details.incorporationCountry}}
                                                {{/ifEquals}}
                                            </td>
                                            <!-- PERCENTAGE -->
                                            <td style="text-transform: capitalize;">
                                                {{#if additional.percentage}}
                                                    {{additional.percentage}} %
                                                {{else}}
                                                    0%
                                                {{/if}}
                                                {{#ifCond lockedByFileReview '!=' ../id}}
                                                    <button
                                                            type="button"
                                                            data-name="{{#ifEquals type
                                                                                   'natural' }} {{details.fullName}} {{else}} {{details.organizationName}} {{/ifEquals}}"
                                                            data-percentage="{{additional.percentage}}"
                                                            data-type="{{type}}"
                                                            data-group="shareholder"
                                                            data-review-id="{{../id}}"
                                                            data-relation-id="{{ _id }}"
                                                            data-toggle="modal"
                                                            data-target="#editPercentageModal"
                                                            class="fa"
                                                    ><i class="fa fa-edit"></i></button>
                                                {{/ifCond}}


                                            </td>
                                            <td style="text-transform: capitalize;">{{ type }}</td>
                                            <td style="text-transform: capitalize;">
                                                {{#ifCond type '===' 'natural'}}
                                                    {{#if electronicIdInfo.isElectronicId}}
                                                        {{electronicIdInfo.status}}
                                                    {{/if}}
                                                {{/ifCond}}
                                            </td>
                                            <td class="p-1 align-middle text-center">
                                                {{#ifCond type '!==' 'natural'}}
                                                    {{#ifCond lockedByFileReview '==' ../id}}
                                                        <a class="btn btn-xs btn-outline-secondary"
                                                           href="/file-reviewer/reviews/{{ ../id }}/organizations/{{ _id }}/positions"
                                                        >Add Position</a>
                                                    {{/ifCond}}
                                                {{/ifCond}}
                                                {{#ifCond type '===' 'natural'}}
                                                    {{#if electronicIdInfo.isElectronicId}}
                                                        {{#ifCond electronicIdInfo.status '!==' 'COMPLETED'}}
                                                            <button
                                                                    type="button"
                                                                    data-review-id="{{../id}}"
                                                                    data-relation-id="{{ _id }}"
                                                                    style="min-width: 100px;"
                                                                    class="btn btn-xs btn-danger showCancelElectronicIdPopup"
                                                            >
                                                                Cancel
                                                            </button>
                                                        {{/ifCond}}
                                                    {{/if}}
                                                {{/ifCond}}
                                            </td>
                                            <td class="p-1 d-flex align-middle justify-content-between">
                                                {{#ifCond lockedByFileReview '!=' ../id}}
                                                    <i class="fa fa-lock pt-2"></i>
                                                {{/ifCond}}
                                            </td>
                                            <td class="p-1 align-middle">
                                                <button
                                                        type="button"
                                                        data-type="{{ type }}"
                                                        data-group="shareholder"
                                                        data-review-id="{{../id}}"
                                                        data-relation-id="{{ _id }}"
                                                        data-name="{{#ifEquals type
                                                                               'natural' }} {{details.fullName}} {{else}} {{details.organizationName}} {{/ifEquals}}"
                                                        data-toggle="modal"
                                                        data-target="#openRelationModal"
                                                        class="btn btn-xs solid royal-blue"
                                                >
                                                    Open
                                                </button>
                                            </td>
                                            <td class="p-1 align-middle">
                                                {{#ifCond lockedByFileReview '==' ../id}}
                                                    <a href="/file-reviewer/open-file-review/{{../id }}/shareholder/edit-relation/{{type}}/{{ _id }}"
                                                       class="btn btn-outline-secondary royal-blue"
                                                    ><i class="fa fa-pen"></i></a>
                                                {{else}}
                                                    <button
                                                            type="button"
                                                            class="btn btn-outline-secondary"
                                                            id="locked-{{ _id }}"
                                                            data-locked-id="{{ lockedByFileReview  }}"
                                                            data-toggle="modal"
                                                            data-target="#lockedReviewModal"
                                                    >
                                                        <i class="fa fa-info-circle"></i>
                                                    </button>
                                                {{/ifCond}}
                                            </td>
                                            <td class="p-1 align-middle">
                                                <button
                                                        type="button"
                                                        class="delete-shareholder btn btn-danger"
                                                        id="delete-shareholder-{{ _id }}"
                                                        data-relation-id="{{ _id }}"
                                                        data-review-id="{{ ../id  }}"
                                                        data-group="shareholder"
                                                        data-type="{{ type }}"
                                                        data-toggle="modal"
                                                        data-target="#deleteRelationModal"
                                                >
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="10" class="text-center font-italic">
                                                There are no shareholders
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div id="positions-table-wrap" class="mt-3">
                            <h5>DIRECTORS</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th style="width: 18%;">Name</th>
                                        <th style="width: 13%;">Country</th>
                                        <th style="width: 12%;"></th>
                                        <th style="width: 12%;">Type</th>
                                        <th style="width: 15%;">ID Status</th>
                                        <th style="width: 12%;"></th>
                                        <th style="width: 3%;"></th>
                                        <th style="width: 5%;"></th>
                                        <th style="width: 5%;"></th>
                                        <th style="width: 5%;"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each directors}}
                                        <tr>
                                            {{!-- MAY NEED REFACTORING --}}
                                            <!-- NAME -->
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.fullName}}
                                                {{else}}
                                                    {{details.organizationName}}
                                                {{/ifEquals}}
                                            </td>
                                            <!-- COUNTRY -->
                                            <td style="text-transform: capitalize;" colspan="2">
                                                {{#ifEquals type 'natural'}}
                                                    {{details.countryBirth}}
                                                {{else}}
                                                    {{details.incorporationCountry}}
                                                {{/ifEquals}}
                                            </td>

                                            <td style="text-transform: capitalize;">{{ type }}</td>
                                            <td style="text-transform: capitalize;">
                                                {{#ifCond type '===' 'natural'}}
                                                    {{#if electronicIdInfo.isElectronicId}}
                                                        {{electronicIdInfo.status}}
                                                    {{/if}}
                                                {{/ifCond}}
                                            </td>
                                            <!-- POSITION -->
                                            <td class="p-1 align-middle text-center">
                                                {{#ifCond type '!==' 'natural'}}
                                                    {{#ifCond lockedByFileReview '==' ../id}}
                                                        <a class="btn btn-xs btn-outline-secondary"
                                                           href="/file-reviewer/reviews/{{ ../id }}/organizations/{{ _id }}/positions"
                                                        >Add Position</a>
                                                    {{/ifCond}}
                                                {{/ifCond}}
                                                {{#ifCond type '===' 'natural'}}
                                                    {{#if electronicIdInfo.isElectronicId}}
                                                        {{#ifCond electronicIdInfo.status '!==' 'COMPLETED'}}
                                                            <button
                                                                    type="button"
                                                                    data-review-id="{{../id}}"
                                                                    data-relation-id="{{ _id }}"
                                                                    style="min-width: 100px;"
                                                                    class="btn btn-xs btn-danger showCancelElectronicIdPopup"
                                                            >
                                                                Cancel
                                                            </button>
                                                        {{/ifCond}}
                                                    {{/if}}
                                                {{/ifCond}}
                                            </td>
                                            <td class="p-1 align-middle">
                                                {{#ifCond lockedByFileReview '!=' ../id}}
                                                    <i class="fa fa-lock pt-2"></i>
                                                {{/ifCond}}
                                            </td>
                                            <td class="p-1 align-middle">
                                                <button
                                                        type="button"
                                                        data-type="{{ type }}"
                                                        data-group="director"
                                                        data-review-id="{{../id}}"
                                                        data-relation-id="{{ _id }}"
                                                        data-name="{{#ifEquals type
                                                                               'natural' }} {{details.fullName}} {{else}} {{details.organizationName}} {{/ifEquals}}"
                                                        data-toggle="modal"
                                                        data-target="#openRelationModal"
                                                        class="btn btn-xs solid royal-blue"
                                                >
                                                    Open
                                                </button>
                                            </td>
                                            <td class="p-1 align-middle">
                                                {{#ifCond lockedByFileReview '==' ../id}}
                                                    <a href="/file-reviewer/open-file-review/{{../id }}/director/edit-relation/{{type}}/{{ _id }}"
                                                       class="btn btn-outline-secondary royal-blue"
                                                    ><i class="fa fa-pen"></i></a>
                                                {{else}}
                                                    <button
                                                            type="button"
                                                            class="btn btn-outline-secondary"
                                                            id="locked-{{ _id }}"
                                                            data-locked-id="{{ lockedByFileReview  }}"
                                                            data-toggle="modal"
                                                            data-target="#lockedReviewModal"
                                                    >
                                                        <i class="fa fa-info-circle"></i>
                                                    </button>
                                                {{/ifCond}}
                                            </td>
                                            <td class="p-1 align-middle">
                                                <button
                                                        type="button"
                                                        class="delete-shareholder btn btn-danger"
                                                        id="delete-shareholder-{{ _id }}"
                                                        data-relation-id="{{ _id }}"
                                                        data-review-id="{{ ../id  }}"
                                                        data-group="director"
                                                        data-type="{{ type }}"
                                                        data-toggle="modal"
                                                        data-target="#deleteRelationModal"
                                                >
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="10" class="text-center font-italic">
                                                There are no directors
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <!-- CONTENT END -->
                    <div class="row mt-2 justify-content-between">
                        <div class="col-md-2">
                            <a href="/file-reviewer/open-file-review/{{ id }}"
                               class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>
                        </div>
                        <div class="col-md-10 text-right">

                            {{#ifCond fileReview.status.code '!=' 'SENT TO CLIENT'}}

                                {{#if hasElectronicIdNotStarted}}
                                    <button
                                            type="button"
                                            id="sendElectronicIdRequest"
                                            class="btn solid royal-blue px-4 mr-3"
                                            data-review-id="{{ id }}"
                                    >
                                        Request Electronic Id
                                    </button>
                                {{/if}}

                                <button
                                        type="button"
                                        id="send-to-compliance-review-button"
                                        class="btn solid royal-blue px-4 mr-3"
                                        data-toggle="modal"
                                        data-target="#changeStatusModal"
                                        data-status="sendCompliance"
                                        data-officer="fileReviewer"
                                        data-id="{{ id }}"
                                        data-page="1"
                                >
                                    Send To Compliance
                                </button>

                                {{#if hasPendingElectronicInfo}}
                                    <button
                                            type="button"
                                            class="btn solid royal-blue px-4"
                                            data-id="{{ id }}"
                                            onclick="showPendingElectronicIdPopup()"
                                    >
                                        Submit
                                    </button>
                                {{else}}
                                    <button
                                            type="button"
                                            id="submit-button"
                                            class="btn solid royal-blue px-4"
                                            data-id="{{ id }}"
                                            data-status="validateReview"
                                            data-officer="fileReviewer"
                                            data-toggle="modal"
                                            data-target="#confirmReviewModal"
                                    >
                                        Submit
                                    </button>
                                {{/if}}

                            {{/ifCond}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>


{{>file-reviewer/download-file-modal}}
{{>file-reviewer/locked-relation-name-modal}}
{{>file-reviewer/modals/change-status-review-modal}}
{{>file-reviewer/modals/confirm-review-modal}}
{{>file-reviewer/edit-percentage-modal}}
{{>file-reviewer/position/open-position-modal}}
{{> file-reviewer/review-relations/modals/delete-relation-modal}}
{{> file-reviewer/review-relations/modals/open-relation-modal}}

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>

<script type="text/javascript">

    $("#sendElectronicIdRequest").on('click', function () {
        const reviewId = $("#sendElectronicIdRequest").data('review-id');
        Swal.fire({
            title: 'Are you sure?',
            text: "This will send an e-mail with ID PAL invitation(s) to all relations that have electronic ID verification selected.",
            type: 'warning',
            backdrop: true,
            showCancelButton: true,
            cancelButtonColor: "#6c757d",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes, send it!',
            reverseButtons: true,
            showLoaderOnConfirm: true,
            preConfirm(inputValue) {
                return fetch('/file-reviewer/reviews/' + reviewId + '/request-electronic-id', {method: 'POST'})
                        .then(response => {
                            try {
                                return response.json()
                            } catch (e) {
                                throw new Error(response.statusText)
                            }

                        })
                        .catch(error => {
                            console.log(error);
                            return {status: 500, error: error}

                        });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.value) {
                swal.showLoading();
                if (result.value.status === 200) {
                    Swal.fire('Success', 'Electronic information has been requested successfully.', 'success').then(() => {
                        location.href = '/file-reviewer/file-review-list';
                    });
                } else if (result.value.status === 400) {
                    Swal.fire('Error', result.value.error, 'error');
                } else {
                    Swal.fire('Error', 'There was an error requesting the electronic information.', 'error');
                }
            }

        })
    });


    function showPendingElectronicIdPopup() {
        Swal.fire({
            text: "Please wait for all the ID Statuses to be completed.",
            type: 'warning',
        })
    }


    $(".showCancelElectronicIdPopup").on('click', function () {
        const reviewId = $(this).data('review-id');
        const relationId = $(this).data('relation-id');
        cancelElectronicId(reviewId, relationId);
    });


    function cancelElectronicId(reviewId, relationId) {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will cancel the ID request and delete all received information from ID-Pal.",
            type: 'warning',
            backdrop: true,
            showCancelButton: true,
            cancelButtonColor: "#6c757d",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes, do it!',
            reverseButtons: true,
            showLoaderOnConfirm: true,
            preConfirm(inputValue) {
                return fetch('/file-reviewer/reviews/' + reviewId + '/relations/' + relationId + "/cancel-electronic-id", {method: 'POST'})
                        .then(response => {
                            try {
                                return response.json()
                            } catch (e) {
                                throw new Error(response.statusText)
                            }

                        })
                        .catch(error => {
                            console.log(error);
                            return {status: 500, error: error}

                        });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.value) {
                swal.showLoading();
                if (result.value.status === 200) {
                    Swal.fire('Success', 'Electronic information has been cancelled successfully.', 'success').then(() => {
                        location.reload()
                    });
                } else if (result.value.status === 400) {
                    Swal.fire('Error', result.value.error, 'error');
                } else {
                    Swal.fire('Error', 'There was an error cancelling the electronic information.', 'error');
                }
            }

        })
    }

</script>

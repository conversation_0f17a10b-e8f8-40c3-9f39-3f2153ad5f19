const fs = require('fs');

exports.generateEmail = function (companyCode, companyName, nameToNotify, emailText) {
  let htmlString = fs
    .readFileSync('notification-email.html', { encoding: 'utf-8' })
    .replace('##COMPANYNAME##', companyName)
    .replace('##COMPANYCODE##', companyCode)
    .replace('##MESSAGE##', emailText)
    .replace('##NAMETONOTIFY##', nameToNotify);

  let textString =
    nameToNotify +
    ', you have a new TBVI notification.\n' +
    'Someone has notified you to let you know that the file review\n' +
    companyName +
    ' with the ID: ' +
    companyCode +
    'has been assigned to you.\n' +
    'Here is what your colleague said:\n"' +
    emailText +
    '"';
  return { textString, htmlString };
};


exports.generateClientIncorporationIdPalEmail = function (url, templateType) {

  let htmlString;
  let textString = '';
  if (templateType === "incorrect-information-template"){
    htmlString = fs
      .readFileSync('public/templates/notifications/client-portal-idpal-incorrect-documents-email.html', { encoding: 'utf-8' })
      .replace('##URL##', url);
    textString = "Hello, \n" +
      "You are receiving this email as part of the Trident Trust Electronic ID verification system powered by ID-Pal. \n" +
      "It appears that one or more documents that were submitted through ID-Pal were incorrect/incomplete and could not be verified.\n" +
      "In order to resubmit please follow the steps as shown below:\n" +
      " 1. Click on the link below to open the ID-Pal app.\n" +
      url + "\n" +
      " 2. Use the ID-Pal app to capture your ID information. You will need to have your passport and/or driver license available.\n" +
      " 3. Verify your information and submit. Trident Trust will receive a client due diligence report upon completion.\n" +
      "For any questions or assistance, please contact <NAME_EMAIL>"
  }
  else{
    htmlString = fs
      .readFileSync('public/templates/notifications/client-portal-idpal-new-invitation-email.html', { encoding: 'utf-8' })
      .replace('##URL##', url);
    textString = "Hello, \n" +
      "You are receiving this email as part of the Trident Trust Electronic ID verification system powered by ID-Pal." +
      " You can learn more about ID-Pal’s key features here.\n" +
      "Please follow the steps as shown below:\n" +
      " 1. Click on the link below to open the ID-Pal app.\n" +
      url + "\n" +
      " 2. Use the ID-Pal app to capture your ID information. You will need to have your passport and/or driver license available.\n" +
      " 3. Verify your information and submit. Trident Trust will receive a client due diligence report upon completion.\n" +
      "For any questions or assistance, please contact <NAME_EMAIL>"
  }
  return { textString, htmlString };
};


exports.generateFileReviewIdPalEmail = function (url, templateType) {

  let htmlString;
  let textString = '';
  if (templateType === "incorrect-information-template"){
    htmlString = fs
      .readFileSync('public/templates/notifications/file-review-idpal-incorrect-documents-email.html', { encoding: 'utf-8' })
      .replace('##URL##', url);
    textString = "Hello, \n" +
      "You are receiving this email as part of the Trident Trust Electronic ID verification system powered by ID-Pal. \n" +
      "It appears that one or more documents that were submitted through ID-Pal were incorrect/incomplete and could not be verified.\n" +
      "In order to resubmit please follow the steps as shown below:\n" +
      " 1. Click on the link below to open the ID-Pal app.\n" +
      url + "\n" +
      " 2. Use the ID-Pal app to capture your ID information. You will need to have your passport and/or driver license available.\n" +
      " 3. Verify your information and submit. Trident Trust will receive a client due diligence report upon completion.\n" +
      "For any questions or assistance, please contact <NAME_EMAIL>"
  }
  else{
    htmlString = fs
      .readFileSync('public/templates/notifications/file-review-idpal-new-invitation-email.html', { encoding: 'utf-8' })
      .replace('##URL##', url);
    textString = "Hello, \n" +
      "You are receiving this email as part of the Trident Trust Electronic ID verification system powered by ID-Pal." +
      " You can learn more about ID-Pal’s key features here.\n" +
      "Please follow the steps as shown below:\n" +
      " 1. Click on the link below to open the ID-Pal app.\n" +
      url + "\n" +
      " 2. Use the ID-Pal app to capture your ID information. You will need to have your passport and/or driver license available.\n" +
      " 3. Verify your information and submit. Trident Trust will receive a client due diligence report upon completion.\n" +
      "For any questions or assistance, please contact <NAME_EMAIL>"
  }
  return { textString, htmlString };
};


exports.generateClientRequestInformationEmail = function () {
  let htmlString = fs
    .readFileSync('public/templates/notifications/client-portal-request-information-email.html', { encoding: 'utf-8' });

  let textString =
    'Dear client, \n ' +
    'You have received a new message from Trident Trust BVI Client Portal regarding your incorporation request, please login to the ' +
    'client portal to view this message.\n' +
    'https://clientportal.tridenttrust.com\n\n' +
    'Kind regards';
  return { textString, htmlString };
};


exports.generateClientSubmittedAttachmentsEmail = function (companies) {
  let companyHtmlList = "";
  let companyStrList = "";

  companies.forEach((c) => {
    companyHtmlList += `<b>- ${c.code}:</b> financial period from ${c.financialPeriodStart} to ${c.financialPeriodEnd}. <br>`;
    companyStrList += `- ${c.code}: financial period from ${c.financialPeriodStart} to ${c.financialPeriodEnd}. \n`;
  })

  let htmlString = 
    '<p>Dear Trident Officer, <br><br>' +
    'Below mentioned companies have filed an ES submission and have attached additional supporting documents to the same.<br>' +
    companyHtmlList +
    'Please login to the management portal to review the submitted information.<br><br>' +
    'Kind regards</p>';

  let textString =
    'Dear Trident Officer, \n\n' +
    'Below mentioned companies have filed an ES submission and have attached additional supporting documents to the same.\n' +
    companyStrList +
    'Please login to the management portal to review the submitted information.\n\n' +
    'Kind regards';

  return { textString, htmlString };
};
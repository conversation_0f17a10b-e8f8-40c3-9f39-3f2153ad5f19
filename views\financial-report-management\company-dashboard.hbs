<main class="">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <h1>{{title}}</h1>

            <!-- FILTERS -->
            <form method='GET' id="accountingCompaniesSearchForm" action="?page={{result.pageNumber}}&pageSize={{result.pageSize}}">
              <div class="row gx-1">
                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                  <label for="filterStatus">Status</label> <br>
                  <select id="filterStatus" class="form-control filter w-100" name="status">
                    <option value="" {{#ifEquals filters.status 'BOTH' }} selected {{/ifEquals}}>
                      BOTH
                    </option>
                    <option value="COMPLIANT" {{#ifEquals filters.status 'COMPLIANT' }} selected {{/ifEquals}}>COMPLIANT</option>
                    <option value="NON-COMPLIANT" {{#ifEquals filters.status 'NON-COMPLIANT' }} selected {{/ifEquals}}>NON-COMPLIANT</option>
                    <option value="EMPTY" {{#ifEquals filters.status 'EMPTY' }} selected {{/ifEquals}}>EMPTY
                    </option>
                  </select>
                </div>

                <div class="col-12  col-md-6 col-lg-4 col-xl-2 px-2">
                  <label for="filterCompanyName">Entity Name</label>
                  <input class='form-control filter' type='text' name='companyName' id='filterCompanyName'
                    value="{{filters.companyName}}" />
                </div>

                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                  <label for="filterMasterclient">Master Client Code</label>
                  <input class='form-control filter' type='text' name='masterClientCode' id='filterMasterclient'
                    value="{{filters.masterClientCode}}" />
                </div>

                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                  <label for="filterIncorporationCode">Incorporation Number</label>
                  <input class='form-control filter' type='text' name='incorporationCode' id='filterIncorporationCode'
                    value="{{filters.incorporationCode}}" />
                </div>

                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                  <label for="incorporatedDateRangeStart">Incorporation Date range start:</label>
                  <input class='form-control filter' type='date' name='incorporatedDateRangeStart' id='incorporatedDateRangeStart'
                    value="{{filters.incorporatedDateRangeStart}}" />
                </div>
                
                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                  <label for="incorporatedDateRangeEnd">Incorporation Date range end:</label>
                  <input class='form-control filter' type='date' name='incorporatedDateRangeEnd' id='incorporatedDateRangeEnd'
                    value="{{filters.incorporatedDateRangeEnd}}" />
                </div>
              </div>

              <div class="row my-1">
                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                  <label for="filterAccountingModule">Accounting Module</label> <br>
                  <select id="filterAccountingModule" class="form-control filter w-100" name="moduleState">
                    <option value="" {{#ifEquals filters.moduleState 'ALL' }} selected {{/ifEquals}}>
                      ALL
                    </option>
                    <option value="ACTIVE" {{#ifEquals filters.moduleState 'ACTIVE' }} selected {{/ifEquals}}>ACTIVE</option>
                    <option value="INACTIVE" {{#ifEquals filters.moduleState 'INACTIVE' }} selected {{/ifEquals}}>INACTIVE
                    </option>
                  </select>
                </div>

                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                  <label for="filterStatus">Company in penalty</label> <br>
                  <select id="filterStatus" class="form-control filter w-100" name="inPenalty">
                    <option value="" {{#ifEquals filters.inPenalty 'ALL' }} selected {{/ifEquals}}>
                      ALL
                    </option>
                    <option value="YES" {{#ifEquals filters.inPenalty 'YES' }} selected {{/ifEquals}}>YES</option>
                    <option value="NO" {{#ifEquals filters.inPenalty 'NO' }} selected {{/ifEquals}}>NO
                    </option>

                  </select>
                </div>

                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                  <label for="daysToDeadlineStart">Days to Deadline Start Range</label> <br>
                  <input class="form-control filter autonumber" type="text" name="daysToDeadlineStart" id="daysToDeadlineStart" data-m-dec="0"
                    value="{{filters.daysToDeadlineStart}}"/>
                </div>

                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                  <label for="daysToDeadlineEnd">Days to Deadline End Range</label> <br>
                  <input class="form-control filter autonumber" type="text" name="daysToDeadlineEnd" data-m-dec="0"
                    id="daysToDeadlineEnd"  value="{{filters.daysToDeadlineEnd}}"/>
                </div>

                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                  <label for="deadlineFrom">Deadline from:</label>
                  <input class='form-control filter' type='date' name='deadlineFrom' id='deadlineFrom'
                    value="{{filters.deadlineFrom}}" />
                </div>
                
                <div class="col-12 col-md-6 col-lg-4 col-xl-2  px-2">
                  <label for="deadlineTo">Deadline to:</label>
                  <input class='form-control filter' type='date' name='deadlineTo' id='deadlineTo'
                    value="{{filters.deadlineTo}}" />
                </div>
              </div>

              <div class="row mt-2">
                <div class="col-12">
                  <button type="submit" form="accountingCompaniesSearchForm" class='btn btn-primary waves-effect mr-2'>Search </button>
                  <button type="button" form="accountingCompaniesSearchForm" id="clearFormBtn" class='btn btn-secondary waves-effect '>Reset
                    filters </button>
                  <div class="btn-group" role="group">
                    <button type="button" id="selectAllTableBtn" class='btn btn-secondary waves-effect '>Select All On Page</button>
                    <button type="button" id="unselectAllTableBtn" class='btn btn-secondary waves-effect ' title='unselect all'>
                      <i class="far fa-square"></i>
                    </button>
                  </div>

                </div>
              </div>

            </form>
            <br /><br />

            <!-- TABLE RESULTS -->

            {{>shared/table-pagination pagination=result formName="accountingCompaniesSearchForm" tableId="accountingCompaniesTable" searching="true"}}

            <table id="accountingCompaniesTable" class="table w-100 nowrap">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Entity Name</th>
                  <th>Referral Office</th>
                  <th>Status</th>
                  <th>Master Client Code</th>
                  <th>Incorporation Number</th>
                  <th>Incorporation Date</th>
                  <th>Initial FP End Date</th>
                  <th>Current Period </th>
                  <th>Exempt Company</th>
                  <th>Deadline 
                    {{#if lastUpdateDeadlineFunction}}
                    <i class="fa fa-info-circle fa-lg text-info " data-toggle="tooltip" data-placement="top" title="Last updated: <br>{{formatDate lastUpdateDeadlineFunction STANDARD_DATETIME_FORMAT}}"></i> 
                    {{/if}} 
                  </th>
                  <th>Days to Deadline
                    {{#if lastUpdateDeadlineFunction}}
                    <i class="fa fa-info-circle fa-lg text-info " data-toggle="tooltip" data-placement="top"
                      title="Last updated:  <br>{{formatDate lastUpdateDeadlineFunction STANDARD_DATETIME_FORMAT}}"></i>
                    {{/if}}
                  </th>
                  <th>Penalty</th>
                  {{#if permissions.allowReportUnreport}}
                  <th>Reported</th>
                  {{/if}}

                  {{#if permissions.showInfo}}
                  <th>View Log</th>
                  {{/if}}
                </tr>
              </thead>
              <tbody>
                {{#each result.data}}
                  <tr >
                    <td>{{_id}}</td>
                    <td>{{name}}</td>
                    <td>{{referralOffice}}</td>
                    <td>{{status}}</td>
                    <td>{{masterClientCode}}</td>
                    <td>{{incorporationCode}}</td>
                    <td >
                        {{formatDate incorporationDate ../STANDARD_DATE_FORMAT}}
                    </td>
                    <td >
                        {{formatDate initialFPDate ../STANDARD_DATE_FORMAT}}
                    </td>
                    
                    <td>
                      {{#ifEquals confirmedPeriod 'NOT STARTED'}}
                        {{confirmedPeriod}}
                      {{else}}
                        {{formatDate confirmedPeriod ../STANDARD_DATE_FORMAT}}
                      {{/ifEquals}}
                    </td>
                                      
                    <td>{{isExemptCompany}}</td>
                    <td >
                      {{formatDate deadline ../STANDARD_DATE_FORMAT}}
                    </td>
                    <td {{#ifCond daysToDeadline "<" 0}} class="text-danger" {{/ifCond}}>{{daysToDeadline}}</td>
                    <td>{{penalty}}</td>
                    {{#if ../permissions.allowReportUnreport}}
                    
                    <td>
                      {{#ifEquals reportingAvailable true}}
                        {{#ifEquals isReported false}}
                          <button type="button" class="btn btn-primary waves-effect waves-light border-white rounded reportCompanyBtn" data-company-id="{{_id}}"
                                  data-company-reported="{{isReported}}">
                              Report
                          </button>
                        {{/ifEquals}}

                        {{#if unreportAvailable}}
                          <button type="button" class="btn btn-primary waves-effect waves-light border-white rounded reportCompanyBtn"
                            data-company-id="{{_id}}" data-company-reported="{{isReported}}">
                            Unreport
                          </button>
                        {{/if}}
                      
                      {{/ifEquals}}
                    </td>
                    {{/if}}

                    {{#if ../permissions.showInfo}}
                    <td>
                      <button type="button" class="btn btn-primary waves-effect waves-light border-white rounded" data-toggle="modal"
                        data-target="#informationModal" data-company-id="{{_id}}">
                        Show Info
                      </button>
                    </td>
                    {{/if}}
                  </tr>
                {{/each}}
              </tbody>
            </table>


            <br>
            <!--FOOTER BUTTONS-->
            <div class="row">
              <div class="col-12 mb-3 d-flex flex-row">
                <div class="mr-2 ">
                  <a href='/financial-report-management/' class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                </div>
                {{#if permissions.exportFiles}}
                <div class=" mr-2">
                  <button class="btn btn-primary width-lg waves-effect waves-light" id="btn-arcompany-export-xls">Export xls</button>
                  <button id="btn-spinner-export-xls" class="btn btn-primary" type="button" disabled style="display: none;">
                    <span class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                    Exporting data...
                  </button>
                </div>
                {{/if}}

                {{#if permissions.allowSendEmail}}
                <div  class="mr-2 ">
                  <button id="sendEmailBtn" class="btn btn-primary width-lg " style="display: none;"
                    data-toggle="modal" data-target="#sendEmailModal">
                    Send Email
                  </button>
                </div>
                {{/if}}
                {{#if permissions.allowReportUnreport}}
                <a id='downloadLink' href="#" style="display: none;" download></a>
                <div  class="mr-2 ">
                  <button id="bulkReport" class="btn btn-primary width-lg " style="display: none;" data-report="true">
                    Bulk Report
                  </button>
                </div>
                <div  class="mr-2 ">
                  <button id="bulkUnreport" class="btn btn-primary width-lg " style="display: none;" data-report="false">
                    Bulk Unreport
                  </button>
                </div>
                {{/if}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {{>financial-report/information-modal}}
  {{>financial-report/email-modal}}
</main>

<script src="/javascripts/libs/jquery-mask-plugin/jquery.mask.min.js"></script>
<script src="/javascripts/libs/autonumeric/autoNumeric-min.js"></script>
<script src="/javascripts/form-masks.init.js"></script>
<script type="text/javascript">
  $('[data-toggle="tooltip"]').tooltip({
    container: 'body',
    boundary: 'window',
    html: true
  });

  let $accountingCompaniesTable;

  $(document).ready(function () {
    $accountingCompaniesTable = $("#accountingCompaniesTable").DataTable({
      dom: "lrtip",
      columnDefs: [{ "visible": false, "targets": [0] }],
      scrollX: !0,
      select: { style: "multi" },
      paging: false,
      info: false,
      sort: false
    });

    $accountingCompaniesTable.on('select', function (e, dt, type, indexes) {
      if (type === 'row') {

        if ($accountingCompaniesTable.rows('.selected').data().length) {
          $("#sendEmailBtn").show();
          $('#bulkReport').show();
          $('#bulkUnreport').show();
        } else {
          $('#sendEmailBtn').hide();
          $('#bulkReport').hide();
          $('#bulkUnreport').hide();
        }
      }
    });

    $accountingCompaniesTable.on('deselect', function (e, dt, type, indexes) {
      if (type === 'row') {
        if ($accountingCompaniesTable.rows('.selected').data().length) {
          $("#sendEmailBtn").show();
          $('#bulkReport').show();
          $('#bulkUnreport').show();
        } else {
          $('#sendEmailBtn').hide();
          $('#bulkReport').hide();
          $('#bulkUnreport').hide();
        }
      }
    });

    $('#daysToDeadlineStart').on('keypress', function (event) {
      if (event.which < 48 || event.which > 57) {
        event.preventDefault();
      }
    });

    $('#daysToDeadlineEnd').on('keypress', function (event) {
      if (event.which < 48 || event.which > 57) {
        event.preventDefault();
      }
    });


    $("#selectAllTableBtn").on('click', function () {
      let showButton = false;

      $accountingCompaniesTable.rows({ page: 'current' }).nodes().each((node) => {
        $(node).addClass('selected');
        showButton = true
      })
      if (showButton) {
        $("#sendEmailBtn").show();
        $('#bulkReport').show();
        $('#bulkUnreport').show();
      }
    })

    $("#unselectAllTableBtn").on('click', function () {
      $accountingCompaniesTable.rows({ page: 'current' }).deselect();
      $('#sendEmailBtn').hide();
      $('#bulkReport').hide();
      $('#bulkUnreport').hide();
    })

  });

  $('#sendEmailBtn').click(function (e) {
    var btn = e.relatedTarget
    var selectedRowsData = $accountingCompaniesTable.rows({ selected: true }).data().toArray();

    var masterClientsSelected = selectedRowsData.map(function(row) {
      return row[4];
    });

    var uniquesMasterClients = Array.from(new Set(masterClientsSelected));
    $('#sendEmailModal #masterclientsSelected').val(uniquesMasterClients)
  })


    $('#bulkUnreport, #bulkReport').click(function () {
      const isReporting = $(this).data('report')
      var selectedRowsData = $accountingCompaniesTable.rows({ selected: true }).data().toArray();

      var companiesId = selectedRowsData.map(function (row) {
        return row[0];
      });

      const headers = {
        'Content-Type': 'application/json'
      }
      const obj = {
        isReported: isReporting,
        companiesIds: companiesId
      }
      Swal.fire({
        title: `Are you sure you want to ${isReporting ? 'report' : 'unreport'} this companies?`,
        type: 'warning',
        backdrop: true,
        showCancelButton: true,
        cancelButtonColor: "#6c757d",
        confirmButtonColor: "#0081B4",
        confirmButtonText: 'Yes, do it!',
        reverseButtons: true,
        showLoaderOnConfirm: true,
        preConfirm(inputValue) {
          return fetch('bulk-report', { method: 'POST', body: JSON.stringify(obj), headers: headers })
            .then(response => {
              try {
                return response.json()
              } catch (e) {
                throw new Error(response.statusText)
              }
            })
            .catch(error => {
              console.log(error);
              return { status: 500, error: error }
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
      }).then((result) => {
        console.log("result ", result)
        if (result.value) {
          swal.showLoading();
          if (result.value.status === 200) {
            if (result.value?.resultFile) {
              const url = 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,' + result.value?.resultFile.xlsxData;
              $('#downloadLink').attr('href', url);
              $('#downloadLink').attr('download', result.value.resultFile.filename);
              document.getElementById('downloadLink').click();
            }

            Swal.fire('Success', result.value.message, 'success').then(() => {
              document.location.reload();
            })
          } else if (result.value.status === 400) {
            Swal.fire('Error', result.value.error, 'error');
          } else {
            Swal.fire('Error', `There was an error reporting the companies.`, 'error');
          }
        }

      })
    })

  $('.reportCompanyBtn').click(function () {
    const isReportedCompany = $(this).data('company-reported')
    const compayId = $(this).data('company-id')
    const obj = {
      isReported: !isReportedCompany 
    }
    const headers = {
      'Content-Type': 'application/json' 
    }
    Swal.fire({
            title: `Are you sure you want to ${!isReportedCompany ? 'report': 'unreport'} this company?`,
            type: 'warning',
            backdrop: true,
            showCancelButton: true,
            cancelButtonColor: "#6c757d",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes, do it!',
            reverseButtons: true,
            showLoaderOnConfirm: true,
            preConfirm(inputValue) {
                return fetch('report-company/' + compayId ,{method: 'POST', body: JSON.stringify(obj), headers: headers})
                        .then(response => {
                            try {
                                return response.json()  
                            } catch (e) {
                                throw new Error(response.statusText)
                            }

                        })
                        .catch(error => {
                            console.log(error);
                            return {status: 500, error: error}

                        });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.value) {
                swal.showLoading();
                if (result.value.status === 200) {
                    Swal.fire('Success', `The company ${!isReportedCompany? 'has been reported successfully': 'is no longer  reported'} .`, 'success').then(() => {
                            document.location.reload();
                      })
                } else if (result.value.status === 400) {
                    Swal.fire('Error', result.value.message, 'error');
                } else {
                    Swal.fire('Error', `There was an error reporting the company.`, 'error');
                }
            }

    })
  })



  $('#btn-arcompany-export-xls').click(function () {
    $('#btn-arcompany-export-xls').prop('disabled', true);
    $('#btn-arcompany-export-xls').hide();
    $("#btn-spinner-export-xls").show();
    let dataToSend = $('#accountingCompaniesSearchForm').serialize();

    $.ajax({
      url: "./export-company-dashboard-xls",
      data: dataToSend,
      method: 'POST',
      xhrFields: {
        responseType: 'blob' // to avoid binary data being mangled on charset conversion
      },
      success: function (blob, status, xhr) {
        $('#btn-arcompany-export-xls').prop('disabled', false);
        $("#btn-spinner-export-xls").hide();
        $('#btn-arcompany-export-xls').show();
        
        
        // check for a filename
        var filename = "";
        var disposition = xhr.getResponseHeader('Content-Disposition');
        if (disposition && disposition.indexOf('attachment') !== -1) {
          var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          var matches = filenameRegex.exec(disposition);
          if (matches != null && matches[1]) filename = matches[1].replace(/['"]/g, '');
        }

        if (typeof window.navigator.msSaveBlob !== 'undefined') {
          window.navigator.msSaveBlob(blob, filename);
        } else {
          var URL = window.URL || window.webkitURL;
          var downloadUrl = URL.createObjectURL(blob);

          if (filename) {
            var a = document.createElement("a");
            if (typeof a.download === 'undefined') {
              window.location.href = downloadUrl;
            } else {
              a.href = downloadUrl;
              a.download = filename;
              document.body.appendChild(a);
              a.click();
            }
          } else {
            window.location.href = downloadUrl;
          }

          setTimeout(function () { URL.revokeObjectURL(downloadUrl); }, 100); // cleanup
        }
      },
      error: function (err){
        Swal.fire('Error', 'There was an error generating the export xlsx file', 'error');
        $('#btn-arcompany-export-xls').prop('disabled', false);
        $("#btn-spinner-export-xls").hide();
        $('#btn-arcompany-export-xls').show();
      }
    })
  });

  $("#clearFormBtn").on('click', () => {
      $("#accountingCompaniesSearchForm")[0].reset();
      $("#accountingCompaniesSearchForm input").val('');
      $('#accountingCompaniesSearchForm input[type=checkbox]').prop('checked', false);
      $("#accountingCompaniesSearchForm select").val('');
    })
</script>



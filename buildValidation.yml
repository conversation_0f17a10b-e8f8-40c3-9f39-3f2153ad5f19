# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript
pool:
  vmImage: windows-latest
jobs:
- job: Build
  workspace:
      clean: all
  steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '18.12.1'
    displayName: 'Install Node.js'

  - script: |
      npm install
    displayName: 'npm install'


  - task: Npm@1
    inputs:
      command: custom
      customCommand: 'run lint'
    displayName: Run ESLint


  - script: |
      npm run build
    displayName: 'npm build'
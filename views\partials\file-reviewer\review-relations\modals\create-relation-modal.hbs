<!-- OPEN OWNER MODAL -->
<div class="modal fade" id="newRelationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modal-relation-title" class="modal-title">Relation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-relation-body" class="modal-body p-3">
                <form action="" class="form" id="newRelationForm">
                    <div class="row">
                        <label class="col-form-label col-sm-6 pt-0" for="createRelationType">
                            Please select the position for the new relation:</label>

                        <div class="col-sm-6">
                            <div class="custom-control custom-checkbox" id="beneficialGroup">
                                <input
                                        type="checkbox"
                                        class="custom-control-input"
                                        name="relationGroupCreate[]"
                                        id="relationGroup1create"
                                        value="beneficial"
                                />
                                <label class="custom-control-label" for="relationGroup1create">Beneficial Owner</label>
                            </div>
                            <div class="custom-control custom-checkbox" id="shareholderGroup">

                                <input
                                        type="checkbox"
                                        class="custom-control-input"
                                        name="relationGroupCreate[]"
                                        id="relationGroup2create"
                                        value="shareholder"
                                />
                                <label class="custom-control-label" for="relationGroup2create">Shareholder</label>
                            </div>
                            <div class="custom-control custom-checkbox" id="directorGroup">

                                <input
                                        type="checkbox"
                                        class="custom-control-input"
                                        name="relationGroupCreate[]"
                                        id="relationGroup3create"
                                        value="director"
                                />
                                <label class="custom-control-label" for="relationGroup3create">Director</label>
                                <div class="invalid-feedback">
                                    You must select at least one position.
                                </div>
                            </div>

                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

                <button class="btn solid royal-blue" type="submit" form="newRelationForm" name="submit" value="Submit">Save</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    let review;
    let relationGroup;
    $('#newRelationModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        review = button.data('review-id');
    });


    $('input[name="relationGroupCreate[]"]').on('change', function () {
        if ($('input[name="relationGroupCreate[]"]:checked').length > 0) {
            $('input[name="relationGroupCreate[]"]').removeClass('is-invalid')
        }
    });

    $('#newRelationForm').submit( function (event) {
        event.preventDefault();

        if ($('input[name="relationGroupCreate[]"]:checked').length === 0) {
            $('input[name="relationGroupCreate[]"]').addClass("is-invalid");
            return false;
        }
        let groups = [];
        $('input[name="relationGroupCreate[]"]:checked').each(function(){
            groups.push($(this).val());

        });

        window.location.href = '/file-reviewer/open-file-review/'+ review +'/relations/'+ groups.join('-') + '/add-relation';
        return false;
    });


</script>

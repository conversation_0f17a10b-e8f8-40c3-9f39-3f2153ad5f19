const express = require("express");
const router = express.Router();

const financialReportController = require("../controllers/financial-report-management/financialReportController");
const uploadController = require('../controllers/financial-report-management/uploadReportFilesController');
const downloadController = require("../controllers/downloadController");

router.get("/", ensureAuthenticatedAccounting({ all: true}), financialReportController.getDashboard);
router.get("/search", ensureAuthenticatedAccounting({ all: true }), financialReportController.getPaginatedSearch);
router.post("/export-search-xls", ensureAuthenticatedAccounting({all: true }),financialReportController.exportXlsxFromPaginatedSearch);

router.get("/import-files", ensureAuthenticatedAccounting({ productOwner: true, manager: true }), financialReportController.getImporterHistory);
router.get("/import-files/new", ensureAuthenticatedAccounting({ productOwner: true, manager: true }), financialReportController.getImporter);
router.post("/import-files/load-file", ensureAuthenticatedAccounting({ productOwner: true, manager: true }), financialReportController.uploadImportReportFile);
router.get("/import-files/download/:filename", ensureAuthenticatedAccounting({ productOwner: true, manager: true }), financialReportController.downloadArchiveFile);


router.get("/company-dashboard", ensureAuthenticatedAccounting({ all: true }), financialReportController.getCompanyDashboard)
router.post("/export-company-dashboard-xls", ensureAuthenticatedAccounting({ productOwner: true, superUser: true, manager: true, accountant: true}), financialReportController.exportXlsxFromCompanyDashboardSearch);

router.get("/requests", ensureAuthenticatedAccounting({ productOwner: true, manager: true, accountant: true }), financialReportController.getPaginatedOfficerRequests);
router.get('/:reportId/report.pdf', ensureAuthenticatedAccounting({ all: true }), financialReportController.downloadSummaryPdf);
router.get("/:reportId/download-files", ensureAuthenticatedAccounting({ manager: true, accountant: true, productOwner: true }), downloadController.downloadAllFinancialReportFiles);

// form
router.get("/:reportId", ensureAuthenticatedAccounting({ productOwner: true, manager: true, accountant: true }), financialReportController.getReportFormPage);
router.post("/:reportId/save", ensureAuthenticatedAccounting({ productOwner: true, manager: true, accountant: true }), financialReportController.saveReportFormPage);

// bulk actions 
router.post("/bulk-reset", ensureAuthenticatedAccounting({ superUser: true, productOwner: true, manager: true, accountant: true }), financialReportController.processBulkReportResetToSaved);
router.post("/bulk-assign", ensureAuthenticatedAccounting({ productOwner: true, manager: true }), financialReportController.processBulkReportAssignOfficer);
router.post("/bulk-report", ensureAuthenticatedAccounting({ productOwner: true, manager: true }), financialReportController.processBulkReport);

// information actions
router.get('/:companyId/show-company-information', ensureAuthenticatedAccounting({  productOwner: true, superUser: true, manager: true, accountant: true }), financialReportController.showCompanyInformation)

// report actions 
router.post('/report-company/:companyId',  ensureAuthenticatedAccounting({ productOwner: true, manager: true }), financialReportController.reportCompany)

// Email Notification 
router.post('/send-email',ensureAuthenticatedAccounting({  productOwner: true, manager: true }), financialReportController.sendEmail)

// form actions
router.post("/:reportId/reset", ensureAuthenticatedAccounting({ superUser: true, productOwner: true, manager: true, accountant: true }), financialReportController.processReportResetToSaved);
router.post("/:reportId/assign", ensureAuthenticatedAccounting({ productOwner: true, manager: true }), financialReportController.assignReportToOfficer);
router.post("/:reportId/decline-request", ensureAuthenticatedAccounting({ productOwner: true, manager: true }), financialReportController.declineReportRequest);
router.post("/:reportId/unassign-in-penalty", ensureAuthenticatedAccounting({ productOwner: true, manager: true }), financialReportController.unassignInPenaltyReport);
router.put("/:reportId/update-period", ensureAuthenticatedAccounting({ superUser: true, productOwner: true, manager: true, accountant: true }), financialReportController.updateFinancialReportPeriod);
router.get("/:reportId/show-information", ensureAuthenticatedAccounting({ productOwner: true, manager: true, accountant: true }), financialReportController.getReportInformationDetails);
router.post("/:reportId/approve", ensureAuthenticatedAccounting({ productOwner: true, manager: true, accountant: true }), financialReportController.approveReportForCompanyExempted);

router.put("/:reportId/request-information", ensureAuthenticatedAccounting({ productOwner: true, manager: true, accountant: true }), financialReportController.startInfoRequest);
router.put("/:reportId/cancel-request-information", ensureAuthenticatedAccounting({ productOwner: true, manager: true, accountant: true }), financialReportController.cancelInfoRequest);
router.get("/:reportId/download/:requestId/:fileId", ensureAuthenticatedAccounting({ productOwner: true, manager: true, accountant: true }), downloadController.downloadFinancialReportRfiFile);

router.get("/:reportId/rfi-files", ensureAuthenticatedAccounting({ productOwner: true, manager: true, accountant: true }), financialReportController.getReportTempRFIListFiles);
router.post("/:reportId/upload-rfi-file", ensureAuthenticatedAccounting({ productOwner: true, manager: true, accountant: true }), uploadController.uploadFinancialReportFile.fields([{ name: 'fileUploaded', maxCount: 5 }]), 
  financialReportController.saveTemporalRfiReportFile);
router.delete("/:reportId/rfi-files/:fileId", ensureAuthenticatedAccounting({ productOwner: true, manager: true, accountant: true }), financialReportController.deleteRFIReportFile);


function ensureAuthenticatedAccounting(options) {
  return function (req, res, next) {
    if (req.session.is_authenticated) {
      const { isAccountingAccountant, isAccountingManager, isAccountingStandard, isAccountingSuperUser, isAccountingProductOwner } = req.session.authentication;
      const { all, accountant, manager, standard, superUser, productOwner } = options;

      if(all === true){
        if (isAccountingAccountant || isAccountingManager || isAccountingStandard || isAccountingSuperUser || isAccountingProductOwner){
          return next();
        }else{
          res.redirect("/not-authorized");
        }
      }else {

        if ( (accountant && isAccountingAccountant) || (manager && isAccountingManager) || (standard && isAccountingStandard) ||
          (superUser && isAccountingSuperUser) || (productOwner && isAccountingProductOwner)) {
          return next();
        } else {
          res.redirect("/not-authorized");
        }
      }

    } else {
      res.redirect("/login");
    }
  }
}
module.exports = router;
(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['importmcccompaniestable'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <table id=\"load-import-mcc-companies-table\" class=\"table  w-100 nowrap\">\r\n        <thead>\r\n            <tr>\r\n                <th>Company Code</th>\r\n                <th>Name</th>\r\n                <th>MC Code</th>\r\n                <th>Incorporation Number</th>\r\n                <th>Referral</th>\r\n                <th>Incorporation Date</th>\r\n                <th>Status</th>\r\n                <th>Fee</th>\r\n                <th>Company Type</th>\r\n                <th>Riskgroup</th>\r\n                <th>Has ITA Date?</th>\r\n                <th>Approved Start Date</th>\r\n                <th>Approved End Date</th>\r\n                <th>Application Date</th>\r\n                <th>Already exist</th>\r\n                <th>Errors</th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"importData") : depth0),{"name":"each","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(17, data, 0),"data":data,"loc":{"start":{"line":24,"column":12},"end":{"line":48,"column":21}}})) != null ? stack1 : "")
    + "        </tbody>\r\n    </table>\r\n";
},"2":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <tr>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"code") || (depth0 != null ? lookupProperty(depth0,"code") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"code","hash":{},"data":data,"loc":{"start":{"line":26,"column":24},"end":{"line":26,"column":32}}}) : helper)))
    + "</td>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"name") || (depth0 != null ? lookupProperty(depth0,"name") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"name","hash":{},"data":data,"loc":{"start":{"line":27,"column":24},"end":{"line":27,"column":32}}}) : helper)))
    + "</td>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"mccode") || (depth0 != null ? lookupProperty(depth0,"mccode") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"mccode","hash":{},"data":data,"loc":{"start":{"line":28,"column":24},"end":{"line":28,"column":34}}}) : helper)))
    + "</td>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"incorporationCode") || (depth0 != null ? lookupProperty(depth0,"incorporationCode") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"incorporationCode","hash":{},"data":data,"loc":{"start":{"line":29,"column":24},"end":{"line":29,"column":45}}}) : helper)))
    + "</td>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"referral") || (depth0 != null ? lookupProperty(depth0,"referral") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"referral","hash":{},"data":data,"loc":{"start":{"line":30,"column":24},"end":{"line":30,"column":36}}}) : helper)))
    + "</td>\r\n                    <td>"
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"incorporationDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":31,"column":24},"end":{"line":31,"column":87}}})) != null ? stack1 : "")
    + "</td>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"status") || (depth0 != null ? lookupProperty(depth0,"status") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"status","hash":{},"data":data,"loc":{"start":{"line":32,"column":24},"end":{"line":32,"column":34}}}) : helper)))
    + "</td>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"fee") || (depth0 != null ? lookupProperty(depth0,"fee") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fee","hash":{},"data":data,"loc":{"start":{"line":33,"column":24},"end":{"line":33,"column":31}}}) : helper)))
    + "</td>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"company_type") || (depth0 != null ? lookupProperty(depth0,"company_type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"company_type","hash":{},"data":data,"loc":{"start":{"line":34,"column":24},"end":{"line":34,"column":40}}}) : helper)))
    + "</td>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"riskgroup") || (depth0 != null ? lookupProperty(depth0,"riskgroup") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"riskgroup","hash":{},"data":data,"loc":{"start":{"line":35,"column":24},"end":{"line":35,"column":37}}}) : helper)))
    + "</td>\r\n                    <td>"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"hasITADate") : depth0),{"name":"if","hash":{},"fn":container.program(5, data, 0),"inverse":container.program(7, data, 0),"data":data,"loc":{"start":{"line":36,"column":24},"end":{"line":36,"column":65}}})) != null ? stack1 : "")
    + "</td>\r\n                    <td>"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"hasITADate") : depth0),{"name":"if","hash":{},"fn":container.program(9, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":37,"column":24},"end":{"line":37,"column":117}}})) != null ? stack1 : "")
    + "</td>\r\n                    <td>"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"hasITADate") : depth0),{"name":"if","hash":{},"fn":container.program(11, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":38,"column":24},"end":{"line":38,"column":115}}})) != null ? stack1 : "")
    + "</td>\r\n                    <td>"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"hasITADate") : depth0),{"name":"if","hash":{},"fn":container.program(13, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":39,"column":24},"end":{"line":39,"column":115}}})) != null ? stack1 : "")
    + "</td>\r\n\r\n                    <td>"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"exist") : depth0),"||",(depth0 != null ? lookupProperty(depth0,"existInternal") : depth0),{"name":"ifCond","hash":{},"fn":container.program(5, data, 0),"inverse":container.program(15, data, 0),"data":data,"loc":{"start":{"line":41,"column":24},"end":{"line":41,"column":88}}})) != null ? stack1 : "")
    + " </td>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"errors") || (depth0 != null ? lookupProperty(depth0,"errors") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"errors","hash":{},"data":data,"loc":{"start":{"line":42,"column":24},"end":{"line":42,"column":34}}}) : helper)))
    + "</td>\r\n                </tr>\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    return " ";
},"5":function(container,depth0,helpers,partials,data) {
    return " Yes ";
},"7":function(container,depth0,helpers,partials,data) {
    return " No";
},"9":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return " "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||container.hooks.helperMissing).call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"approvedITAStartDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":37,"column":43},"end":{"line":37,"column":109}}})) != null ? stack1 : "")
    + " ";
},"11":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return " "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||container.hooks.helperMissing).call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"approvedITAEndDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":38,"column":43},"end":{"line":38,"column":107}}})) != null ? stack1 : "")
    + " ";
},"13":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return " "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||container.hooks.helperMissing).call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"applicationITADate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":39,"column":43},"end":{"line":39,"column":107}}})) != null ? stack1 : "")
    + " ";
},"15":function(container,depth0,helpers,partials,data) {
    return " No ";
},"17":function(container,depth0,helpers,partials,data) {
    return "                <tr>\r\n                    <td colspan=\"16\" class=\"text-left font-italic\">There are no Companies to import</td>\r\n                </tr>\r\n";
},"19":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <table id=\"load-import-mcc-companies-table\" class=\"table  w-100 nowrap\">\r\n        <thead>\r\n        <tr>\r\n            <th>Master Client Code</th>\r\n            <th>Email Option 1</th>\r\n            <th>Email Option 2</th>\r\n            <th>Already exist</th>\r\n            <th>Errors</th>\r\n        </tr>\r\n        </thead>\r\n        <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"importData") : depth0),{"name":"each","hash":{},"fn":container.program(20, data, 0),"inverse":container.program(23, data, 0),"data":data,"loc":{"start":{"line":63,"column":8},"end":{"line":75,"column":17}}})) != null ? stack1 : "")
    + "        </tbody>\r\n    </table>\r\n\r\n";
},"20":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            <tr>\r\n                <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"code") || (depth0 != null ? lookupProperty(depth0,"code") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"code","hash":{},"data":data,"loc":{"start":{"line":65,"column":20},"end":{"line":65,"column":28}}}) : helper)))
    + "</td>\r\n                <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"option1") || (depth0 != null ? lookupProperty(depth0,"option1") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"option1","hash":{},"data":data,"loc":{"start":{"line":66,"column":20},"end":{"line":66,"column":32}}}) : helper)))
    + "</td>\r\n                <td>"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"option2") : depth0),{"name":"if","hash":{},"fn":container.program(21, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":67,"column":20},"end":{"line":67,"column":56}}})) != null ? stack1 : "")
    + "</td>\r\n                <td>"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"exist") : depth0),"||",(depth0 != null ? lookupProperty(depth0,"existInternal") : depth0),{"name":"ifCond","hash":{},"fn":container.program(5, data, 0),"inverse":container.program(15, data, 0),"data":data,"loc":{"start":{"line":68,"column":20},"end":{"line":68,"column":84}}})) != null ? stack1 : "")
    + " </td>\r\n                <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"errors") || (depth0 != null ? lookupProperty(depth0,"errors") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"errors","hash":{},"data":data,"loc":{"start":{"line":69,"column":20},"end":{"line":69,"column":30}}}) : helper)))
    + "</td>\r\n            </tr>\r\n";
},"21":function(container,depth0,helpers,partials,data) {
    var helper, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return " "
    + container.escapeExpression(((helper = (helper = lookupProperty(helpers,"option2") || (depth0 != null ? lookupProperty(depth0,"option2") : depth0)) != null ? helper : container.hooks.helperMissing),(typeof helper === "function" ? helper.call(depth0 != null ? depth0 : (container.nullContext || {}),{"name":"option2","hash":{},"data":data,"loc":{"start":{"line":67,"column":37},"end":{"line":67,"column":48}}}) : helper)))
    + " ";
},"23":function(container,depth0,helpers,partials,data) {
    return "            <tr>\r\n                <td colspan=\"5\" class=\"text-left font-italic\">There are no Master Clients to import</td>\r\n            </tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||container.hooks.helperMissing).call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"importType") : depth0),"Company",{"name":"ifEquals","hash":{},"fn":container.program(1, data, 0),"inverse":container.program(19, data, 0),"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":79,"column":13}}})) != null ? stack1 : "")
    + "\r\n";
},"useData":true});
})();
const FinancialReportModel = require("../models/financialreport");
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config();


const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const result = await removeARCompleteDetailsFields();

    console.log('Script run success', result);
  } catch (error) {
    console.error('Error en el script:', error);
  } finally {
    mongoose.disconnect();
  }
}

// update complete details object for reports on v1 to remove unused values on v2
async function removeARCompleteDetailsFields() {
  try {
    let updateLog = [['Entry ID', 'Company', 'Status', 'Update date', 'Action']];

    const incomeRemovedFields = [
      "dividendAmount",
      "interestAmount",
      "netGainOnInvestments",
      "otherIncome"
    ]

    const expensesRemovedFields = [
      "netLossOnInvestments",
      "compAdminFees",
      "portMngmntFees",
      "bankFees",
      "loanInterest",
      "otherExpenses",
    ]

    const reportsToUpdate = await FinancialReportModel.find({
      version: "1.0",
      status: {
        "$nin": ["CONFIRMED", "HELP COMPLETED"]
      },
      "reportDetails.serviceType": { 
        "$in": ["self-service-complete", "trident-service-complete", "trident-service-drop"]
      }
    }, 
    {
      _id: 1, 
      companyData:1, 
      status:1,
      completeDetails:1,
      version:1
    });

    for (let index = 0; index < reportsToUpdate.length; index++) {
      const report = reportsToUpdate[index];
      
      try {
        const completeDetails = report.completeDetails ? report.completeDetails.toObject() : {};

        const incomeObj = completeDetails?.income ?? {};
        const expensesObj = completeDetails?.expenses ?? {};
        const assetsObj = completeDetails?.assets ?? {};
        const liabilitiesObj = completeDetails?.liabilities ?? {};

        // check if has any field to remove
        const hasRemovedFields = hasAnyRemovedField(incomeObj, incomeRemovedFields) || 
          hasAnyRemovedField(expensesObj, expensesRemovedFields) ||
          assetsObj.otherAssets !== undefined ||
          liabilitiesObj.otherLiabilities !== undefined;

        let updateValues = {};
        let skipLog = false;

        // if has removed field then create the completeDetails object of v2
        if (hasRemovedFields === true){
          updateValues = {
            version: "2.0",
            completeDetails: {
              income: {
                total: incomeObj.total !== undefined ? incomeObj.total : null,
                costOfSales: incomeObj.costOfSales !== undefined ? incomeObj.costOfSales : null,
              },
              expenses: {
                operatingExpenses: expensesObj.operatingExpenses !== undefined? expensesObj.operatingExpenses : null,
                totalOtherExpenses: expensesObj.totalOtherExpenses !== undefined? expensesObj.totalOtherExpenses : null,
                incomeTax: expensesObj.incomeTax !== undefined? expensesObj.incomeTax : null,
                totalOfExpenses: expensesObj.totalOfExpenses !== undefined? expensesObj.totalOfExpenses : null,
              },
              assets: {
                cashAmount: assetsObj.cashAmount !== undefined? assetsObj.cashAmount : null,
                loansAndReceivables: assetsObj.loansAndReceivables !== undefined ? assetsObj.loansAndReceivables : null,
                investmentsAssetsAmount: assetsObj.investmentsAssetsAmount !== undefined ? assetsObj.investmentsAssetsAmount : null,
                fixedAssetsAmount: assetsObj.fixedAssetsAmount !== undefined ? assetsObj.fixedAssetsAmount : null,
                intangibleAssetsAmount: assetsObj.intangibleAssetsAmount !== undefined ?  assetsObj.intangibleAssetsAmount : null,
                total: assetsObj.total !== undefined ? assetsObj.total  : null,
                totalOtherAssets: assetsObj.totalOtherAssets !== undefined ? assetsObj.totalOtherAssets : null,
              },
              liabilities: {
                accountsPayable: liabilitiesObj.accountsPayable !== undefined ? liabilitiesObj.accountsPayable : null,
                longTermDebts: liabilitiesObj.longTermDebts !== undefined ? liabilitiesObj.longTermDebts : null,
                total: liabilitiesObj.total !== undefined ? liabilitiesObj.total : null,
                totalOtherLiabilities: liabilitiesObj.totalOtherLiabilities !== undefined ? liabilitiesObj.totalOtherLiabilities : null,
              },
              shareholderEquity: completeDetails?.shareholderEquity !== undefined? completeDetails.shareholderEquity : null,
              grossProfit: completeDetails?.grossProfit !== undefined ? completeDetails.grossProfit : null,
              netIncome: completeDetails?.netIncome !== undefined ? completeDetails.netIncome : null,
            }
          }          
        }else{
          // update only the version because there is not fields to remove, also skip the log
          updateValues = { version: "2.0"}
          skipLog = true;
        }
        
        const result = await FinancialReportModel.updateOne({ _id: report._id }, {
          $set: updateValues
        });
        
        if (result.nModified > 0) {
          if(skipLog === false){
            updateLog.push([report._id?.toString(), report.companyData?.code, report.status, new Date(), 'UPDATED']);
          }
        } else {
          updateLog.push([report._id?.toString(), report.companyData?.code, "", new Date(), 'ERROR: NOT FOUND']);
        }
        

      } catch (error) {
        console.error('Error:', error.message);
        updateLog.push([report._id?.toString(), report.companyData?.code, "", new Date(), 'ERROR UPDATING']);
      }
    }

    // create entities bo
    console.log("accounting records updated ", updateLog.length - 1);



    const filename = 'remove_ar_fields__log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'reports ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);


    return { "success": true, "totalRows": updateLog.length - 1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}

function hasAnyRemovedField(object, fields) {
  for (const field of fields) {
    if (object[field] !== undefined) {
      return true;
    }
  }
  return false;
}


runScript();
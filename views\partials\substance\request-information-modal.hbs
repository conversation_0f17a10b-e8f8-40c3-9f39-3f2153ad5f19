{{!-- REQUEST INFORMATION MODAL --}}
<div class="modal fade" id="rfiModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Request Information</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <div class="row">
                    <div class="col-xs-12 col-md-12 col-lg-6">
                        <label for="deadlineDate">
                            Request deadline:
                        </label>

                        <input id="deadlineDate" name="deadlineDate"  class="form-control datepicker"  type="text" placeholder="MM/DD/YYYY"  >
                    </div>
                </div>
                <br>

                <div class="row">
                    <div class="col-12">
                        <label for="commentRFI" >
                            Please specify the information that the client has to provide:
                        </label>
                        <textarea id="commentRFI"  class="form-control" rows="3"  name="commentRFI"
                                  placeholder="Write the information to request..."
                                  maxlength="2000"  aria-label="Write the information to request..." ></textarea>
                    </div>
                </div>
                <br>
                <div id="uploadRFIFilesDiv">
                    <label >
                        Add documentation:
                    </label>
                    <div id="uploadRequestInfoFiles" class="dropzone p-1">
                        <div class="fallback">
                            <input name="fileUploaded" type="file" multiple/>
                        </div>
                        <div class="dz-message needsclick my-1">
                            <i class="h1 my-0 text-muted dripicons-cloud-upload"></i>
                            <h4>
                                Drop files here or click to upload.
                            </h4>
                            <span class="text-muted">
                                Maximum of 3 File(s), PDF only. File must not be password protected. <br>
                                Files will be automatically uploaded.
                            </span>
                        </div>
                    </div>
                    <div id="uploadedRFIFiles" class="mt-2 text-center text-muted">

                    </div>
                </div>
            </div>

            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

                <button
                        id="submitRFIButton"
                        type="button"
                        class="btn btn-primary waves-effect waves-light"
                >
                    Submit
                </button>

                <button id="submitRFISpinner" style="display: none;" class="btn btn-primary waves-effect waves-light" type="button" disabled>
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    Submit...
                </button>
            </div>
        </div>
    </div>
</div>
<script type='text/javascript' src='/javascripts/form-advanced.init.js'></script>
<script type="text/javascript" src="/templates/substance/uploadedrfifiles.precompiled.js"></script>
<script type="text/javascript">
    Dropzone.autoDiscover = false;
    let entryRfiId = "";
    let myDropzone;
    $(document).ready(function() {
        initUploadDropZone();

        $( "#deadlineDate" ).datepicker({
            format: 'mm/dd/yyyy',
            autoclose: true
        }).on('hide', function(event) {
            event.preventDefault();
            event.stopPropagation();
        });
    });

    $('#rfiModal').on('shown.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        entryRfiId = button.data('entry-id');
        refreshRFIFiles();
    });


    $('#rfiModal').on('hide.bs.modal', function () {
        $("#commentRFI").val('');
        $('#deadlineDate').val('');
        $('#uploadedRFIFiles').html('');
        Dropzone.forElement('#uploadRequestInfoFiles').removeAllFiles(true)
    });

    $("#submitRFIButton").on('click', function (event) {
        $(this).prop('disabled', true);
        $(this).hide();
        $("#submitRFISpinner").show();
        event.stopPropagation();
        const comment = $("#commentRFI").val();
        const deadLineDate = $("#deadlineDate").val();
        if (!comment || comment === '') {
            toastr["warning"]("Please provide the information to request");
            $("#submitRFISpinner").hide();
            $(this).prop('disabled', false).show();
            return false;
        }

        if (!deadLineDate || deadLineDate === '') {
            toastr["warning"]("Please enter the request deadline");
            $("#submitRFISpinner").hide();
            $(this).prop('disabled', false).show();
            return false;
        }
        $.ajax({
            type: "PUT",
            url: `/substance/${entryRfiId}/request-information`,
            contentType: "application/json; charset=utf-8",
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            data: JSON.stringify({
                requestComment: comment,
                deadLineDate: deadLineDate
            }),
            success: function (data) {
                if (data.status === 200) {
                    Swal.fire('Success', data.message, 'success').then(() => {
                        location.reload();
                    });
                } else if (data.status === 400) {
                    Swal.fire('Error', data.error, 'error');
                    $("#submitRFIButton").prop('disabled', false).show();
                    $("#submitRFISpinner").hide();

                }
            },
            error: (err) => {
                if (err.responseJSON?.error){
                    toastr["error"](err.responseJSON?.error);
                }
                else {
                    toastr["error"]('Sorry, There was an error requesting the information');
                }
                $("#submitRFISpinner").hide();
                $("#submitRFIButton").prop('disabled', false).show();
            },

        });
    });

    function initUploadDropZone(){
        let field = '';
        new Dropzone('#uploadRequestInfoFiles', {
            url: '/',
            acceptedFiles: 'application/pdf',
            autoProcessQueue: true,
            parallelUploads: 3,
            maxFiles: 3,
            maxFilesize: 5,
            autoDiscover: false,
            paramName: function () {
                return 'fileUploaded';
            },
            uploadMultiple: true,
            init: function () {
                this.on('processing', function () {
                    this.options.url = `/substance/${entryRfiId}/upload-rfi-file`;
                });
                this.on('success', function () {
                    refreshRFIFiles();
                });
                this.on('sending', function (file, xhr, formData) {
                    if (!formData.has('fileName')) {
                        formData.append('fileName', field);
                    }
                    if (!formData.has('entryId')) {
                        formData.append('entryId', entryRfiId);
                    }
                });

                this.on('errormultiple', function () {
                  toastr["error"]("There was an error uploading the file, please try again...");
                });

                this.on('maxfilesexceeded', function () {
                    toastr["warning"]("The limit of files to upload is three.");
                });

                this.on('resetFiles', function () {
                    if (this.files.length !== 0) {
                        for (let i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                    }
                });
            },
        });
    }

    function refreshRFIFiles() {
        $.get(`/substance/${entryRfiId}/rfi-files`, function (data) {

            let template = Handlebars.templates.uploadedrfifiles;
            let d = {
                entryId: entryRfiId,
                requestId: data.requestId || null,
                files: data.files ? data.files : [],
            };
            let html = template(d);
            $('#uploadedRFIFiles').html(html);
        });
    }

    function deleteRfiFile(entryId,requestId,fileId, filename){
        $.ajax({
            type: 'DELETE',
            url: `/substance/${entryId}/rfi-files/${fileId}`,
            data: {
                requestId: requestId,
            },
            success: function (res) {
                if (res.status === 200) {
                    refreshRFIFiles();
                    const objDZ = Dropzone.forElement('#uploadRequestInfoFiles');
                    const file = objDZ.files.find((file) => file.name === filename);
                    if (file){
                        objDZ.removeFile(file);
                    }
                }else{
                    toastr["error"]("There was an error deleting the file, please try again...");
                }
            },
            dataType: 'json',
        });
        return false;
    }

</script>

(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['incorporationfilestable'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"table-responsive\">\r\n        <table class=\"table table-striped mb-0\">\r\n            <thead>\r\n            <tr>\r\n                <th scope=\"col\">File</th>\r\n                <th scope=\"col\">Type</th>\r\n                <th scope=\"col\">Name</th>\r\n                <th scope=\"col\" style=\"width: 10%\">Download</th>\r\n            </tr>\r\n            </thead>\r\n            <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"files") : depth0),{"name":"each","hash":{},"fn":container.program(2, data, 0, blockParams, depths),"inverse":container.program(4, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":13,"column":12},"end":{"line":29,"column":21}}})) != null ? stack1 : "")
    + "            </tbody>\r\n        </table>\r\n    </div>\r\n";
},"2":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <tr>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"fileName") || (depth0 != null ? lookupProperty(depth0,"fileName") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileName","hash":{},"data":data,"loc":{"start":{"line":15,"column":24},"end":{"line":15,"column":36}}}) : helper)))
    + "</td>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"mimeType") || (depth0 != null ? lookupProperty(depth0,"mimeType") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"mimeType","hash":{},"data":data,"loc":{"start":{"line":16,"column":24},"end":{"line":16,"column":36}}}) : helper)))
    + "</td>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"originalName") || (depth0 != null ? lookupProperty(depth0,"originalName") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"originalName","hash":{},"data":data,"loc":{"start":{"line":17,"column":24},"end":{"line":17,"column":40}}}) : helper)))
    + "</td>\r\n                    <td class=\"pl-2 py-1 text-center align-middle\">\r\n                        <a href=\"/client-incorporation/"
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"incorporationId") : depths[1]), depth0))
    + "/download-document/"
    + alias4(((helper = (helper = lookupProperty(helpers,"fileId") || (depth0 != null ? lookupProperty(depth0,"fileId") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileId","hash":{},"data":data,"loc":{"start":{"line":19,"column":96},"end":{"line":19,"column":106}}}) : helper)))
    + "?relation="
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"relationId") : depths[1]), depth0))
    + "&fileType="
    + alias4(((helper = (helper = lookupProperty(helpers,"fileTypeId") || (depth0 != null ? lookupProperty(depth0,"fileTypeId") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileTypeId","hash":{},"data":data,"loc":{"start":{"line":19,"column":143},"end":{"line":19,"column":157}}}) : helper)))
    + "\"\r\n                        target=\"_blank\">\r\n                            <i class=\"fa fa-download\" aria-hidden=\"true\"> </i>\r\n                        </a>\r\n                    </td>\r\n                </tr>\r\n";
},"4":function(container,depth0,helpers,partials,data) {
    return "                    <tr>\r\n                        <td colspan=\"4\" class=\"text-center font-italic\">There are no uploaded files</td>\r\n                    </tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"files") : depth0),{"name":"if","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":33,"column":7}}})) != null ? stack1 : "");
},"useData":true,"useDepths":true});
})();
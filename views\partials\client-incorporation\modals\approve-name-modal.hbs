{{!-- CONFIRM MODAL --}}
<div class="modal fade" id="approveNameReservationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div  class="modal-body p-3 text-justify">
                <div id="message_modal">
                    <form id="approveNameReservationForm" novalidate>
                        <div class="row mb-1">
                            <div class="col-md-8">
                                <label>Is this name approved?</label>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex justify-content-end">
                                    <div class="custom-control custom-radio ml-2">
                                        <input class="custom-control-input form-control" type="radio"
                                               id="approvedNameYes" name="approvedName" value="YES" required>
                                        <label class="custom-control-label" for="approvedNameYes">
                                            Yes
                                        </label>
                                    </div>
                                    <div class=" custom-control custom-radio ml-2">
                                        <input class="custom-control-input form-control" type="radio"
                                               id="approvedNameNo" name="approvedName" value="NO">
                                        <label class="custom-control-label" for="approvedNameNo">
                                            No
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="suggestionBox" style="display: none">
                            <label >Please suggest some valid names:</label>
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="suggestion1">Name #1*</label>
                                    <input type="text" class="form-control"  id="suggestion1"  name="suggestions[]"
                                           value="" required/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="suggestion2">Name #2</label>
                                    <input type="text" class="form-control"  id="suggestion2"  name="suggestions[]"
                                           value=""/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="suggestion3">Name #3</label>
                                    <input type="text" class="form-control"  id="suggestion3"  name="suggestions[]"
                                           value=""/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="suggestion4">Name #4</label>
                                    <input type="text" class="form-control"  id="suggestion4"  name="suggestions[]"
                                           value=""/>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="submit" class="btn solid royal-blue" id="sendReservationNameBtn" form="approveNameReservationForm">
                    Confirm
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    $('#approveNameReservationModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
    });

    $('#approveNameReservationModal').on('hide.bs.modal', function (event) {
        $("#sendReservationNameBtn").prop('disabled', false);
        $("#suggestionBox").hide();
        $("#approveNameReservationForm").trigger("reset");
    });



    $("input[name='approvedName']").on('change', function (e) {
        const value = $(this).val() === 'NO';
        if (value){
            $("#suggestionBox").show(200);
        }
        else{
            $("#suggestionBox").hide();
        }
    });

    $("#approveNameReservationForm").on('submit', function (event) {
        event.preventDefault();
        $(this).prop('disabled', true);

        $('#approveNameReservationForm input[required]:visible ').trigger('keyup');
        $("#approveNameReservationForm input[type='radio'][required]:visible ").each(function () {
            const val = $('input:radio[name="' + this.name + '"]:checked').val();

            if (val === undefined) {
                $('input:radio[name="' + this.name + '"]').toggleClass("is-invalid", true);
            } else {
                $('input:radio[name="' + this.name + '"]').toggleClass("is-invalid", false);
            }

        });
        if ($(".is-invalid:visible").length === 0) {
            $.ajax({
                type: "POST",
                url: window.location.pathname + "/update-reservation-status",
                data: $(this).serialize(),
                success: () => {
                    Swal.fire('Success','Client company information updated successfully.', 'success').then(() => {
                        location.href = '/client-incorporation/list';
                    });
                },
                error: (err) => {
                    Swal.fire('Error', 'There was an error updating the information.', 'error').then(() => {
                        $('#approveNameReservationModal').modal('hide');
                    });
                },
            });
        } else {
            setTimeout(function () {
                $("#sendReservationNameBtn").prop('disabled', false);
            }, 1);
        }
    });
</script>

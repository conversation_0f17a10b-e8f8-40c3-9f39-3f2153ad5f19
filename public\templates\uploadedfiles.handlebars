{{#if files}}
    <div class="table-responsive">
        <table class="table table-striped mb-0">
            <thead>
            <tr>
                <th style="width: 60%">Uploaded Files</th>
                <th style="width: 20%">Download</th>
                <th style="width: 20%">Delete</th>
            </tr>
            </thead>
            <tbody>
            {{#each files}}
                <tr>
                    <td>{{originalName}}</td>
                    <td>
                        <a type="button" class="btn btn solid royal-blue"
                           onclick="downloadFile('{{../id}}','{{../group}}','{{../rowId}}','{{fileId}}');return false">
                            Download</a>
                    </td>
                    <td>
                        <button class="demo-delete-row btn btn-danger btn-xs btn-icon"
                                onclick="deleteFile('{{../id}}','{{../group}}','{{../rowId}}','{{fileId}}' , '{{ blobName }}');return false">
                            <i class="fa fa-times"></i>
                        </button>
                    </td>
                </tr>
            {{/each}}
            </tbody>
        </table>
    </div> <!-- end .padding -->
{{/if}}

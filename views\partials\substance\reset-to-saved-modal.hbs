<div class="modal hide fade confirmation-modal-md" tabindex="-1" role="dialog" aria-labelledby="mediumModal"
  id="confirmation_modal" style="display: none;" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="mediumModal">Confirmation</h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-12">
            <p>This will re-open the submission for the client to amend changes. Are you sure?
            </p>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <div class="form-group">
              <label class="mt-1" for="reopenReason">Please provide a note for the client:</label>
              <textarea rows="3" name="reopenReason" id="reopenReason" class="form-control" required
                minlength="50"></textarea>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label>Do you want to correct the Financial Period?*</label>
            <div class="custom-control custom-radio">
              <input type="radio" class="custom-control-input" id="changeFinancialPeriodYes" required
                name="changeFinancialPeriod" value="true">
              <label class="custom-control-label" for="changeFinancialPeriodYes">Yes</label>
            </div>
            <div class="custom-control custom-radio">
              <input type="radio" class="custom-control-input" id="changeFinancialPeriodNo" name="changeFinancialPeriod"
                value="false">
              <label class="custom-control-label" for="changeFinancialPeriodNo">No</label>
            </div>
          </div>
        </div>
        <div class="row" id="changePeriodDatesRow" style="display: none">
          <div class="col-6">
            <label for="newFinancialStartDate">Correct start date:</label>
            <input type="text" class="form-control datepicker mr-2" placeholder="MM/DD/YYYY"
              name="newFinancialStartDate" id="newFinancialStartDate">
            <div class="w-100 text-right">
              <h6 id="currentStartDate"></h6>
            </div>

          </div>
          <div class="col-6">
            <label for="newFinancialEndDate">Correct end date:</label>
            <input type="text" class="form-control datepicker" placeholder="MM/DD/YYYY" name="newFinancialEndDate"
              id="newFinancialEndDate">
            <div class="w-100 text-right">
              <h6 id="currentEndDate"></h6>
            </div>
          </div>
        </div>


      </div>
      <div class="modal-footer">
        <input type="button" class="btn btn-secondary waves-effect waves-light" data-dismiss="modal" value="Cancel">

        <input type="button" class="btn btn-primary waves-effect waves-light ml-2" id="confirmIndividualRTSBtn"
          value="Confirm">
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>
<script type="text/javascript">

  $('#confirmation_modal').on('hide.bs.modal', function () {
    $("#reopenReason").val('');
    $("input[name='changeFinancialPeriod']").prop('checked', false);
    $("#newFinancialStartDate").val('');
    $("#newFinancialEndDate").val('');
    $("#changePeriodDatesRow").hide();
  });

  $("input[name='changeFinancialPeriod']").on('change', function () {
    const val = $("input[name='changeFinancialPeriod']:checked").val() === 'true';

    if (val) {
      $("#changePeriodDatesRow").show();
    } else {
      $("#changePeriodDatesRow").hide();
    }
  })
  
  $("#confirmIndividualRTSBtn").on('click', function(e) {
    $('#confirmIndividualRTSBtn').prop('disabled', true);
    const reason = $("#reopenReason").val();
    const changePeriodDate = $("input[name='changeFinancialPeriod']:checked").val();
    if (!reason || reason === "") {
      toastr["warning"]('Please provide the reason to re-open.');
      $('#confirmIndividualRTSBtn').prop('disabled', false);
      return false;
    }
    else if (reason && reason.length < 50) {
      toastr["warning"]('Please provide more information.');
      $('#confirmIndividualRTSBtn').prop('disabled', false);
      return false;
    }

    if (!changePeriodDate || changePeriodDate === "") {
      toastr["warning"]('Please make a selection to correct the financial period');
      $('#confirmIndividualRTSBtn').prop('disabled', false);
      return false;
    }

    const newFinancialStartDate = changePeriodDate === "true" ? $("#newFinancialStartDate").val() : null;
    const newFinancialEndDate = changePeriodDate === "true" ? $("#newFinancialEndDate").val() : null;

    if (changePeriodDate === "true" && (!newFinancialStartDate || !newFinancialEndDate)) {
      toastr["warning"]('Please select the correct financial start/end dates');
      $('#confirmIndividualRTSBtn').prop('disabled', false);
      return false;
    }



    $.ajax({
      type: "POST",
      url: "./search/save",
      contentType: "application/json; charset=utf-8",
      data: JSON.stringify({
        id: selectedId,
        company: selectedCompany,
        reason: reason,
        changePeriodDate: changePeriodDate === "true",
        newFinancialStartDate: newFinancialStartDate,
        newFinancialEndDate: newFinancialEndDate,
      }),
      success: function (data) {
        if (data.success) {
          toastr.success('Submission updated successfully');
          window.setTimeout(function () {
            document.location.reload();
          }, 200)
        } else {
          toastr["error"]('Sorry, there was an error updating the submission.');
          $('#confirmIndividualRTSBtn').prop('disabled', false);
        }
      },
      error: (err) => {
        if (err.responseJSON?.error) {
          toastr["error"](err.responseJSON?.error);
          $('#confirmIndividualRTSBtn').prop('disabled', false);
        }
        else {
          toastr["error"]('Sorry, there was an error updating the submission.');
          $('#confirmIndividualRTSBtn').prop('disabled', false);
        }
      },

    });
  });
</script>
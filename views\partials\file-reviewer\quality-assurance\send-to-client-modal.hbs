{{!-- CONFIRM CLIENT MODAL --}}
<div class="modal fade" id="confirmClientModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                You are about to submit the File Review back to
                <span class="font-weight-bold">Client.</span> ¿Are you sure?
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="button" class="btn solid royal-blue" id="sendToClientButton" data-status="send-client">Confirm</button>
            </div>
        </div>
    </div>
</div>

<script>
    $sendButton = $('#sendToClientButton');
    const clientId = $('#send-to-client-button').data('id');
    const clientStatus =  $sendButton.data('status');
    $sendButton.on('click',function () {
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/quality-assurance-review/'+clientId + '/submit-review',
            data: {
                status: clientStatus
            },
            success: () => {
                Swal.fire('Success', 'The review has been submitted to the Client successfully', 'success').then(() => {
                    location.href = '/file-reviewer/quality-assurance-list';
                });
            },
            error: (err) => {
                $('#confirmClientModal').modal('hide');
                Swal.fire('Error', 'There was an error submitting your review to the Client', 'error');
            },
        });
    });
</script>

const express = require("express");
const router = express.Router();

const substance_controller = require("../controllers/substance");
const export_files_controller = require("../controllers/exportFilesController");
const export_search_controller = require("../controllers/exportSearchController");
const download_controller = require("../controllers/downloadController");
const upload_controller = require('../controllers/uploadController');

router.get("/", ensureAuthenticated, substance_controller.getDashboard);

router.get("/search", ensureAuthenticated, substance_controller.getSearch);
router.get("/search-paginated", ensureAuthenticated, substance_controller.getPaginatedSubstanceSearch);
router.get("/export-paginated", ensureAuthenticated, substance_controller.getPaginatedExport);
router.post("/search", ensureAuthenticated, substance_controller.getSearch);
router.post("/search/save", ensureAuthenticated, substance_controller.saveSubmission);
router.put("/search/update-financial-period", ensureAuthenticated, substance_controller.updateFinancialPeriod);
router.get("/:entryId/submission.pdf", ensureAuthenticated, substance_controller.downloadPdf);
router.get("/:entryId/support-attachments", ensureAuthenticated, substance_controller.downloadSupportAttachments);
router.get("/pending-payments", ensureAuthenticated, substance_controller.getPendingPayments);
router.post("/pending-payments", ensureAuthenticated, substance_controller.getPendingPayments);
router.post("/pending-payments/pay", ensureAuthenticated, substance_controller.markAsPaid);
router.get("/export-overview", ensureAuthenticated, substance_controller.getExportOverview);
router.post("/export-overview", ensureAuthenticated, substance_controller.getExportOverview);
router.get("/import-entries", ensureAuthenticated, substance_controller.getImportEntries);
router.post("/import-entries", ensureAuthenticated, substance_controller.saveImportEntries);
router.post("/import-entries/load-file", ensureAuthenticated, substance_controller.processImportEntries);
router.post("/export", ensureAuthenticated, substance_controller.doExport);
router.post("/export-xls", ensureAuthenticated, export_files_controller.doExportXls);
router.post("/export-search-xls", ensureAuthenticated, export_search_controller.exportSearchXls);
router.post("/export-evidence", ensureAuthenticated, substance_controller.doExportEvidence);
router.post("/export-json", ensureAuthenticated, substance_controller.doExportJSON);
router.post("/reset-to-saved-bulk", ensureAuthenticated, substance_controller.processBulkEntryResetToSaved);

router.put("/:entryId/request-information", ensureAuthenticated, substance_controller.sendRequestInformation);
router.put("/:entryId/cancel-request-information", ensureAuthenticated, substance_controller.cancelRequestInformation);
router.get("/:entryId/show-information", ensureAuthenticated, substance_controller.getInformationDetails);
router.get("/:entryId/download/:requestId/:fileId", ensureAuthenticated, download_controller.downloadEntryRfiFile);

router.post("/:entryId/upload-rfi-file", ensureAuthenticated,
  upload_controller.uploadSubstanceFile.fields([{name: 'fileUploaded', maxCount: 5}]),
  substance_controller.saveTemporalRFIFile);
router.get("/:entryId/rfi-files", ensureAuthenticated, substance_controller.getTempRFIListFiles);
router.delete("/:entryId/rfi-files/:fileId", ensureAuthenticated, substance_controller.deleteRFIFile);

router.get("/import-payments", ensureAuthenticatedSuperUser, substance_controller.getImportPayments);
router.put("/import-payments", ensureAuthenticatedSuperUser, substance_controller.updateBulkEntryPayments);
router.post("/import-payments/load-file", ensureAuthenticatedSuperUser, substance_controller.processBulkEntryPayments);

router.get("/requests-information", ensureAuthenticatedSuperUser, substance_controller.getRequestsInformationView);

function ensureAuthenticated(req, res, next) {
  if (req.session.is_authenticated ) {
    if (req.session.authentication.isSubsManagers || req.session.authentication.isSubsSuperUser ) {
      return next();
    } else {
      res.redirect("/not-authorized");
    }
  } else {
    res.redirect("/login");
  }
}

function ensureAuthenticatedSuperUser(req, res, next) {
  if (req.session.is_authenticated ) {
    if (req.session.authentication.isSubsSuperUser ) {
      return next();
    } else {
      res.redirect("/not-authorized");
    }
  } else {
    res.redirect("/login");
  }
}

module.exports = router;

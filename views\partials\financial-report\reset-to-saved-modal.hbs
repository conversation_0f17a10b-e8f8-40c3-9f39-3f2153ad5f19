<div class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="mediumModal"
  id="rtsModal" style="display: none;" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="mediumModal">Confirmation</h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-12">
            <p>This will re-open the submission for the client to amend changes. Are you sure?
            </p>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <div class="form-group">
              <label class="mt-1" for="reopenReason">Please provide a note for the client:</label>
              <textarea rows="3" name="reopenReason" id="reopenReason" class="form-control" required
                minlength="50"></textarea>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label>Do you want to correct the Financial Period?*</label>
            <div class="custom-control custom-radio">
              <input type="radio" class="custom-control-input" id="changeFinancialPeriodYes" required
                name="changeFinancialPeriod" value="true">
              <label class="custom-control-label" for="changeFinancialPeriodYes">Yes</label>
            </div>
            <div class="custom-control custom-radio">
              <input type="radio" class="custom-control-input" id="changeFinancialPeriodNo" name="changeFinancialPeriod"
                value="false">
              <label class="custom-control-label" for="changeFinancialPeriodNo">No</label>
            </div>
          </div>
        </div>
        <div class="row" id="rtsNewPeriodDatesRow" style="display: none">
          <div class="col-6">
            <label for="rtsNewFinancialStartDate">Correct start date:</label>
            <input type="text" class="form-control mr-2" placeholder="MM/DD/YYYY"
              name="rtsNewFinancialStartDate" id="rtsNewFinancialStartDate">
            <div class="w-100 text-right">
              <h6 id="currentStartDate"></h6>
            </div>

          </div>
          <div class="col-6">
            <label for="rtsNewFinancialEndDate">Correct end date:</label>
            <input type="text" class="form-control " placeholder="MM/DD/YYYY" name="rtsNewFinancialEndDate"
              id="rtsNewFinancialEndDate">
            <div class="w-100 text-right">
              <h6 id="currentEndDate"></h6>
            </div>
          </div>
        </div>


      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
          Close
        </button>

        <button id="submitRTSButton" type="button" class="btn btn-primary waves-effect waves-light">
          Confirm
        </button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>
<script type="text/javascript">
  let rtsId= "";
  let rtsCompany = "";

  $('#rtsModal').on('shown.bs.modal', function (event) {
    let button = $(event.relatedTarget);
    rtsId = button.data('report-id');
    const startDate = button.data('start-date');
    const endDate = button.data('end-date');

    $("#currentStartDate").html('Current: ' + startDate);
    $("#currentEndDate").html('Current: ' + endDate);

    $("#rtsNewFinancialStartDate").flatpickr({
      dateFormat: "m/d/Y",
      autoclose: true,
      allowInput: true,
      defaultDate: startDate
    })

    $("#rtsNewFinancialEndDate").flatpickr({
      dateFormat: "m/d/Y",
      autoclose: true,
      allowInput: true,
      defaultDate: endDate
    })

  });


  $('#rtsModal').on('hide.bs.modal', function () {
    $("#reopenReason").val('');
    $("input[name='changeFinancialPeriod']").prop('checked', false);
    $("#rtsNewFinancialStartDate").val('');
    $("#rtsNewFinancialEndDate").val('');
    $("#rtsNewPeriodDatesRow").hide();
    rtsId = "";
    rtsCompany = "";
  });

  $("#submitRTSButton").on('click', function (event) {
    $('#submitRTSButton').prop('disabled', true);
    const reason = $("#reopenReason").val();
    const changePeriodDate = $("input[name='changeFinancialPeriod']:checked").val();
    if (!reason || reason === "") {
      toastr["warning"]('Please provide the reason to re-open.');
      $('#submitRTSButton').prop('disabled', false);
      return false;
    }
    else if (reason && reason.length < 50) {
      toastr["warning"]('Please provide more information.');
      $('#submitRTSButton').prop('disabled', false);
      return false;
    }

    if (!changePeriodDate || changePeriodDate === "") {
      toastr["warning"]('Please make a selection to correct the financial period');
      $('#submitRTSButton').prop('disabled', false);
      return false;
    }

    const newFinancialStartDate = changePeriodDate === "true" ? $("#rtsNewFinancialStartDate").val() : null;
    const newFinancialEndDate = changePeriodDate === "true" ? $("#rtsNewFinancialEndDate").val() : null;

    if (changePeriodDate === "true" && (!newFinancialStartDate || !newFinancialEndDate)) {
      toastr["warning"]('Please select the correct financial start/end dates');
      $('#submitRTSButton').prop('disabled', false);
      return false;
    }

    $.ajax({
      type: "POST",
      url: `/financial-report-management/${rtsId}/reset`,
      contentType: "application/json; charset=utf-8",
      data: JSON.stringify({
        reason: reason,
        changePeriodDate: changePeriodDate === "true",
        newFinancialStartDate: newFinancialStartDate,
        newFinancialEndDate: newFinancialEndDate,
      }),
      success: function (data) {
        if (data.status === 200) {
          toastr.success('Report updated successfully');
          window.setTimeout(function () {
            document.location.reload();
          }, 200)
        } else {
          toastr["error"]('Sorry, there was an error updating the report.');
          $('#submitRTSButton').prop('disabled', false);
        }
      },
      error: (err) => {
        if (err.responseJSON?.error) {
          toastr["error"](err.responseJSON?.error);
          $('#submitRTSButton').prop('disabled', false);
        }
        else {
          toastr["error"]('Sorry, there was an error updating the report.');
          $('#submitRTSButton').prop('disabled', false);
        }
      },

    });
  });

  $("input[name='changeFinancialPeriod']").on('change', function () {
    const val = $("input[name='changeFinancialPeriod']:checked").val() === 'true';

    if (val) {
      $("#rtsNewPeriodDatesRow").show();
    } else {
      $("#rtsNewPeriodDatesRow").hide();
    }
  })
</script>
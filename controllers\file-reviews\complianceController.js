const {filereview: FileReviewModel} = require('../../models/filereview');
const ClientReviewModel = require('../../models/client');
const relationController = require('../relations/relationController');
const moment = require("moment");


//Controller function for rendering the file reviewer dashboard
exports.getComplianceList = async function (req, res) {
  try {
    let pickedReviews = await FileReviewModel.find({
      'compliance.username': req.session.user.username.toLowerCase(),
      'status.code': {$in: ["COMPLIANCE"]}
    });

    const reviewsByOfficers = await FileReviewModel.find({
      'status.code': {$in: ["COMPLIANCE BY FR", "COMPLIANCE BY QA"]},
      $or: [{'compliance.username': {$exists: false}}, {'compliance.username': req.session.user.username.toLowerCase()}],
    });

    res.render('file-reviewer/compliance/compliance-dashboard', {
      user: req.session.user,
      title: 'Dashboard compliance',
      pickedReviews: pickedReviews,
      reviewsByOfficers: reviewsByOfficers,
      allReviews: [],

    });
  } catch (error) {
    console.log(error);
    res.redirect('/');
  }
};

exports.getReviewsByFilters = async function (req, res) {
  try {

    let allReviews = [];
    let query = [];

    const pickedReviews = await FileReviewModel.find({
      'compliance.username': req.session.user.username.toLowerCase(),
      'status.code': {$in: ["COMPLIANCE"]}
    });


    const reviewsByOfficers = await FileReviewModel.find({
      'status.code': {$in: ["COMPLIANCE BY FR", "COMPLIANCE BY QA"]},
      $or: [{'compliance.username': {$exists: false}}, {'compliance.username': req.session.user.username.toLowerCase()}],
    });

    const filters = {
      complianceFilter: req.body.complianceFilter ? req.body.complianceFilter : '',
      searchText: req.body.searchText ? req.body.searchText : '',
    };


    if (filters.complianceFilter || filters.searchText) {
      if (filters.complianceFilter === "validated") {
        query.push({
          "status.code": "VALIDATED QA"
        })
      } else if (filters.complianceFilter === "all") {
        query.push({
          "status.code": {
            $in: ["VALIDATED QA", "REVIEWED", "COMPLIANCE", "COMPLIANCE BY FR", "COMPLIANCE BY QA",
              "SEND TO FILE REVIEW OFFICER BY QA", "SEND TO FILE REVIEW OFFICER BY CO"]
          }
        });
        query.push({
          "fileReview.username": {"$nin": ['', null]}
        })
      }

      if (filters.searchText.length > 2) {
        query.push({
          "$or": [{"companyCode": {$regex: filters.searchText, $options: 'i'}},
            {"companyName": {$regex: filters.searchText, $options: 'i'}},
            {"masterClientCode": {$regex: filters.searchText, $options: 'i'}}]
        })
      }
      allReviews = await FileReviewModel.find({$and: query}).limit(200);

      if (allReviews) {
        allReviews.map((review) => {
          if (review.status.code === "COMPLIANCE BY FR" || review.status.code === "COMPLIANCE BY QA") {
            if (!review.compliance ||
              (review.compliance && review.compliance.username && review.compliance.username.toLowerCase() ===
                req.session.user.username.toLowerCase())) {
              review["availableForPick"] = true
            }
          } else {
            review["availableForPick"] = review.status.code === "VALIDATED QA";
          }
          if (review.status.code === "SEND TO FILE REVIEW OFFICER BY QA" ||
            review.status.code === "SEND TO FILE REVIEW OFFICER BY CO") {
            review.status.code = review.status.code === "SEND TO FILE REVIEW OFFICER BY QA" ?
              "SEND TO FR BY QA" : "SEND TO FR BY CO";
          }

          review["availableToUnassigned"] = !(review.status.code === "UNASSIGNED" || review.status.code === "NOT STARTED" ||
            review.status.code === "SEND TO CLIENT BY FR" || review.status.code === 'SEND TO CLIENT BY CO');
        });
      }
    }

    res.render('file-reviewer/compliance/compliance-dashboard', {
      user: req.session.user,
      title: 'Dashboard compliance',
      pickedReviews: pickedReviews,
      reviewsByOfficers: reviewsByOfficers,
      allReviews: allReviews ? allReviews : [],
      filters: filters
    });
  } catch (error) {
    console.log(error);
    res.redirect('/');
  }
};

// Controller function for ajax call to pick a submitted file
exports.pickComplianceReview = async function (req, res) {
  try {
    let review = await FileReviewModel.findById(req.body.id);
    if (review) {

      review.compliance = {
        username: req.session.user.username,
        name: req.session.user.name,
        dateAssigned: new Date(),
        assignedBy: review.status.code === "COMPLIANCE BY FR" ? 'FR' :
          review.status.code === "COMPLIANCE BY QA" ? 'QA' : 'CO'
      };
      review.status = {
        code: 'COMPLIANCE',
        statusDate: new Date(),
      };
      await review.save();

      return res.status(200).end();
    } else {
      return res.status(400).end();
    }

  } catch (error) {
    console.log(error);
    return res.status(500).end();
  }
};

exports.openComplianceReview = async function (req, res) {
  try {
    let review = await FileReviewModel.findById(req.params.reviewId);
    const clientReview = await ClientReviewModel.findById(review.clientId);
    let companyActivity = clientReview.companyActivity;
    let companyActivityReview = review.companyActivityReview;
    // check if there is any pep inside the file review

    res.render('file-reviewer/compliance/compliance-review', {
      user: req.session.user,
      title: 'Company Files: ' + review.companyName,
      id: review._id,
      review: review,
      clientReview: clientReview,
      companyActivity: companyActivity,
      companyActivityReview: companyActivityReview,
    });

  } catch (error) {
    console.log(error);
    res.redirect('/file-reviewer/quality-assurance-list');
  }

};

exports.getRelations = async function (req, res) {
  try {
    let relationsData = await relationController.getReviewRelations(req.params.reviewId);

    const review = relationsData.file;

    const canValidate = (review.status.code.includes("COMPLIANCE")) &&
      (review.compliance && review.compliance.username.toLowerCase() === req.session.user.username.toLowerCase());

    if (relationsData) {
      res.render('file-reviewer/compliance/compliance-relations-list', {
        user: req.session.user,
        review: relationsData.file,
        title: relationsData.file.companyName + ': relations',
        id: req.params.reviewId,
        canValidate: canValidate,
        beneficialOwners: relationsData.beneficialOwners,
        shareholders: relationsData.shareholders,
        directors: relationsData.directors
      });
    } else {
      res.redirect('/file-reviewer/dashboard');
    }
  } catch (error) {
    console.log(error);
    res.redirect('/');
  }
};

exports.submitComplianceReview = async function (req, res) {
  try {
    let review = await FileReviewModel.findById(req.params.reviewId);

    if (review && review.compliance.username.toLowerCase() === req.session.user.username.toLowerCase()) {
      review.status.code = "VALIDATED BY CO";
      review.status.statusDate = new Date();
      review.compliance.validatedDate = new Date();

      const reviewComment = {
        username: req.session.user.username,
        role: 'CO',
        comment: req.body.comment,
        date: new Date(),
        from: 'CO',
        to: '',
      };
      review.comments.push(reviewComment);

      await review.save();
      return res.status(200).json({success: true});

    } else {
      return res.status(400).end();
    }
  } catch (e) {
    console.log("error ", e);
    res.redirect('/')
  }
};

exports.unassignOfficer = async function (req, res) {
  try {
    let review = await FileReviewModel.findById(req.params.reviewId);
    if (review && req.session.user.username) {
      const officersToUnassign = req.body.officers;
      let comment = {
        username: req.session.user.username,
        role: "CO",
        date: new Date(),
      };
      if (officersToUnassign === 'file-reviewer') {

        if (review.fileReview && review.fileReview.username !== '' &&
          review.fileReview.username !== undefined) {
          comment.comment = "The file reviewer officer " + review.fileReview.username.toLowerCase() + " assigned at " +
            moment(review.fileReview.dateAssigned).format('YYYY-MM-DD') + " was unassigned by " +
            req.session.user.username + " at " + moment().format('YYYY-MM-DD');
          review.comments.push(comment);
          review.fileReview = {};
        }

        review.status = {
          code: 'UNASSIGNED',
          statusDate: new Date()
        }
      }
      if (officersToUnassign === 'quality-assurance') {

        if (review.qualityAssurance && review.qualityAssurance.username !== '' &&
          review.qualityAssurance.username !== undefined) {
          comment.comment = "The quality assurance officer " + review.qualityAssurance.username.toLowerCase() + " assigned at " +
            moment(review.qualityAssurance.dateAssigned).format('YYYY-MM-DD') + " was unassigned by " +
            req.session.user.username + " at " + moment().format('YYYY-MM-DD');
          review.comments.push(comment);
          review.qualityAssurance = {};
        }

        review.status = {
          code: 'REVIEWED',
          statusDate: new Date()
        }

      }
      if (officersToUnassign === 'compliance') {
        if (review.compliance && review.compliance.username !== '' &&
          review.compliance.username !== undefined) {
          comment.comment = "The compliance officer " + review.compliance.username.toLowerCase() + " assigned at " +
            moment(review.compliance.dateAssigned).format('YYYY-MM-DD') + " was unassigned by " +
            req.session.user.username + " at " + moment().format('YYYY-MM-DD');
          review.comments.push(comment);
          review.compliance = {};
        }

        review.status = {
          code: 'VALIDATED QA',
          statusDate: new Date()
        }
      }
      await review.save();
      return res.status(200).json({status: 200, message: "Officers unassigned successfully"});
    } else {
      return res.status(400).json({status: 400, message: "Error... Review not found"});
    }
  } catch (e) {
    console.log("error ", e);
    return res.status(500).json({status: 400, message: "Internal error"});
  }
};

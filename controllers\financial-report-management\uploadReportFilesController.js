
const MULTER = require('multer');
const MULTER_AZURE_STORAGE = require('../../classes/MulterAzureStorage');
const { renameBlob } = require('../../utils/azureStorage');

exports.uploadFinancialReportFile = MULTER({
  storage: new MULTER_AZURE_STORAGE({
    containerName: process.env.AZURE_STORAGE_CONTAINER_FINANCIAL_REPORTS,
    accessKey: process.env.AZURE_STORAGE_ACCESS_KEY,
    accountName: process.env.AZURE_STORAGE_ACCOUNT,
  })
});

exports.moveUploadFRFile = async function (id, file, fileType) {
  try {
    if (file) {
      const newName = id + '/' + file.blobName.replace('fileUploaded', fileType);
      await renameBlob(
        process.env.AZURE_STORAGE_ACCOUNT,
        process.env.AZURE_STORAGE_ACCESS_KEY,
        process.env.AZURE_STORAGE_CONTAINER_FINANCIAL_REPORTS, 
        file.blobName,
        newName
      );
    }
  } catch (error) {
    console.log('ERROR: ' + error);
    return error;
  }
};

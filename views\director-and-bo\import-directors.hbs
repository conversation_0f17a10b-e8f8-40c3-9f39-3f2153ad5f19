<main class="">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card">
                    <form method="POST" id="submitForm">
                        <div class="card-body">
                            <h1>{{title}}</h1>
                            <div id="import-table">

                            </div>
                            <div class="row" id="uploadRow">
                                <div class="col-md-12">
                                    <p>
                                        Maximum of 1 file, XLSX only. File must not
                                        be password
                                        protected.
                                    </p>
                                    <div id="uploadFile" class="dropzone">
                                        <div class="fallback">
                                            <input name="fileUploaded" type="file" multiple />
                                        </div>
                                        <div class="dz-message needsclick">
                                            <i class="h1 text-muted dripicons-cloud-upload"></i>
                                            <h3>Drop files here or click to upload.</h3>
                                            <span class="text-muted">Files will be automatically uploaded</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="row">
                                <div class="mb-3 ml-3">
                                    <a href='/director-and-bo/import-directors'
                                        class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>


<script type="text/javascript">
    let importedData;
    Dropzone.autoDiscover = false;
    $(function () {
        let field = '';
        var myDropZone = new Dropzone('#uploadFile', {
            url: './load-file',
            acceptedFiles: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            uploadMultiple: false,
            autoProcessQueue: true,
            parallelUploads: 1,
            maxFiles: 1,
            maxFilesize: 25,
            paramName: function () {
                return 'fileUploaded';
            },
            init: function () {
                this.on('processing', function () {
                    this.options.url = './load-file';
                });
                this.on("success", function (file, response) {
                    if (response.status === 200) {
                        loadDirectorFileSuccess(response.message);

                    } else {
                        toastr["warning"](response.error);
                        if (this.files.length != 0) {
                            for (i = 0; i < this.files.length; i++) {
                                this.files[i].previewElement.remove();
                            }
                            this.files.length = 0;
                        }
                    }
                })
                this.on("sending", function (file, xhr, formData) {
                    $("#btnSubmit").prop('disabled', true);
                    if (!formData.has('filetype')) {
                        formData.append("filetype", field);
                    }
                })

                this.on('maxfilesexceeded', function (file) { });

                this.on('resetFiles', function () {
                    if (this.files.length != 0) {
                        for (i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                        this.files.length = 0;
                    }
                    $('#maxUpload').text(this.options.maxFiles);
                });
            },
        });
    });


    function loadDirectorFileSuccess(message){
        Swal.fire('Success', message, 'success').then(() => {
            location.href = '/director-and-bo/import-directors';
        });
    }
</script>

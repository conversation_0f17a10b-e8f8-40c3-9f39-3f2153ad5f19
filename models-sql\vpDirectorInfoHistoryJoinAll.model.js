const moment = require('moment');

module.exports = (sequelize, Sequelize) => {
  const VpDirectorInfoHistoryJoinAll = sequelize.define('VpDirectorInfoHistoryJoinAll', {
      CompanyNumber: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      ReferralOffice: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      MasterClientCode: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      EntityCode: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
      },
      EntityName: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
      },
      Name: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
      },
      Code: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
      },
      RelationType: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
        primaryKey: true
      },
      FormerName: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
      },
      FileType: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
      },
      UniqueRelationId: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
        primaryKey: true
      },
      OfficerType: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
      },
      FromDate: {
        type: Sequelize.DATEONLY,
        allowNull: true,
        get() {
            const date = this.getDataValue('FromDate') ?
                moment.utc(this.getDataValue('FromDate')).format('YYYY-MM-DD') : null;
            return date;
        }
      },
      ToDate: {
        type: Sequelize.DATEONLY,
        allowNull: true,
        get() {
            const date = this.getDataValue('ToDate') ?
                moment.utc(this.getDataValue('ToDate')).format('YYYY-MM-DD') : null;
            return date;
        }
      },
      ServiceAddress: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
      },
      ResidentialOrRegisteredAddress: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
      },
      DateOfBirthOrIncorp: {
        type: Sequelize.DATE,
        allowNull: true,
        unique: false,
      },
      PlaceOfBirthOrIncorp: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      Nationality: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      Country: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      ProductionOffice: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      CorporateRegistrationNo: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      UpdateRequestDate: {
          type: Sequelize.DATE,
          allowNull: true,
          unique: false,
      },
      ConfirmedDate: {
          type: Sequelize.DATE,
          allowNull: true,
          unique: false,
      },
      VPDataReceived: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      Status: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      SubStatus: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      UserEmail: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      TypeOfUpdateRequest: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: false,
      },
      UpdateRequestComments: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      TIN: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      NameOfRegulator: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      StockExchange: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      StockCode: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      JurisdictionOfRegulationOrSovereignState: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      BoDirIncorporationNumber: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      DirectorIsAlternateToId: {
          type: Sequelize.INTEGER,
          allowNull: true,
          unique: false,
      },
      DirectorIsAlternateToName: {
          type: Sequelize.STRING,
          allowNull: true,
          unique: false,
      },
      MissingInfo: {
          type: Sequelize.INTEGER,
          allowNull: false,
          unique: false,
      }
  }, {
      sequelize,
      tableName: 'VPDirectorInfoHistoryJoinAllv2',
      schema: 'dbo',
      timestamps: false
  });

  return VpDirectorInfoHistoryJoinAll
}


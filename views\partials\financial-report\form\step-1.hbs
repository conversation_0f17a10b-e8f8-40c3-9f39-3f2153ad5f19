<div>
  <div class="row ">
    <div class="col-md-12">
      <h4 class="font-weight-bold text-center">
          STATEMENT OF INCOME AND EXPENSES FOR THE PERIOD FROM
          {{formatDate report.financialPeriod.start STANDARD_DATE_FORMAT}} TO
          {{formatDate report.financialPeriod.end STANDARD_DATE_FORMAT}}
      </h4>
    </div>
  </div>
  <br>

  <div class="row">
    <div class="col-12">
      <label for="serviceType">Service Type</label> <br>
      <select id="serviceType" class="form-control  w-100" disabled>
        <option value="trident-service-complete" {{#ifContains 'trident-service-complete'
          report.reportDetails.serviceType }} selected {{/ifContains}}>
          Have TridentTrust complete the company's annual return in the prescribed format
        </option>
        <option value="trident-service-drop" {{#ifContains 'trident-service-drop' report.reportDetails.serviceType
          }} selected {{/ifContains}}>
          Drop Accounting Records for assistance
        </option>
      </select>
    </div>
  </div>

  <br>

  <div class="row">
    <div class="col-md-8">
      <h4>
        <label for="completeReportCurrency" class="mb-2 lbl-read-only" style="cursor:default; color: black;">
          Reporting currency:
        </label>
      </h4>
    </div>
    <div class="col-md-4">
      <div class="form-group mb-3 input-group">
        <select name="completeReportCurrency" id="completeReportCurrency" class="form-control" data-toggle="select2"
          style="width:100%;" data-value="{{report.currency}}">
          {{#each currencies}}
          <option value="{{cc}}" {{#if ../report.currency}} {{#ifEquals cc ../report.currency }} selected {{/ifEquals}}
            {{else}} {{#ifEquals cc 'USD' }} selected {{/ifEquals}} {{/if}}>
            {{cc}} - {{name}}
          </option>
          {{/each}}
  
        </select>
      </div>
    </div>
  </div>

  <div class="row mb-2">
    <div class="col-md-12">
      <h4>Income</h4>
    </div>
  </div>

  <div class="row" id="completeDetailsRevenueRow">
    <div class="col-md-8">
      <div class="form-group ml-2">
        <label class="lbl-read-only mb-0" for="completeDetailsRevenue">Revenue</label> <br>
        <span>
          <small class="font-weight-bold">Please consider: Dividend income, Coupon interest income, Bank Interest Income,
            Loan interest income, Sale of Goods /
            Services, Gain from the sale of Investments, Listed Financial Assets, Tangible / Intangible Assets, Unrealized
            Gain on
            the Market Value of Listed Financial Assets or any other type of income.</small>
        </span>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group input-group mb-1">
        <div class="input-group-prepend">
          <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
        </div>
        <input type="text" id="completeDetailsRevenue" class="form-control autonumber text-right complete-income"
          data-a-sep="," placeholder="0.00" value="{{report.completeDetails.income.total}}" data-m-dec="2"
          name="completeDetails[revenue]">
      </div>
    </div>
  </div>

  <div class="row" id="completeDetailsCostOfSalesRow">
    <div class="col-md-8">
      <div class="form-group ml-2">
        <label class="lbl-read-only mb-0" for="completeDetailsCostOfSales">Cost of Sales </label><br>
        <span>
          <small class="font-weight-bold">Please consider the direct costs incurred for the generation of income:
            Purchase of Goods / Services, Loss from the sale
            of Investments, Listed Financial Assets, Tangible / Intangible Assets, Unrealized Loss on the Market Value of
            Listed
            Financial Assets, Loan interest expense, etc.</small>
        </span>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group input-group mb-1">
        <div class="input-group-prepend">
          <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
        </div>
        <input type="text" id="completeDetailsCostOfSales" class="form-control autonumber text-right complete-income"
          data-a-sep="," placeholder="0.00" placeholder="0.00" value="{{report.completeDetails.income.costOfSales}}"
          data-m-dec="2" name="completeDetails[costOfSales]">
      </div>
    </div>
  </div>
  
  <br>
  <div class="row" id="completeDetailsGrossProfitRow">
    <div class="col-md-8">
      <div class="form-group ml-2">
        <label class="lbl-read-only" for="completeDetailsGrossProfit">
          <b>GROSS PROFIT</b>
        </label>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group input-group mb-1">
        <div class="input-group-prepend">
          <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
        </div>
        <input type="text" id="completeDetailsGrossProfit" class="form-control autonumber text-right " data-a-sep=","
          placeholder="0.00" data-m-dec="2" name="completeDetails[grossProfit]"
          value="{{report.completeDetails.grossProfit}}" readonly>
      </div>
    </div>
  </div>

  <div class="row mb-2">
    <div class="col-md-12">
      <h4>Expenses</h4>
    </div>
  </div>

  <div class="row" id="completeDetailsOperatingExpensesRow">
    <div class="col-md-8">
      <div class="form-group ml-2">
        <label class="lbl-read-only mb-0" for="completeDetailsOperatingExpenses">Operating Expenses</label> <br>
        <span>
          <small class="font-weight-bold">Please consider the expenses incurred regarding the Company’s operational
            activities: Professional fees paid, Company
            Administration fees, Portfolio management fees and related Services, etc.</small>
        </span>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group input-group mb-1">
        <div class="input-group-prepend">
          <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
        </div>
        <input type="text" id="completeDetailsOperatingExpenses"
          class="form-control autonumber text-right complete-expenses" placeholder="0.00"
          value="{{report.completeDetails.expenses.operatingExpenses}}" data-a-sep="," data-m-dec="2"
          name="completeDetails[operatingExpenses]">
      </div>
    </div>
  </div>
  
  <div class="row" id="completeDetailsTotalOtherExpensesRow">
    <div class="col-md-8">
      <div class="form-group ml-2">
        <label class="lbl-read-only mb-0" for="completeDetailsTotalOtherExpenses">Other Expenses</label> <br>
        <span>
          <small class="font-weight-bold">
            Please consider the expenses that are not related to the activities of the Company: Balances Written-Off, Bank
            fees, Government fees, Impairment expense, Accounting / Audit Fees, Foreign exchange loss, etc.
          </small>
        </span>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group input-group mb-1">
        <div class="input-group-prepend">
          <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
        </div>
        <input type="text" id="completeDetailsTotalOtherExpenses"
          class="form-control autonumber text-right complete-expenses" placeholder="0.00"
          value="{{report.completeDetails.expenses.totalOtherExpenses}}" data-a-sep="," data-m-dec="2"
          name="completeDetails[totalOtherExpenses]">
      </div>
    </div>
  </div>
  
  <div class="row" id="completeDetailsIncomeTaxExpenseRow">
    <div class="col-md-8">
      <div class="form-group ml-2">
        <label class="lbl-read-only" for="completeDetailsIncomeTaxExpense">Income Tax Expenses</label> <br>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group input-group mb-1">
        <div class="input-group-prepend">
          <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
        </div>
        <input type="text" id="completeDetailsIncomeTaxExpense"
          class="form-control autonumber text-right complete-expenses" placeholder="0.00"
          value="{{report.completeDetails.expenses.incomeTax}}" data-a-sep="," data-m-dec="2"
          name="completeDetails[incomeTax]">
      </div>
    </div>
  </div>
  
  <br>
  
  <div class="row" id="completeDetailsExpensesTotalRow">
    <div class="col-md-8">
      <div class="form-group ml-2">
        <label class="lbl-read-only font-weight-bold" for="completeDetailsExpensesTotal">
          <b>TOTAL EXPENSES</b>
        </label>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group input-group mb-1">
        <div class="input-group-prepend">
          <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
        </div>
        <input type="text" id="completeDetailsExpensesTotal" class="form-control autonumber text-right" data-a-sep=","
          data-m-dec="2" name="completeDetails[expensesTotal]" placeholder="0.00"
          value="{{report.completeDetails.expenses.totalExpenses}}" readonly>
      </div>
    </div>
  </div>
  
  <hr>
  
  <div class="row" id="completeDetailsNetIncomeRow">
    <div class="col-md-8">
      <div class="form-group ml-2">
        <label class="lbl-read-only" for="completeDetailsNetIncome"> <b>NET INCOME</b>
        </label>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group input-group mb-1">
        <div class="input-group-prepend">
          <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
        </div>
        <input type="text" id="completeDetailsNetIncome" class="form-control autonumber net-income-value text-right"
          data-a-sep="," data-m-dec="2" name="completeDetails[netIncome]" placeholder="0.00"
          value="{{report.completeDetails.netIncome}}" readonly>
      </div>
    </div>
  </div>

</div>
{{!-- CONFIRM REVIEW MODAL --}}

<div class="modal fade" id="lockedReviewModal" tabindex="-1" role="dialog" aria-hidden="true"
     style="display: none; z-index: 2000;">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content border rounded border-secondary">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;" id="lockedModalTitle">Entity lookup</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <div class=" m-2 text-left text-muted">
                    <p class="text-dark" id="lockedReviewBody">
                    </p>

                </div>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>


<script>
    $('#lockedReviewModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal

        const modalInfoValidateReview = {
            "modalMessage": "This entity is editable by <b> #username </b> " +
                    " and connected to the filereview of <b>#companyname </b>",
            "successMessage": 'The review was submitted successfully',
            "errorMessage": 'There was an error getting the review information'
        };

        $.ajax({
            type: 'GET',
            url: '/file-reviewer/reviews/' + button.data('locked-id'),
            success: function (response) {
                if (response.success) {
                    console.log(response.review);
                    let msg = modalInfoValidateReview["modalMessage"];
                    msg = msg.replace("#username", response.review.fileReview.username)
                            .replace("#companyname", response.review.companyName);
                    $('#lockedReviewBody').html(msg);
                } else {
                    $('#lockedReviewBody').html(modalInfoValidateReview["errorMessage"]);
                }

            },
            error: function () {
                $('#lockedReviewBody').html(modalInfoValidateReview["errorMessage"]);
            },
        });

    });

    $('#lockedReviewModal').on('hide.bs.modal', function (event) {
        $('.modal').css('overflow-y', 'auto');
    });

</script>

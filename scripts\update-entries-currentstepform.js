const EntryModel = require("../models/entry").EntryModel;
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config();


const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const result = await updateEntriesCurrentStep();

    console.log('Script ejecutado correctamente', result);
  } catch (error) {
    console.error('Error en el script:', error);
  } finally {
    mongoose.disconnect();
  }
}


async function updateEntriesCurrentStep() {
  try {

    let updateLog = [['Entry ID', 'Company', 'Update date', 'Action']];

    const entriesToUpdate = await EntryModel.find({ version: { $ne: "5.0" }, currentStepForm: "entity-details" }, 
      {_id:  1, company:1, currentStepForm: 1})

    console.log("entriesToUpdate length ", entriesToUpdate.length);
    if (entriesToUpdate.length > 0) {
      for (let i = 0; i < entriesToUpdate.length; i++) {
        console.log('processing ' + i + '  from ' + entriesToUpdate.length)

        const entry = entriesToUpdate[i];

        try {
          const result = await EntryModel.updateOne({ _id: entry._id }, {
            $set: {
              currentStepForm: 'financial-period'
            }
          });

          if (result.nModified > 0) {
            updateLog.push([entry._id?.toString(), entry.company, new Date(), 'SUCCESS']);
          } else {
            updateLog.push([entry._id?.toString(), entry.company, new Date(), 'ERROR: NOT FOUND']);
          }
        } catch (error) {
          console.error('Error:', error.message);
          updateLog.push([entry._id?.toString(), entry.company, new Date(), 'ERROR UPDATING']);
        }
      }
    }


    // create entities bo
    console.log("entries updated ", updateLog.length -1);

    const filename = 'update_entries_currentstep_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'entries ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalRows": updateLog.length -1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}


runScript();
<div class="modal hide fade" tabindex="-1" role="dialog" id="financialPeriodChangeModal" style="display: none;"
  aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="mediumModal">Change Financial Period</h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
      </div>
      <div class="modal-body">
        <div class="row" id="newPeriodDatesRow">
          <div class="col-6">
            <label for="financialStartDateControl">Start Date:</label>
            <input type="text" class="form-control mr-2" placeholder="MM/DD/YYYY" name="financialStartDate"
              id="financialStartDateControl">
          </div>
          <div class="col-6">
            <label for="financialEndDateControl">End Date:</label>
            <input type="text" class="form-control " placeholder="MM/DD/YYYY" name="financialEndDate"
              id="financialEndDateControl">
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
          Close
        </button>

        <button type="button" class="btn btn-primary waves-effect waves-light ml-2" id="confirmFinancialPeriodButton">Confirm </button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>

<script type="text/javascript">
  let fpChangeId = "";
  let endDatePicker
  
 $('#financialPeriodChangeModal').on('shown.bs.modal', function (event) {
  let button = $(event.relatedTarget); // Button that triggered the modal
  fpChangeId = button.data('report-id');

  const startDate = button.data('start-date');
  const endDate = button.data('end-date');
  const incorporationDate = button.data('incorporation-date');
  const isFirstReport = button.data('first-report');


  const maxDate = moment(startDate, 'MM/DD/YYYY').add(1, 'years').subtract(1, 'days').format('MM/DD/YYYY');

  endDatePicker = $("#financialEndDateControl").flatpickr({
    dateFormat: "m/d/Y",
    autoclose: true,
    allowInput: false,
    defaultDate: endDate,
    maxDate: maxDate,
    minDate: moment(startDate, 'MM/DD/YYYY').format('MM/DD/YYYY')
  });

  if (!isFirstReport) {
    $("#financialStartDateControl").prop('disabled', true).css('background-color', 'lightgray');
    $("#financialStartDateControl").val(startDate);
  } else {
    $("#financialStartDateControl").flatpickr({
      dateFormat: "m/d/Y",
      autoclose: true,
      allowInput: false,
      defaultDate: startDate,
      minDate: '01/01/2023',
      maxDate: (moment(incorporationDate).isBefore('01/01/2023') ? '12/31/2023' : null)
    });
  }

  
});

$(document).on('change', '#financialStartDateControl', function() {
        const newStartDate = moment($(this).val(), 'MM/DD/YYYY').add(1, 'days').format('MM/DD/YYYY');
        const newMaxDate = moment($(this).val(), 'MM/DD/YYYY').add(1, 'years').subtract(1, 'days').format('MM/DD/YYYY');

        endDatePicker.set('minDate', newStartDate);
        endDatePicker.set('maxDate', newMaxDate);
})


  $('#financialPeriodChangeModal').on('hide.bs.modal', function () {
    $("#financialStartDateControl").val('');
    $("#financialEndDateControl").val('');
    $("#financialStartDateControl").prop('disabled', false).css('background-color', 'white');
    fpChangeId = "";
    fpCompany = "";
  });

  $("#confirmFinancialPeriodButton").on('click', function (event) {
    $('#confirmFinancialPeriodButton').prop('disabled', true);

    const newFinancialStartDate = $("#financialStartDateControl").val();
    const newFinancialEndDate = $("#financialEndDateControl").val();

    if (!newFinancialStartDate || !newFinancialEndDate) {
      toastr["warning"]('Please select financial period start/end dates');
      $('#confirmFinancialPeriodButton').prop('disabled', false);
      return;
    }

    $.ajax({
      type: "PUT",
      url: `/financial-report-management/${fpChangeId}/update-period`,
      contentType: "application/json; charset=utf-8",
      data: JSON.stringify({
        newFinancialStartDate: newFinancialStartDate,
        newFinancialEndDate: newFinancialEndDate,
      }),
      success: function (data) {
        if (data.status === 200) {
          toastr.success('Report updated successfully');
          window.setTimeout(function () {
            document.location.reload();
          }, 200)
        } else {
          toastr["error"]('Sorry, there was an error updating the report.');
          $('#confirmFinancialPeriodButton').prop('disabled', false);
        }
      },
      error: (err) => {
        if (err.responseJSON?.error) {
          toastr["error"](err.responseJSON?.error);
          $('#confirmFinancialPeriodButton').prop('disabled', false);
        }
        else {
          toastr["error"]('Sorry, there was an error updating the report.');
          $('#confirmFinancialPeriodButton').prop('disabled', false);
        }
      },

    });
  
    
  });


</script>
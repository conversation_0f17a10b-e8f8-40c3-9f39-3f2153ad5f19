<!-- OP<PERSON> OWNER MODAL -->
<div class="modal fade" id="openRelationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modal-relation-title" class="modal-title">Relation:</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-relation-body" class="modal-body p-3">
                <p class="text-muted text-center">LOADING...</p>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="/templates/peekrelation.precompiled.js"></script>
<script type="text/javascript">
    $('#openRelationModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        let reviewId = button.data('review-id');
        let relationId = button.data('relation-id');
        let relationType = button.data('type');
        let relationName = button.data('name');
        let relationGroup = button.data('group');
        let title = "";
        if (relationGroup === "beneficial"){
            title = 'Beneficial Owner: ' + relationName;
        }
        else if (relationGroup === "shareholder"){
            title = 'Shareholder: ' + relationName;
        }
        else{
            title = 'Director: ' + relationName;
        }

        $('#modal-relation-title').text(title);

        $.ajax({
            type: 'GET',
            url: '/file-reviewer/peek-relation/' + reviewId,
            data: { group: relationGroup, type: relationType, relationId: relationId },
            success: function (data) {
                if (data.success){
                    let template = Handlebars.templates.peekrelation;
                    let d = {
                        relation: data.relation,
                        relationInformation: data.relationInformation,
                        relationFiles: data.relationFiles,
                        reviewId: reviewId
                    };
                    let html = template(d);
                    $('#modal-relation-body').html(html);
                }

            },
            error: function () {
                Swal.fire(
                        'Error',
                        'There was an error while trying to fetch the relation',
                        'error'
                ).then(() => {
                    $('#openRelationModal').modal('hide');
                });
            },
        });
    });
</script>

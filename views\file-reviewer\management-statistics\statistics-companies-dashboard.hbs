<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title">
                            <h2>{{ title }}</h2>
                        </div>
                        <br>
                        <div class="mx-auto">
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead class="thead-light">
                                    <tr>
                                        <th>Total file reviews to be done:</th>
                                        <th>{{allData.total}}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>File reviews completed by File Reviewer:</td>
                                        <td>{{allData.completedByFR}}</td>
                                    </tr>
                                    <tr>
                                        <td>File reviews completed by Quality Assurance:</td>
                                        <td>{{allData.completedByQA}}</td>
                                    </tr>
                                    <tr>
                                        <td>File reviews completed by Compliance:</td>
                                        <td>{{allData.completedByCO}}</td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>Total file reviews left to be done by File Reviewer:</td>
                                        <td>{{allData.totalVsDoneFileReviewer}}</td>
                                    </tr>
                                    <tr>
                                        <td>Total file reviews left to be done by Quality Assurance:</td>
                                        <td>{{allData.totalVsDoneQA}}</td>
                                    </tr>
                                    <tr>
                                        <td>Total file reviews left to be done by Compliance:</td>
                                        <td>{{allData.totalVsDoneCO}}</td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>File reviews in progress File Reviewer:</td>
                                        <td>{{allData.progressFileReviewer}}</td>
                                    </tr>
                                    <tr>
                                        <td>File reviews in progress Quality Assurance:</td>
                                        <td>{{allData.progressQA}}</td>
                                    </tr>
                                    <tr>
                                        <td>File reviews in progress Compliance:</td>
                                        <td>{{allData.progressCO}}</td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td>File reviews pending Client:</td>
                                        <td>{{allData.pendingClient}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex pt-2 pl-2">
                                <p>
                                <h5>
                                    <b>Total completed by FR consist of:</b>
                                    <br>
                                    REVIEWED, VALIDATED QA, VALIDATED BY CO, IN PROGRESS BY QA, COMPLIANCE, SEND TO QA
                                    BY CO,
                                    ASSIGNED QA BY CO, COMPLIANCE BY QA
                                </h5>
                                </p>
                            </div>
                            <div class="d-flex pt-2 pl-2">
                                <p>
                                <h5>
                                    <b>Total completed by QA consist of:</b>
                                    <br>
                                    VALIDATED QA, VALIDATED BY CO, COMPLIANCE
                                </h5>
                                </p>
                            </div>
                            <div class="d-flex pt-2 pl-2">
                                <p>
                                <h5>
                                    <b>Total completed by CO consist of:</b>
                                    <br>
                                    VALIDATED BY CO
                                </h5>
                                </p>
                            </div>

                        </div>

                        <!-- CONTENT END -->
                        <div class="row mt-2 justify-content-between ">
                            <div class="col-md-2">
                                <a href="/file-reviewer/dashboard"
                                   class="btn btn-secondary width-lg waves-effect waves-light">
                                    Back
                                </a>
                            </div>
                            <div class="col-md-2 text-right">
                                <button id="statistics-export-btn" class="btn btn-blue width-lg waves-effect waves-light">Export</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript">

    $("#statistics-export-btn").on('click', function (event) {
        event.preventDefault();
        $(this).prop('disabled', true);
        $.ajax({
            url: "./export-xls",
            method: 'POST',
            xhrFields: {
                responseType: 'blob' // to avoid binary data being mangled on charset conversion
            },
            success: function (blob, status, xhr) {
                // check for a filename
                let filename = "";
                const disposition = xhr.getResponseHeader('Content-Disposition');
                if (disposition && disposition.indexOf('attachment') !== -1) {
                    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    const matches = filenameRegex.exec(disposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['"]/g, '');
                    }
                }

                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    // IE workaround for "HTML7007: One or more blob URLs were revoked by closing the blob for which
                    // they were created. These URLs will no longer resolve as the data backing the URL has been freed."
                    window.navigator.msSaveBlob(blob, filename);
                } else {
                    const URL = window.URL || window.webkitURL;
                    const downloadUrl = URL.createObjectURL(blob);

                    if (filename) {
                        // use HTML5 a[download] attribute to specify filename
                        const a = document.createElement("a");
                        // safari doesn't support this yet
                        if (typeof a.download === 'undefined') {
                            window.location.href = downloadUrl;
                        } else {
                            a.href = downloadUrl;
                            a.download = filename;
                            document.body.appendChild(a);
                            a.click();
                        }
                    } else {
                        window.location.href = downloadUrl;
                    }

                    setTimeout(function () {
                        URL.revokeObjectURL(downloadUrl);
                    }, 100); // cleanup
                }
                $("#statistics-export-btn").prop('disabled', false);
            },
            error: function (err) {
                console.log(err);
                $("#statistics-export-btn").prop('disabled', false);
            }
        });
    })


</script>

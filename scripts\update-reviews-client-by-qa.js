const { filereview: FileReviewModel } = require('../models/filereview');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
dotenv.config();


try {
  mongoose.connect(process.env.MONGODB, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    useFindAndModify: false,
  }, function (err) {
    throw err;
  });
  const db = mongoose.connection;
  db.on('error', console.error.bind(console, 'MongoDB connection error:'));
  // Start proccess - Process submissions from excel file
  updateSubmissionsInClientByQA().then(r => console.log("FINISH, total updated: ", r));
  // Search imported submissions in database, update if found
  // and create new if not
} catch (e) {
  console.log(e);
}

async function updateSubmissionsInClientByQA() {
  try {


    const updateFields = {
      status: {
        code: "ASSIGNED",
        statusDate: new Date()
      },
      "fileReview.dateAssigned": new Date()
    };

    const reviewsUpdated = await FileReviewModel.updateMany(
      {
        "status.code": "SEND TO CLIENT BY QA",
      },
      {"$set": updateFields}
    );

    console.log("reviews updated", reviewsUpdated);
    return reviewsUpdated.nModified;
  } catch (e) {
    console.log("Error updating the file review date: ", e);
    return []
  }

}




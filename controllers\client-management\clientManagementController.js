
const appInsightsClient = require("applicationinsights").defaultClient;
const MasterClientCodeModel = require("../../models/masterClientCode");
const UserModel = require("../../models/user");
const ConfigurationModel = require("../../models/configuration");
const CompanyIncorporationModel = require("../../models/clientIncorporation");
const CompanyModel = require("../../models/company").schema;
const {filereview: FileReviewModel} = require('../../models/filereview');
const FinancialReportModel = require('../../models/financialreport');
const RequestedFileModel = require('../../models/requestedFile');
const MessageModel = require("../../models/message");
const MailController = require("../mailController");
const masterClientInvitation = require("../master-client-invitation");
const EntryModel = require("../../models/entry").EntryModel;
const ArchivedEntryModel = require("../../models/entry").ArchivedEntryModel;
const ClientReviewModel = require('../../models/client');
const BeneficialOwnerModel = require('../../models/beneficialowners');
const DocumentChangesModel = require("../../models/documentchangelog");
const utils = require('../../utils/utils');
const xlsx = require("xlsx");
const moment = require("moment");
const { getContainerClient } = require('../../utils/azureStorage');
const httpConstants = require('http2').constants;
const axios = require('axios').default;
const archiver = require("archiver");
const { STANDARD_DATE_FORMAT, STANDARD_DATETIME_FORMAT } = require('../../utils/constants');


exports.getDashboard = async function (req, res) {
    try {
        let configModel = await ConfigurationModel.findOne({});
        res.render("client-management/index", {
            user: req.session.user,
            title: "Client Management Dashboard",
            authentication: req.session.authentication,
            STANDARD_DATE_FORMAT,
            canStartMoveFilesProcess: !!(configModel && configModel.isMovingMccFiles === false)
        });
    } catch (e) {
        console.log(e);
        res.redirect("/");
    }
};

exports.getInvitations = function (req, res) {
    let masterclientfilter = {};
    masterclientfilter["date_invited"] = null;

    if (req.body.filter_masterclient && req.body.filter_masterclient.length > 2) {
        masterclientfilter["code"] = {
            $regex: req.body.filter_masterclient,
            $options: "i",
        };
    }

    MasterClientCodeModel.find(masterclientfilter)
        .sort("code")
        .limit(50)
        .exec(function (err, masterclients) {
            if (!err) {
                res.render("client-management/masterclient-invitations", {
                    user: req.session.user,
                    title: "Master Client Invitations",
                    result: masterclients,
                    STANDARD_DATE_FORMAT
                });
            }
        });
};

exports.sendInvitations = async function (req, res) {
    let masterclients = req.body.masterclients;
    let finished = 0;
    let finishedSuccesfully = 0;
    masterclients.forEach(async function (masterclient) {
        MasterClientCodeModel.findOne(
            { _id: masterclient.id, code: masterclient.code },
            async function (err, client) {
                if (err || !client) {
                    console.log(err);
                    finished++;
                    if (
                        finishedSuccesfully < masterclients.length &&
                        finished == masterclients.length
                    ) {
                        res.json({ success: false });
                    }
                } else {
                    let errors = 0;
                    for (let owner of masterclient.owners) {
                        let email = masterClientInvitation.generateEmail(
                            masterclient.code,
                            owner
                        );
                        console.log(email);
                        let sentEmailResult = await MailController.asyncSend(
                            owner,
                            "Economic Substance Data Submission",
                            email.textString,
                            email.htmlString
                        );
                        if (!sentEmailResult.accepted) {
                            errors++;
                        }
                    }
                    if (errors) {
                        console.log(errors + " errors sending emails");
                        finished++;
                        if (
                            finishedSuccesfully < masterclients.length &&
                            finished == masterclients.length
                        ) {
                            res.json({ success: false });
                        }
                    } else {
                        let dateInvited = new Date(new Date().toUTCString());
                        MasterClientCodeModel.findOneAndUpdate(
                            {
                                _id: masterclient.id,
                                code: masterclient.code,
                                partitionkey: "masterclientcode",
                            },
                            { date_invited: dateInvited },
                            function (err) {
                                if (err) {
                                    console.log(err);
                                    finished++;
                                    if (
                                        finishedSuccesfully < masterclients.length &&
                                        finished == masterclients.length
                                    ) {
                                        res.json({ success: false });
                                    }
                                } else {
                                    finishedSuccesfully++;
                                    finished++;
                                    if (finishedSuccesfully == masterclients.length) {
                                        res.json({ success: true });
                                    } else if (
                                        finishedSuccesfully < masterclients.length &&
                                        finished == masterclients.length
                                    ) {
                                        res.json({ success: false });
                                    }
                                }
                            }
                        );
                    }
                }
            }
        );
    });
};

exports.getImport = function (req, res) {
    res.render("client-management/import", { user: req.session.user, title: "Import data", STANDARD_DATE_FORMAT });
};

exports.getMasterclientById = async function (req, res) {
    try {
        const masterClient = await MasterClientCodeModel.findById(req.params.id);
        const configuration = await ConfigurationModel.findOne({});
        if (!masterClient) {
            throw new Error;
        }
        res.json({success: true, masterClient, configuration});
    } catch (e) {
        res.json({success: false});
    }
};

exports.getSearchMasterclients = function (req, res) {
    let masterclientfilter = "dummyfilter";

    if (req.body.filter_masterclient && req.body.filter_masterclient.length > 2) {
        masterclientfilter = req.body.filter_masterclient;
    }

    MasterClientCodeModel.find({
        $or: [
            { code: { $regex: masterclientfilter, $options: "i" } },
            { owners: { $regex: masterclientfilter, $options: "i" } },
        ],
    })
        .sort("code")
        .limit(100)
        .exec(function (err, masterclients) {
            if (!err) {
                res.render("client-management/masterclient-list", {
                    user: req.session.user,
                    title: "Master Clients",
                    result: masterclients,
                    STANDARD_DATE_FORMAT,
                    updateAllowed: (req.session.authentication.isSubsSuperUser || req.session.authentication.isClientManagementSuperUser),
                    authentication: req.session.authentication
                });
            }
        });
};

exports.updateMasterclient = async function (req, res) {
    try{
        const mcc = await MasterClientCodeModel.findById(req.body.id);

        if(!mcc){
            return res.json({
                success: false,
                message: `Master Client not found`
            })
        }


        const owners = req.body.owners ? req.body.owners.map((owner) => owner.toLowerCase().trim()) : [];
        const invalidOwners = owners.filter((owner) => !validateEmail(owner));

        if (invalidOwners.length > 0) {
            return res.json({
                success: false,
                message: `The following owners have an invalid email format: ${invalidOwners.join(', ')}`
            })
        }

        const updateValues = {
            owners: owners,
            customIncorporationPayment: req.body.customIncorporationPayment,
            incorporation: req.body.incorporation
        }

        appInsightsClient.trackEvent({
            name: "pre-update masterclient",
            properties: {
                email: req.session.user.username,
                masterclientcode: mcc.code,
                date: new Date(),
                owners: mcc.owners ? mcc.owners.join(', ') : ""
            }
        });

        const mccUpdated = await MasterClientCodeModel.findOneAndUpdate(
            { _id: req.body.id, code: req.body.code, partitionkey: "masterclientcode" },
            updateValues,
            { new: true }
        );


        if (mccUpdated) {
            appInsightsClient.trackEvent({
                name: "post-update masterclient",
                properties: {
                    email: req.session.user.username,
                    masterclientcode: mccUpdated.code,
                    date: new Date(),
                    owners: mccUpdated.owners ? mccUpdated.owners.join(', ') : ""
                }
            });
            return res.json({
                success: true,
                message: `Master Client updated successfully.`
            })
        } else {
            appInsightsClient.trackEvent({
                name: "post-update masterclient",
                properties: {
                    email: req.session.user.username,
                    masterclientcode: mcc.code,
                    date: new Date(),
                    owners: mcc.owners ? mcc.owners.join(', ') : "",
                    error: "internal error updating masterclient"
                }
            });
            return res.json({
                success: false,
                message: `There is an error updating the Master Client, try again later.`
            })
        }
    }catch(e){
        console.log("e ",e);
        return res.json({
            success: false,
            message: "Internal server error"
        })
    }

};

exports.clearInvitationMasterclient = function (req, res) {
    MasterClientCodeModel.findOneAndUpdate(
        { _id: req.body.id, code: req.body.code, partitionkey: "masterclientcode" },
        { date_invited: null },
        function (err) {
            if (err) {
                res.json({ success: false });
            } else {
                res.json({ success: true });
            }
        }
    );
};

exports.searchMasterclientList = async function (req, res) {
    try {
        let masterclientfilter = "dummyfilter";

        if (req.query.searchFilter && req.query.searchFilter.length > 2) {
            masterclientfilter = req.query.searchFilter;
        }

        let masterClientCodes = await MasterClientCodeModel.find({
            $or: [
                  { code: { $regex: masterclientfilter, $options: "i" } },
                  { owners: { $regex: masterclientfilter, $options: "i" } },
              ],
          }, {_id: 1, code:1, owners: 1 }).sort("code").limit(100);

        if (masterClientCodes.length > 0){
            masterClientCodes = masterClientCodes.map((mcc) => {
                return {
                    _id: mcc._id,
                    id: mcc.code,
                    totalOwners: mcc.owners.length,
                    owners: mcc.owners.length > 0 ? mcc.owners.join(', ') : '',
                }
            })
        }

        res.json({status: 200, items: masterClientCodes, totalCount: masterClientCodes.length});
    } catch (e) {
        res.status(500).json({status: 500, error: "Internal Server Error"});
    }
}

exports.getSearchCompanies = async function (req, res) {
    try {
        let companyfilter = "dummyfilter";

        const configuration = await ConfigurationModel.findOne({});

        if (!configuration){
            return res.status(404).json({ success: false, message: "Configuration not found" });
        }

        if (req.body.filter_company && req.body.filter_company.length > 2) {
            companyfilter = req.body.filter_company;
        }

        let modulesFilter = {};
        if (req.body.filter_substance_module) {
            console.log(req.body.filter_substance_module)
            if (req.body.filter_substance_module === 'true') {
                modulesFilter["substanceModule.active"] = true;
            }
            else if (req.body.filter_substance_module === 'false') {
                modulesFilter["substanceModule.active"] = { '$ne': true };
            }
        }

        if (req.body.filter_accounting_module) {
            if (req.body.filter_accounting_module === 'true') {
                modulesFilter["accountingRecordsModule.active"] = true;
            }
            else if (req.body.filter_accounting_module === 'false') {
                modulesFilter["accountingRecordsModule.active"] = { '$ne': true };
            }
        }

        if (req.body.filter_dirbo_module) {
            if (req.body.filter_dirbo_module === 'true') {
                modulesFilter["dirboModule.active"] = true;
            }
            else if (req.body.filter_dirbo_module === 'false') {
                modulesFilter["dirboModule.active"] = { '$ne': true };
            }
        }


        const companies = await CompanyModel.aggregate([
            {
                '$match': {
                    $or: [
                        { name: { $regex: companyfilter, $options: "i" } },
                        { code: { $regex: companyfilter, $options: "i" } },
                        { masterclientcode: { $regex: companyfilter, $options: "i" } },
                        { incorporationcode: { $regex: companyfilter, $options: "i" } },
                    ],
                    ...modulesFilter
                }
            },
            {
                '$lookup': {
                    'from': 'clients',
                    'localField': 'code',
                    'foreignField': 'companyCode',
                    'as': 'client'
                }
            },
            {
                '$project': {
                    '_id': 1,
                    'name': 1,
                    'address':1,
                    'code': 1,
                    'incorporationcode':1,
                    'incorporationdate':1,
                    'masterclientcode': 1,
                    'referral_office':1,
                    'isDeleted':1,
                    'deletedAt':1,
                    'hasITADate':1,
                    'approvedITAStartDate':1,
                    'approvedITAEndDate':1,
                    'applicationITADate':1,
                    'fileReviewName': '$client.companyName',
                    'substanceModule.active': 1,
                    'accountingRecordsModule.active': 1,
                    'dirboModule.active': 1
                }
            },
            {
                $sort: { "name": 1 },
            },
            {
                $limit: 100
            }
        ]);

        let referralOffices = configuration.referralOffices?.length > 0 ? configuration.referralOffices.map((r) => r.code) : [];


        const paymentYearsList = utils.getAvailablePaymentYearsList();

        res.render("client-management/company-list", {
            user: req.session.user,
            filters: req.body,
            title: "Companies",
            result: companies,
            referralOffices,
            paymentYearsList,
            authentication: req.session.authentication,
            disallowChangeIncorporationDate: (!req.session.authentication.isClientManagementSuperUser && !req.session.authentication.isSubsSuperUser),
            STANDARD_DATE_FORMAT
        });
    } catch (e) {
        console.log(e);
        res.json({ success: false });
    }
};


exports.getUnapprovedCompanies = async function (req, res) {
    try {
        const configuration = await ConfigurationModel.findOne({});

        if (!configuration) {
            return res.status(404).json({ success: false, message: "Configuration not found" });
        }

        const query = {
            "substanceModule.approval.approved": { "$ne": true },
        }
        if (req.query.filter_company && req.query.filter_company.length > 2) {
            query["$or"] = [
                { name: { $regex: req.query.filter_company, $options: "i" } },
                { code: { $regex: req.query.filter_company, $options: "i" } },
                { masterclientcode: { $regex: req.query.filter_company, $options: "i" } },
                { incorporationcode: { $regex: req.query.filter_company, $options: "i" } },
            ]
        }

        const companies = await CompanyModel.aggregate([
            {
                '$match': query
            },
            {
                '$project': {
                    '_id': 1,
                    'name': 1,
                    'address': 1,
                    'code': 1,
                    'incorporationcode': 1,
                    'incorporationdate': 1,
                    'masterclientcode': 1,
                    'referral_office': 1,
                    'isDeleted': 1,
                    'deletedAt': 1,
                    'hasITADate': 1,
                    'approvedITAStartDate': 1,
                    'approvedITAEndDate': 1,
                    'applicationITADate': 1
                }
            },
            {
                $sort: { "name": 1 },
            },
            {
                $limit: 100
            }
        ]);

        let referralOffices = configuration.referralOffices?.length > 0 ? configuration.referralOffices.map((r) => r.code) : [];


        const paymentYearsList = utils.getAvailablePaymentYearsList();

        res.render("client-management/company-unapproved-list", {
            user: req.session.user,
            filters: req.query,
            title: "New/Approve ES Companies",
            result: companies,
            referralOffices,
            paymentYearsList,
            authentication: req.session.authentication,
            disallowChangeIncorporationDate: (!req.session.authentication.isClientManagementSuperUser && !req.session.authentication.isSubsSuperUser),
            STANDARD_DATE_FORMAT            
        });
    } catch (e) {
        console.log(e);
        res.json({ success: false });
    }

};

exports.updateCompany = async function (req, res) {
    try {

        let companyApproved = false; //conditionally looking for changes to track in analytics
        let companyEsActivationChange = false; //conditionally looking for changes to track in analytics
        let companyAccRecsActivationChange = false; //conditionally looking for changes to track in analytics
        let companyBoDirActivationChange = false; //conditionally looking for changes to track in analytics

        let masterclients = await MasterClientCodeModel.find({ code: req.body.masterclientcode?.trim() });

        if (!masterclients.length) {
            return res.json({success: false, invalidCode: true});
        }

        let company = await CompanyModel.findOne({ _id: req.body.id, code: req.body.code?.trim(), partitionkey: "company" });
        if (!company) {
            return res.json({ success: false });
        }


        if(req.body.hasITADate === true){
            if (!req.body.approvedStartDate || !req.body.approvedEndDate ){
                return res.json({ success: false, error: "Please provide the approved ITA start and end date" });
            }

            if (moment(req.body.approvedStartDate, 'MM/DD/YYYY').diff(moment(req.body.approvedEndDate, 'MM/DD/YYYY'), 'days') > 0){
                return res.json({ success: false, error: "Approved start date can't be greater than approved end date" });
            }
        }




        const newMcc= req.body.masterclientcode?.trim();
        let isNewMcc = company.masterclientcode !== req.body.masterclientcode?.trim();

        if (isNewMcc){
            appInsightsClient.trackEvent({
                name: "pre-change company masterclient",
                properties: {
                    email: req.session.user.username,
                    masterclientcode: company.masterclientcode,
                    company: company.code,
                    date: new Date()
                }
            });
            // delete requested files
            let filereview = await FileReviewModel.findOne({companyCode: company.code,
                masterClientCode: company.masterclientcode});

            if (filereview){

                if (filereview.status.code ===  "SENT TO CLIENT"){
                    await RequestedFileModel.findOneAndDelete({referenceId:filereview._id,
                        masterClientCode:company.masterclientcode,  status: {"$in": ["IN PROGRESS", 'NOT STARTED']} });
                    await MessageModel.findOneAndDelete({ masterClientCodes: company.masterclientcode,
                        subject: 'File update queries', status: 'SCHEDULED' });

                    filereview.status = {
                        code: 'ASSIGNED',
                        statusDate: new Date()
                    };

                    const comment = {
                        username: req.session.user.username.toLowerCase(),
                        role: 'MG',
                        comment: 'Masterclient is changed by email address '+ req.session.user.username.toLowerCase() +
                          ' and there for missing file request is cancelled.',
                        date: new Date(),
                        from: '',
                        to: ''
                    };
                    filereview.comments.push(comment);
                    filereview.markModified('comments');
                }
                filereview.masterClientCode = newMcc;
                await filereview.save();
            }

            await EntryModel.updateMany({"company_data.code": company.code, "company_data.masterclientcode": company.masterclientcode},
              {"company_data.masterclientcode": newMcc});

            await ArchivedEntryModel.updateMany({"company_data.code": company.code, "company_data.masterclientcode": company.masterclientcode},
              {"company_data.masterclientcode": newMcc})

            await CompanyIncorporationModel.updateMany({"companyCode": company.code, "masterClientCode": company.masterclientcode},
              {"masterClientCode": newMcc})


            await FinancialReportModel.updateMany({ "companyData.code": company.code, "companyData.masterclientcode": company.masterclientcode },
                { "companyData.masterclientcode": newMcc, "masterClientCode": newMcc });
        }

        company.masterclientcode = newMcc;
        req.body.referralOffice = ((req.body.referralOffice).toUpperCase()).replace(/\s/g,'');


        company.referral_office = req.body.referralOffice?.trim();

        if (req.body.incorporationDate && req.body.incorporationDate !==  company.incorporationdate){
            company.incorporationdate = req.body.incorporationDate;
        }


        company.hasITADate = req.body.hasITADate === true;
        company.approvedITAStartDate =  req.body.hasITADate === true ? req.body.approvedStartDate : null;
        company.approvedITAEndDate = req.body.hasITADate === true ? req.body.approvedEndDate : null;
        company.applicationITADate = req.body.hasITADate === true ? req.body.applicationITADate : null;
        company.paymentYears = req.body.paymentYears || [];


        if (req.body.activeSubstanceModule !== null && req.body.activeSubstanceModule  !== company.substanceModule?.active) {
            company.isDeleted = req.body.activeSubstanceModule !== true,
            company.deletedAt = req.body.activeSubstanceModule !== true ? new Date() : null;
            companyEsActivationChange=true;
        }

        //console.log("udpate company",company.code)
        company.substanceModule.active = req.body.activeSubstanceModule === true;
        company.substanceModule = {...company.substanceModule} 
        

        if (req.body.approvedSubstanceModule === true){
            if(company.substanceModule?.approval?.approved !== true){
                // console.log("approving")
                company.substanceModule.active = true
                company.isDeleted = false,
                company.deletedAt = null;
                company.substanceModule.approval = {
                    approved: true,
                    by: req.session.user.username.toLowerCase(),
                    date: new Date()
                }
                //we are not tracking deactivations
                companyApproved = true;
                companyEsActivationChange = true;
            }
        }


        if (req.body.activeAccountingModule !== null){
            company.accountingRecordsModule = { ...company.accountingRecordsModule }
            companyAccRecsActivationChange =  company.accountingRecordsModule.active !== req.body.activeAccountingModule;
            company.accountingRecordsModule.active = req.body.activeAccountingModule
        }

        if (req.body.activeDirBoModule !== null){
            company.dirboModule = { ...company.dirboModule }
            companyBoDirActivationChange = company.dirboModule.active !== req.body.activeDirBoModule;
            // console.log(company.dirboModule.active , req.body.activeDirBoModule)
            company.dirboModule.active = req.body.activeDirBoModule
        }

        company.modifiedBy = req.session.user.username;
        await company.save();

        if(isNewMcc){
            appInsightsClient.trackEvent({
                name: "post-change company masterclient",
                properties: {
                    email: req.session.user.username,
                    masterclientcode: company.masterclientcode,
                    company: company.code,
                    date: new Date()
                }
            });
        }

        // do all the tracking after the await.save success
        if(companyApproved){ 
            //console.log("companyApproved",company.code)
            appInsightsClient.trackEvent({
                name: "new company es approved",
                properties: {
                    email: req.session.user.username,
                    masterclientcode: company.masterclientcode,
                    company: company.code,
                    date: new Date()
                }
            }); 
        } 
        if(companyEsActivationChange){  
            const strSubstanceActivation = req.body.activeSubstanceModule===true ? "activated" : "deactivated";
            // console.log("companyEsActivationChange",company.code,strSubstanceActivation);
            appInsightsClient.trackEvent({
                name: `module ${strSubstanceActivation}: SubstanceModule`,
                properties: {
                    email: req.session.user.username,
                    masterclientcode: company.masterclientcode,
                    company: company.code,
                    date: new Date()
                }
            }); 
        } 
        if(companyAccRecsActivationChange){   
            const strAccRecActivation = req.body.activeAccountingModule===true ? "activated" : "deactivated";
            //console.log("companyAccRecsActivationChange",company.code,strAccRecActivation);
            appInsightsClient.trackEvent({
                name: `module ${strAccRecActivation}: AccountingModule`,
                properties: {
                    email: req.session.user.username,
                    masterclientcode: company.masterclientcode,
                    company: company.code,
                    date: new Date()
                }
            }); 
        } 
        if(companyBoDirActivationChange){ 
            const strDirBOActivation = req.body.activeDirBoModule===true ? "activated" : "deactivated";
            //console.log("companyBoDirActivationChange",company.code,strDirBOActivation);
            appInsightsClient.trackEvent({
                name: `module ${strDirBOActivation}: DirBoModule`,
                properties: {
                    email: req.session.user.username,
                    masterclientcode: company.masterclientcode,
                    company: company.code,
                    date: new Date()
                }
            }); 
        }
        

        return res.json({ success: true });
    } catch (e) {
        console.log("Error updating company", e);
        res.json({ success: false });
    }

};

exports.updateCompanyName = async function (req, res) {
    try {
        const data = req.body;
        data.name = data.name?.trim();
        const company = await CompanyModel.findOne({ _id: data.id, code: data.code });

        const companyNameChangeLog = {
            email: req.session.user.username.toLowerCase(),
            originalName: company.name,
            newName: data.name,
            modulesChanged: [],
            changedAt: new Date()
        };

        company.name = data.name;


        if (data.nameChangesClients) {
            await FileReviewModel.updateMany({ companyCode: data.code }, { companyName: data.name });
            await ClientReviewModel.updateMany({ companyCode: data.code }, { companyName: data.name });
            companyNameChangeLog.modulesChanged.push("File Review clients");
        }

        if (data.nameChangesSubmitted) {
            await ArchivedEntryModel.updateMany({ 'company_data.code': data.code }, { 'company_data.name': data.name });
            companyNameChangeLog.modulesChanged.push("Substance Submitted (History)");
        }
        if (data.nameChangesPending) {
            await EntryModel.updateMany({ 'company_data.code': data.code }, { 'company_data.name': data.name });
            companyNameChangeLog.modulesChanged.push("Substance Pending (Current Future)");
        }
        if (data.nameChangesRequestedFiles) {
            await RequestedFileModel.updateMany({ companyCode: data.code }, { companyName: data.name });
            companyNameChangeLog.modulesChanged.push("File Review requested Files");
        }
        if (data.nameChangesBeneficialOwners) {
            await BeneficialOwnerModel.updateMany({ companyCode: data.code }, { companyName: data.name });
            companyNameChangeLog.modulesChanged.push("File Review relations");
        }
        if (data.nameChangesAccounting) {
            await FinancialReportModel.updateMany({ 'companyData.code': data.code }, { 'companyData.name': data.name });
            companyNameChangeLog.modulesChanged.push("Accounting records submissions (History)");
        }
        if (!company.companyNameChanges){
            company.companyNameChanges = [companyNameChangeLog]
        }else{
            company.companyNameChanges.push(companyNameChangeLog)
        }

        company.modifiedBy = req.session.user.username;
        await company.save();
        return res.json({ success: true });
    } catch (e) {
        console.log(e);
        res.json({ success: false });
    }

};

exports.approveCompany = async function (req, res){

    //this is jsut for the flow from the row button, not popup
    try {
        let company = await CompanyModel.findById(req.params.companyId);

        if (!company) {
            return res.json({ status: 404, error: "Company not found" });
        }

        if (company.substanceModule?.approval?.approved === true){
            return res.json({ status: 400, error: "Company already approved" });
        }

        company.substanceModule = { ...company.substanceModule }
        company.substanceModule.approval = {
            approved: true,
            by: req.session.user.username.toLowerCase(),
            date: new Date()
        }

        if(company.substanceModule.active !== true){
            company.substanceModule.active = true;
            company.isDeleted = false;
            company.deletedAt = null;
        }

        await company.save();
        
        // do all the tracking after the await.save success
        appInsightsClient.trackEvent({
            name: "new company es approved (approveCompany)",
            properties: {
                email: req.session.user.username,
                masterclientcode: company.masterclientcode,
                company: company.code,
                date: new Date()
            }
        }); 
        appInsightsClient.trackEvent({
            name: `module activated: SubstanceModule (approveCompany)`,
            properties: {
                email: req.session.user.username,
                masterclientcode: company.masterclientcode,
                company: company.code,
                date: new Date()
            }
        }); 


        return res.json({ status: 200, message: "Company approved successfully" });
    } catch (e) {
        console.log("Error updating company", e);
        res.json({ status: 500, error: "Internal Server Error" });
    }
}


exports.getSearchUsers = function (req, res) {
    UserModel.find({}, function (err, users) {
        res.render("client-management/user-list", { title: "Users", result: users, STANDARD_DATE_FORMAT });
    });
};

exports.unlockUser = function (req, res) {
    UserModel.findOneAndUpdate(
        { _id: req.body.id, email: req.body.email },
        { locked: false },
        function (err) {
            if (err) {
                console.log(err);
                res.json({ success: false });
            } else {
                res.json({ success: true });
            }
        }
    );
};

exports.blockUser = function (req, res) {
    UserModel.findOneAndUpdate(
        { _id: req.body.id, email: req.body.email },
        { locked: true },
        function (err) {
            if (err) {
                console.log(err);
                res.json({ success: false });
            } else {
                res.json({ success: true });
            }
        }
    );
};


exports.clear2faUser = function (req, res) {
    UserModel.findOneAndUpdate(
        { _id: req.body.id, email: req.body.email },
        { secret_2fa: null, mfa_type: null },
        function (err) {
            if (err) {
                console.log(err);
                res.json({ success: false });
            } else {
                res.json({ success: true });
            }
        }
    );
};

exports.getImportData = function (req, res) {
    res.render("client-management/import-file", { title: "Import File", STANDARD_DATE_FORMAT });
};

exports.saveImportData = async function (req, res) {
    const data = req.body.importedData;
    if (data.type === "Company") {
        const companies = [];
        for (let company of data.data) {
            if (!company.exist && !company.existInternal && !company.errors.length) {
                companies.push({
                    name: company.name,
                    code: company.code?.trim(),
                    incorporationcode: company.incorporationCode?.trim(),
                    incorporationdate: company.incorporationDate ? moment(company.incorporationDate).utc().format('YYYY-MM-DD') : undefined,
                    masterclientcode: company.mccode?.trim(),
                    referral_office: company.referral,
                    riskgroup: company.riskgroup,
                    company_type: company.company_type,
                    partitionkey: "company",
                    hasITADate:  company.hasITADate === true,
                    approvedITAStartDate:   company.hasITADate === true ? moment(company.approvedITAStartDate).utc().format('YYYY-MM-DD') : undefined,
                    approvedITAEndDate:  company.hasITADate === true ? moment(company.approvedITAEndDate).utc().format('YYYY-MM-DD') : undefined,
                    applicationITADate: company.hasITADate === true ?moment(company.applicationITADate).utc().format('YYYY-MM-DD')  : undefined,
                    createdBy: req.session.user.username,
                    substanceModule: { active: false },
                    accountingRecordsModule: { 
                      active: true,
                      selfServiceCompleteAnnualReturnAmount: null,
                      selfServicePrepareAnnualReturnAmount: null,
                      tridentServiceCompleteAnnualReturnAmount: null,
                      tridentServiceDropAccountingRecordsAmount: null,
                    },
                    dirboModule: { active: true }

                });
            }
        }
        CompanyModel.insertMany(companies, function (err) {
            if (!err) {
                res.json({ success: true, inserted: companies.length });
            } else {
                console.log(err);
                res.json({ success: false });
            }
        });
    } else if (data.type === "Masterclient") {
        const masterclients = [];
        for (let masterclient of data.data) {
            if (!masterclient.exist && !masterclient.existInternal && !masterclient.errors.length) {
                const owners = [masterclient.option1.toLowerCase().trim()];
                if (masterclient.option2){
                    owners.push(masterclient.option2.toLowerCase().trim())
                }
                masterclients.push({
                    partitionkey: "masterclientcode",
                    code: masterclient.code?.trim(),
                    owners,
                });
            }
        }
        MasterClientCodeModel.insertMany(masterclients, function (err) {
            if (!err) {
                res.json({ success: true, inserted: masterclients.length });
            } else {
                console.log(err);
                res.json({ success: false, err });
            }
        });
    } else {
        res.json({ success: false, type: "Not found" });
    }
};

exports.processImport = function (req, res) {
    try {
        if (!req.files) {
            res.json({ error: "Files not found" });
            return;
        }
        const file = req.files.fileUploaded;
        const data = new Uint8Array(file.data);
        const workbook = xlsx.read(data, {
            type: "array",
            cellText: false,
            cellDates: true,
        });

        const worksheet = workbook.Sheets[workbook.SheetNames[0]];

    const rows  =  xlsx.utils.sheet_to_json(worksheet, {
        blankrows: false,
        header: ['column1', 'column2', 'column3', 'column4', 'column5','column6', 'column7', 'column8', 'column9',
            'column10', 'column11', 'column12', 'column13', 'column14' ],
        raw: true,
        rawNumbers: true
    });

    let masterclients = [];
    let companies = [];
    let title = {};
    let type = "";
    const validateCodeRegex = /^[a-zA-Z0-9_-]+$/;

    if (rows.length > 1){
        title = rows.shift();

        if (title.column1 === "MC Code" &&  title.column2 === "Email Option 1" && title.column3 === "Email Option 2" ) {
            type = "Masterclient";

            masterclients = rows.map((r) => {
                return {
                    code: r.column1 ? r.column1.toString().trim() : '',
                    option1: r.column2 ? r.column2.toString().trim() : '',
                    option2: r.column3 ? r.column3.toString().trim() : '',
                }
            })
        }
        else if(title.column1 === "Company Code" && title.column2 === "Name" &&  title.column3 === "MC Code" &&
          title.column4 === "Incorporation Number" && title.column5 === "Referral" && title.column6 === "Incorporation Date" &&
          title.column7 === "Status" && title.column8 === "Fee" && title.column9 === "Company Type" && title.column10 === "Riskgroup"){
            type = "Company";

            companies = rows.map((r) => {
               return {
                   code:  r.column1 ? r.column1.toString().trim() : '',
                   name:  r.column2 ?  r.column2.toString().trim() : '',
                   mccode:  r.column3  ?  r.column3.toString().trim() : '',
                   incorporationCode:  r.column4  ?  r.column4.toString().trim() : '',
                   referral:  r.column5  ?  r.column5.toString().trim() : '',
                   incorporationDate:  r.column6,
                   status:  r.column7  ?  r.column7.toString().trim() : '',
                   fee:  r.column8,
                   company_type:  r.column9  ?  r.column9.toString().trim() : '',
                   riskgroup:  r.column10  ?  r.column10.toString().trim() : '',
                   hasITADate:  r.column11 && r.column11.toString().trim().toLowerCase() === "yes",
                   approvedITAStartDate:  r.column12,
                   approvedITAEndDate:  r.column13,
                   applicationITADate:  r.column14,
               }
            });

        }else{
            return res.json({ error: "Invalid data template!" });
        }
    }else{
        return  res.json({ error: "The file is empty" });
    }

        if (type === "Masterclient") {
            let total = masterclients.length;
            let validated = 0;
            //translate all codes to uppercase
            //translate all options to lowercase
            for (let masterclientItem of masterclients) {
                // validate fields
                masterclientItem.errors = [];
                masterclientItem.code = masterclientItem.code.toUpperCase().trim();

                if (!validateCodeRegex.test(masterclientItem.code)){
                    masterclientItem.errors.push('No special characters are allowed in the master client code');
                }


                if (masterclientItem.option1 === "" || !utils.validateEmailFormat(masterclientItem.option1)){
                    masterclientItem.errors.push('Email Option 1 is required. Please provide a valid email address');
                }
                else{
                    masterclientItem.option1 = masterclientItem.option1.toLowerCase();
                }

                if (masterclientItem.option2 !== ""){
                    if (!utils.validateEmailFormat(masterclientItem.option2)) {
                        masterclientItem.errors.push('Email Option 2 is invalid. Please provide a valid email address');
                    } else {
                        masterclientItem.option2 = masterclientItem.option2.toLowerCase();
                    }
                }
            }

            //check for duplicates within in the file
            for (let mccIdx = 0; mccIdx < masterclients.length; mccIdx++) {
                for (
                  let mccIdxInternal = mccIdx + 1;
                  mccIdxInternal < masterclients.length;
                  mccIdxInternal++
                ) {
                    if (masterclients[mccIdx].code === masterclients[mccIdxInternal].code) {
                        masterclients[mccIdxInternal].existInternal = true;
                    }
                }
            }

            //check for duplicates in the database
            for (let masterclientItem of masterclients) {
                MasterClientCodeModel.exists({ code: masterclientItem.code }, function (
                  err,
                  exist
                ) {
                    masterclientItem.exist = exist;
                    validated++;
                    if (validated === total) {
                        res.json({ data: masterclients, type });
                    }
                });
            }
        }
        else if (type === "Company") {
            let total = companies.length;
            let validated = 0;

            for (let companyItem of companies) {
                //translate mccode uppercase
                companyItem.mccode = companyItem.mccode.toUpperCase().trim();

                // validate fields
                companyItem.errors = [];

                if (!validateCodeRegex.test(companyItem.code)) {
                    companyItem.errors.push('No special characters are allowed in the company code');
                }

                if (!validateCodeRegex.test(companyItem.incorporationCode)) {
                    companyItem.errors.push('No special characters are allowed in the incorporation code');
                }

                if (!validateCodeRegex.test(companyItem.mccode)) {
                    companyItem.errors.push('No special characters are allowed in the master client code');
                }

                if (companyItem.riskgroup !== 'Low' &&
                  companyItem.riskgroup !== 'Medium' &&
                  companyItem.riskgroup !== 'High') {
                    companyItem.errors.push('Invalid risk group');
                }
                if (companyItem.company_type !== 'managed' && companyItem.company_type !== 'non managed') {
                    companyItem.errors.push('Invalid company type');
                }

                if(!companyItem.incorporationDate || companyItem.incorporationDate === ""){
                    companyItem.errors.push('Missing incorporation date');
                }

                if(companyItem.hasITADate === true){
                    if(!companyItem.approvedITAStartDate || !companyItem.approvedITAEndDate || !companyItem.applicationITADate){
                        companyItem.errors.push('Missing required ITA dates');
                    }
                }
            }

            //check for duplicates within in the file
            for (
              let companyIndex = 0;
              companyIndex < companies.length;
              companyIndex++
            ) {
                for (
                  let companyIndexInternal = companyIndex + 1;
                  companyIndexInternal < companies.length;
                  companyIndexInternal++
                ) {
                    if (
                        companies[companyIndex].code === companies[companyIndexInternal].code || 
                        companies[companyIndex].incorporationCode === companies[companyIndexInternal].incorporationCode
                    ) {
                        companies[companyIndexInternal].existInternal = true;
                    }
                }
            }

            //check for duplicates in the database
            for (let companyItem of companies) {
                CompanyModel.exists({
                    $or: [
                        { code: companyItem.code.trim() },
                        { incorporationcode: companyItem.incorporationCode.trim() }
                    ]
                }, function (err, exist) {
                    companyItem.exist = exist;
                    validated++;
                    if (validated === total) {
                        res.json({ data: companies, type });
                    }
                });
            }
        }
        else {
            res.json({ error: "Invalid data template!" });
        }
    }catch (e) {
        console.log(e);
        return  res.json({ error: "Internal error" });
    }

};

exports.getDeleteCompanies = function (req, res) {
    res.render("client-management/delete-companies", { title: "Inactivate Modules", STANDARD_DATE_FORMAT });
};

exports.saveDeleteCompanies = async function (req, res) {
    let data = req.body.importedData;
    let companiesDeleted = 0;
    //Format fields
    try {
        for (let company of data.companies) {
            if (!company.error) {
                const modulesDeactivated = []
                if(company.EconomicSubstanceModule) {
                    modulesDeactivated.push('Economic Substance Module')
                }
                if(company.AccountingModule) {
                    modulesDeactivated.push('Accounting Records Module')
                }
                if(company.BoAndDirectorModule) {
                    modulesDeactivated.push('Bo and Director Module')
                }

                const currentCompany = await CompanyModel.findOne({ partitionkey: "company", code: company.code })
                await CompanyModel.findOneAndUpdate({ partitionkey: "company", code: company.code }, { 
                    "$set": {
                        "isDeleted": company.EconomicSubstanceModule ? true : currentCompany.isDeleted, 
                        "substanceModule.active": company.EconomicSubstanceModule ? false : currentCompany.substanceModule.active, 
                        "deletedAt": new Date(), 
                        "modifiedBy": req.session.user.username,
                        "accountingRecordsModule.active": company.AccountingModule ? false : currentCompany.accountingRecordsModule.active,
                        "dirboModule.active": company.BoAndDirectorModule ? false : currentCompany.dirboModule.active ,
                    }
                });
                companiesDeleted++;
            }
        }
        res.json({ success: true, inserted: companiesDeleted });
    } catch (e) {
        console.log(e);
        res.json({ success: false });
    }
};

exports.processDeleteCompanies = async function (req, res, next) {
    try{
        if (!req.files) {
            res.json({ error: "Files not found" });
            return;
        }
        const file = req.files.fileUploaded;
        const data = new Uint8Array(file.data);
        const workbook = xlsx.read(data, {
            type: "array",
            cellText: false,
            cellDates: true,
        });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];


        const companies = [];
        const companyCodes = [];

        for (let cell in worksheet) {
            const cellAsString = cell.toString();
            if (cellAsString[0] === "A" && cellAsString !== 'A1') {
                companies.push({ code: String(worksheet[cell].v) });
                companyCodes.push(String(worksheet[cell].v));
            }

            if (cellAsString[0] === 'B' && cellAsString !== 'B1'){
                if(String(worksheet[cell].v).toLowerCase() === 'inactive') {
                    companies[companies.length -1].EconomicSubstanceModule = true
                }
            }
            if (cellAsString[0] === 'C' && cellAsString !== 'C1'){
                if (String(worksheet[cell].v).toLowerCase() === 'inactive') {
                    companies[companies.length -1].AccountingModule = true
                }
            }
            if (cellAsString[0] === 'D' && cellAsString !== 'D1'){
                if (String(worksheet[cell].v).toLowerCase() === 'inactive') {
                    companies[companies.length -1].BoAndDirectorModule = true
                }
            }
        }

        // Get DB data
        const companyList = await CompanyModel.find({ code: { $in: companyCodes } });
        for (let company of companies) {
            const dbCompany = companyList.find(c => c.code === company.code);
            if (dbCompany) {
              
                if (!company.EconomicSubstanceModule && !company.AccountingModule && !company.BoAndDirectorModule){
                    company.error = 'Please specify in the Excel file the modules where you want the company to be deleted'
                } else {
                    const error = []
                    if(company.AccountingModule && company.AccountingModule === true) {
                        if (dbCompany.accountingRecordsModule.active === false) {
                            error.push('Accounting Module already inactive');
                        }
                    }
                    if(company.EconomicSubstanceModule && company.EconomicSubstanceModule === true) {
                        if (dbCompany.substanceModule.active === false) {
                            error.push('Economic Substance Module already inactive');
                        }
                    }
                    if(company.BoAndDirectorModule && company.BoAndDirectorModule === true) {
                        if (dbCompany.dirboModule.active === false) {
                            error.push('Beneficial Owner and Director Module already inactive');
                        }
                    }
                    company.error = error.toString()
                }
               
            } else {
                company.error = 'Not found';
            }
        }
        res.json({ companies });
    }catch(e){
        console.log(e);
        next(e);
    }
};

exports.getReportInvoicesView = async function (req, res, next) {
  try {
    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_INVOICES_REPORTS,
      process.env.AZURE_STORAGE_ACCOUNT,
      process.env.AZURE_STORAGE_ACCESS_KEY
    );

    const response = containerClient.listBlobsFlat();
    const listOfReports = [];

    for await (const blob of response) {
      listOfReports.push({
        name: blob.name,
        createdAt: moment(blob.properties.createdOn).format(STANDARD_DATETIME_FORMAT),
        updatedAt: moment(blob.properties.lastModified).format(STANDARD_DATETIME_FORMAT)
      });
    }

    listOfReports.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.render('client-management/report-list',
    {
        user: req.session.user,
        title: "Financial Reports",
        reports: listOfReports,
        STANDARD_DATE_FORMAT
    });
  } catch (e) {
    console.log(e);
    next(e);
  }
};

exports.downloadReport = async function (req, res, next) {
    try {
        const containerClient = getContainerClient(
          process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_INVOICES_REPORTS,
          process.env.AZURE_STORAGE_ACCOUNT,
          process.env.AZURE_STORAGE_ACCESS_KEY
        );

        const blobClient = containerClient.getBlobClient(req.params.filename);

        blobClient.download().then((downloadResponse) => {
          if (downloadResponse.contentLength === 0) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
          }
          downloadResponse.readableStreamBody.pipe(res);
        }).catch((error) => {
          console.error("Error downloading blob:", error);
          return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).send({ "message": "Error downloading file" });
        });
    } catch (e) {
        console.log("error ", e);
        const err = new Error('Internal Server Error');
        err.status = 502;
        return next(err);
    }
};


exports.getExportIncorporations = async function (req, res, next) {
    try {
        let incorporationFilter = {};

        if (req.body.filter_masterclient && req.body.filter_masterclient.length > 2) {
            incorporationFilter["masterClientCode"] = {
                $regex: req.body.filter_masterclient.replace(' ', '|'),
                $options: "i",
            };
        }

        if (req.body.filter_company && req.body.filter_company.length > 2) {
            incorporationFilter["name"] = {
                $regex: req.body.filter_company.replace(' ', '|'),
                $options: "i",
            };
        }

        if (req.body.relevant_activities && req.body.relevant_activities.length) {
            req.body.relevantActivities = {};
            if (Array.isArray(req.body.relevant_activities)) {
                let activities = [];
                for (let relevantActivity of req.body.relevant_activities) {
                    activities.push(relevantActivity);
                    req.body.relevantActivities[relevantActivity] = true;
                }
                incorporationFilter["principalBusinessActivity"] = { "$in": activities };
            } else {
                incorporationFilter["principalBusinessActivity"] = req.body.relevant_activities;
                req.body.relevantActivities[req.body.relevant_activities] = true;
            }
        }

        if (req.body.resident_outside_bvi && req.body.resident_outside_bvi === "Yes") {
            req.body.relevantActivities = {};
            incorporationFilter["taxResidence"] = "Foreign";
        }

        if (req.body.filter_range_start) {
            incorporationFilter["submittedAt"] = {
                $gte: req.body.filter_range_start,
                $lte: req.body.filter_range_end ? req.body.filter_range_end : new Date(),
            };
        } else if (req.body.filter_range_end) {
            incorporationFilter["submittedAt"] = { $lte: req.body.filter_range_end };
        }


        let limit = 100;
        let isFiltering = false;
        if (Object.keys(incorporationFilter).length) {
            limit = process.env.MAX_QUERY_SIZE ? Number(process.env.MAX_QUERY_SIZE) : 5000 ;
            isFiltering = true;
        }
        let incorporations = await CompanyIncorporationModel.find({ status: "PAID", ...incorporationFilter }).limit(limit);

        res.render("client-management/export-incorporations", {
            title: "Export Incorporations",
            data: incorporations,
            filters: req.body,
            showLimitAlert: incorporations.length >= limit && isFiltering,
            STANDARD_DATE_FORMAT,
            authentication: req.session.authentication
        });
    } catch(e){
        console.log(e);
        next(e);
    }
};


exports.startProcessToMoveMccFiles = async function (req, res) {
    try {

        const configuration = await ConfigurationModel.findOne({});

        if (configuration && configuration.isMovingMccFiles === true) {
            return res.status(400).json({
                status: 400,
                message: "Already exists a process running to move the mcc files"
            });
        }
        const URL = process.env.MOVE_MCC_FILES_FUNCTION_URL;

        axios.post(URL, {},
          {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
          });

        return res.status(200).json({status: 200, message: "The process has been started successfully."});


    } catch (e) {
        console.log(e);
        return res.status(500).json({status: 500, message: "Internal Server Error"});
    }
};

/**
 * Get the information of a company
 * @function getCompanyDetails
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the company model info
 */
exports.getCompanyDetails = async function (req, res) {
    try {
        let company = await CompanyModel.findById(req.params.companyId);

        if (!company) {
            return res.status(404).json({
                status: 404,
                error: "Company not found"
            })
        }
        const returnCompany = {
            _id: company._id,
            name: company.name,
            address: company.address,
            code: company.code,
            incorporationcode: company.incorporationcode,
            incorporationdate: company.incorporationdate ? moment(company.incorporationdate, 'DD/MM/YYYY').utc().format(STANDARD_DATE_FORMAT) : '',
            masterclientcode: company.masterclientcode,
            referral_office: company.referral_office,
            amount: company.amount,
            isDeleted: company.isDeleted,
            deletedAt: company.deletedAt,
            hasITADate: company.hasITADate,
            approvedITAStartDate: company.approvedITAStartDate ? moment(company.approvedITAStartDate, 'DD/MM/YYYY').utc().format(STANDARD_DATE_FORMAT) : '',
            approvedITAEndDate: company.approvedITAEndDate ? moment(company.approvedITAEndDate, 'DD/MM/YYYY').utc().format(STANDARD_DATE_FORMAT) : '',
            applicationITADate: company.applicationITADate ? moment(company.applicationITADate, 'DD/MM/YYYY').utc().format(STANDARD_DATE_FORMAT) : '',
            paymentYears: company.paymentYears || [],
            substanceModule: company.substanceModule || {},
            accountingRecordsModule: company.accountingRecordsModule || {},
            dirboModule: company.dirboModule || {},
        };

        return res.status(200).json({
            status: 200,
            company: returnCompany,
            allowEditAccountingModule: req.session.authentication.isAccountingSuperUser,
            allowEditDirboModule: req.session.authentication.isDirBoImportManager,
            allowActivatingSubstanceModule: returnCompany.substanceModule.approval?.approved === true,
            authentication: req.session.authentication
        })
    } catch (e) {
        console.log(e);
        return res.status(500).json({
            status: 500,
            error: "Internal Server Error"
        })
    }
};

/**
 * Get the list of change logs of a company
 * @function getCompanyChangeLogs
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the change logs
 */
exports.getCompanyChangeLogs = async function (req, res) {
    try {
        let company = await CompanyModel.findById(req.params.companyId);

        if (!company) {
            return res.status(404).json({
                status: 404,
                error: "Company not found"
            })
        }

        const dbLogs = await DocumentChangesModel.aggregate([
            {
                $match: { documentId: company._id }
            },
            {
                $project: {
                    _id: 1,
                    modifiedValuesLength: { $size: "$modifiedValues" },
                    modifiedBy: 1,
                    createdAt: 1,
                    updatedAt: 1
                }
            },
            {
                $sort: { createdAt: -1 } 
            }
        ]);

        const returnLogs = {
            documentId: company._id,
            changeLogs: dbLogs
        };

        return res.status(200).json({
            status: 200,
            data: returnLogs,
        })
    } catch (e) {
        console.log(e);
        return res.status(500).json({
            status: 500,
            error: "Internal Server Error"
        })
    }
};

/**
 * Get the details of a company change log
 * @function getCompanyChangeLogs
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return JSON object with the details of company log
 */
exports.getCompanyLogDetails = async function (req, res) {
    try {
        let company = await CompanyModel.findById(req.params.companyId);

        if (!company) {
            return res.status(404).json({
                status: 404,
                error: "Company not found"
            })
        }

        const dbLog = await DocumentChangesModel.findById(req.params.logId, { _id: 1, collectionName:1, modifiedValues: 1});
        const logValuesMapped = dbLog.getModifiedValuesMapped()

        return res.status(200).json({
            status: 200,
            data: {
                _id: dbLog._id,
                modifiedValues: logValuesMapped
            }
        })
    } catch (e) {
        console.log(e);
        return res.status(500).json({
            status: 500,
            error: "Internal Server Error"
        })
    }
};


/**
 * Export a zip file with all the files of the selected incorporations
 * @function getCompanyDetails
 * @param {object}  req HTTP request properties.
 * @param {object}  res Response data to send to client-side.
 * @return Zip File 
 */
exports.doExportIncorporationFiles = async function (req, res, next) {
    try {
        let incorporationId = req.body["incorporationId"];

        const containerClient = getContainerClient(
          process.env.AZURE_STORAGE_CONTAINER_CLIENT_INCORPORATION,
          process.env.AZURE_STORAGE_ACCOUNT,
          process.env.AZURE_STORAGE_ACCESS_KEY
        );

        const archive = archiver("zip", {
            zlib: { level: 9 },
        });

        const fileKeys = ["structureChartFiles", "passportFiles", "addressProofFiles", "otherDeclarationFiles", "officialIncorporationFiles"];
        
        const incorporation = await CompanyIncorporationModel.findOne({ _id: { "$in": incorporationId}}, {_id:1, name: 1, files:1})

        if(!incorporation){
            const err = new Error('Incorporation not found');
            err.status = 404;
            return next(err);
        }

        archive.append("", { name: `${incorporation._id}/` })
        if (incorporation.files && Object.keys(incorporation.files).length !== 0) {
            for (const key of fileKeys) {
                const value = incorporation.files[key];

                if (value?.length > 0) {
                    for (let evidence of value) {
                        const blobClient = containerClient.getBlobClient(`${incorporation._id}/${evidence.blobName}`);
                        let stream = (await blobClient.download()).readableStreamBody;

                        archive.append(stream, {
                            name: `${incorporation._id}/${key}/${evidence.originalName}`,
                        });
                    }
                }

            }
        }
        
        archive.on("error", function (err) {
            throw err;
        });

        archive.pipe(res);
        archive.finalize();

    } catch (e) {
        console.log(e);
        next(e);
    }

};



function validateEmail(email) {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}
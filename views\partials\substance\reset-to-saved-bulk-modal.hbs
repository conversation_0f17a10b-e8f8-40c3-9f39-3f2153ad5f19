<div class="modal hide fade confirmation-modal-md" tabindex="-1" role="dialog" 
  id="rtsBulkModal" style="display: none;" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="resetToSaveBulkModalTitle">Confirmation</h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-12">
            <p>
              This will re-open the submissions for the client to amend changes. Are you sure? 
            </p>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <div class="form-group">
              <a id='downloadLink' href="#" style="display: none;" download></a>
              <label class="mt-1" for="reasonBulk">Please provide a note for the client:</label>
              <textarea rows="3" name="reasonBulk" id="reasonBulk" class="form-control" required
                minlength="50"></textarea>
            </div>
          </div>
        </div>

        <div class="row mt-2 mb-0">
          <div class="col-12">
            <p class="m-0">Total entries to be reset: <b id="totalEntries">0</b></p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
          Close
        </button>
        
        <button id="submitRTSBulkButton" type="button" class="btn btn-primary waves-effect waves-light">
          Submit
        </button>
        
        <button id="submitRTSBulkSpinner" style="display: none;" class="btn btn-primary waves-effect waves-light" type="button"
          disabled>
          <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
          Submit...
        </button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>
<script type="text/javascript">

  const $submitRTSBulkSpinner = $("#submitRTSBulkSpinner");
  let rtsBulkEntries = [];

  $('#rtsBulkModal').on('shown.bs.modal', function (event) {
    let button = $(event.relatedTarget); 
    const data = $('#entriesTable').DataTable().rows('.selected').data();
    for (let idx = 0; idx < data.length; idx++) {
      rtsBulkEntries.push(data[idx][0])
    }

    $("#totalEntries").text(rtsBulkEntries.length);

  });


  $('#rtsBulkModal').on('hide.bs.modal', function () {
    $("#reopenReasonBulk").val('');
    rtsBulkEntries = [];
  });

  $("#submitRTSBulkButton").on('click', function (event) {
    $(this).prop('disabled', true);
    $(this).hide();
    $submitRTSBulkSpinner.show();
    event.stopPropagation();

    const reason = $("#reasonBulk").val();

    if (!reason || reason === '' ) {
      toastr["warning"]("Please provide the reason to re-open the submissions");
      $submitRTSBulkSpinner.hide();
      $(this).prop('disabled', false).show();
      return false;
    }
    else if(reason.length < 50){
      toastr["warning"]("Please provide more information in the note for the client.");
      $submitRTSBulkSpinner.hide();
      $(this).prop('disabled', false).show();
      return false;
    }

    $.ajax({
      type: "POST",
      url: `./reset-to-saved-bulk`,
      contentType: "application/json; charset=utf-8",
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      data: JSON.stringify({
        reason: reason,
        entryIds: rtsBulkEntries
      }),
      success: function (response) {
        if (response.status === 200) {
          if(response.data?.xlsxData){
            const url = 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,' + response.data?.xlsxData;
            $('#downloadLink').attr('href', url);
            $('#downloadLink').attr('download', response.data?.filename);
            document.getElementById('downloadLink').click();
          }
          Swal.fire('Success', response.message, 'success').then(() => {
            location.reload();
          });
        } else if (response.status === 400) {
          Swal.fire('Error', response.error, 'error');
          $("#submitRTSBulkButton").prop('disabled', false).show();
          $submitRTSBulkSpinner.hide();

        }
      },
      error: (err) => {
        if (err.responseJSON?.error) {
          toastr["error"](err.responseJSON?.error);
        }
        else {
          toastr["error"]('Sorry, There was an error with the reset to saved request');
        }
         $submitRTSBulkSpinner.hide();
        $("#submitRTSBulkButton").prop('disabled', false).show();
      },

    });
  });
</script>
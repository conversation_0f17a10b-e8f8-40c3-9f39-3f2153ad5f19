<main class="">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-10">
                <div class="card">
                    <form method="POST" id="submitForm">
                        <div class="card-body">
                            <h1>{{title}}</h1>
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="filter_masterclient">Masterclient</label>
                                    <input class='form-control' type='text' name='filter_masterclient'
                                        id='filter_masterclient' />
                                </div>
                                <div class="col-md-4" style="padding-top:25px">
                                    <input type='SUBMIT' class='btn btn-light btn-sm waves-effect' value='Search' />
                                </div>
                            </div>
                            <br /><br />
                            <table id="scroll-horizontal-datatable" class="table w-100 nowrap">
                                <thead>
                                    <tr>
                                        <th>Id</th>
                                        <th>Master Client Code</th>
                                        <th>Owners</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {{#each result}}
                                    <tr>
                                        <td>{{_id}}</td>
                                        <td>{{code}}</td>
                                        <td>
                                            <div class="checkbox checkbox-primary mb-2 form-group" id="{{_id}}">
                                                {{#each owners}}
                                                <input type="checkbox" class="custom-control-input send-invitation"
                                                    id="send_invitation_{{this}}_{{../_id}}"
                                                    name="send_invitation_{{../_id}}" value="{{this}}"
                                                    onchange="onSelect('{{../_id}}')">
                                                <label class="mb-2"
                                                    for="send_invitation_{{this}}_{{../_id}}">{{this}}</label>
                                                <br />
                                                {{/each}}
                                            </div>
                                        </td>
                                        <td style="vertical-align: middle;">
                                            <input type="button" name="select_owner" id="select_owners_{{_id}}"
                                                class="btn btn-primary waves-effect waves-light send-all-button"
                                                onclick="selectOwners('{{_id}}')" value="Select All Owners">
                                        </td>
                                    </tr>
                                    {{/each}}
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-12">
                            <div class="row">
                                <div class="mb-3 ml-3">
                                    <a href='/client-management/'
                                        class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                                    <input type="button" class="btn btn-primary width-lg waves-effect waves-light"
                                        id="changeAllButton" onclick="changeAll()" value="Check All">
                                </div>
                                <div class="mb-3 ml-2">
                                    <input type="button" class="btn btn-primary width-lg waves-effect waves-light"
                                        id="showConfirmationButton" onclick="showConfirmation()"
                                        value="Send Invitation">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal hide fade invitation-modal-lg" tabindex="-1" role="dialog"
                aria-labelledby="myLargeModalLabel" id="invitation_modal" style="display: none;" aria-hidden="true">
                <div class="modal-dialog modal-md modal-dialog-scrollable">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title" id="myLargeModalLabel">Confirm Invitation</h4>
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info" role="alert" id="wait_alert" style="display: none;">
                                <i class="fa fa-spinner"></i> <strong>Emails are being sent! </strong>Please wait for
                                them to finish
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <p>Do you want to send an invitation to the following Master Clients?</p>
                                    <ul class="ml-3" style="list-style-type:square">
                                        <div id="masterclientsConfirmation"></div>
                                    </ul>
                                    <div class="d-flex justify-content-between mt-3">
                                        <input type="button" class="btn btn-secondary waves-effect waves-light"
                                            data-dismiss="modal" value="Cancel">

                                        <input type="button" class="btn btn-primary waves-effect waves-light"
                                            id="sendInvitationButton" onclick="sendInvitation()" value="Confirm">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div><!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </div>
        </div>
    </div>
</main>

<script type="text/javascript">
    let table;
    let selectedMasterClients = [];
    $(document).ready(function () {

        table = $("#scroll-horizontal-datatable").DataTable({ "pageLength": 50, "columnDefs": [{ "visible": false, "targets": [0] }], "order": [[1, "asc"]], scrollX: !0, language: { paginate: { previous: "<i class='mdi mdi-chevron-left'>", next: "<i class='mdi mdi-chevron-right'>" } }, drawCallback: function () { $(".dataTables_paginate > .pagination").addClass("pagination-rounded") } });
    });

    function changeAll() {
        if ($('#changeAllButton').val() == 'Check All') {
            $('.send-invitation').prop('checked', true);
            $('#changeAllButton').val('Uncheck All');
            $('.send-all-button').val('Uncheck All Owners');
        } else {
            $('.send-invitation').prop('checked', false);
            $('#changeAllButton').val('Check All');
            $('.send-all-button').val('Select All Owners');
        }
    }

    function selectOwners(id) {
        if ($(`#select_owners_${id}`).val() == 'Select All Owners') {
            $(`input[name=send_invitation_${id}]`).prop('checked', true);
            $(`#select_owners_${id}`).val('Uncheck All Owners');
            $('#changeAllButton').val('Uncheck All');
        } else {
            $(`input[name=send_invitation_${id}]`).prop('checked', false);
            $(`#select_owners_${id}`).val('Select All Owners');
            $('#changeAllButton').val('Check All');
        }
    }

    function onSelect(id) {
        if ($(`input[name=send_invitation_${id}]`).is(':checked')) {
            $('#changeAllButton').val('Uncheck All');
            $(`#select_owners_${id}`).val('Uncheck All Owners');
        } else {
            $('#changeAllButton').val('Check All');
            $(`#select_owners_${id}`).val('Select All Owners');
        }
    }

    function showConfirmation() {
        let codesStr = '';
        selectedMasterClients = [];
        table.rows().every(function (rowIdx, tableLoop, rowLoop) {
            let id = this.data()[0];
            let code = this.data()[1];

            if ($(`input[name=send_invitation_${id}]`).is(':checked')) {
                selectedMasterClients.push({
                    id,
                    code,
                    owners: []
                });
                codesStr += `<li>${code}</li>`
            }
            $(`input:checkbox[name=send_invitation_${id}]:checked`).each(function () {
                selectedMasterClients[selectedMasterClients.length - 1].owners.push($(this).val());
                codesStr += `<ul class="ml-3 my-1" style="list-style-type:disc"><li>${$(this).val()}</li></ul>`;
            });
        });
        if (selectedMasterClients.length == 0) {
            toastr["warning"]('Please select at least one Master Client!');
        } else if (selectedMasterClients.length > 50) {
            toastr["warning"]('You can select up to 50 Master Clients at a time!');
        } else {
            $("#masterclientsConfirmation").html(codesStr);
            $("#invitation_modal").modal();
        }
    }

    function sendInvitation() {
        $('#sendInvitationButton').prop('disabled', true);
        $('#wait_alert').show();
        $.ajax({
            type: "POST",
            url: "./invitations/send",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({
                masterclients: selectedMasterClients,
            }),
            success: function (data) {
                if (data.success) {
                    $('#wait_alert').hide();
                    toastr.success('Invitations sent');
                    $('#invitation_modal').modal('hide');
                    window.setTimeout(function () {
                        document.location.reload();
                    }, 1000)
                } else {
                    $('#wait_alert').hide();
                    toastr["warning"]('Sorry, there was an error sending the invitations.');
                    $('#sendInvitationButton').prop('disabled', false);
                }
            }
        });
    }

</script>
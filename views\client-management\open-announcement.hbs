<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h2>
                            {{title}}
                        </h2>
                    </div>
                    <div class="card-body">
                        <form id="messageForm" novalidate>

                            <div class="row">
                                <div class="col-md-12">
                                    <label for="subject-control">Subject*</label>
                                    <input class="form-control" id="subject-control" name="subject"
                                           placeholder="Add subject..." value="{{message.subject}}">
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="email-subject-control">Email subject*</label>
                                    <textarea class="form-control" id="email-subject-control" name="emailSubject" rows="2"
                                              placeholder="Add message...">{{message.emailSubject}}</textarea>
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="content-control">Content*</label>
                                    <textarea class="form-control" id="content-control" name="content" rows="5"
                                              placeholder="Add message...">{{message.content}}</textarea>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12">
                                    <label>Send message now?*</label>
                                </div>
                                <div class="col-12">
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" class="custom-control-input" id="send-message-now-yes"
                                            {{#if isEdit}}
                                                {{#unless message.scheduleMessage }}checked {{/unless}}
                                                {{#if message.isPartialEdit}} disabled {{/if}}
                                            {{/if}}
                                               name="sendNow" required value="YES"/>
                                        <label class="custom-control-label" for="send-message-now-yes">Yes</label>
                                    </div>
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" class="custom-control-input" id="send-message-now-no"
                                               name="sendNow" required
                                            {{#if isEdit}}
                                                {{#if message.scheduleMessage }} checked {{/if}}
                                                {{#if message.isPartialEdit}} disabled {{/if}}
                                            {{/if}} value="NO"/>
                                        <label class="custom-control-label" for="send-message-now-no">No</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-2" id="scheduleDateRow"
                                {{#if isEdit}}
                                    {{#unless message.scheduleMessage  }} style="display: none" {{/unless}}

                                {{else}}
                                 style="display: none"
                                {{/if}}>
                                <div class="col-md-3">
                                    <label for="schedule-date">Select date:</label> <br>
                                    <input class='form-control' type='date' name='scheduleDate' id='schedule-date'
                                           min="{{currentDate}}"
                                        {{#if isEdit}}
                                            {{#if message.isPartialEdit}} disabled {{/if}}
                                        {{/if}}
                                           value="{{message.scheduledDate}}"/>
                                </div>

                                <div class="col-md-2">
                                    <label for="schedule-time">Select time:</label> <br>
                                    <select class="custom-select w-75" id="schedule-time" name="scheduleTime"
                                        {{#if isEdit}}
                                            {{#if message.isPartialEdit}} disabled {{/if}}
                                        {{/if}}
                                    >
                                        {{#each timeHours}}
                                            <option value="{{value}}">{{hour}}</option>
                                        {{/each}}

                                    </select>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12">
                                    <label>Send message to all Master Client Codes*</label>
                                </div>
                                <div class="col-12">
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" class="custom-control-input" id="send-to-all-yes"
                                            {{#if message.sendToAll }}  checked {{/if}}
                                            {{#if isEdit}}
                                                {{#if message.isPartialEdit}} disabled {{/if}}
                                            {{/if}}
                                               name="sendToAll" required value="YES"/>
                                        <label class="custom-control-label" for="send-to-all-yes">Yes</label>
                                    </div>
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" class="custom-control-input" id="send-to-all-no"
                                               name="sendToAll" required
                                            {{#if isEdit}}
                                                {{#unless message.sendToAll }}    checked {{/unless}}
                                                {{#if message.isPartialEdit}} disabled {{/if}}

                                            {{/if}}
                                               value="NO"/>
                                        <label class="custom-control-label" for="send-to-all-no">No</label>
                                    </div>
                                </div>
                            </div>
                            <br>
                            <div class="row" id="search-mcc-row"
                                {{#if isEdit}}
                                    {{#if message.sendToAll  }} style="display: none" {{/if}}
                                {{/if}}>
                                <div class="col-md-12" id="searchMccContainer">
                                    <label for="mccList">Select Masterclients*</label><br>
                                    <select id="mccList" class="mcc-search-list w-100" style="width: 100% " name="masterClientCodes[]"
                                        {{#if isEdit}}
                                            {{#if message.isPartialEdit}} disabled {{/if}}
                                        {{/if}}
                                            multiple="multiple">
                                    </select>

                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-6">
                                    <button type="button"
                                            class="btn btn-primary  width-xl"
                                            data-toggle="modal"
                                            id="message-files-btn"
                                            data-target="#upload-message-file-modal"
                                            data-message-id="{{ messageId }}"
                                            data-field="announcementFile"
                                        {{#if message.files}}
                                            style=" background-color: #0AC292 !important;
                                            border-color: #0AC292 !important; "

                                        {{else}}
                                            style="background-color: #0081b4; border-color: #0081b4 ; "
                                        {{/if}}
                                    >
                                        Modify Attachments
                                    </button>
                                </div>
                            </div>
                            <br>
                            <br>


                        </form>

                    </div>
                    <hr>
                    <!-- CARD BODY END -->
                    <!-- CARD FOOTER NAV -->
                    <div class="row mt-2">
                        <div class="d-flex col-md-12 justify-content-between">
                            <a href="/client-management/announcements"
                               class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>

                            <button type="submit" class="btn solid royal-blue" id="saveButton" form="messageForm"
                                    data-message-id="{{messageId}}">
                                Save
                            </button>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>client-management/modals/upload-message-file-modal}}
<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>

<script type="text/javascript">

    let isEdit = '{{isEdit}}';
    let messageID = '{{messageId}}';
    let messageTime = '{{message.scheduledTime}}';

    if (isEdit === 'true') {
        $('#schedule-time').val(Number(messageTime) + (moment().utcOffset() / 60));
    }

    $(document).ready(function () {
        let mccSelected = '{{message.masterClientCodes}}';

        //Set Dropdown with SearchBox via dropdownAdapter option (https://stackoverflow.com/questions/35799854/how-to-add-selectall-using-dropdownadapter-on-select2-v4)
        let Utils = $.fn.select2.amd.require('select2/utils');
        let Dropdown = $.fn.select2.amd.require('select2/dropdown');
        let DropdownSearch = $.fn.select2.amd.require('select2/dropdown/search');
        let AttachBody = $.fn.select2.amd.require('select2/dropdown/attachBody');

        let dropdownAdapter = Utils.Decorate(Utils.Decorate(Dropdown, DropdownSearch), AttachBody);

        $(".mcc-search-list").select2({

            placeholder: 'Search for a Master Client',
            minimumInputLength: 3,
            closeOnSelect: false,
            allowClear: true,
            multiple: true,
            dropdownAdapter: dropdownAdapter,
            ajax: {
                url: "/client-management/search-masterclients/list",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        searchFilter: params.term, // search term
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    return {
                        results: data.items,
                        pagination: {
                            more: (params.page * 30) < data.totalCount
                        }
                    };
                },
                cache: true
            },

            templateResult: formatMcc,
            templateSelection: formatMccSelection
        });

        if (mccSelected) {
            mccSelected = mccSelected.split(',');
            let data = [];
            for (let i = 0; i < mccSelected.length; i++) {
                // create the option and append to Select2
                let item = {
                    id: mccSelected[i],
                    text: mccSelected[i]
                };
                let option = new Option(item.text, item.id, true, true);

                data.push(item);
                $(".mcc-search-list").append(option).trigger('change');

            }
            // manually trigger the `select2:select` event
            $(".mcc-search-list").trigger({
                type: 'select2:select',
                params: {
                    data: data
                }
            });
        }

        if (isEdit === "false") {
            $("#search-mcc-row").hide();
        }
    });


    $('input[name="sendNow"]').on('change', function () {
        const value = $(this).val() === 'NO';
        if (value) {
            $('#scheduleDateRow').show(200);
        } else {
            $('#scheduleDateRow').hide(200);
        }
    });

    $('input[name="sendToAll"]').on('change', function () {
        const value = $(this).val() === 'NO';
        if (value) {
            $('#search-mcc-row').show(200);
        } else {
            $('#search-mcc-row').hide(200);
        }
    });


    function formatMcc(item) {
        if (item.loading) {
            return item.text;
        }

        let $container = $(
                "<div class='select2-result-mcc clearfix'>" +
                "<div class='select2-result-mcc__title font-weight-bold'></div>" +
                "<div class='select2-result-mcc__owners text-truncate'><i class='fa fa-li'></i> </div>" +
                "</div>"
        );

        $container.find(".select2-result-mcc__title").text(item.id + " - Total owners " + item.totalOwners);
        $container.find(".select2-result-mcc__owners").append(item.owners);

        return $container;
    }

    function formatMccSelection(item) {
        return item.id;
    }


    $("#messageForm").on('submit', function (event) {
        event.preventDefault();
        $(this).prop('disabled', true);

        $('input[required]:visible ').trigger('keyup');
        $('textarea[required]:visible ').trigger('keyup');
        $("input[type='radio']:visible").each(function () {
            const val = $('input:radio[name="' + this.name + '"]:checked').val();
            if (val === undefined) {
                $('input:radio[name="' + this.name + '"]').toggleClass("is-invalid", true);
            } else {
                $('input:radio[name="' + this.name + '"]').toggleClass("is-invalid", false);
            }
        });
        const scheduleDate = $('#schedule-date').val();
        const scheduleTime = $('#schedule-time').val();
        let scheduledAt = '';
        if (scheduleDate && scheduleTime && $('#send-message-now-no').is(':checked')) {
            scheduledAt = moment(`${scheduleDate} ${scheduleTime}`, 'YYYY-MM-DD HH').toDate().toISOString();
            scheduledAt = '&scheduledAt=' + scheduledAt;
        }

        if ($(".is-invalid:visible").length === 0) {
            $.ajax({
                type: isEdit === "true" ? "PUT" : "POST",
                url: window.location.href,
                data: $(this).serialize() + "&mid=" + messageID + scheduledAt,
                success: function (data) {
                    if (data.status === 200) {
                        Swal.fire('Success', 'Announcement saved successfully!', 'success').then(() => {
                            $("#saveButton").prop('disabled', false);
                            $("#messageForm").modal('hide');
                            location.href = '/client-management/announcements';
                        });
                    } else {
                        toastr["warning"](data.error ? 'Sorry, Error saving the information. ' + data.error :
                                'Sorry, Error saving the information. Try again later...');
                        $("#saveButton").removeAttr('disabled');
                    }
                },
                error: function (err) {
                    toastr["error"](err.responseJSON && err.responseJSON.error ? 'Sorry, Error saving the information. ' + err.responseJSON.error :
                            'Sorry, Error saving the information. Try again later...');
                    $("#saveButton").prop('disabled', false);
                }
            });
        } else {
            setTimeout(function () {
                $("#saveButton").prop('disabled', false);
            }, 1);
        }
    });
</script>

<div class="modal fade" id="companyChangeLogsModal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" style="text-transform: capitalize;">Company Change Logs</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body p-3 text-justify">
        <div>
          <div class="table-responsive">
            <table id="table-logs" class="table table-sm w-100">
              <thead>
                <tr>
                  <th>Created At</th>
                  <th>Modified By</th>
                  <th># Changes</th>
                  <th></th>
                </tr>
              </thead>

              <tbody id="tbodyLogs">

              </tbody>
            </table>
          </div>
        </div>
        <br>
        <div >
          <h5>Log Details</h5>
          <div >
            <table id="table-detail-logs" class="table  table-sm table-striped w-100" style="display: none;">
              <thead>
                <tr>
                  <th style="width: 33%;">Field</th>
                  <th style="width: 33%;">Previous Value</th>
                  <th style="width: 33%;">New Value</th>
                </tr>
              </thead>
              <tbody id="logDetails">
                <tr>
                  <td></td>
                  <td></td>
                  <td></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

      </div>
      <div class="modal-footer justify-content-between">
        <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
          Close
        </button>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript" src="/templates/companies/changelogslist.precompiled.js"></script>
<script type="text/javascript" src="/templates/companies/companylogdetails.precompiled.js"></script>
<script>
  let tbodyLogs = null;

  $('#companyChangeLogsModal').on('show.bs.modal', function (event) {
    $(".companyLogsBtn").prop('disabled', true);
    let button = $(event.relatedTarget); // Button that triggered the modal
    let companyId = button.data('company-id');

    $.ajax({
      type: 'GET',
      url: './search-companies/' + companyId + '/logs',
      timeout: 5000,
      success: (response) => {
        if (response.status !== 200) {
          Swal.fire('Error', 'There was an error getting the company information', 'error').then(() => {
            $('#companyChangeLogsModal').modal('hide');
          });
        }
        else {
          let template = Handlebars.templates.changelogslist;
          let d = {
            data: response.data,
          };


          let html = template(d);
          
          $('#tbodyLogs').html(html);
          tbodyLogs = $("#table-logs").DataTable({
            "pageLength": 3,
            "lengthChange": false,
            "ordering": false,
            info: false,
            destroy: true,
            language: {
              paginate: {
                previous: "<i class='mdi mdi-chevron-left'>",
                next: "<i class='mdi mdi-chevron-right'>"
              }
            },
            bFilter: false,
            lengthMenu: [[3, 5, 10], [3, 5, 10]],
            drawCallback: function () {
              $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
              $(".paginate_button.active a").css({ "background-color": "transparent", "color": "black", "font-weight": "bold" });
            }
          });


        }
      },
      error: (err) => {
        console.log(err);
        Swal.fire('Error', 'There was an error getting the company information', 'error').then(() => {
          $('#companyChangeLogsModal').modal('hide');
        });

      },
    });
  });


  $('#companyChangeLogsModal').on('hide.bs.modal', function () {
    $(".companyLogsBtn").prop('disabled', false);
    $("#logDetails").html('');
    $("#table-detail-logs").hide();
    tbodyLogs.destroy();
    $("#tbodyLogs").html('');

  });


  function getLogDetails(company, logId){

    $.ajax({
      type: 'GET',
      url: './search-companies/' + company + '/logs/' + logId,
      timeout: 5000,
      success: (response) => {
        if (response.status !== 200) {
          toastr["error"]('There was an error getting the details of log');
        }
        else {
          let template = Handlebars.templates.companylogdetails;
          let d = {
            logDetails: response.data,
          };
          let html = template(d);
          $('#logDetails').html(html);
          

          $("#table-detail-logs").show();
        }
      },
      error: (err) => {
        console.log(err);
        toastr["error"]('There was an error getting the details of log');
        $("#table-detail-logs").hide();
      },
    });
      
  }


</script>
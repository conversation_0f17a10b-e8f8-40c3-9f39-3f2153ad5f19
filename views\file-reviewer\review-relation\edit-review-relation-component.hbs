<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            {{ file.companyName }}
                            <span class="font-weight-bold" style="text-transform: capitalize;">
                                {{#ifEquals relation.type 'natural'}}
                                    : {{ relation.details.fullName }}
                                {{else}}
                                    : {{ relation.details.organizationName }}
                                {{/ifEquals}}</span>
                        </h3>
                    </div>
                    <div class="card-body">
                        <form id="reviewRelationForm" method="POST" autocomplete="off">
                            <div class="row">
                                <div class="col-12">
                                    <h4>
                                        Type:
                                        <span class="font-weight-bold" style="text-transform: capitalize;">{{
                                        relation.type
                                        }}</span>
                                    </h4>
                                </div>
                            </div>
                            <hr class="mt-2"/>
                            <div>
                                {{#if reviewGroup}}
                                    <h4 id="relationTitle">
                                        {{#ifEquals reviewGroup 'beneficial'}}
                                            Beneficial Ownership Details
                                        {{/ifEquals}}

                                        {{#ifEquals reviewGroup 'shareholder'}}
                                            Shareholder Details
                                        {{/ifEquals}}

                                        {{#ifEquals reviewGroup 'director'}}
                                            Director Details
                                        {{/ifEquals}}
                                    </h4>
                                {{/if}}
                                <!-- TITLE -->

                                {{#ifEquals relation.type "natural"}}
                                    {{>file-reviewer/review-relations/natural-form-component showPep="true" relation=relation
                                            relationInformation=relationInformation}}
                                {{/ifEquals}}

                                {{#ifEquals relation.type "corporate"}}
                                    {{>file-reviewer/review-relations/corporate-form-component relation=relation
                                            relationInformation=relationInformation}}
                                {{/ifEquals}}

                                {{#ifEquals relation.type "foundation"}}
                                    {{>file-reviewer/review-relations/foundation-form-component relation=relation
                                            relationInformation=relationInformation}}
                                {{/ifEquals}}

                                {{#ifEquals relation.type "trust"}}
                                    {{>file-reviewer/review-relations/trust-form-component relation=relation
                                            relationInformation=relationInformation}}
                                {{/ifEquals}}

                                {{#ifEquals relation.type "limited"}}
                                    {{>file-reviewer/review-relations/limited-partnership-form-component relation=relation
                                            relationInformation=relationInformation}}
                                {{/ifEquals}}

                                {{#ifEquals reviewGroup 'shareholder'}}
                                    <hr class="mt-2" />
                                    <div id="shareholderAdditionalForm">
                                        <!-- ADITIONAL REQUIRED INFO FOR SHAREHOLDERS  -->
                                        <h4>Additional Shareholder Details</h4>
                                        <div class="row mt-4">
                                            <div class="col-2">
                                                <label for="additional-percentage">Share Percentage</label>
                                            </div>
                                            <div class="col-4 input-group mb-3">
                                                <input
                                                        name="additional[percentage]"
                                                        id="additional-percentage"
                                                        type="number"
                                                        min="0"
                                                        max="100"
                                                        class="form-control"
                                                        value="{{relation.additional.percentage}}"
                                                />
                                                <div class="input-group-append">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="table-responsive pl-2" id="shareholderInfo" hidden>
                                            <table class="table table-striped">
                                                <thead>
                                                <tr>
                                                    <th style="width: 34%;">Files Required</th>
                                                    <th style="width: 13%;" class="text-center">Present</th>
                                                    <th style="width: 20%;" class="text-center">Action</th>
                                                    <th style="width: 33%;">Explanation</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr>
                                                    <td>Certified legible copy of passport/government issued ID</td>
                                                    <td class="text-center">
                                                        <div class="custom-control custom-checkbox">
                                                            <input
                                                                    type="checkbox"
                                                                    class="custom-control-input"
                                                                    name="additional-20-id-present-file"
                                                                    id="additional-20-id-present-file"
                                                            />
                                                            <label
                                                                    class="custom-control-label"
                                                                    for="additional-20-id-present-file"
                                                            ></label>
                                                        </div>
                                                    </td>
                                                    <td class="text-center">
                                                        <button
                                                                type="button"
                                                                class="btn solid royal-blue"
                                                                data-toggle="modal"
                                                                data-target="#upload-temp-modal"
                                                                data-id="{{ id }}"
                                                                data-field="Certified legible copy of passport/government issued ID"
                                                                data-row="20"
                                                        >
                                                            Upload
                                                        </button>
                                                    </td>
                                                    <td>
                                                      <textarea
                                                              class="form-control"
                                                              name="additional-20-id-explanation-file"
                                                              id="additional-20-id-explanation-file"
                                                              rows="1"
                                                      ></textarea>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Certified legible copy of Proof of Address</td>
                                                    <td class="text-center">
                                                        <div class="custom-control custom-checkbox">
                                                            <input
                                                                    type="checkbox"
                                                                    class="custom-control-input"
                                                                    name="additional-21-proofaddress-present-file"
                                                                    id="additional-21-proofaddress-present-file"
                                                            />
                                                            <label
                                                                    class="custom-control-label"
                                                                    for="additional-21-proofaddress-present-file"
                                                            ></label>
                                                        </div>
                                                    </td>
                                                    <td class="text-center">
                                                        <button
                                                                type="button"
                                                                class="btn solid royal-blue"
                                                                data-toggle="modal"
                                                                data-target="#upload-temp-modal"
                                                                data-id="{{ id }}"
                                                                data-field="Certified legible copy of Proof of Address"
                                                                data-row="21"
                                                        >
                                                            Upload
                                                        </button>
                                                    </td>
                                                    <td>
                                                      <textarea
                                                              class="form-control"
                                                              name="additional-21-proofaddress-explanation-file"
                                                              id="additional-21-proofaddress-explanation-file"
                                                              rows="1"
                                                      ></textarea>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <!-- SHAREHOLDER TABLE END -->
                                        <div class="row pt-3">
                                            <div class="col-12 d-flex justify-content-end">
                                                <div class="custom-control custom-checkbox">
                                                    <input
                                                            type="checkbox"
                                                            class="custom-control-input completeCheck"
                                                            name="additional[correct]"
                                                            id="correctAdditional"
                                                            {{#if relationInformation.additional.complete}} checked {{/if}}
                                                    />
                                                    <label class="custom-control-label" for="correctAdditional"
                                                    >Complete Information</label
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr class="mt-2"/>
                                {{/ifEquals}}
                            </div>

                        </form>
                    </div>
                    <div class="row mt-4 justify-content-between">
                        <div class="col-2">
                            <a
                                    href="/file-reviewer/open-file-review/{{ id }}/beneficial-owners"
                                    class="btn btn-secondary width-lg waves-effect waves-light"
                            >
                                Back
                            </a>
                        </div>
                        <div class="col-8 d-flex justify-content-end">

                            <div class="custom-control custom-checkbox pr-2 pt-2">
                                <input  type="checkbox" name="allCompleteCheck"
                                        class="custom-control-input completeCheck"
                                        id="allCompleteCheck"
                                        form="reviewRelationForm"
                                />
                                <label class="custom-control-label" for="allCompleteCheck">Mark all as complete</label>
                            </div>
                            {{#ifEquals relation.type "natural"}}
                                <button
                                        type="button"
                                        id="sendNewIdPalRequest"
                                        data-relation-id="{{relation._id}}"
                                        data-review-id="{{ id }}"
                                        data-toggle="modal"
                                        data-target="#startNewElectronicRequestModal"
                                        class="btn btn-xs mr-1 solid royal-blue"
                                    {{#ifEquals relation.electronicIdInfo.allowNewRequest false}} disabled {{/ifEquals}}
                                    {{#unless relation.electronicIdInfo.isElectronicId}} style="display: none" {{/unless}}
                                >
                                    Send Request ID
                                </button>
                            {{/ifEquals}}
                            <button
                                    id="submitRelationBtn"
                                    type="submit"
                                    form="reviewRelationForm"
                                    class="btn solid royal-blue px-4"
                                    data-id="{{ id }}" data-group="{{reviewGroup}}" data-type="{{ relation.type }}" data-index="{{ relation._id}}"
                            >
                                Save
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

{{>file-reviewer/modals/start-new-electronic-request-modal}}
{{>file-reviewer/upload-temp-modal}}

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/showrelationtypeform.precompiled.js"></script>
<script type="text/javascript" src="/templates/addpartnerfilerow.precompiled.js"></script>
<script>
    $('.toggle-section-check').change(function () {
        const name = "content-" + $(this).attr('id');
        $('#'+ name).toggle(200);
    });

    $('#world-check-confirmation').change(function () {
        $('#worldCheckBody').toggle(200);
    });

    $('.addCertificatePartnerRow').on('click', function () {
        const tableId = $(this).data("table-id");
        const reviewId = $(this).data("review-id");
        const relationId =  $(this).data("relation-id");
        const group =  $(this).data("group");
        const fileRow = $(this).data("row");
        const rowCount = $('#' + tableId +' tr').length -1;
        $.ajax({
            url: '/file-reviewer/get-template-files',
            type: 'GET',
            timeout: 1500,
            data: {
                fieldToSearch: 'relationFiles',
                group: group,
                fileType: 'detailsPartner',
                row: fileRow,
                newFile: true
            },
            success: function (response) {
                if (response.success){
                    let template = Handlebars.templates.addpartnerfilerow;
                    let d = {
                        file: response.data,
                        row: rowCount,
                        group: group,
                        reviewId: reviewId,
                        relationId: relationId
                    };
                    let html = template(d);
                    $("#"+tableId + " > table > tbody").append(html);
                }
                else {
                    Swal.fire('Error', 'There was an error adding a new row file', 'error');
                }
            },
            error: function () {
                Swal.fire('Error', 'There was an error adding a new row file', 'error');
            },
        });
    });
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body',
        boundary: 'window'
    });
    $('input[name="details[fullName]"]').on('input', function () {
        $('input[name="details[fullName]"]').removeClass('is-invalid');
    });

    $('input[name="details[organizationName]"]').on('input', function () {
        $('input[name="details[organizationName]"]').removeClass('is-invalid');
    });

    $('#reviewRelationForm').submit(function (event) {
        const submitBtn = $("#submitRelationBtn");

        submitBtn.prop('disabled', true);
        event.preventDefault();

        let reviewId = $('button[type=submit]').data('id');
        let group = $('button[type=submit]').data('group');
        let type = $('button[type=submit]').data('type');
        let editIndex = $('button[type=submit]').data('index');

        let name = type === "natural" ?  $('input[name="details[fullName]"]') : $('input[name="details[organizationName]"]');
        if (!name.val()) {
            name.addClass('is-invalid');
            name.focus();
            setTimeout(function(){submitBtn.prop('disabled', false); }, 0);
        } else {
            $.ajax({
                url: '/file-reviewer/open-file-review/' + reviewId + '/' + group +  '/edit-relation/' + type + '/' + editIndex,
                type: 'POST',
                timeout: 5000,
                data: $(this).serialize(),
                success: function () {
                    location.href = '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
                },
                error: function () {
                    Swal.fire('Error', 'There was an error updating the beneficial owner', 'error').then(() => {
                        location.href = '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
                    });
                },
            });
        }
    });

    $('#allCompleteCheck').on('change', function (){
        const checked = $(this).is(':checked');
        if(checked){
            $('.completeCheck').prop('checked', true);
        }
        else {
            $('.completeCheck').prop('checked', false);
        }
    });
</script>

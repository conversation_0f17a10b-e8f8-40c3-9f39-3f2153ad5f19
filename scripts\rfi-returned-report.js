const EntryModel = require("../models/entry").EntryModel;
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const xlsx = require('xlsx');
const moment = require('moment');

dotenv.config();


try {
  mongoose.connect(process.env.MONGODB, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    useFindAndModify: false,
  });
  const db = mongoose.connection;
  db.on('error', console.error.bind(console, 'MongoDB connection error:'));
  console.log("Connected to MongoDB");
  generateReport().then(() => console.log("FINISH report")).catch(e => console.log(e));

} catch (e) {
  console.log(e);
}


async function generateReport() {
  try {
    const exportFrom = new Date("2023-09-10T00:00:00.000Z");
    const data = await EntryModel.aggregate([
      {
        $match: {
          "requested_information.details": { $exists: true, $ne: [] },
          "client_returned_information.details": { $exists: true, $ne: [] },
        }
      },
      {
        $unwind: "$client_returned_information.details",
      },
      {
        $match: {
          "client_returned_information.details.returned_at": { $gte: exportFrom },
        }
      },
      {
        $project: {
          "_id": 1,
          "client_comment": "$client_returned_information.details.comment",
          "client_return_date": "$client_returned_information.details.returned_at",
          "request_id": "$client_returned_information.details.request_id",
          "response_id": "$client_returned_information.details._id",
          "files_returned": { $size: "$client_returned_information.details.files" },
          "company_id": "$company_data._id",
          "company_code": "$company_data.code",
          "company_name": "$company_data.name",
          "requested_information": 1
        }
      },
      {
        $unwind: "$requested_information.details",
      },
      {
        $match: {
          $expr: {
            $eq: ["$requested_information.details.id", "$request_id"]
          }
        }
      },
      {
        $project: {
          "_id": 1,
          "client_comment": 1,
          "client_return_date": 1,
          "response_id": 1,
          "company_id": 1,
          "company_code": 1,
          "company_name": 1,
          "request_id": 1,
          "files_returned": 1,
          "requested_information_comment": "$requested_information.details.comment"
        }
      }
    ]);

    const exportData = [[
      "Company ID",
      "Company Code",
      "Company Name",
      "Request ID",
      "Response ID",
      "Files Returned Count",
      "Client Return Date",
      "Client Returned Comment",
      "Requested Information by TT Comment"
    ]];

    for (const row of data) {
      exportData.push([
        row.company_id,
        row.company_code,
        row.company_name,
        row.request_id,
        row.response_id,
        row.files_returned,
        row.client_return_date,
        row.client_comment,
        row.requested_information_comment
      ]);
    }

    console.log("RFI found: " + data.length)

    const filename = 'rfi_returned_report_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(exportData);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'RFI report');
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true };
  } catch (e) {
    console.log(e);
    return { "success": false };
  }
}



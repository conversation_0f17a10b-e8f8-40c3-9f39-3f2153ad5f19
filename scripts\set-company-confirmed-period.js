const CompanyModel = require("../models/company").schema;
const FinancialReportModel = require('../models/financialreport')
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config();


const CONFIRMED_START_PERIOD = moment("2023-01-01").utc().startOf('day');
const CONFIRMED_END_PERIOD = moment("2023-12-31").utc().startOf('day');
const START_LIMIT_INCORPORATION_DATE = moment("2023-01-01").utc().startOf('day');
const END_LIMIT_INCORPORATION_DATE = moment("2024-01-01").utc().startOf('day').toDate();

const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    console.log("CONFIRMED_START_PERIOD: ", CONFIRMED_START_PERIOD);
    console.log("CONFIRMED_END_PERIOD: ", CONFIRMED_END_PERIOD);
    console.log("LIMIT_INCORPORATION_DATE: ", START_LIMIT_INCORPORATION_DATE);
    console.log("LIMIT_INCORPORATION_DATE2: ", END_LIMIT_INCORPORATION_DATE);
    const result = await setCompanyConfirmedPeriod();

    console.log('Script executed successfully', result);
  } catch (error) {
    console.error('Error in the script:', error);
  } finally {
    mongoose.disconnect();
  }
}


async function setCompanyConfirmedPeriod() {
  try {

    let updateLog = [['ID', 'Company Code', 'Update date', 'Action']];
    const companiesToUpdate = await CompanyModel.aggregate([
      {
        $match: {
          "accountingRecordsModule.active": true,
          "accountingRecordsModule.firstFinancialPeriodStart": {"$in": [null, undefined] },
          "incorporationdate": {"$lt": END_LIMIT_INCORPORATION_DATE}
        }
      },
      {
        $lookup: {
          from: "financialreportsbspls",
          localField: "code",  
          foreignField: "companyData.code",
          as: "financialReports"
        }
      }
    ]);


    console.log("companiesToUpdate  ", companiesToUpdate.length);
    if (companiesToUpdate.length > 0) {
      for (let i = 0; i < companiesToUpdate.length; i++) {
        console.log('processing ' + i + '  from ' + companiesToUpdate.length)

        const company = companiesToUpdate[i];

        try {
          let accountingRecords = company?.accountingRecordsModule ? company.accountingRecordsModule : {};
          const incorporationDate = moment(company.incorporationdate).utc();

          let newStartPeriod = null;
          let newEndPeriod = null;

          if (!incorporationDate){
            updateLog.push([company._id?.toString(), company.code, new Date(), 'ERROR: INVALID INCORPORATION DATE']);
            continue;
          }

          console.log("incorporation date ", incorporationDate);


          if (incorporationDate.isBefore(START_LIMIT_INCORPORATION_DATE)){
            newStartPeriod = CONFIRMED_START_PERIOD.clone();
            newEndPeriod = CONFIRMED_END_PERIOD.clone();
          }else{
            newStartPeriod = incorporationDate.clone();
            newEndPeriod = incorporationDate.clone().add('1', 'years').subtract('1', 'days');
          }

          let newDeadline = newEndPeriod.clone().add(9, 'months').format('YYYY-MM-DD');
          accountingRecords.firstFinancialPeriodStart = newStartPeriod.format('YYYY-MM-DD');
          accountingRecords.firstFinancialPeriodEnd = newEndPeriod.format('YYYY-MM-DD');
          accountingRecords.currentDeadline = newDeadline;

          const result = await CompanyModel.updateOne(
            { _id: company._id }, 
            { 
              $set: { 
                "accountingRecordsModule": accountingRecords,
                "modifiedBy": "auto updated by set-company-confirmed-period script"
              },
            },
            { new: true }
          );
          

          if (result.nModified > 0 ) {

            if(company.financialReports.length > 0){
              updateLog.push([company._id?.toString(), company.code, new Date(), 'ERROR: Company updated but a report was already created']);
              continue;
            }

            //delete company.financialReports;
            const newReport = await createNewReport(company, newStartPeriod.format('YYYY-MM-DD'), newEndPeriod.format('YYYY-MM-DD'));
            if (newReport !== null){
              updateLog.push([company._id?.toString(), company.code, new Date(), 'SUCCESS']);
            }else{
              updateLog.push([company._id?.toString(), company.code, new Date(), 'ERROR: Company updated but the report could not be created, please check data']);
            }
          } 
          else {
            updateLog.push([company._id?.toString(), company.code, new Date(), 'ERROR: NOT FOUND']);
          }
          
        } catch (error) {
          console.error('Error:', error.message);
          updateLog.push([company._id?.toString(), company.code, new Date(), 'ERROR UPDATING']);
        }
      }
    }


    // create entities bo
    console.log("companies updated ", updateLog.length - 1);

    const filename = 'set_confirmed_period_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'companies ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalRows": updateLog.length - 1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}

async function createNewReport(company, newStartPeriod, newEndPeriod){
  try{
    // create report
    let report = new FinancialReportModel({
      masterClientCode: company.masterclientcode,
      companyData: company,
      status: "SAVED",
      createdBy: "self created",
      createdAt: new Date(),
      currency: "USD",
      financialPeriod: {
        start: newStartPeriod,
        end: newEndPeriod,
      },
      files: {
        exemptEvidenceFiles: [],
        copyResolutionFiles: [],
        accountingRecordFiles: [],
        annualReturnFiles: []
      },
      version: "2.0"
    });
    await report.save();
    console.log("report ", report)
    return report._id
  }catch(e){
    console.log("error creating new report: ", e);
    return null
  }
}


runScript();
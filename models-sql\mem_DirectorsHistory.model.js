const moment = require('moment');

module.exports = (sequelize, Sequelize) => {
    const mem_DirectorsHistory = sequelize.define("mem_DirectorsHistory", {
         Id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            allowNull: false,
            autoIncrement: true
        },
        UniqueRelationID: {
            type: Sequelize.STRING(42),
            allowNull: false
        },
        ClientCode: {
            type: Sequelize.STRING(10),
            allowNull: true
        },
        ClientName: {
            type: Sequelize.STRING(356),
            allowNull: true
        },
        ClientUniqueNr: {
            type: Sequelize.INTEGER,
            allowNull: false
        },
        EntityCode: {
            type: Sequelize.STRING(10),
            allowNull: false
        },
        EntityName: {
            type: Sequelize.STRING(356),
            allowNull: true
        },
        EntityUniqueNr: {
            type: Sequelize.INTEGER,
            allowNull: false
        },
        EntityLegacyID: {
            type: Sequelize.STRING(30),
            allowNull: true
        },
        DirCode: {
            type: Sequelize.STRING(10),
            allowNull: true
        },
        DirName: {
            type: Sequelize.STRING(356),
            allowNull: true
        },
        DirUniqueNr: {
            type: Sequelize.INTEGER,
            allowNull: false
        },
        DirFileType: {
            type: Sequelize.STRING(50),
            allowNull: true
        },
        RelationType: {
            type: Sequelize.STRING(150),
            allowNull: true
        },
        DirOfficerType: {
            type: Sequelize.STRING(150),
            allowNull: true
        },
        DirFromDate: {
            type: Sequelize.DATE,
            allowNull: true,
            get() {
                const date = this.getDataValue('DirFromDate');
                return date ? moment.utc(date).format('YYYY-MM-DD') : null;
            }
        },
        DirToDate: {
            type: Sequelize.DATE,
            allowNull: true,
            get() {
                const date = this.getDataValue('DirToDate');
                return date ? moment.utc(date).format('YYYY-MM-DD') : null;
            }
        },
        DirStatus: {
            type: Sequelize.STRING(9),
            allowNull: false
        },
        LicenseeEntityCode: {
            type: Sequelize.STRING(10),
            allowNull: true
        },
        LicenseeEntityName: {
            type: Sequelize.STRING(356),
            allowNull: true
        },
        DirCapacityCode: {
            type: Sequelize.STRING(20),
            allowNull: true
        },
        DirCapacity: {
            type: Sequelize.STRING(255),
            allowNull: true
        },
        DirID: {
            type: Sequelize.STRING(100),
            allowNull: true
        },
        PreviousUniqueRelationId: {
            type: Sequelize.STRING(100),
            allowNull: false
        },
        UpdateRequestDate: {
            type: Sequelize.DATE,
            allowNull: true
        },
        ConfirmedDate: {
            type: Sequelize.DATE,
            allowNull: true
        },
        Status: {
            type: Sequelize.STRING(50),
            allowNull: true
        },
        UserEmail: {
            type: Sequelize.STRING(255),
            allowNull: true
        },
        TypeOfUpdateRequest: {
            type: Sequelize.STRING(255),
            allowNull: true
        },
        UpdateRequestComments: {
            type: Sequelize.STRING(2500),
            allowNull: true
        },
        isLicensed: { type: Sequelize.BOOLEAN, allowNull: true },
        CreatedAt: {
            type: Sequelize.DATE,
            allowNull: true
        },
        UpdatedAt: {
            type: Sequelize.DATE,
            allowNull: true
        }
    }, {
        sequelize,
        tableName: 'mem_DirectorsHistory',
        schema: 'dbo',
        timestamps: true,
        createdAt: 'CreatedAt',
        updatedAt: 'UpdatedAt'
    });

    mem_DirectorsHistory.associate = (models) => {
        mem_DirectorsHistory.belongsTo(models.mem_MemberProfilesHistory, {
            foreignKey: 'DirUniqueNr',
            targetKey: 'MFUniqueNr',
            as: 'directorProfileHistory'
        });
    };


    return mem_DirectorsHistory;
};

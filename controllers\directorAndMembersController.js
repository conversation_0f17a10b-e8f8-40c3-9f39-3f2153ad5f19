const httpConstants = require('http2').constants;
const moment = require("moment");
const excel = require("node-excel-export");
const sqlDb = require('../models-sql');
const VpDirectorInfoHistoryJoinAll = sqlDb.VpDirectorInfoHistoryJoinAll;
const mem_Directors = sqlDb.mem_Directors;
const mem_Shareholders = sqlDb.mem_Shareholders;
const mem_DirectorsHistory = sqlDb.mem_DirectorsHistory;
const mem_ShareholdersHistory = sqlDb.mem_ShareholdersHistory;
const { Op } = require('sequelize');
const _ = require('lodash');
const { getContainerClient } = require('../utils/azureStorage');
const { DIRECTOR_REQUIRED_FIELDS, MEMBER_REQUIRED_FIELDS } = require('../utils/constants');
const CompanyModel = require('../models/company').schema;

exports.getDashboard = async function (req, res, next) {
  try {
    const showImportDataModule = req.session.authentication.isDirBoImportManager;

    res.render('director-and-members/index',
      {
        user: req.session.user,
        title: "Company Information Dashboard",
        authentication: req.session.authentication,
        hasProductionOfficeGroups: req.session.productionOfficeGroups?.length > 0,
        showImportDataModule: showImportDataModule
      });
  } catch (e) {
    console.log(e);
    next(e);
  }
};

exports.getSearch = async function (req, res, next) {
  try {
    const baseFilter = {
      filter_name: '',
      filter_director_name: '',
      filter_masterclient: '',
      filter_entity_number: '',
      filter_referral: '',
      filter_position: '',
      filter_production: '',
      filter_confirmed_range_start: '',
      filter_confirmed_range_end: ''
    }
    const filters = _.isEmpty(req.body) ? baseFilter : req.body;

    const result = await searchData(req, next)
    const vpDirectors = _.isEqual(baseFilter, req.body) ? [] : result[0];
    const usedFilter = result[1];

    res.render('director-and-members/search', {
      title: 'Director/Member/Partner/BO Search',
      vpDirectors: vpDirectors,
      user: req.user,
      filters: filters,
      hasFilter: usedFilter,
      productionOfficeOptions: req.session.productionOfficeGroups,
      messages: req.session.messages
    });

  } catch (e) {
    console.log(e);
    const err = new Error('Internal server error');
    err.status = 500;
    return next(err);
  }
};

exports.getImportDataHistoryView = async function (req, res, next) {
  try {
    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_VPDIRECTORINFO_ARCHIVE,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCOUNT,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCESS_KEY
    );

    const response = containerClient.listBlobsFlat();
    const importFiles = [];

    for await (const blob of response) {
      let filename = "";
      if (blob.name.includes("/")) {
        const splitName = blob.name.split("/");
        filename = splitName[splitName.length - 1]
      } else {
        filename = blob.name;
      }

      importFiles.push({
        name: blob.name,
        filename: filename,
        createdAt: moment(blob.properties.createdOn).utc().format('YYYY-MM-DD HH:mm'),
        updatedAt: moment(blob.properties.lastModified).utc().format('YYYY-MM-DD HH:mm')
      });
    }

    importFiles.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.render("director-and-members/import-data-history", { title: "Import Company Information Data", importFiles });

  } catch (e) {
    console.log(e);
    next(e);
  }
};

exports.downloadArchiveVPDirectorFile = async function (req, res, next) {
  try {
    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_VPDIRECTORINFO_ARCHIVE,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCOUNT,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCESS_KEY
    );

    const blobClient = containerClient.getBlobClient(req.query.pathname);

    if (req.query.pathname.length > 0) {
      blobClient.download().then((downloadResponse) => {
        if (downloadResponse.contentLength === 0) {
          return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
        }
        res.set('Content-Disposition', `attachment; filename="${req.query.pathname}"`);
        downloadResponse.readableStreamBody.pipe(res);
      }).catch((error) => {
        console.error("Error downloading blob:", error);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).send({ "message": "Error downloading file" });
      });
    } else {
      const err = new Error('File not found');
      err.status = 404;
      return next(err);
    }
  } catch (e) {
    console.log("error ", e);
    const err = new Error('Internal Server Error');
    err.status = 502;
    return next(err);
  }
};

exports.exportSearchXls = async function (req, res, next) {
  try {

    let result = await searchData(req, next);
    let entries = result[0];

    const styles = {
      headerTable: {
        fill: {
          fgColor: {
            rgb: "ffffff",
          },
        },
        border: {
          top: { style: "thin", color: "000000" },
          bottom: { style: "thin", color: "000000" },
          left: { style: "thin", color: "000000" },
          right: { style: "thin", color: "000000" },
        },
        font: {
          color: {
            rgb: "000000",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
    };

    const specification = {
      Entity_Name: {
        displayName: "Entity Name",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Entity_Number: {
        displayName: "VP Entity Number",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Company_Number: {
        displayName: "Entity Portal Code",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Master_Client_Code: {
        displayName: "Master Client Code",
        headerStyle: styles.headerTable,
        width: 160,
      },
      Referral_Office: {
        displayName: "Referral Office",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Production_Office: {
        displayName: "Production Office",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Director_and_BO: {
        displayName: "Director/BO Name",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Position: {
        displayName: "Position",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Status: {
        displayName: "Status",
        headerStyle: styles.headerTable,
        width: 120,
      },
      SubStatus: {
        displayName: "Specifics",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Request_Update_Date: {
        displayName: "Request Update Date",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Confirmed_Update_Date: {
        displayName: "Confirmed Date",
        headerStyle: styles.headerTable,
        width: 120,
      },
    };

    const dataset = [];
    for (let i = 0; i < entries.length; i++) {
      dataset.push({
        Entity_Name: entries[i].EntityName,
        Entity_Number: entries[i].EntityCode,
        Master_Client_Code: entries[i].MasterClientCode,
        Company_Number: entries[i].CompanyNumber,
        Referral_Office: entries[i].ReferralOffice,
        Director_and_BO: entries[i].Name ?? '',
        Position: entries[i].RelationType ? entries[i].RelationType === 'Owner/Controller' ? 'BO' : entries[i].RelationType : '',
        Production_Office: entries[i].ProductionOffice ?? '',
        Status: entries[i].Status === 'VP DATA RECEIVED' ? 'SUBSEQUENT' : entries[i].Status,
        SubStatus: entries[i].Status === 'CONFIRMED' || entries[i].Status === 'PENDING UPDATE REQUEST' ? '' : (entries[i].MissingInfo === 1 ? 'MISSING INFORMATION' : ''),
        Request_Update_Date: entries[i].UpdateRequestDate
          ? moment(entries[i].UpdateRequestDate).format("YYYY-MM-DD")
          : "",
        Confirmed_Update_Date: (entries[i].Status === 'VP DATA RECEIVED' || entries[i].Status === 'REFRESHED' ? '' : (entries[i].ConfirmedDate ? moment(entries[i].ConfirmedDate).format("YYYY-MM-DD") : ""))
      });
    }

    const report = excel.buildExport([
      {
        name: "Directors And Beneficial Owners",
        specification: specification,
        data: dataset,
      },
    ]);

    res.attachment("Search Report.xlsx");
    res.send(report);
  } catch (error) {
    console.log(error);
    next(error);
  }
};

async function searchData(req, next) {
  try {
    // Validate that at least one filter is provided to prevent massive queries
    const hasRequiredFilter = req.body.filter_name ||
      req.body.filter_director_name ||
      req.body.filter_masterclient ||
      req.body.filter_entity_number ||
      req.body.filter_referral ||
      req.body.filter_position ||
      req.body.status ||
      req.body.subStatus;

    if (!hasRequiredFilter) {
      return [[], false, {}];
    }

    const productionOfficeGroups = req.session.productionOfficeGroups;
    let Status = [];
    let SubStatus = [];

    // Parse status and substatus filters
    if (req.body.subStatus && req.body.subStatus !== '' && req.body.subStatus.length) {
      SubStatus = Array.isArray(req.body.subStatus) ? req.body.subStatus : [req.body.subStatus];
    }

    if (req.body.status && req.body.status !== '' && req.body.status.length) {
      Status = Array.isArray(req.body.status) ? req.body.status : [req.body.status];
    }

    // Build MongoDB company filter
    const companyFilter = { partitionkey: "company", isDeleted: { $ne: true } };

    if (req.body.filter_name) {
      companyFilter.name = { $regex: req.body.filter_name, $options: "i" };
    }

    if (req.body.filter_masterclient && req.body.filter_masterclient.length >= 2) {
      companyFilter.masterclientcode = { $regex: req.body.filter_masterclient, $options: "i" };
    }

    if (req.body.filter_entity_number && req.body.filter_entity_number.length > 2) {
      companyFilter.code = { $regex: req.body.filter_entity_number, $options: "i" };
    }

    if (req.body.filter_referral && req.body.filter_referral !== '') {
      companyFilter.referral_office = { $regex: req.body.filter_referral, $options: "i" };
    }

    // Get companies from MongoDB
    const companies = await CompanyModel.find(companyFilter, {
      _id: 1,
      name: 1,
      code: 1,
      masterclientcode: 1,
      referral_office: 1,
      productionOffice: 1
    }).limit(1000); // Limit to prevent massive queries

    if (companies.length === 0) {
      return [[], true, companyFilter];
    }

    const companyCodes = companies.map(c => c.code);
    const results = [];

    // Search directors and shareholders for each company
    for (const company of companies) {
      const companyResults = await searchDirectorsAndShareholders(company, req.body, Status, SubStatus, productionOfficeGroups);
      results.push(...companyResults);
    }

    return [results, true, companyFilter];

  } catch (error) {
    console.log(error);
    next(error);
  }
}

async function searchDirectorsAndShareholders(company, filters, Status, SubStatus, productionOfficeGroups) {
  const results = [];

  try {
    // Search directors
    const directorFilter = { EntityLegacyID: company.code };

    if (filters.filter_director_name) {
      directorFilter.DirName = { [Op.like]: `%${filters.filter_director_name}%` };
    }

    if (filters.filter_position && filters.filter_position === 'Directors') {
      // Only search directors when position filter is "Directors"
    } else if (filters.filter_position && filters.filter_position === 'Members') {
      // Skip directors when position filter is "Members"
      directorFilter.DirUniqueNr = null; // This will return no results
    }

    const directors = await mem_Directors.findAll({
      where: directorFilter
    });

    // Process directors
    for (const director of directors) {
      const result = await processDirectorRecord(director, company, Status, SubStatus, productionOfficeGroups, filters);
      if (result) {
        results.push(result);
      }
    }

    // Search shareholders (grouped by SHUniqueNr)
    const shareholderFilter = { EntityLegacyID: company.code };

    if (filters.filter_director_name) {
      shareholderFilter.SHName = { [Op.like]: `%${filters.filter_director_name}%` };
    }

    if (filters.filter_position && filters.filter_position === 'Members') {
      // Only search shareholders when position filter is "Members"
    } else if (filters.filter_position && filters.filter_position === 'Directors') {
      // Skip shareholders when position filter is "Directors"
      shareholderFilter.SHUniqueNr = null; // This will return no results
    }

    const shareholders = await mem_Shareholders.findAll({
      where: shareholderFilter
    });

    // Group shareholders by SHUniqueNr and process
    const groupedShareholders = _.groupBy(shareholders, 'SHUniqueNr');

    for (const [shUniqueNr, shareholderGroup] of Object.entries(groupedShareholders)) {
      if (shUniqueNr !== 'null' && shUniqueNr !== 'undefined') {
        const result = await processShareholderGroup(shareholderGroup, company, Status, SubStatus, productionOfficeGroups, filters);
        if (result) {
          results.push(result);
        }
      }
    }

    // If no directors or shareholders found, return company info only when appropriate
    if (results.length === 0 && shouldReturnCompanyOnly(SubStatus)) {
      const companyOnlyResult = createCompanyOnlyRecord(company, productionOfficeGroups);
      if (companyOnlyResult) {
        results.push(companyOnlyResult);
      }
    }

  } catch (error) {
    console.log('Error in searchDirectorsAndShareholders:', error);
  }

  return results;
}

async function processDirectorRecord(director, company, Status, SubStatus, productionOfficeGroups, filters) {
  try {
    // Get history records for this director
    const historyRecords = await mem_DirectorsHistory.findAll({
      where: { UniqueRelationID: director.UniqueRelationID }
    });

    // Determine status based on history
    const { status, confirmedDate, updateRequestDate } = determineStatusFromHistory(historyRecords);

    // Apply status filters
    if (Status.length > 0 && !Status.includes(status)) {
      return null;
    }

    // Apply production office filter
    if (!checkProductionOfficeAccess(company.productionOffice, productionOfficeGroups, filters.filter_production)) {
      return null;
    }

    // Apply date range filters
    if (!checkDateRangeFilter(confirmedDate, filters)) {
      return null;
    }

    // Apply substatus filters
    if (!checkSubStatusFilter(SubStatus, director, status)) {
      return null;
    }

    // Create result in VpDirectorInfoHistoryJoinAll format
    return {
      CompanyNumber: company.code,
      ReferralOffice: company.referral_office,
      MasterClientCode: company.masterclientcode,
      EntityCode: company.code,
      EntityName: company.name,
      Name: director.DirName,
      Code: director.DirCode,
      RelationType: 'Director',
      FormerName: null,
      FileType: director.DirFileType,
      UniqueRelationId: director.UniqueRelationID,
      OfficerType: director.DirOfficerType,
      FromDate: director.DirFromDate,
      ToDate: director.DirToDate,
      ServiceAddress: null,
      ResidentialOrRegisteredAddress: null,
      DateOfBirthOrIncorp: null,
      PlaceOfBirthOrIncorp: null,
      Nationality: null,
      Country: null,
      ProductionOffice: company.productionOffice,
      CorporateRegistrationNo: null,
      UpdateRequestDate: updateRequestDate,
      ConfirmedDate: confirmedDate,
      VPDataReceived: null,
      Status: status,
      SubStatus: null,
      UserEmail: null,
      TypeOfUpdateRequest: null,
      UpdateRequestComments: null,
      TIN: null,
      NameOfRegulator: null,
      StockExchange: null,
      StockCode: null,
      JurisdictionOfRegulationOrSovereignState: null,
      BoDirIncorporationNumber: null,
      DirectorIsAlternateToId: null,
      DirectorIsAlternateToName: null,
      MissingInfo: 0
    };

  } catch (error) {
    console.log('Error processing director record:', error);
    return null;
  }
}

function determineStatusFromHistory(historyRecords) {
  let status = 'NOT CONFIRMED';
  let confirmedDate = null;
  let updateRequestDate = null;

  if (historyRecords && historyRecords.length > 0) {
    // Sort by CreatedAt descending to get the latest record
    const sortedHistory = historyRecords.sort((a, b) => new Date(b.CreatedAt) - new Date(a.CreatedAt));
    const latestHistory = sortedHistory[0];

    status = latestHistory.Status || 'NOT CONFIRMED';
    confirmedDate = latestHistory.ConfirmedDate;
    updateRequestDate = latestHistory.UpdateRequestDate;

    // Check if any history record has confirmed status
    const confirmedRecord = historyRecords.find(h => h.Status === 'CONFIRMED');
    if (confirmedRecord) {
      status = 'CONFIRMED';
      confirmedDate = confirmedRecord.ConfirmedDate;
    }
  }

  return { status, confirmedDate, updateRequestDate };
}

function checkProductionOfficeAccess(companyProductionOffice, productionOfficeGroups, filterProduction) {
  if (filterProduction && filterProduction.length > 0 && filterProduction !== '' && filterProduction !== 'null') {
    // Specific production office filter
    if (companyProductionOffice !== filterProduction) {
      // Check if it's in allowed groups or not in restricted list
      if (!productionOfficeGroups.includes(companyProductionOffice) &&
        ['THKO', 'TBVI', 'TCYP', 'TPANVG'].includes(companyProductionOffice)) {
        return false;
      }
    }
  } else {
    // Default case - check access permissions
    if (companyProductionOffice &&
      !productionOfficeGroups.includes(companyProductionOffice) &&
      ['THKO', 'TBVI', 'TCYP', 'TPANVG'].includes(companyProductionOffice)) {
      return false;
    }
  }
  return true;
}

function checkDateRangeFilter(confirmedDate, filters) {
  if (filters.filter_confirmed_range_start && filters.filter_confirmed_range_end &&
    filters.filter_confirmed_range_start !== '' && filters.filter_confirmed_range_end !== '') {
    if (!confirmedDate) return false;
    const date = new Date(confirmedDate);
    const startDate = new Date(filters.filter_confirmed_range_start);
    const endDate = new Date(filters.filter_confirmed_range_end);
    return date >= startDate && date <= endDate;
  } else if (filters.filter_confirmed_range_start && filters.filter_confirmed_range_start !== '') {
    if (!confirmedDate) return false;
    const date = new Date(confirmedDate);
    const startDate = new Date(filters.filter_confirmed_range_start);
    return date >= startDate;
  } else if (filters.filter_confirmed_range_end && filters.filter_confirmed_range_end !== '') {
    if (!confirmedDate) return false;
    const date = new Date(confirmedDate);
    const endDate = new Date(filters.filter_confirmed_range_end);
    return date < endDate;
  }
  return true;
}

function checkSubStatusFilter(SubStatus, record, status) {
  if (SubStatus.length === 0) return true;

  if (SubStatus.includes('NO DIRECTOR')) {
    return false; // This record has a director/member, so it doesn't match "NO DIRECTOR"
  }

  if (SubStatus.includes('HAS DIRECTOR')) {
    return true; // This record has a director/member
  }

  if (SubStatus.includes('MISSING INFORMATION')) {
    return status !== 'CONFIRMED' && status !== 'PENDING UPDATE REQUEST';
  }

  return true;
}

async function processShareholderGroup(shareholderGroup, company, Status, SubStatus, productionOfficeGroups, filters) {
  try {
    // Take the first shareholder as representative (since they're grouped by SHUniqueNr)
    const representative = shareholderGroup[0];

    // Get history records for this shareholder group
    const historyRecords = await mem_ShareholdersHistory.findAll({
      where: { UniqueRelationID: representative.UniqueRelationID }
    });

    // Determine status based on history
    const { status, confirmedDate, updateRequestDate } = determineStatusFromHistory(historyRecords);

    // Apply status filters
    if (Status.length > 0 && !Status.includes(status)) {
      return null;
    }

    // Apply production office filter
    if (!checkProductionOfficeAccess(company.productionOffice, productionOfficeGroups, filters.filter_production)) {
      return null;
    }

    // Apply date range filters
    if (!checkDateRangeFilter(confirmedDate, filters)) {
      return null;
    }

    // Apply substatus filters
    if (!checkSubStatusFilter(SubStatus, representative, status)) {
      return null;
    }

    // Create result in VpDirectorInfoHistoryJoinAll format
    return {
      CompanyNumber: company.code,
      ReferralOffice: company.referral_office,
      MasterClientCode: company.masterclientcode,
      EntityCode: company.code,
      EntityName: company.name,
      Name: representative.SHName,
      Code: representative.SHCode,
      RelationType: 'Member',
      FormerName: null,
      FileType: representative.SHFileType,
      UniqueRelationId: representative.UniqueRelationID,
      OfficerType: representative.MemberType,
      FromDate: representative.MemberDateStart,
      ToDate: representative.MemberDateEnd,
      ServiceAddress: null,
      ResidentialOrRegisteredAddress: representative.SHAddress,
      DateOfBirthOrIncorp: null,
      PlaceOfBirthOrIncorp: null,
      Nationality: null,
      Country: null,
      ProductionOffice: company.productionOffice,
      CorporateRegistrationNo: null,
      UpdateRequestDate: updateRequestDate,
      ConfirmedDate: confirmedDate,
      VPDataReceived: null,
      Status: status,
      SubStatus: null,
      UserEmail: null,
      TypeOfUpdateRequest: null,
      UpdateRequestComments: null,
      TIN: null,
      NameOfRegulator: null,
      StockExchange: null,
      StockCode: null,
      JurisdictionOfRegulationOrSovereignState: null,
      BoDirIncorporationNumber: null,
      DirectorIsAlternateToId: null,
      DirectorIsAlternateToName: null,
      MissingInfo: 0
    };

  } catch (error) {
    console.log('Error processing shareholder group:', error);
    return null;
  }
}

function shouldReturnCompanyOnly(SubStatus) {
  return SubStatus.includes('NO DIRECTOR');
}

function createCompanyOnlyRecord(company, productionOfficeGroups) {
  // Check production office access
  if (!checkProductionOfficeAccess(company.productionOffice, productionOfficeGroups, null)) {
    return null;
  }

  return {
    CompanyNumber: company.code,
    ReferralOffice: company.referral_office,
    MasterClientCode: company.masterclientcode,
    EntityCode: company.code,
    EntityName: company.name,
    Name: null,
    Code: null,
    RelationType: null,
    FormerName: null,
    FileType: null,
    UniqueRelationId: null,
    OfficerType: null,
    FromDate: null,
    ToDate: null,
    ServiceAddress: null,
    ResidentialOrRegisteredAddress: null,
    DateOfBirthOrIncorp: null,
    PlaceOfBirthOrIncorp: null,
    Nationality: null,
    Country: null,
    ProductionOffice: company.productionOffice,
    CorporateRegistrationNo: null,
    UpdateRequestDate: null,
    ConfirmedDate: null,
    VPDataReceived: null,
    Status: 'NO DIRECTOR',
    SubStatus: null,
    UserEmail: null,
    TypeOfUpdateRequest: null,
    UpdateRequestComments: null,
    TIN: null,
    NameOfRegulator: null,
    StockExchange: null,
    StockCode: null,
    JurisdictionOfRegulationOrSovereignState: null,
    BoDirIncorporationNumber: null,
    DirectorIsAlternateToId: null,
    DirectorIsAlternateToName: null,
    MissingInfo: 0
  };
}

function getListFieldsToValidate(isMember, filetype, officerType) {
  const fieldsByType = isMember ? MEMBER_REQUIRED_FIELDS : DIRECTOR_REQUIRED_FIELDS;
  let fieldsToValidate = [];

  if (isMember) {
    fieldsToValidate = fieldsByType[officerType] || [];

  } else {
    // Check if the filetype is individual 
    const isIndividual = filetype && filetype.toLowerCase() === "individual";
    fieldsToValidate = isIndividual ? fieldsByType["individual"] : fieldsByType["corporate"];
  }

  return fieldsToValidate
}

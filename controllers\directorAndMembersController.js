const httpConstants = require('http2').constants;
const moment = require("moment");
const excel = require("node-excel-export");
const sqlDb = require('../models-sql');
const VpDirectorInfoHistoryJoinAll = sqlDb.VpDirectorInfoHistoryJoinAll;
const { Op } = require('sequelize');
const _ = require('lodash');
const { getContainerClient } = require('../utils/azureStorage');

exports.getDashboard = async function (req, res, next) {
  try {
    const showImportDataModule = req.session.authentication.isDirBoImportManager;

    res.render('director-and-members/index',
      {
        user: req.session.user,
        title: "Company Information Dashboard",
        authentication: req.session.authentication,
        hasProductionOfficeGroups: req.session.productionOfficeGroups?.length > 0,
        showImportDataModule: showImportDataModule
      });
  } catch (e) {
    console.log(e);
    next(e);
  }
};

exports.getSearch = async function (req, res, next) {
  try {
    const baseFilter = {
      filter_name: '',
      filter_director_name: '',
      filter_masterclient: '',
      filter_entity_number: '',
      filter_referral: '',
      filter_position: '',
      filter_production: '',
      filter_confirmed_range_start: '',
      filter_confirmed_range_end: ''
    }
    const filters = _.isEmpty(req.body) ? baseFilter : req.body;

    const result = await searchData(req, next)
    const vpDirectors = _.isEqual(baseFilter, req.body) ? [] : result[0];
    const usedFilter = result[1];

    res.render('director-and-members/search', {
      title: 'Director/Member/Partner/BO Search',
      vpDirectors: vpDirectors,
      user: req.user,
      filters: filters,
      hasFilter: usedFilter,
      productionOfficeOptions: req.session.productionOfficeGroups,
      messages: req.session.messages
    });

  } catch (e) {
    console.log(e);
    const err = new Error('Internal server error');
    err.status = 500;
    return next(err);
  }
};

exports.getImportDataHistoryView = async function (req, res, next) {
  try {
    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_VPDIRECTORINFO_ARCHIVE,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCOUNT,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCESS_KEY
    );

    const response = containerClient.listBlobsFlat();
    const importFiles = [];

    for await (const blob of response) {
      let filename = "";
      if (blob.name.includes("/")) {
        const splitName = blob.name.split("/");
        filename = splitName[splitName.length - 1]
      } else {
        filename = blob.name;
      }

      importFiles.push({
        name: blob.name,
        filename: filename,
        createdAt: moment(blob.properties.createdOn).utc().format('YYYY-MM-DD HH:mm'),
        updatedAt: moment(blob.properties.lastModified).utc().format('YYYY-MM-DD HH:mm')
      });
    }

    importFiles.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.render("director-and-members/import-data-history", { title: "Import Company Information Data", importFiles });

  } catch (e) {
    console.log(e);
    next(e);
  }
};

exports.downloadArchiveVPDirectorFile = async function (req, res, next) {
  try {
    const containerClient = getContainerClient(
      process.env.AZURE_STORAGE_CONTAINER_VPDIRECTORINFO_ARCHIVE,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCOUNT,
      process.env.AZURE_STORAGE_VPDIRECTOR_ACCESS_KEY
    );

    const blobClient = containerClient.getBlobClient(req.query.pathname);

    if (req.query.pathname.length > 0) {
      blobClient.download().then((downloadResponse) => {
        if (downloadResponse.contentLength === 0) {
          return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
        }
        res.set('Content-Disposition', `attachment; filename="${req.query.pathname}"`);
        downloadResponse.readableStreamBody.pipe(res);
      }).catch((error) => {
        console.error("Error downloading blob:", error);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).send({ "message": "Error downloading file" });
      });
    } else {
      const err = new Error('File not found');
      err.status = 404;
      return next(err);
    }
  } catch (e) {
    console.log("error ", e);
    const err = new Error('Internal Server Error');
    err.status = 502;
    return next(err);
  }
};

exports.exportSearchXls = async function (req, res, next) {
  try {

    let result = await searchData(req, next);
    let entries = result[0];

    const styles = {
      headerTable: {
        fill: {
          fgColor: {
            rgb: "ffffff",
          },
        },
        border: {
          top: { style: "thin", color: "000000" },
          bottom: { style: "thin", color: "000000" },
          left: { style: "thin", color: "000000" },
          right: { style: "thin", color: "000000" },
        },
        font: {
          color: {
            rgb: "000000",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
    };

    const specification = {
      Entity_Name: {
        displayName: "Entity Name",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Entity_Number: {
        displayName: "VP Entity Number",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Company_Number: {
        displayName: "Entity Portal Code",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Master_Client_Code: {
        displayName: "Master Client Code",
        headerStyle: styles.headerTable,
        width: 160,
      },
      Referral_Office: {
        displayName: "Referral Office",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Production_Office: {
        displayName: "Production Office",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Director_and_BO: {
        displayName: "Director/BO Name",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Position: {
        displayName: "Position",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Status: {
        displayName: "Status",
        headerStyle: styles.headerTable,
        width: 120,
      },
      SubStatus: {
        displayName: "Specifics",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Request_Update_Date: {
        displayName: "Request Update Date",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Confirmed_Update_Date: {
        displayName: "Confirmed Date",
        headerStyle: styles.headerTable,
        width: 120,
      },
    };

    const dataset = [];
    for (let i = 0; i < entries.length; i++) {
      dataset.push({
        Entity_Name: entries[i].EntityName,
        Entity_Number: entries[i].EntityCode,
        Master_Client_Code: entries[i].MasterClientCode,
        Company_Number: entries[i].CompanyNumber,
        Referral_Office: entries[i].ReferralOffice,
        Director_and_BO: entries[i].Name ?? '',
        Position: entries[i].RelationType ? entries[i].RelationType === 'Owner/Controller' ? 'BO' : entries[i].RelationType : '',
        Production_Office: entries[i].ProductionOffice ?? '',
        Status: entries[i].Status === 'VP DATA RECEIVED' ? 'SUBSEQUENT' : entries[i].Status,
        SubStatus: entries[i].Status === 'CONFIRMED' || entries[i].Status === 'PENDING UPDATE REQUEST' ? '' : (entries[i].MissingInfo === 1 ? 'MISSING INFORMATION' : ''),
        Request_Update_Date: entries[i].UpdateRequestDate
          ? moment(entries[i].UpdateRequestDate).format("YYYY-MM-DD")
          : "",
        Confirmed_Update_Date: (entries[i].Status === 'VP DATA RECEIVED' || entries[i].Status === 'REFRESHED' ? '' : (entries[i].ConfirmedDate ? moment(entries[i].ConfirmedDate).format("YYYY-MM-DD") : ""))
      });
    }

    const report = excel.buildExport([
      {
        name: "Directors And Beneficial Owners",
        specification: specification,
        data: dataset,
      },
    ]);

    res.attachment("Search Report.xlsx");
    res.send(report);
  } catch (error) {
    console.log(error);
    next(error);
  }
};

async function searchData(req, next) {
  try {

    let vpDirectors
    let filter = {};
    let filterCompanies = {};
    let Status = []
    let SubStatus = []
    const productionOfficeGroups = req.session.productionOfficeGroups

    if (req.body.subStatus && req.body.subStatus !== '' && req.body.subStatus.length) {
      if (Array.isArray(req.body.subStatus)) {
        SubStatus = req.body.subStatus;
      } else {
        SubStatus = [req.body.subStatus];
      }
    }

    if (req.body.status && req.body.status !== '' && req.body.status.length) {
      if (Array.isArray(req.body.status)) {
        Status = req.body.status;
      } else {
        Status = [req.body.status];
      }
      filter.Status = { [Op.in]: Status }
    }

    if (req.body.filter_name) {
      filter.EntityName = { [Op.like]: `%${req.body.filter_name}%` };
      filterCompanies.EntityName = { [Op.like]: `%${req.body.filter_name}%` };
    }

    if (req.body.filter_director_name) {
      filter.Name = { [Op.like]: `%${req.body.filter_director_name}%` };
    }

    if (req.body.filter_masterclient && req.body.filter_masterclient.length >= 2) {
      filter.MasterClientCode = { [Op.like]: `%${req.body.filter_masterclient}%` };
      filterCompanies.MasterClientCode = { [Op.like]: `%${req.body.filter_masterclient}%` };
    }

    if (req.body.filter_entity_number && req.body.filter_entity_number.length > 2) {
      filter.EntityCode = { [Op.like]: `%${req.body.filter_entity_number}%` };
    }


    if (req.body.filter_position && (req.body.filter_position === 'Director' || req.body.filter_position === 'Owner/Controller')) {
      filter.RelationType = { [Op.eq]: `${req.body.filter_position}` };
    }

    if (req.body.filter_production && req.body.filter_production.length > 0 && req.body.filter_production !== '' && req.body.filter_production !== 'null') {

      filter.ProductionOffice = {
        [Op.and]: [
          {
            [Op.or]: [
              { [Op.eq]: `${req.body.filter_production}` },
              { [Op.notIn]: ['THKO', 'TBVI', 'TCYP', 'TPANVG'] },
              null
            ]
          },
          {
            [Op.or]: [
              { [Op.in]: [...productionOfficeGroups, null] }, //force-restriced the groups **belong to**, or allow where they dont match the basics, or null
              { [Op.notIn]: ['THKO', 'TBVI', 'TCYP', 'TPANVG'] }, // NOT IN condition
              null
            ]
          }]
      }
    } else
      filter.ProductionOffice = {   // default case, no dropdown
        [Op.or]: [
          { [Op.in]: [...productionOfficeGroups, null] },  //force-restriced the groups **belong to**, or allow where they dont match the basics, or null
          { [Op.notIn]: ['THKO', 'TBVI', 'TCYP', 'TPANVG'] }, // NOT IN condition
          null
        ]
      }


    if (req.body && req.body.filter_referral && req.body.filter_referral !== '') {
      filter.ReferralOffice = { [Op.like]: `%${req.body.filter_referral}%` };
      filterCompanies.ReferralOffice = { [Op.like]: `%${req.body.filter_referral}%` };
    }

    if (req.body && req.body.filter_confirmed_range_start && req.body.filter_confirmed_range_end && req.body.filter_confirmed_range_start !== '' && req.body.filter_confirmed_range_end !== '') {
      filter.ConfirmedDate = { [Op.between]: [req.body.filter_confirmed_range_start, req.body.filter_confirmed_range_end] }
    } else if (req.body && req.body.filter_confirmed_range_start !== '' && req.body.filter_confirmed_range_end === '') {
      filter.ConfirmedDate = { [Op.gte]: req.body.filter_confirmed_range_start }
    } else if (req.body && req.body.filter_confirmed_range_end && req.body.filter_confirmed_range_start === '' && req.body.filter_confirmed_range_end !== '') {
      filter.ConfirmedDate = { [Op.lt]: req.body.filter_confirmed_range_end }
    }


    if (SubStatus.length > 0) {

      if (SubStatus.includes('NO DIRECTOR')) {
        filter.UniqueRelationId = { [Op.eq]: null }
      }
      if (SubStatus.includes('HAS DIRECTOR')) {
        filter.UniqueRelationId = { [Op.ne]: null }
      }

      if (SubStatus.includes('MISSING INFORMATION')) {
        filter.MissingInfo = { [Op.eq]: 1 }
        filter.Status = { [Op.notIn]: ["CONFIRMED", "PENDING UPDATE REQUEST"] }
      }

    }
    //console.log( filter );

    if (Object.keys(filter).length > 1)
      vpDirectors = await VpDirectorInfoHistoryJoinAll.findAll({ where: filter });

    return [vpDirectors, Object.keys(filter).length > 1, filter];

  } catch (error) {
    console.log(error);
    next(error);
  }
}

function getListFieldsToValidate(isMember, filetype, officerType){
    const fieldsByType = isMember ? MEMBER_REQUIRED_FIELDS : DIRECTOR_REQUIRED_FIELDS;
    let fieldsToValidate = [];

    if (isMember){
        fieldsToValidate = fieldsByType[officerType] || [];
            
    }else{
        // Check if the filetype is individual 
        const isIndividual = filetype && filetype.toLowerCase() === "individual";
        fieldsToValidate = isIndividual ? fieldsByType["individual"] : fieldsByType["corporate"];
    }
    
    return fieldsToValidate
}

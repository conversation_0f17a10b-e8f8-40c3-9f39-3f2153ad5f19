<div id="upload-message-file-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-lg contour container-fluid">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">Upload files</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <h4>Maximum of 3 Files, PDF or XLSX only. File must not be password protected</h4>
                            <div id="messageUploadModal" class="dropzone">
                                <div class="fallback">
                                    <input name="SubmitEvidence" type="file" multiple/>
                                </div>
                                <div class="dz-message needsclick">
                                    <i class="h1 text-muted dripicons-cloud-upload"></i>
                                    <h3>Drop files here or click to upload.</h3>
                                    <span class="text-muted font-13"></span>
                                </div>
                            </div>
                            <div id="uploadedMessageFiles" class="mt-2 text-center text-muted"></div>

                            <div class="clearfix text-right mt-3">
                                <button type="button" class="btn solid royal-blue" data-dismiss="modal"><i
                                        class="mdi mdi-send mr-1"></i> Close
                                </button>
                            </div>
                        </div> <!-- end card-body-->
                    </div> <!-- end card-->
                </div> <!-- end col -->
            </div> <!-- end row -->
        </div>
    </div>
</div>
<script type="text/javascript" src="/templates/messagefilestable.precompiled.js"></script>
<script type="text/javascript">
    Dropzone.autoDiscover = false;
    $(function () {
        let filename = '';
        let mid = '';
        let myDropZone = new Dropzone("#messageUploadModal", {
            url: "/",
            acceptedFiles: 'application/pdf,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel',
            uploadMultiple: true,
            autoProcessQueue: true,
            parallelUploads: 5,
            maxFilesize: 5,
            paramName: function () {
                return 'fileUploaded';
            },
            init: function () {
                this.on("processing", function (file) {
                    this.options.url = "/client-management/announcements/upload-document";
                });
                this.on("success", function () {
                    refreshMessageUploadedFiles(mid);
                });
                this.on("sending", function (file, xhr, formData) {
                    if (!formData.has('filename')) {
                        formData.append("filename", filename);
                    }

                    if (!formData.has('mid')) {
                        formData.append("mid", mid);
                    }

                });

                this.on("errormultiple", function (files, response) {
                });

                this.on("maxfilesexceeded", function (file) {

                });

                this.on('resetFiles', function () {
                    if (this.files.length !== 0) {
                        for (i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                        this.files.length = 0;
                    }
                });
            }
        })

        $('#upload-message-file-modal').on('show.bs.modal', function (event) {
            let button = $(event.relatedTarget);
            filename = button.data('field');

            mid = button.data('message-id');
            refreshMessageUploadedFiles(mid);
            let objDZ = Dropzone.forElement("#messageUploadModal");
            objDZ.emit("resetFiles");

        });

        $('#upload-message-file-modal').on('hide.bs.modal', function (event) {
            let objDZ = Dropzone.forElement('#messageUploadModal');
            objDZ.removeAllFiles(true);
            $('#uploadedMessageFiles').html('');
        });
    });

    function deleteMessageFile(id, fileId) {
        $.ajax({
            type: "DELETE",
            url: "/client-management/announcements/" + id + "/files",
            data: {
                fileId: fileId
            },
            success: function (data) {
                if (data.status === 200) {
                    refreshMessageUploadedFiles(id);
                }
            },
            dataType: "json"
        });
        return false;
    }


    function refreshMessageUploadedFiles(mid) {
        $.get('/client-management/announcements/' + mid + "/files",
            function (data) {
                let template = Handlebars.templates.messagefilestable;
                let d = {
                    id: mid,
                    files: data.files ? data.files : []
                };
                let html = template(d);
                $('#uploadedMessageFiles').html(html);
                let btn = $('#message-files-btn');

                if (data.files && data.files.length > 0) {
                    btn.text('Modify');
                    btn.css({
                        'background-color': '#0AC292',
                        'border-color': '#0AC292',
                    });
                } else {
                    btn.text('Upload');
                    btn.css({
                        'background-color': '#0081b4',
                        'border-color': '#0081b4',
                    });
                }
            }
        );
    }

</script>

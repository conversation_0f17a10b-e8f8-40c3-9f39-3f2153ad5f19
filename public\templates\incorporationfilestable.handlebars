{{#if files}}
    <div class="table-responsive">
        <table class="table table-striped mb-0">
            <thead>
            <tr>
                <th scope="col">File</th>
                <th scope="col">Type</th>
                <th scope="col">Name</th>
                <th scope="col" style="width: 10%">Download</th>
            </tr>
            </thead>
            <tbody>
            {{#each files}}
                <tr>
                    <td>{{fileName}}</td>
                    <td>{{mimeType}}</td>
                    <td>{{originalName}}</td>
                    <td class="pl-2 py-1 text-center align-middle">
                        <a href="/client-incorporation/{{../incorporationId}}/download-document/{{fileId}}?relation={{../relationId}}&fileType={{fileTypeId}}"
                        target="_blank">
                            <i class="fa fa-download" aria-hidden="true"> </i>
                        </a>
                    </td>
                </tr>
                {{else}}
                    <tr>
                        <td colspan="4" class="text-center font-italic">There are no uploaded files</td>
                    </tr>
            {{/each}}
            </tbody>
        </table>
    </div>
{{/if}}

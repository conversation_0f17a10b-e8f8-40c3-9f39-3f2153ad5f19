<div class="modal fade" id="sendEmailModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Send Email</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                        <form id="messageForm" novalidate>

                            <div class="row">
                                <div class="col-md-12">
                                    <label for="subject-control">Subject*</label>
                                    <input class="form-control" id="subject-control" name="subject" required
                                           placeholder="Add subject..." value="{{message.subject}}">
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="email-subject-control">Email subject*</label>
                                    <textarea class="form-control" id="email-subject-control" name="emailSubject" rows="2" required
                                              placeholder="Add message...">{{message.emailSubject}}</textarea>
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-md-12">
                                    <label for="content-control">Content*</label>
                                    <textarea class="form-control" id="content-control" name="content" rows="5" required
                                              placeholder="Add message...">{{message.content}}</textarea>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12">
                                    <label>Master Client Codes*</label>
                                    <input readonly value="" id="masterclientsSelected"  class="form-control">
                                </div>
                            </div>
                        </form>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="submit" id="saveButton" class="btn btn-primary" form="messageForm">
                    Send
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">

    $('#sendEmailModal').on('hidden.bs.modal', function(event) {
      $('#masterclientsSelected').val('');
    })

    $("#messageForm").on('submit', function (event) {
        event.preventDefault();
        $(this).prop('disabled', true);


        $('#subject-control').val() === '' ? $('#subject-control').toggleClass("is-invalid", true) : $('#subject-control').toggleClass("is-invalid", false)
        $('#email-subject-control').val() === '' ? $('#email-subject-control').toggleClass("is-invalid", true) : $('#email-subject-control').toggleClass("is-invalid", false)
        $('#content-control').val() === '' ? $('#content-control').toggleClass("is-invalid", true) : $('#content-control').toggleClass("is-invalid", false)

        if ($(".is-invalid:visible").length === 0) {
            const obj = {
                subject: $('#subject-control').val(),
                emailSubject: $('#email-subject-control').val(),
                content: $('#content-control').val(),
                masterClientCodes: ($('#masterclientsSelected').val()).split(',')
            }
            $.ajax({
                type: "POST",
                url: 'send-email',
                data: obj,
                success: function (data) {
                    if (data.status === 200) {
                        Swal.fire('Success', 'Announcement saved successfully!', 'success').then(() => {
                            $("#saveButton").prop('disabled', false);
                            $("#sendEmailModal").modal('hide');
                            $('#subject-control').val(null) 
                            $('#email-subject-control').val(null)
                            $('#content-control').val(null)
                        });
                    } else {
                        toastr["warning"](data.error ? 'Sorry, Error saving the information. ' + data.error :
                                'Sorry, Error saving the information. Try again later...');
                        $("#saveButton").removeAttr('disabled');
                    }
                },
                error: function (err) {
                    toastr["error"](err.responseJSON && err.responseJSON.error ? 'Sorry, Error saving the information. ' + err.responseJSON.error :
                            'Sorry, Error saving the information. Try again later...');
                    $("#saveButton").prop('disabled', false);
                }
            });
        } else {
            setTimeout(function () {
                $("#saveButton").prop('disabled', false);
            }, 1);
        }
    });
</script>

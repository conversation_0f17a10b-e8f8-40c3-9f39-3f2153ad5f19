const CompanyModel = require("../models/company").schema;
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const xlsx = require('xlsx');
const fs = require('fs');
const moment = require('moment');

dotenv.config();


async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const db = mongoose.connection;
    db.on('error', console.error.bind(console, 'MongoDB connection error:'));
    // Start process - Read file (change file name as needed)
    const data = new Uint8Array(fs.readFileSync('DEV_test.companies.csv'));
    const workbook = xlsx.read(data, {
      type: "array",
      cellText: false,
      cellDates: true,
      sheetStubs: true,
    });
    
    const result = await enableAccountingModule(workbook);

    console.log('Script ejecutado correctamente', result);
  } catch (error) {
    console.error('Error en el script:', error);
  } finally {
    mongoose.disconnect();
  }
}




function getCompaniesRows(workbook) {
  try {
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const companies = [];

      for (let cell in worksheet) {
          const cellAsString = cell.toString();
          const rowNumber = Number(cellAsString.replace(/\D/g, ''));
          const dataStartRow = 2;
          const companyIndex = rowNumber - dataStartRow;
          const rowName = cellAsString.replace(/[0-9]/g, '');

          if (companyIndex >= 0 && worksheet[cell].v) {
              if (!companies[companyIndex]) {
                  companies.push({
                      id: '',
                      name: '',
                      code: '',
                      incorporationcode: ''
                  })
              }

              switch (rowName) {
                case "A":
                    companies[companyIndex].id = worksheet[cell].v;
                    break;
                case "B":
                    companies[companyIndex].name = worksheet[cell].v;
                    break;
                case "C":
                    companies[companyIndex].code = worksheet[cell].v;
                    break;
                case "D":
                    companies[companyIndex].incorporationcode = worksheet[cell].v;
                    break;
                // Agrega más casos según las columnas que tengas en tu archivo
            }
          }
      }
      return companies;
  } catch (e) {
      console.log("Error processing xlsx data: ", e);
      return []
  }

}


async function enableAccountingModule(workbook) {
  try {

      let importCompanyLog = [['ID', 'Company Code', 'Update date', 'Action']];

      // Get company + bo info
      const companies = getCompaniesRows(workbook);


      console.log("companies length ", companies.length);

      for (let i = 0; i < companies.length; i++) {
          console.log('processing ' + i + '  from ' + companies.length)

          const company = companies[i];
          const existsCompany = await CompanyModel.findOne({ _id: company.id, code: company.code, incorporationcode: company.incorporationcode });
  
          // create companies
          if (existsCompany) {
              if (existsCompany.accountingRecordsModule && !existsCompany.accountingRecordsModule.active){
                  existsCompany.accountingRecordsModule.active = true
                  await existsCompany.save();
                  importCompanyLog.push([existsCompany._id.toString(), existsCompany.code, new Date(), 'UPDATED'])
              }
              else{
                  importCompanyLog.push([existsCompany._id.toString(), existsCompany.code, new Date(), 'SKIPPED'])
              }
          }
          else {
              importCompanyLog.push([company.id, company.code,  new Date(), 'NOT FOUND COMPANY CREATED FOR THE COMPANY CODE'])
          }
      }
      // create entities bo
      console.log("companies updated ", importCompanyLog.length);

      const filename = 'update_ITA_application_date_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
      const logWorkbook = xlsx.utils.book_new();
      const logWorksheet1 = xlsx.utils.aoa_to_sheet(importCompanyLog);

      xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'companies ' + moment.utc().format('YYYY-MM-DD'));
      xlsx.writeFile(logWorkbook, filename);

      return { "success": true, "totalRows": importCompanyLog.length  };
  } catch (e) {
      console.log(e);
      return { "success": false };
  }
}

runScript()




<tr>
    <td>{{file.external}}</td>
    <td class="text-center">
        <div class="custom-control custom-checkbox">
            <input
                    type="checkbox"
                    class="custom-control-input"
                    name="detailsPartner[files][{{row}}][present]"
                    id="{{group}}-detailsPartner-{{row}}-present-file"
            />
            <label
                    class="custom-control-label"
                    for="{{group}}-detailsPartner-{{row}}-present-file"
            ></label>
        </div>
    </td>
    <td class="text-center"> <!--falta fileId y relationId -->
        <button
                type="button"
                class="btn solid royal-blue"
                data-toggle="modal"
                data-target="#upload-temp-modal"
                id="btn-detailsPartner-{{row}}"
                data-id="{{ file.id }}"
                data-review-id="{{ reviewId}}"
                data-relation-id="{{ relationId}}"
                data-row="{{row}}"
                data-file-type="{{group}}"
                data-field="{{file.internal}}"
                data-file-group="detailsPartner"
                {{#if file.present}}style="background-color:#0AC292;border-color: #0AC292;"{{/if}}
        >
            {{#if file.present}}Modify{{else}}Upload{{/if}}
        </button>
    </td>
    <td>
            <textarea
                    class="form-control"
                    name="detailsPartner[files][{{row}}][explanation]"
                    id="{{group}}-detailsPartner-{{row}}-explanation-file"
                    rows="1"
                    data-value="{{file.explanation}}"
            ></textarea>
        <label for="{{group}}-detailsPartner-{{row}}-explanation-file" hidden></label>
    </td>
</tr>

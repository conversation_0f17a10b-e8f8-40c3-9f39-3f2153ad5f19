<main class="">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <h2  id="financialReportTitle">
          {{title}}
        </h2>
      </div>
    </div>
    
    <br>
    <div >
      <div class="card">
        <div class="card-body">
          <form method="POST" class='enquiry' autocomplete="off" id="reportForm">
            <div class="container-fluid">

                        
              <div id="step-1">
                <div class="row px-2">
                  <div class="col-12">
                    {{> financial-report/form/step-1 report=report }}
                  </div>

                </div>
                <hr>
                <div class="row">
                  <div class="col-12 mb-3 d-flex flex-row justify-content-between">
                    <div class="mr-2 ">
                      <a type="button" href="/financial-report-management/requests"
                        class="btn btn-secondary waves-effect waves-light width-lg">
                        Back
                      </a>
                    </div>
                
                    <button type="button" data-step="1" data-action="NEXT" data-id="{{ report._id }}" data-save="true"
                      class="btn btn-primary waves-effect waves-light width-lg nextBtn">
                      Continue
                    </button>
                  </div>
                </div>
              </div>


              <div id="step-2" style="display: none">
                <div class="row px-2">
                  <div class="col-12">
                    {{> financial-report/form/step-2 report=report }}
                  </div>
              
                </div>
                <hr>
                <div class="row">
                  <div class="col-12 mb-3 d-flex flex-row justify-content-between">
                    <button type="button" class="btn btn-secondary waves-effect waves-light width-xl backBtn" data-step="2"
                      data-action="BACK" data-id="{{ report._id }}">Back
                    </button>

                    <button type="button" data-step="2" data-action="SAVE" data-id="{{ report._id }}"
                      class="btn solid royal-blue width-lg nextBtn">
                      Save
                    </button>

                    <button type="button" data-step="2" data-action="REVIEW" data-id="{{ report._id }}"
                      class="btn btn-primary waves-effect waves-light width-lg reviewBtn">
                      Review
                    </button>


                    <button type="button" data-step="2" data-action="COMPLETE" data-id="{{ report._id }}"
                      class="btn btn-primary waves-effect waves-light width-lg nextBtn">
                      SUBMIT
                    </button>

                    
                  </div>
                </div>
              </div>          
            </div>
          </form>
        </div>
      </div>

    </div>

  </div>
</main>

<script src="/javascripts/libs/jquery-mask-plugin/jquery.mask.min.js"></script>
<script src="/javascripts/libs/autonumeric/autoNumeric-min.js"></script>
<script src="/javascripts/form-masks.init.js"></script>
<script type='text/javascript' src='/templates/financial-report/createothervaluerow.precompiled.js'></script>
<script type="text/javascript">
  $('.autonumber-pos').autoNumeric('init', {
    vMin: "0",
    vMax: "99999999999",
    aSep: ",",
    mDec: "2"
  });
  
  const stepsFlow = {
    "1": { previous: null, next: 2 },
    "2": { previous: 1, next: null },
  }



  $('.nextBtn').click(function () {
    changeStep(parseInt($(this).attr('data-step')), $(this).attr('data-action'), $(this).attr('data-id'))
  })

  $('.backBtn').click(function () {
    changeStep(parseInt($(this).attr('data-step')), $(this).attr('data-action'), $(this).attr('data-id'))
  })

  $('.reviewBtn').click(function () {
     changeStep(parseInt($(this).attr('data-step')), $(this).attr('data-action'), $(this).attr('data-id'))
  })

  $('input[required]').on('keyup', function () {
    const empty = $(this).val() === "";
    $(this).toggleClass("is-invalid", empty);
  });


  $(document).ready(function () {
    $('#completeReportCurrency').select2();
    calculateGrossProfit();
    calculateCompleteTotalValues($('.complete-expenses'), $('#completeDetailsExpensesTotal'));
    calculateNetIncome();

  });

  $(document).on('change paste keyup', '.complete-income', function () {
    calculateGrossProfit();
    calculateNetIncome();
  });

  $(document).on('change paste keyup', '.complete-expenses', function () {
    calculateCompleteTotalValues($('.complete-expenses'), $('#completeDetailsExpensesTotal'));
  });

  $(document).on('change paste keyup', '.complete-assets', function () {
      calculateCompleteTotalValues($('.complete-assets'), $('#completeDetailsAssetsTotal'));
  });

  $(document).on('change paste keyup', '.complete-liabilities', function () {
    calculateCompleteTotalValues($('.complete-liabilities'), $('#completeDetailsLiabilitiesTotal'));
  });
 
  $('#completeDetailsGrossProfit').on('keyup', function () {
    calculateNetIncome();
  })

  $('#completeDetailsExpensesTotal').on('keyup', function () {
    calculateNetIncome();
  })

  $('#completeDetailsAssetsTotal').on('keyup', function (e) {
    calculateTotalShareholders();
  })

  $('#completeDetailsLiabilitiesTotal').on('keyup', function (e) {
    calculateTotalShareholders();
  })

  $("#completeReportCurrency").on('change', function () {
      const val = $(this).val();
      $(".complete-currency").html(`${val}`)
  })


  async function saveReportForm(reportId, isSaveOrCompleted, currentStep, nextStep, isReview = null) {
    let invalidRadios = false;
    let invalidFiles = false;
    $('#reportForm input[required]:visible').trigger('keyup');

    if ($(".is-invalid:visible").length === 0) {
      const form = getFormDataByStep(currentStep);
      form.actionType = isSaveOrCompleted;
      form.currentStepForm = currentStep;
      $.ajax({
        type: "POST",
        url: `/financial-report-management/${reportId}/save`,
        data: JSON.stringify(form),
        contentType: "application/json; charset=utf-8",
        success: function (data) {
          if (data.status === 200) {
            
            if(isSaveOrCompleted && !isReview){
              Swal.fire('Success', data.message, 'success').then(() => {
                 window.location.href = `/financial-report-management/requests`;
              });
            } else if (isReview){
              toastr["success"](data.message, 'The empty fields has been populated with 0!');
              var url = window.location.href.concat('/report.pdf');
              window.open(url, '_blank'); 
            } else{
              toastr["success"](data.message, 'Success!');
              $("#step-" + nextStep).show();
              $('#step-' + currentStep).hide();

              $("html, body").animate({
                scrollTop: $("#financialReportTitle").offset().top
              }, 200);
            }

          } else if (data.status === 400 && data.formErrors && data.formErrors.length) {
            for (let error of data.formErrors) {
              toastr["warning"](error, 'Error!');
            }
          } else {
            toastr["warning"](data.message, 'Error!');
          }
        },
        error: function (err) {
          toastr["warning"]('Financial report could not be saved, please try again later.', 'Error!');
        }
      });

    } else {
      return true;
    }
  }

  function changeStep(currentStep, action, id) {
    let stepValues = stepsFlow[currentStep];

    if (action === 'NEXT') {
      saveReportForm(id, null, currentStep, stepValues.next);
    } else if (action === 'BACK') {
      $('#step-' + stepValues.previous).show();
      $('#step-' + currentStep).hide();
      $("html, body").animate({
        scrollTop: $("#financialReportTitle").offset().top
      }, 200);

    } else if (action === 'COMPLETE' || action === "SAVE") {
      saveReportForm(id, action, currentStep, stepValues.next);
    } else {
      $('input:visible:not([readonly])').each(function() {
        if ($(this).val().trim() === '') {
            $(this).val('0.00')
            $(this).trigger('keyup')
        }
      });
      saveReportForm(id, action, currentStep, stepValues.next, true);
    }
  }

  function calculateGrossProfit() {
    const revenueStr = $('#completeDetailsRevenue').val();
    const costOfSalesStr = $('#completeDetailsCostOfSales').val();

    const revenueTotal = parseFloat(revenueStr.replace(/,/g, '')) || 0;
    const costOfSalesTotal = parseFloat(costOfSalesStr.replace(/,/g, '')) || 0;

    $('#completeDetailsGrossProfit').val(showDecimalValue(revenueTotal - costOfSalesTotal))
  }


  function calculateNetIncome() {
    const grossProfitTotalStr = $('#completeDetailsGrossProfit').val();
    const expensesTotalStr = $('#completeDetailsExpensesTotal').val();

    const grossProfit = parseFloat(grossProfitTotalStr.replace(/,/g, '')) || 0;
    const expensesTotal = parseFloat(expensesTotalStr.replace(/,/g, '')) || 0;


    $('.net-income-value').val(showDecimalValue(grossProfit - expensesTotal))
  }

  function calculateCompleteTotalValues(items, totalField) {
    let total = 0;
    items.each(function () {
      const item = $(this)
      const itemValue = item.val()
      const value = parseFloat(itemValue.replace(/,/g, '')) || 0;
      total += value;

    });

    totalField.val(showDecimalValue(total)).trigger('keyup');
  }

  function calculateTotalShareholders() {
    const assetsTotalStr = $('#completeDetailsAssetsTotal').val();
    const liabilitiesTotalStr = $('#completeDetailsLiabilitiesTotal').val();

    const assetsTotal = parseFloat(assetsTotalStr.replace(/,/g, '')) || 0;
    const liabilitiesTotal = parseFloat(liabilitiesTotalStr.replace(/,/g, '')) || 0;

    const totalShareholdersEquity = (assetsTotal - liabilitiesTotal);
    $('#completeDetailsShareholderEquity').val(showDecimalValue(totalShareholdersEquity))
  }

  function showDecimalValue(dVal) {
    if (dVal) {
      return Number(dVal).toLocaleString("en", { minimumFractionDigits: 2 });
    } else {
      return 0.00;
    }
  };


  function getFormDataByStep(step){
    let formData = {};

    if(step === 1){
      formData = {
        completeReportCurrency: $('#completeReportCurrency').val(),
        revenue: $("#completeDetailsRevenue").val(),
        costOfSales: $("#completeDetailsCostOfSales").val(),
        operatingExpenses: $("#completeDetailsOperatingExpenses").val(),
        incomeTax: $("#completeDetailsIncomeTaxExpense").val(),
        totalOtherExpenses: $("#completeDetailsTotalOtherExpenses").val(),
      }
    }
    else if(step === 2){
      formData = {
        cashAmount: $("#completeDetailsCashAmount").val(),
        loansAndReceivables: $("#completeDetailsLoansAndReceivables").val(),
        investmentsAssetsAmount: $("#completeDetailsInvestmentsAssetsAmount").val(),
        fixedAssetsAmount: $("#completeDetailsFixedAssetsAmount").val(),
        intangibleAssetsAmount: $("#completeDetailsIntangibleAssetsAmount").val(),
        totalOtherAssets: $("#completeDetailsTotalOtherAssets").val(),
        accountsPayable: $("#completeDetailsAccountsPayable").val(),
        longTermDebts: $("#completeDetailsLongTermDebts").val(),
        totalOtherLiabilities: $("#completeDetailsTotalOtherLiabilities").val(),
      }
    }

    return formData;
  }

</script>
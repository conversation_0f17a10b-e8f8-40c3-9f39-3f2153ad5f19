{"name": "trident-bvi-management", "version": "0.0.0", "private": true, "scripts": {"start": "node server.js", "devstart": "nodemon -r dotenv/config server.js", "build": "echo 'build script executed'", "lint": "npx eslint ./", "import-bo": "node scripts/import-bo-directors.js", "import-bo2": "node scripts/import-bo2.js", "set-filereview-dates": "node scripts/update-filereviewer-validation-date.js", "total-files-report": "node scripts/total-files-report.js", "rfi-returned-report": "node scripts/rfi-returned-report.js", "update-reviews-client-by-qa": "node scripts/update-reviews-client-by-qa.js", "update-incorporation-dates": "node scripts/update-company-incorporation-date.js", "update-ita-dates": "node scripts/update-company-ITA-date.js", "update-application-ita-dates": "node scripts/update-company-ITA-application-date.js", "update-entries-arrays": "node scripts/update-entries-array-fields.js", "update-entries-currentstepform": "node scripts/update-entries-currentstepform.js", "update-entries-support-comments": "node scripts/update-support-details-comments.js", "update-entries-meetings-in-bvi": "node scripts/update-entries-v5-meetings-in-bvi.js", "update-company-modules": "node scripts/update-company-modules.js", "update-company-approval": "node scripts/update-company-approval.js", "update-company-accounting-module": "node scripts/update-company-accounting-module-excel.js", "update-files-mimetype": "node scripts/update-files-mimetype.js", "update-ar-complete-details-fields": "node scripts/update-ar-complete-details-fields.js", "update-company-names": "node scripts/update-company-names.js", "set-company-confirmed-period": "node scripts/set-company-confirmed-period.js", "update-entries-initial-date": "node scripts/update-entries-initial-submit-date.js", "update-company-new-deadline-date": "node scripts/set-company-new-deadline.js", "set-company-update-deadlines": "node scripts/set-company-update-deadlines.js"}, "engines": {"node": ">=18.12.1"}, "dependencies": {"@azure/identity": "^4.0.1", "@azure/storage-blob": "^12.17.0", "@handlebars/allow-prototype-access": "^1.0.5", "applicationinsights": "^2.9.0", "archiver": "^6.0.1", "async": "^3.2.4", "axios": "^1.2.3", "body-parser": "latest", "connect-redis": "^6.1.3", "cookie-parser": "~1.4.6", "debug": "~4.3.4", "express": "^4.19.2", "express-async-errors": "^3.1.1", "express-fileupload": "^1.4.3", "express-handlebars": "^7.1.2", "express-session": "^1.18.0", "form-serializer": "^2.5.0", "handlebars": "^4.7.8", "hbs": "^4.2.0", "http-errors": "^2.0.0", "into-stream": "6.0.0", "lodash": "latest", "moment": "^2.29.4", "mongoose": "^5.13.14", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-excel-export": "^1.4.4", "node-fetch": "^2.6.1", "nodemailer": "^6.9.9", "pdfmake": "^0.2.9", "redis": "^3.1.2", "sequelize": "^6.37.1", "tedious": "^16.4.0", "uuid": "^9.0.1", "xlsx": "^0.18.5", "xml2js": "^0.6.2"}, "devDependencies": {"acorn": "^8.8.1", "dotenv": "^16.0.3", "eslint": "^8.32.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "nodemon": "^3.0.3", "npm-check-updates": "^16.6.2", "prettier": "^2.8.3", "rollup": "^3.10.0"}}
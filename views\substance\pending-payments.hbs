<div class="card">
    <div class="card-body">

        <div class="row">
            <div class="col-12">
                <h1>{{title}}</h1>
                <form method='POST' id="generalForm">
                    <div class="row">
                        <div class="col-md-2">
                            <label for="filter_company">Company</label>
                            <input class='form-control' type='text' name='filter_company' id='filter_company'
                                value="{{filters.filter_company}}" />
                        </div>
                        <div class="col-md-2">
                            <label for="filter_masterclient">Masterclient</label>
                            <input class='form-control' type='text' name='filter_masterclient' id='filter_masterclient'
                                value="{{filters.filter_masterclient}}" />
                        </div>
                        <div class="col-md-2">
                            <label for="filter_referral">Referral Office</label>
                            <input class='form-control' type='text' name='filter_referral' id='filter_referral'
                                value="{{filters.filter_referral}}" />
                        </div>
                        <div class="col-md-4">
                            <div class="row">
                                <label for="filter_referral">Relevant Activities</label>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="relevant_activities"
                                            id="none" value="none" {{#if filters.relevantActivities.none }} checked
                                            {{/if}} />
                                        <label class="custom-control-label" for="none">None</label>
                                    </div>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="relevant_activities"
                                            id="banking_business" value="banking_business"
                                            {{#if filters.relevantActivities.banking_business }} checked {{/if}} />
                                        <label class="custom-control-label" for="banking_business">Banking
                                            Business</label>
                                    </div>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="relevant_activities"
                                            id="insurance_business" value="insurance_business"
                                            {{#if filters.relevantActivities.insurance_business }}checked {{/if}} />
                                        <label class="custom-control-label" for="insurance_business">Insurance
                                            Business</label>
                                    </div>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="relevant_activities"
                                            id="fund_management_business" value="fund_management_business"
                                            {{#if filters.relevantActivities.fund_management_business }}checked
                                            {{/if}} />
                                        <label class="custom-control-label" for="fund_management_business">Fund
                                            Management Business</label>
                                    </div>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="relevant_activities"
                                            id="finance_leasing_business" value="finance_leasing_business"
                                            {{#if filters.relevantActivities.finance_leasing_business }}checked
                                            {{/if}} />
                                        <label class="custom-control-label"
                                            for="finance_leasing_business">Finance/Leasing Business</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="relevant_activities"
                                            id="headquarters_business" value="headquarters_business"
                                            {{#if filters.relevantActivities.headquarters_business }}checked {{/if}} />
                                        <label class="custom-control-label" for="headquarters_business">Headquarters
                                            Business</label>
                                    </div>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="relevant_activities"
                                            id="shipping_business" value="shipping_business"
                                            {{#if filters.relevantActivities.shipping_business }}checked {{/if}} />
                                        <label class="custom-control-label" for="shipping_business">Shipping
                                            Business</label>
                                    </div>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="relevant_activities"
                                            id="holding_business" value="holding_business"
                                            {{#if filters.relevantActivities.holding_business }}checked {{/if}} />
                                        <label class="custom-control-label" for="holding_business">Holding
                                            Business</label>
                                    </div>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="relevant_activities"
                                            id="intellectual_property_business" value="intellectual_property_business"
                                            {{#if filters.relevantActivities.intellectual_property_business }}checked
                                            {{/if}} />
                                        <label class="custom-control-label"
                                            for="intellectual_property_business">Intellectual Property
                                            Business</label>
                                    </div>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="relevant_activities"
                                            id="service_centre_business" value="service_centre_business"
                                            {{#if filters.relevantActivities.service_centre_business }}checked
                                            {{/if}} />
                                        <label class="custom-control-label" for="service_centre_business">Service Centre
                                            Business</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-md-2">
                            <label for="filter_range_start">Submitted after:</label>
                            <input class='form-control' type='date' name='filter_range_start' id='filter_range_start'
                                value="{{filters.filter_range_start}}" />
                        </div>
                        <div class="col-md-2">
                            <label for="filter_range_end">Submitted before:</label>
                            <input class='form-control' type='date' name='filter_range_end' id='filter_range_end'
                                value="{{filters.filter_range_end}}" />
                        </div>
                        <div class="col-md-2" style="padding-top:30px">
                            <input type='SUBMIT' class='btn btn-light btn-sm waves-effect' value='Search' />
                        </div>
                    </div>
                </form>
                <br /><br />
                <table id="scroll-horizontal-datatable" class="table w-100 nowrap">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Company Name</th>
                            <th>Company Code</th>
                            <th>Masterclient</th>
                            <th>Status</th>
                            <th>Financial period end date</th>
                            <th>Date created</th>
                            <th>Date submitted</th>
                        </tr>
                    </thead>


                    <tbody>
                        {{#each data}}
                        <tr data-id="{{id}}">
                            <td>{{id}}</td>
                            <td>{{company_data.name}}</td>
                            <td>{{company_data.code}}</td>
                            <td>{{company_data.masterclientcode}}</td>
                            <td>{{status}}</td>
                            <td data-sort="{{formatDate entity_details.financial_period_ends 'YYYY-MM-DD HH:mm'}}">
                                {{formatDate entity_details.financial_period_ends ../STANDARD_DATE_FORMAT}}
                            </td>
                            <td data-sort="{{formatDate createdAt 'YYYY-MM-DD HH:mm'}}">
                                {{formatDate createdAt ../STANDARD_DATE_FORMAT}}
                            </td>
                            <td data-sort="{{formatDate submitted_at 'YYYY-MM-DD HH:mm'}}">
                                {{formatDate submitted_at ../STANDARD_DATE_FORMAT}}
                            </td>
                        </tr>
                        {{/each}}

                    </tbody>
                </table>

            </div>
        </div>
        <div class="row">
            <div class="col-12">&nbsp;</div>
        </div>
        <div class="row">
            <div class="col-12 text-sm-center form-inline">
                <div class="form-group mr-2">
                    <a href='/substance/' class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                </div>
                <div class="form-group mr-2">
                    <button id="btn-mark-as-paid" class="btn btn-primary"><i
                            class="mdi mdi mdi-content-save-edit mr-2"></i> Mark as PAID</button>

                </div>
                <div class="form-group mr-2 hidden">
                    <button id="btn-export-csv" class="btn btn-primary"><i
                            class="mdi mdi mdi-content-save-edit mr-2"></i> Export to CSV</button>
                </div>
                <div class="form-group mr-2 hidden">
                    <button id="btn-export-json" class="btn btn-primary"><i
                            class="mdi mdi mdi-content-save-edit mr-2"></i> Export to JSON</button>
                </div>
            </div>
        </div>

    </div>
</div>
<form method="POST" action="./export" id="submitForm" name="submitForm">
    <input type="hidden" name="entryIds" value="" />
</form>
<form method="POST" action="./export-json" id="submitFormJSON" name="submitFormJSON">
    <input type="hidden" name="entryIds" value="" />
</form> <!-- end row-->

<script type="text/javascript">

    $(document).ready(function () {
        let showLimitAlert = "{{showLimitAlert}}" === 'true';
        if (showLimitAlert) {
            toastr["warning"]('Maximum number of records reached. Please refine your search to reduce the size of query.', 'Limit reached!', {
                "timeOut": 100000,
            });
        }

        //$("#basic-datatable").DataTable({language:{paginate:{previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"}},drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")}});var a=$("#datatable-buttons").DataTable({lengthChange:!1,buttons:["copy","print","pdf"],language:{paginate:{previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"}},drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")}});
        var table = $("#scroll-horizontal-datatable").DataTable({ "columnDefs": [{ "visible": false, "targets": [0] }], select: { style: "multi" }, language: { paginate: { previous: "<i class='mdi mdi-chevron-left'>", next: "<i class='mdi mdi-chevron-right'>" } }, drawCallback: function () { $(".dataTables_paginate > .pagination").addClass("pagination-rounded") } })
        //$("#key-datatable").DataTable({keys:!0,language:{paginate:{previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"}},drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")}}),a.buttons().container().appendTo("#datatable-buttons_wrapper .col-md-6:eq(0)"),
        //$("#alternative-page-datatable").DataTable({pagingType:"full_numbers",drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")}}),
        //$("#scroll-vertical-datatable").DataTable({scrollY:"350px",scrollCollapse:!0,paging:!1,language:{paginate:{previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"}},drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")}}),
        //$("#scroll-horizontal-datatable").DataTable({scrollX:!0,language:{paginate:{previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"}},drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")}}),
        //$("#complex-header-datatable").DataTable({language:{paginate:{previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"}},drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")},columnDefs:[{visible:!1,targets:-1}]}),
        //$("#row-callback-datatable").DataTable({language:{paginate:{previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"}},drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")},createdRow:function(a,i,e){15e4<1*i[5].replace(/[\$,]/g,"")&&$("td",a).eq(5).addClass("text-danger")}}),
        //$("#state-saving-datatable").DataTable({stateSave:!0,language:{paginate:{previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"}},drawCallback:function(){$(".dataTables_paginate > .pagination").addClass("pagination-rounded")}})
        $('#btn-mark-as-paid').click(function () {
            var data = table.rows('.selected').data();

            var entryIds = [];
            for (var idx = 0; idx < data.length; idx++) {
                entryIds.push(data[idx][0])
            }
            $.ajax({
                type: "POST",
                url: "./pending-payments/pay",
                data: {
                    entries: entryIds
                },
                success: function (data) {
                    if (data.success) {
                        toastr.success('Submission(s) marked as Paid successfully');
                        window.setTimeout(function () {
                            document.location.reload();
                        }, 3000);
                    } else {
                        toastr["warning"]('Sorry, there was an error marking as Paid. Please try again.');
                    }
                },
                dataType: "json"
            });
        });

        $('#btn-export-csv').click(function () {
            var data = table.rows('.selected').data();

            var entryIds = [];
            for (var idx = 0; idx < data.length; idx++) {
                entryIds.push(data[idx][0])
            }
            var oForm = document.forms["submitForm"];
            oForm.elements["entryIds"].value = entryIds.join(';');
            oForm.submit();
        });

        $('#btn-export-json').click(function () {
            var data = table.rows('.selected').data();

            var entryIds = [];
            for (var idx = 0; idx < data.length; idx++) {
                entryIds.push(data[idx][0])
            }
            var oForm = document.forms["submitFormJSON"];
            oForm.elements["entryIds"].value = entryIds.join(';');
            oForm.submit();
        });
    });
</script>

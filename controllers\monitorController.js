let appInsights = require('applicationinsights');
const { getContainerClient } = require('../utils/azureStorage');
const Readable = require('stream').Readable;

const mongoose = require("mongoose");

exports.verifyPerformance = async function (req, res, next) {
  try{
    console.log(new Date().toString() + "data monitoring");
    let client = appInsights.defaultClient;
    let monitoringResponse;

    const isDatabaseOk = await testDatabase(client);
    let isSubstanceAzureStorageOk;
    let isFileReviewAzureStorageOk;

    if (!process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_STATISTICS && !process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW) {
      client.trackException({ exception: new Error("Azure Storage Container value is empty") });
    }
    if (process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_STATISTICS !== '') {
      isSubstanceAzureStorageOk = await testAzureStorage(client, process.env.AZURE_STORAGE_ACCOUNT,
        process.env.AZURE_STORAGE_ACCESS_KEY,
        process.env.AZURE_STORAGE_CONTAINER_SUBSTANCE_STATISTICS);
    }
    if (process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW !== '') {
      isFileReviewAzureStorageOk = await testAzureStorage(client, process.env.AZURE_STORAGE_ACCOUNT,
        process.env.AZURE_STORAGE_ACCESS_KEY,
        process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW);
    }


    if (isDatabaseOk && (isSubstanceAzureStorageOk || isFileReviewAzureStorageOk)) {
      monitoringResponse = {
        status: 200,
        message: "All services are available"
      }
    } else {
      monitoringResponse = {
        status: 500,
        message: "Error, services are unavailable"
      }
    }

    res.render("monitor", {
      user: req.session.user,
      title: "Monitor performance",
      data: monitoringResponse
    });

  }catch(e){
    console.log(e);
    next(e);
  }

};


async function testDatabase(client) {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });
    const db = mongoose.connection;
    db.on('error', function (err) {
      console.error.bind(console, 'MongoDB connection error: ', err);
      client.trackException({exception: new Error("MongoDB connection error " + err)});
      return false;
    });
    return true;
  } catch (e) {
    console.log(e);
    client.trackException({exception: new Error("MongoDB connection error " + e)});
    return false;
  }
}


async function testAzureStorage(client, azureAccount, azureKey, azureContainer) {
  return new Promise((resolve) => {
    try {
      const fileReviewStream = new Readable();
      const testJson = {
        "name": "performance azure storage",
        "value": "Upload Azure Storage Test"
      };
      fileReviewStream.push(JSON.stringify(testJson));
      fileReviewStream.push(null);

      const options = {
        blobHTTPHeaders: {
            blobContentType: 'application/json'
        }
      };

      const containerClient = getContainerClient(
        azureContainer,
        azureAccount,
        azureKey
      );

      const blockBlobClient = containerClient.getBlockBlobClient('performance-test.json');
      
      blockBlobClient.uploadStream(fileReviewStream, undefined, undefined, options).then(() => {
        resolve(true);
      }).catch((error) => {
        client.trackException({exception: new Error("Error uploading to Azure Storage container: " + error.message)});
        resolve(false);
      });
    } catch (e) {
      console.log("azure error ", e);
      client.trackException({exception: new Error("Error uploading to Azure Storage ")});
      resolve(false);
    }
  });


}


<!-- NOFITY MODAL -->
<div class="modal fade" id="sendNotificationModal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          Send Notification
        </h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body py-4">
        <div class="row m-0">
          <span class="font-weight-bold mr-1">To:</span><span id="userToNotify"></span>
        </div>
        <div class="row mx-0 mt-1">
          <span class="font-weight-bold mr-1">Subject Client File:</span>
          <span id="companyName"></span>
        </div>
        <form id="notifyFileReviewerForm">
          <div class="form-group">
            <label class="mt-1" for="notificationMessage">Message:</label>
            <textarea
              name="notificationMessage"
              id="notificationMessage"
              class="form-control"
            ></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer justify-content-between">
        <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
          Close
        </button>
        <button
          type="submit"
          id="sendNotificationButton"
          form="notifyFileReviewerForm"
          class="btn solid royal-blue"
          disabled
        >
          Send
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  let companyCode = '';
  let companyName = '';
  let nameToNotify = '';
  let emailToNotify = '';
  let emailText = '';
  $('.notifyButton').on('click', function () {
    companyCode = $(this).data('companycode');
    companyName = $(this).data('companyname');
    nameToNotify = $(this).data('frname');
    emailToNotify = $(this).data('fremail');
    $('#userToNotify').text(emailToNotify);
    $('#companyName').text(companyName);
  });

  $('#notificationMessage').on('input', function () {
    if ($('#notificationMessage').val()) {
      $('#notificationMessage').removeClass('is-invalid');
      $('#sendNotificationButton').prop('disabled', false);
    } else {
      $('#sendNotificationButton').prop('disabled', true);
    }
  });

  $('#notifyFileReviewerForm').on('submit', function (event) {
    event.preventDefault();
    emailText = $('#notificationMessage').val();
    if (emailText) {
      $('#sendNotificationButton').prop('disabled', true);
      $.ajax({
        type: 'POST',
        url: '/file-reviewer/send-file-review-notification',
        data: {
          companyCode: companyCode,
          companyName: companyName,
          nameToNotify: nameToNotify,
          emailToNotify: emailToNotify,
          emailText: emailText,
        },
        success: function (res) {
          Swal.fire('Email sent', 'An email was sent to your colleague', 'success');
          location.href = '/file-reviewer/file-review-list';
        },
        error: function (res) {
          Swal.fire('Email not sent', 'There was an error sending the email', 'error');
        },
      });
    } else {
      $('#notificationMessage').addClass('is-invalid');
    }
  });
</script>
<!-- NOFITY MODAL END -->

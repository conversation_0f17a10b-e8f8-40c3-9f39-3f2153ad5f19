const FinancialReportModel = require('../models/financialreport');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const xlsx = require('xlsx');
const fs = require('fs');
const moment = require('moment');

dotenv.config();

async function runScript() {
try {
    await mongoose.connect(process.env.MONGODB, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        useFindAndModify: false,
      });

    // Start process - Read file (change file name as needed)
    const data = new Uint8Array(fs.readFileSync('scripts/Mismatching MC.xlsx'));
    const workbook = xlsx.read(data, {
        type: "array",
        cellText: false,
        cellDates: true,
        sheetStubs: true,
    });

    updateData(workbook).then(r =>  console.log("FINISH, total updated: ", r));

} catch (e) {
    console.log(e);
}

}

function getRows(workbook) {
    try {
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const submissions = [];

        for (let cell in worksheet) {
            const cellAsString = cell.toString();
            const rowNumber = Number(cellAsString.replace(/\D/g, ''));
            const dataStartRow = 2;
            const submissionIndex = rowNumber - dataStartRow;
            const rowName = cellAsString.replace(/[0-9]/g, '');

            if (submissionIndex >= 0 && worksheet[cell].v) {
                if (!submissions[submissionIndex]) {
                    submissions.push({
                        id: '',
                        mc: '',
                    })
                }
                if (rowName === "A") {
                    submissions[submissionIndex].id = worksheet[cell].v;
                }
                if (rowName === "E") {
                    submissions[submissionIndex].mc = worksheet[cell].v;                    
                }
            }
        }
        return submissions;
    } catch (e) {
        console.log("Error processing xlsx data: ", e);
        return []
    }

}


async function updateData(workbook) {
    try {

        let importCompanyLog = [['ID', 'Company Code', 'Update date', 'Action']];

        // Get company + bo info
        const submissions = getRows(workbook);


        console.log(submissions.length);


        for (let i = 0; i < submissions.length; i++) {
            console.log('processing ' + i + '  from ' + submissions.length)
            const submission = submissions[i];

            const existsSubmission = await FinancialReportModel.findById(submission.id);

            // create companies
            if (existsSubmission) {
                console.log(existsSubmission)
                existsSubmission.masterClientCode =  submission.mc;
                existsSubmission.companyData.masterclientcode = submission.mc
                await existsSubmission.save();
                
                importCompanyLog.push([existsSubmission._id.toString(), existsSubmission.mc, new Date(), 'UPDATED'])
            }
            else {
                importCompanyLog.push([submission.id, submission.mc,  new Date(), 'NOT FOUND'])
            }
        }
        // create entities bo
        console.log("companies updated ", importCompanyLog.length);

        const filename = 'update_afr_masterclientcode_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
        const logWorkbook = xlsx.utils.book_new();
        const logWorksheet1 = xlsx.utils.aoa_to_sheet(importCompanyLog);

        xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'companies ' + moment.utc().format('YYYY-MM-DD'));
        xlsx.writeFile(logWorkbook, filename);

        return { "success": true, "totalRows": importCompanyLog.length  };
    } catch (e) {
        console.log(e);
        return { "success": false };
    }
}


runScript();
const express = require("express");
const router = express.Router();

const clientManagementController = require("../controllers/client-management/clientManagementController");
const exportFilesController = require("../controllers/client-incorporation/clientExportController");
const messageController = require("../controllers/client-management/messageController");
const uploadMessageFilesController = require("../controllers/client-management/uploadMessageFilesController");
const downloadController = require("../controllers/downloadController");

router.get("/", ensureAuthenticated, clientManagementController.getDashboard);

router.get("/invitations", ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser, clientManagementController.getInvitations);
router.post("/invitations", ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser, clientManagementController.getInvitations);
router.post("/invitations/send", ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser, clientManagementController.sendInvitations);
router.get("/search-masterclients", ensureAuthenticated, clientManagementController.getSearchMasterclients);
router.post("/search-masterclients", ensureAuthenticated, clientManagementController.getSearchMasterclients);
router.post("/search-masterclients/update", ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser, clientManagementController.updateMasterclient);
router.post("/search-masterclients/clear-invitation", ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser, clientManagementController.clearInvitationMasterclient);
router.get("/search-masterclients/list",  ensureAuthenticated, clientManagementController.searchMasterclientList);
router.get("/search-masterclients/:id", ensureAuthenticated, clientManagementController.getMasterclientById);
router.get("/search-companies", ensureAuthenticated, clientManagementController.getSearchCompanies);
router.post("/search-companies", ensureAuthenticated, clientManagementController.getSearchCompanies);
router.post("/search-companies/update", ensureAuthenticated, clientManagementController.updateCompany);
router.post("/search-companies/update-name", ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser, clientManagementController.updateCompanyName);
router.get("/search-companies/:companyId/details", ensureAuthenticated, clientManagementController.getCompanyDetails);
router.get("/search-companies/:companyId/logs", ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser, clientManagementController.getCompanyChangeLogs);
router.get("/search-companies/:companyId/logs/:logId", ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser, clientManagementController.getCompanyLogDetails);
router.get("/unapproved-companies", ensureAuthenticated, clientManagementController.getUnapprovedCompanies);
router.post("/unapproved-companies/:companyId/approve", ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser, clientManagementController.approveCompany);
router.get("/search-users", ensureAuthenticated, clientManagementController.getSearchUsers);
router.post("/search-users/unlock", ensureAuthenticated, clientManagementController.unlockUser);
router.post("/search-users/block", ensureAuthenticated, clientManagementController.blockUser);
router.post("/search-users/clear-2fa", ensureAuthenticated, clientManagementController.clear2faUser);
router.get("/import", ensureAuthenticatedClientManagementSuperUser, clientManagementController.getImportData);
router.post("/import", ensureAuthenticatedClientManagementSuperUser, clientManagementController.saveImportData);
router.post("/import/load-file", ensureAuthenticatedClientManagementSuperUser, clientManagementController.processImport);
router.get("/delete-companies", ensureAuthenticatedClientManagementSuperUser, clientManagementController.getDeleteCompanies);
router.post("/delete-companies", ensureAuthenticatedClientManagementSuperUser, clientManagementController.saveDeleteCompanies);
router.post("/delete-companies/load-file", ensureAuthenticatedClientManagementSuperUser, clientManagementController.processDeleteCompanies);
router.get("/report-invoices", ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser, clientManagementController.getReportInvoicesView);
router.get("/report-invoices/download/:filename", ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser, clientManagementController.downloadReport);

router.get("/export-incorporations", ensureAuthenticated, clientManagementController.getExportIncorporations);
router.post("/export-incorporations", ensureAuthenticated, clientManagementController.getExportIncorporations);
router.post("/export-incorporations/export-xls", ensureAuthenticated, exportFilesController.doClientIncorporationExportXls);
router.post("/export-incorporations/export-evidences", ensureAuthenticatedClientManagementSuperUser, clientManagementController.doExportIncorporationFiles);


router.get("/announcements", ensureAuthenticated, messageController.getMessageDashboard);
router.get("/announcements/create", ensureAuthenticatedAnnouncements, messageController.getMessageView);
router.post("/announcements/create", ensureAuthenticatedAnnouncements, messageController.createMessage);
router.get("/announcements/:messageId", ensureAuthenticated, messageController.getMessageView);
router.put("/announcements/:messageId", ensureAuthenticatedAnnouncements, messageController.updateMessage);
router.delete("/announcements/:messageId", ensureAuthenticatedAnnouncements, messageController.deleteMessage);
router.get("/announcements/:messageId/details", ensureAuthenticated, messageController.getMessageDetails);

//POST temp store files
router.post('/announcements/upload-document', ensureAuthenticatedAnnouncements,
  uploadMessageFilesController.uploadMessageFile.fields([{name: 'fileUploaded', maxCount: 5}]),
  messageController.storeTempMessageFiles
);
router.get("/announcements/:messageId/files", ensureAuthenticated, messageController.getMessageFiles);
router.delete("/announcements/:messageId/files", ensureAuthenticatedAnnouncements, messageController.deleteMessageFile);
router.get("/announcements/:messageId/files/:fileId", ensureAuthenticated, downloadController.downloadMessageFile);

router.post("/move-mcc-files", ensureAuthenticatedAnnouncements, clientManagementController.startProcessToMoveMccFiles);

/*//Import Directors
router.get("/import-directors", ensureAuthenticatedDirectorManager, clientManagementController.getImportDirectorHistoryView);
router.get("/import-directors/new", ensureAuthenticatedDirectorManager, clientManagementController.getNewImportDirectorDataView);
router.post("/import-directors/load-file", ensureAuthenticatedDirectorManager, clientManagementController.uploadImportDirectorFile);
router.get("/import-directors/download/:filename", ensureAuthenticatedDirectorManager, clientManagementController.downloadArchiveVPDirectorFile);

*/
function ensureAuthenticated(req, res, next) {
    if (req.session.is_authenticated ) {
        if (req.session.authentication.isSubsManagers || req.session.authentication.isSubsSuperUser || req.session.authentication.isClientManagementSuperUser) {
            return next();
        } else {
            res.redirect("/not-authorized");
        }
    } else {
        res.redirect("/login");
    }
}

function ensureAuthenticatedClientManagementSuperUserAndSubstanceSuperUser(req, res, next) {
    if (req.session.is_authenticated ) {
        if (req.session.authentication.isClientManagementSuperUser || req.session.authentication.isSubsSuperUser) {
            return next();
        } else {
            res.redirect("/not-authorized");
        }
    } else {
        res.redirect("/login");
    }
}

function ensureAuthenticatedClientManagementSuperUser(req, res, next) {
    if (req.session.is_authenticated ) {
        if (req.session.authentication.isClientManagementSuperUser) {
            return next();
        } else {
            res.redirect("/not-authorized");
        }
    } else {
        res.redirect("/login");
    }
}

function ensureAuthenticatedAnnouncements(req, res, next) {
    if (req.session.is_authenticated ) {
        if ((req.session.authentication.isSubsManagers || req.session.authentication.isSubsSuperUser) && req.session.authentication.isAnnouncementManager) {
            return next();
        } else {
            res.redirect("/not-authorized");
        }
    } else {
        res.redirect("/login");
    }
}


module.exports = router;

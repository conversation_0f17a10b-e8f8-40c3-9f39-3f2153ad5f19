const mongoose = require('mongoose');

const MasterClientMessageSchema = new mongoose.Schema(
  {
    messageId: { type: mongoose.Schema.Types.ObjectId, ref: 'messages' },
    masterClientCode: { type: String, required: true },
    sentAt: { type: Date, required: false },
    openedAt: { type: Date, required: false },
    openedBy: { type: String, required: false },
    important: {type: Boolean, required: false},
  },
  {
    timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' }
  }
);

//Export model
module.exports = mongoose.model('masterclientmessages', MasterClientMessageSchema);

const EntryModel = require("../models/entry").EntryModel;
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config();


const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });
    
    const result = await updateEntriesArrayFields();

    console.log('Script ejecutado correctamente', result);
  } catch (error) {
    console.error('Error en el script:', error);
  } finally {
    mongoose.disconnect();
  }
}


async function updateEntriesArrayFields() {
  try {

    let updateLog = [['Entry ID', 'Company', 'Update date', 'Action']];

    const entriesToUpdate = await EntryModel.aggregate([
      {
        "$match": {
          $expr: {
            $or: [
              { $isArray: "$requested_information" },
              { $isArray: "$client_returned_information" },
              { $isArray: "$financial_period_changes" }
            ]
          }
        }
      },
      {
        $project: {
          _id: 1,
          company: 1,
          requested_information1: {
            $cond: {
              if: {
                $and: [
                  { $eq: [{ $type: "$requested_information" }, "object"] },
                  { $ne: [{ $ifNull: ["$requested_information.details", null] }, null] }
                ]
              },
              then: "$requested_information",
              else: {
                $cond: {
                  if: { $isArray: "$requested_information" },
                  then: { details: "$requested_information" },
                  else: { details: [] }
                }
              }
            }
          },
          client_returned_information1: {
            $cond: {
              if: {
                $and: [
                  { $eq: [{ $type: "$client_returned_information" }, "object"] },
                  { $ne: [{ $ifNull: ["$client_returned_information.details", null] }, null] }
                ]
              },
              then: "$client_returned_information",
              else: {
                $cond: {
                  if: { $isArray: "$client_returned_information" },
                  then: { details: "$client_returned_information" },
                  else: { details: [] }
                }
              }
            }
          },
          financial_period_changes1: {
            $cond: {
              if: {
                $and: [
                  { $eq: [{ $type: "$financial_period_changes" }, "object"] },
                  { $ne: [{ $ifNull: ["$financial_period_changes.details", null] }, null] }
                ]
              },
              then: "$financial_period_changes",
              else: {
                $cond: {
                  if: { $isArray: "$financial_period_changes" },
                  then: { details: "$financial_period_changes" },
                  else: { details: [] }
                }
              }
            }
          }
        }
      }
    ])

    console.log("entriesToUpdate length ", entriesToUpdate.length);
    if (entriesToUpdate.length > 0) {
      for (let i = 0; i < entriesToUpdate.length; i++) {
        console.log('processing ' + i + '  from ' + entriesToUpdate.length)

        const entry = entriesToUpdate[i];

        try {
          const result = await EntryModel.updateOne({ _id: entry._id }, {
            $set: {
              requested_information: entry.requested_information1,
              client_returned_information: entry.client_returned_information1,
              financial_period_changes: entry.financial_period_changes1
            }
          });

          if (result.nModified > 0) {
            updateLog.push([entry._id?.toString(), entry.company, new Date(), 'SUCCESS']);
          } else {
            updateLog.push([entry._id?.toString(), entry.company, new Date(), 'ERROR: NOT FOUND']);
          }
        } catch (error) {
          console.error('Error:', error.message);
          updateLog.push([entry._id?.toString(), entry.company, new Date(), 'ERROR UPDATING']);
        }
      }
    }


    // create entities bo
    console.log("entries updated ", updateLog.length -1);

    const filename = 'update_entries_date_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'entries ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalRows": updateLog.length -1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}


runScript();
const ObjectId = require('mongoose').Types.ObjectId;
const { ACCOUNTING_SERVICE_TYPES_LABELS} = require('../utils/financialReportConstants');
const { IGNORE_UPDATE_KEYS } = require('./constants');

function traverseSchema(schema, parentPath = '', document = {}) {
  const properties = [];

  schema.eachPath((path, schemaType) => {
    if (path === '_id' || schemaType instanceof ObjectId || document[path] === null || document[path] === "") {
      return;
    }

    const fullPath = parentPath ? `${parentPath}.${path}` : path;
    const order = schemaType.options.order || 0; // Use the specified order or default to 0
    const hideFromDisplay = schemaType.options.hideFromDisplay || false
    const isCalculated = schemaType.options.isCalculated || false

    if (schemaType.schema) {

      // It's a sub-schema, so recursively traverse it
      const subProperties = traverseSchema(schemaType.schema, fullPath, document[path] || {});
      properties.push(...subProperties);
    } else if (schemaType.options.isArray === true) {

      if (Array.isArray(document[path])) {
        const arrayProperties = [];

        document[path].forEach((arrayElement, index) => {
          const arrayElementFullPath = `${fullPath}.${index}`;
          const arrayElementOrder = schemaType.options.order || 0;
          let subProperties = traverseSchema(schemaType.options.childType, arrayElementFullPath, arrayElement);

          subProperties = subProperties.map(prop => ({
            ...prop,
            order: arrayElementOrder + prop.order,
            label: prop.label + ` (${index + 1})`
          }))

          arrayProperties.push(subProperties);
        });

        properties.push({
          path: fullPath,
          label: schemaType.options.label || path,
          value: arrayProperties, // Use the value from the document
          order: order,
          isArray: schemaType.options.isArray,
          hideFromDisplay: hideFromDisplay,
          isCalculated: isCalculated
        })
      }
    }
    else {
      // It's a leaf node, add the property with value and order

      const keys = path.split('.');

      let resultValue = document;
      for (const key of keys) {
        resultValue = resultValue[key];
      }

      const documentValue = schemaType.options.get ? schemaType.options.get(resultValue) : resultValue;

      properties.push({
        path: fullPath,
        label: schemaType.options.label || path,
        value: documentValue, // Use the value from the document
        order: order,
        hideFromDisplay: hideFromDisplay,
        isCalculated: isCalculated
      });
    }
  });



  // Sort the properties based on the order
  properties.sort((a, b) => a.order - b.order);

  return properties;
}


function getBooleanFormat(value){
    return value === true ? "Yes" : value === false ? "No" : "";
}

function getServiceTypeFormat(value){
    return ACCOUNTING_SERVICE_TYPES_LABELS[value];
}


function compareSchema(schema, parentPath = '', previousDocument = {}, currentDocument = {}) {
  const propertyChanges = [];

  if (previousDocument == undefined)
    previousDocument = {}

  if (currentDocument == undefined)
    currentDocument = {}


  // Loop through each path in the schema
  schema.eachPath((path, schemaType) => {
    if (IGNORE_UPDATE_KEYS.includes(path) || schemaType instanceof ObjectId || previousDocument[path] === null || previousDocument[path] === "") {
      return;
    }

    const fullPath = parentPath ? `${parentPath}.${path}` : path;

    if (schemaType.schema) {

      // It's a sub-schema, so recursively traverse it
      const subPropertyChanges = compareSchema(schemaType.schema, fullPath, previousDocument[path] || {}, currentDocument[path] || {});
      propertyChanges.push(...subPropertyChanges);
    } else {
      // It's a leaf node, add the property with value and order

      const keys = path.split('.');

      let previousResultValue = previousDocument;
      let currentResultValue = currentDocument;
      for (const key of keys) {
        previousResultValue = previousResultValue[key] ?? null;
        currentResultValue = currentResultValue[key] ?? null;
      }

      const previousDocumentValue = schemaType.options.get ? schemaType.options.get(previousResultValue) : previousResultValue;
      const currentDocumentValue = schemaType.options.get ? schemaType.options.get(currentResultValue) : currentResultValue;


      // Check for dates
      if (schemaType.options.type === Date) {
        if (previousDocumentValue?.toString() !== currentDocumentValue?.toString()) {
          propertyChanges.push({
            fieldPath: fullPath,
            oldValue: previousDocumentValue, // Use the value from the old document
            newValue: currentDocumentValue, // Use the value from the new document
          });
        }
      // Check for arrays
      } else if (Array.isArray(previousDocumentValue) || Array.isArray(currentDocumentValue)) {
        if (JSON.stringify(previousDocumentValue) !== JSON.stringify(currentDocumentValue)) {
          propertyChanges.push({
            fieldPath: fullPath,
            oldValue: previousDocumentValue, // Use the value from the old document
            newValue: currentDocumentValue, // Use the value from the new document
          });
        }
      } else {
        if (previousDocumentValue !== currentDocumentValue) {
          propertyChanges.push({
            fieldPath: fullPath,
            oldValue: previousDocumentValue, // Use the value from the old document
            newValue: currentDocumentValue, // Use the value from the new document
          });
        }
      }
    }
  });

  return propertyChanges;
}


function getObjectMappingFieldByFullPath(schema, fullPath) {
  const paths = fullPath.split('.');
  let currentPath = schema;

  for (const path of paths) {
    if (!Object.prototype.hasOwnProperty.call(currentPath, path)) {
      return null;
    }
    currentPath = currentPath[path];
  }

  return currentPath
}

module.exports = {
  compareSchema,
  traverseSchema,
  getBooleanFormat,
  getServiceTypeFormat,
  getObjectMappingFieldByFullPath
};
<table class="table table-striped">
    <thead>
    <tr>
        <th style="width: 30%;">Name</th>
        <th style="width: 25%;">Country</th>
        <th style="width: 25%;">Position</th>
        <th style="width: 20%;"></th>
    </tr>
    </thead>
    <tbody>
    {{#each positions}}
        <tr>
            <td style="text-transform: capitalize;">{{referenceId.details.fullName}}</td>
            <td style="text-transform: capitalize;">{{referenceId.details.countryBirth}}</td>
            <td style="text-transform: capitalize;">{{ type }}</td>
            <td class="text-center align-middle">
                <button
                        type="button"
                        class="btn solid royal-blue"
                        data-toggle="modal"
                        data-target="#openQualityPositionModal"
                        data-open-mode="{{../openMode}}"
                        data-review-id="{{../reviewId}}"
                        data-organization-id="{{../organizationId}}"
                        data-position-id="{{_id }}"
                >
                    Open
                </button>
            </td>
        </tr>
    {{else}}
        <tr>
            <td colspan="8" class="text-center font-italic">
                There are no positions.
            </td>
        </tr>
    {{/each}}
    </tbody>
</table>


{{!-- CONFIRM MODAL --}}
<div class="modal fade" id="confirmQualityButtonsModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div  class="modal-body p-3 text-justify">
                <div id="message_modal">

                </div>
                <div class="mt-1 text-justify">
                    <label for="modalInputComment"><small></small></label> <br>
                    <textarea class="form-control" name="modalInputComment" id="modalInputComment" placeholder="Add comment..." rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="button" class="btn solid royal-blue" id="sendButton" data-status="send-officer">Confirm</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    const modalInfo = {
        "sendOfficer": {
            "modalMessage": 'You are about to submit the File Review back to the <b>File Review Officer.</b> Are you sure?',
            "validatedMessage": 'You are about to submit the File Review back to the <b>File Review Officer.</b> Are you sure?',
            "successMessage": 'The review has been submitted to the <b>Review Officer</b> successfully.',
            "errorMessage": 'There was an error submitting the review to the <b>Review Officer</b>'
        },
        "sendClient": {
            "modalMessage": 'You are about to submit the File Review to the <b>Client.</b>',
            "validatedMessage": 'You are about to submit the File Review to the <b>Client.</b> Are you sure? All the non-validated files/information will be requested from the client.',
            "successMessage": 'The review has been submitted to the <b>Client</b> successfully.',
            "errorMessage": 'There was an error submitting the review to the <b>Client.</b>'
        },
        "compliance": {
            "modalMessage": 'You are about to submit the File Review to <b>Compliance.</b>',
            "validatedMessage": 'You are about to submit the File Review to <b>Compliance.</b>',
            "successMessage": 'The review has been submitted to the <b>Compliance</b> successfully.',
            "errorMessage": 'There was an error submitting the review to the <b>Compliance.</b>'
        }
    };
    
    let status = '';
    let statusId = '';
    let comment = '';

    $('#confirmQualityButtonsModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        status = button.data('status');
        statusId = button.data('id');
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/quality-assurance-review/'+ statusId + '/validate-review',
            data: {},
            success: function (response) {
                if (response.success){
                    if (response.isIncomplete === true) {
                        $('#message_modal').html(modalInfo[status].validatedMessage);
                    }
                    else {
                        $('#message_modal').html(modalInfo[status].modalMessage);
                    }

                }
                else{
                    $('#message_modal').html(modalInfo[status].errorMessage);
                }

            },
            error: function () {
                Swal.fire('Error', modalInfo[status].errorMessage, 'error').then(() => {
                    $('#confirmValidateModal').modal('hide');
                });
            },
        });
        
    });
    $('#sendButton').on('click', function () {
        comment = $('#modalInputComment').val();
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/quality-assurance-review/'+ statusId + '/submit-review',
            data: { status: status, comment: comment },
            success: () => {
                $('#confirmQualityButtonsModal').modal('hide');
                Swal.fire('Success', modalInfo[status].successMessage, 'success').then(() => {
                    location.href = '/file-reviewer/quality-assurance-list';
                });
            },
            error: (err) => {
                $('#confirmQualityButtonsModal').modal('hide');
                Swal.fire('Error', modalInfo[status].errorMessage, 'error');
            },
        });
    });
</script>

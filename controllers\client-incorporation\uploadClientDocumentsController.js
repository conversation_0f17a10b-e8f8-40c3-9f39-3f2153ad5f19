
const multer = require('multer');
const multerAzureStorage = require('../../classes/MulterAzureStorage');
const { renameBlob } = require('../../utils/azureStorage');

let removeIncorporationFileReference = function (files, originalname) {
    for (let idx = 0; idx < files.length; idx++) {
        if (files[idx].originalname == originalname) {
            files.splice(idx, 1);
            break;
        }
    }
    return files;
}


exports.uploadIncorporationFile = multer({
    storage: new multerAzureStorage({
        containerName: process.env.AZURE_STORAGE_CONTAINER_CLIENT_INCORPORATION,
        accessKey: process.env.AZURE_STORAGE_ACCESS_KEY,
        accountName: process.env.AZURE_STORAGE_ACCOUNT,
    }),
});

let doMoveIncorporationUpload = async function (incorporation, file, fileType) {
    try {
      if (file) {
        const newName = incorporation._id + '/' + file.blobName.replace('Document', fileType);
        await renameBlob(
          process.env.AZURE_STORAGE_ACCOUNT,
          process.env.AZURE_STORAGE_ACCESS_KEY,
          process.env.AZURE_STORAGE_CONTAINER_CLIENT_INCORPORATION, 
          file.blobName,
          newName
        );
      }
    } catch (error) {
        console.log("ERROR: ", error);
    }
};

exports.moveIncorporationUpload = doMoveIncorporationUpload;

exports.saveUpload = function (req, res) {
    try {
        let sessData = req.session;
        const uploadedFiles = req.files['Document'];
        const incorporationId = req.params.incorporationId;
        if (!incorporationId){
            return res.sendStatus(500)
        }
        if (uploadedFiles && uploadedFiles.length > 0) {
            const incorporationFiles = sessData[incorporationId] ?sessData[incorporationId] : {} ;

            if (incorporationFiles[req.body.fieldGroup]) {
                //append
                incorporationFiles[req.body.fieldGroup] = incorporationFiles[req.body.fieldGroup].concat(uploadedFiles)
            } else {
                incorporationFiles[req.body.fieldGroup] = uploadedFiles
            }
            sessData[incorporationId] = incorporationFiles;
            res.sendStatus(200)
        }
    }catch (e) {
        console.log(e);
        return res.sendStatus(500)
    }

};


exports.deleteUpload = function (req, res) {

    let sessData = req.session;
    const incorporationId = req.params.incorporationId;
    if (!incorporationId){
        return res.sendStatus(500);
    }
    let documents = sessData[incorporationId];
    if (documents[req.body.group]){
        documents[req.body.group] = removeIncorporationFileReference(documents[req.body.group], req.body.name);
    }

    sessData[incorporationId] = documents;
    res.sendStatus(200);
};



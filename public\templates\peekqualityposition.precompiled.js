(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['peekqualityposition'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":7,"column":16},"end":{"line":11,"column":23}}})) != null ? stack1 : "");
},"2":function(container,depth0,helpers,partials,data) {
    return "                    <span class=\"badge badge-success\">Complete</span>\r\n";
},"4":function(container,depth0,helpers,partials,data) {
    return "                    <span class=\"badge badge-warning text-dark\">Incomplete</span>\r\n";
},"6":function(container,depth0,helpers,partials,data) {
    return " ";
},"8":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <div class=\"custom-control custom-checkbox\">\r\n                        <input\r\n                                type=\"checkbox\"\r\n                                class=\"custom-control-input\"\r\n                                name=\"details[validated]\"\r\n                                id=\"details[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1),{"name":"if","hash":{},"fn":container.program(9, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":48,"column":28},"end":{"line":48,"column":89}}})) != null ? stack1 : "")
    + "\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,(depth0 != null ? lookupProperty(depth0,"onlyRead") : depth0),{"name":"if","hash":{},"fn":container.program(11, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":49,"column":28},"end":{"line":49,"column":61}}})) != null ? stack1 : "")
    + "\r\n                        />\r\n                        <label class=\"custom-control-label\"\r\n                               for=\"details[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                        >Validate</label\r\n                        >\r\n                    </div>\r\n";
},"9":function(container,depth0,helpers,partials,data) {
    return " checked ";
},"11":function(container,depth0,helpers,partials,data) {
    return " disabled ";
},"13":function(container,depth0,helpers,partials,data) {
    return "                    <i class=\"fa fa-lock\"></i>\r\n";
},"15":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"identification") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":69,"column":16},"end":{"line":73,"column":23}}})) != null ? stack1 : "");
},"17":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <div class=\"custom-control custom-checkbox\">\r\n                        <input\r\n                                type=\"checkbox\"\r\n                                class=\"custom-control-input\"\r\n                                name=\"identification[validated]\"\r\n                                id=\"identification[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"identification") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"identification") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1),{"name":"if","hash":{},"fn":container.program(9, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":99,"column":28},"end":{"line":99,"column":96}}})) != null ? stack1 : "")
    + "\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,(depth0 != null ? lookupProperty(depth0,"onlyRead") : depth0),{"name":"if","hash":{},"fn":container.program(11, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":100,"column":28},"end":{"line":100,"column":61}}})) != null ? stack1 : "")
    + "\r\n                        />\r\n                        <label class=\"custom-control-label\"\r\n                               for=\"identification[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"identification") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                        >Validate</label\r\n                        >\r\n                    </div>\r\n";
},"19":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":120,"column":16},"end":{"line":124,"column":23}}})) != null ? stack1 : "");
},"21":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <div class=\"custom-control custom-checkbox\">\r\n                        <input\r\n                                type=\"checkbox\"\r\n                                class=\"custom-control-input\"\r\n                                name=\"principalAddress[validated]\"\r\n                                id=\"principalAddress[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1),{"name":"if","hash":{},"fn":container.program(9, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":155,"column":28},"end":{"line":155,"column":98}}})) != null ? stack1 : "")
    + "\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,(depth0 != null ? lookupProperty(depth0,"onlyRead") : depth0),{"name":"if","hash":{},"fn":container.program(11, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":156,"column":28},"end":{"line":156,"column":61}}})) != null ? stack1 : "")
    + "\r\n                        />\r\n                        <label class=\"custom-control-label\"\r\n                               for=\"principalAddress[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                        >Validate</label\r\n                        >\r\n                    </div>\r\n";
},"23":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"mailingAddress") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":176,"column":16},"end":{"line":180,"column":23}}})) != null ? stack1 : "");
},"25":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <div class=\"custom-control custom-checkbox\">\r\n                        <input\r\n                                type=\"checkbox\"\r\n                                class=\"custom-control-input\"\r\n                                name=\"mailingAddress[validated]\"\r\n                                id=\"mailingAddress[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"mailingAddress") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"mailingAddress") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1),{"name":"if","hash":{},"fn":container.program(9, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":211,"column":28},"end":{"line":211,"column":96}}})) != null ? stack1 : "")
    + "\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,(depth0 != null ? lookupProperty(depth0,"onlyRead") : depth0),{"name":"if","hash":{},"fn":container.program(11, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":212,"column":28},"end":{"line":212,"column":61}}})) != null ? stack1 : "")
    + "\r\n                        />\r\n                        <label class=\"custom-control-label\"\r\n                               for=\"mailingAddress[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"mailingAddress") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                        >Validate</label\r\n                        >\r\n                    </div>\r\n";
},"27":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"taxResidence") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":232,"column":16},"end":{"line":236,"column":23}}})) != null ? stack1 : "");
},"29":function(container,depth0,helpers,partials,data) {
    return "Confirmed ";
},"31":function(container,depth0,helpers,partials,data) {
    return " Not Confirmed ";
},"33":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <div class=\"custom-control custom-checkbox\">\r\n                        <input\r\n                                type=\"checkbox\"\r\n                                class=\"custom-control-input\"\r\n                                name=\"taxResidence[validated]\"\r\n                                id=\"taxResidence[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"taxResidence") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"taxResidence") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1),{"name":"if","hash":{},"fn":container.program(9, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":257,"column":28},"end":{"line":257,"column":94}}})) != null ? stack1 : "")
    + "\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,(depth0 != null ? lookupProperty(depth0,"onlyRead") : depth0),{"name":"if","hash":{},"fn":container.program(11, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":258,"column":28},"end":{"line":258,"column":61}}})) != null ? stack1 : "")
    + "\r\n                        />\r\n                        <label class=\"custom-control-label\"\r\n                               for=\"taxResidence[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"taxResidence") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                        >Validate</label\r\n                        >\r\n                    </div>\r\n";
},"35":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":278,"column":16},"end":{"line":282,"column":23}}})) != null ? stack1 : "");
},"37":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <div class=\"custom-control custom-checkbox\">\r\n                        <input\r\n                                type=\"checkbox\"\r\n                                class=\"custom-control-input\"\r\n                                name=\"advisorDetails[validated]\"\r\n                                id=\"advisorDetails[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1),{"name":"if","hash":{},"fn":container.program(9, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":319,"column":28},"end":{"line":319,"column":96}}})) != null ? stack1 : "")
    + "\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,(depth0 != null ? lookupProperty(depth0,"onlyRead") : depth0),{"name":"if","hash":{},"fn":container.program(11, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":320,"column":28},"end":{"line":320,"column":61}}})) != null ? stack1 : "")
    + "\r\n                        />\r\n                        <label class=\"custom-control-label\"\r\n                               for=\"advisorDetails[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                        >Validate</label\r\n                        >\r\n                    </div>\r\n";
},"39":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":340,"column":16},"end":{"line":344,"column":23}}})) != null ? stack1 : "");
},"41":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <div class=\"custom-control custom-checkbox\">\r\n                        <input\r\n                                type=\"checkbox\"\r\n                                class=\"custom-control-input\"\r\n                                name=\"residentialAddress[validated]\"\r\n                                id=\"residentialAddress[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1),{"name":"if","hash":{},"fn":container.program(9, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":375,"column":28},"end":{"line":375,"column":100}}})) != null ? stack1 : "")
    + "\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,(depth0 != null ? lookupProperty(depth0,"onlyRead") : depth0),{"name":"if","hash":{},"fn":container.program(11, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":376,"column":28},"end":{"line":376,"column":61}}})) != null ? stack1 : "")
    + "\r\n                        />\r\n                        <label class=\"custom-control-label\"\r\n                               for=\"residentialAddress[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                        >Validate</label\r\n                        >\r\n                    </div>\r\n";
},"43":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"worldCheck") : stack1)) != null ? lookupProperty(stack1,"complete") : stack1),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":395,"column":16},"end":{"line":399,"column":23}}})) != null ? stack1 : "");
},"45":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <div class=\"custom-control custom-checkbox\">\r\n                        <input\r\n                                type=\"checkbox\"\r\n                                class=\"custom-control-input\"\r\n                                name=\"worldCheck[validated]\"\r\n                                id=\"worldCheck[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"worldCheck") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"worldCheck") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1),{"name":"if","hash":{},"fn":container.program(9, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":412,"column":28},"end":{"line":412,"column":92}}})) != null ? stack1 : "")
    + "\r\n                            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,(depth0 != null ? lookupProperty(depth0,"onlyRead") : depth0),{"name":"if","hash":{},"fn":container.program(11, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":413,"column":28},"end":{"line":413,"column":61}}})) != null ? stack1 : "")
    + "\r\n                        />\r\n                        <label class=\"custom-control-label\"\r\n                               for=\"worldCheck[validated]-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"positionInformation") : depth0)) != null ? lookupProperty(stack1,"worldCheck") : stack1)) != null ? lookupProperty(stack1,"validated") : stack1), depth0))
    + "\"\r\n                        >Validate</label\r\n                        >\r\n                    </div>\r\n";
},"47":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <tr>\r\n                    <td style=\"text-transform: capitalize;\">"
    + alias4(((helper = (helper = lookupProperty(helpers,"external") || (depth0 != null ? lookupProperty(depth0,"external") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"external","hash":{},"data":data,"loc":{"start":{"line":444,"column":60},"end":{"line":444,"column":72}}}) : helper)))
    + "</td>\r\n                    <td style=\"text-transform: capitalize;\">"
    + alias4(((helper = (helper = lookupProperty(helpers,"fileGroup") || (depth0 != null ? lookupProperty(depth0,"fileGroup") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileGroup","hash":{},"data":data,"loc":{"start":{"line":445,"column":60},"end":{"line":445,"column":73}}}) : helper)))
    + "</td>\r\n                    <td class=\"text-center\" style=\"text-transform: capitalize;\">\r\n                        <div class=\"custom-control custom-checkbox\">\r\n                            <input type=\"checkbox\"\r\n                                   disabled\r\n                                   class=\"custom-control-input\"\r\n                                   id=\"standardFilePresent-"
    + alias4(((helper = (helper = lookupProperty(helpers,"key") || (data && lookupProperty(data,"key"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"key","hash":{},"data":data,"loc":{"start":{"line":451,"column":59},"end":{"line":451,"column":69}}}) : helper)))
    + "\"\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"present") : depth0),{"name":"if","hash":{},"fn":container.program(48, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":452,"column":32},"end":{"line":454,"column":39}}})) != null ? stack1 : "")
    + "                            />\r\n                            <label\r\n                                    class=\"custom-control-label\"\r\n                                    for=\"standardFilePresent-"
    + alias4(((helper = (helper = lookupProperty(helpers,"key") || (data && lookupProperty(data,"key"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"key","hash":{},"data":data,"loc":{"start":{"line":458,"column":61},"end":{"line":458,"column":71}}}) : helper)))
    + "\"\r\n                            ></label>\r\n                        </div>\r\n                    </td>\r\n                    <td style=\"text-transform: capitalize;\">"
    + alias4(((helper = (helper = lookupProperty(helpers,"explanation") || (depth0 != null ? lookupProperty(depth0,"explanation") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"explanation","hash":{},"data":data,"loc":{"start":{"line":462,"column":60},"end":{"line":462,"column":77}}}) : helper)))
    + "</td>\r\n                    <td class=\"text-center align-middle\">\r\n                        <button class=\"btn solid royal-blue download-button\"\r\n                                id=\"standardFileDownload-"
    + alias4(((helper = (helper = lookupProperty(helpers,"key") || (data && lookupProperty(data,"key"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"key","hash":{},"data":data,"loc":{"start":{"line":465,"column":57},"end":{"line":465,"column":67}}}) : helper)))
    + "\"\r\n                                type=\"button\"\r\n                                data-toggle=\"modal\"\r\n                                data-target=\"#downloadFileModal\"\r\n                                data-review-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"reviewId") : depths[1]), depth0))
    + "\"\r\n                                data-relation-id=\""
    + alias4(alias5(((stack1 = (depths[1] != null ? lookupProperty(depths[1],"position") : depths[1])) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\"\r\n                                data-file-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":471,"column":46},"end":{"line":471,"column":54}}}) : helper)))
    + "\"\r\n                                data-file-group=\""
    + alias4(alias5(((stack1 = (depths[1] != null ? lookupProperty(depths[1],"position") : depths[1])) != null ? lookupProperty(stack1,"type") : stack1), depth0))
    + "\"\r\n                            "
    + ((stack1 = lookupProperty(helpers,"unless").call(alias1,(depth0 != null ? lookupProperty(depth0,"present") : depth0),{"name":"unless","hash":{},"fn":container.program(11, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":473,"column":28},"end":{"line":473,"column":68}}})) != null ? stack1 : "")
    + "\r\n                        >Download\r\n                        </button>\r\n                    </td>\r\n                    <td class=\"text-center align-middle\">\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depths[1] != null ? lookupProperty(depths[1],"position") : depths[1])) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depths[1] != null ? lookupProperty(depths[1],"reviewId") : depths[1]),{"name":"ifCond","hash":{},"fn":container.program(50, data, 0, blockParams, depths),"inverse":container.program(53, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":478,"column":24},"end":{"line":495,"column":35}}})) != null ? stack1 : "")
    + "                    </td>\r\n                </tr>\r\n";
},"48":function(container,depth0,helpers,partials,data) {
    return "                                   checked\r\n";
},"50":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                            <div class=\"custom-control custom-checkbox\">\r\n                                <input type=\"checkbox\" name=\"files["
    + alias4(((helper = (helper = lookupProperty(helpers,"fileGroup") || (depth0 != null ? lookupProperty(depth0,"fileGroup") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileGroup","hash":{},"data":data,"loc":{"start":{"line":480,"column":67},"end":{"line":480,"column":82}}}) : helper)))
    + "]["
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":480,"column":84},"end":{"line":480,"column":90}}}) : helper)))
    + "]\"\r\n                                       class=\"custom-control-input\"\r\n                                       id=\"standard-file-validated-"
    + alias4(((helper = (helper = lookupProperty(helpers,"fileGroup") || (depth0 != null ? lookupProperty(depth0,"fileGroup") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileGroup","hash":{},"data":data,"loc":{"start":{"line":482,"column":67},"end":{"line":482,"column":82}}}) : helper)))
    + "-"
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":482,"column":83},"end":{"line":482,"column":89}}}) : helper)))
    + "\"\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"validated") : depth0),{"name":"if","hash":{},"fn":container.program(51, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":483,"column":36},"end":{"line":485,"column":43}}})) != null ? stack1 : "")
    + "                                    "
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depths[1] != null ? lookupProperty(depths[1],"onlyRead") : depths[1]),{"name":"if","hash":{},"fn":container.program(11, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":486,"column":36},"end":{"line":486,"column":72}}})) != null ? stack1 : "")
    + "\r\n                                />\r\n                                <label\r\n                                        class=\"custom-control-label\"\r\n                                        for=\"standard-file-validated-"
    + alias4(((helper = (helper = lookupProperty(helpers,"fileGroup") || (depth0 != null ? lookupProperty(depth0,"fileGroup") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileGroup","hash":{},"data":data,"loc":{"start":{"line":490,"column":69},"end":{"line":490,"column":84}}}) : helper)))
    + "-"
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":490,"column":85},"end":{"line":490,"column":91}}}) : helper)))
    + "\"\r\n                                ></label>\r\n                            </div>\r\n";
},"51":function(container,depth0,helpers,partials,data) {
    return "                                       checked\r\n";
},"53":function(container,depth0,helpers,partials,data) {
    return "                            <i class=\"fa fa-lock\"></i>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3=container.lambda, alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<form id=\"qualityPositionForm\" method=\"POST\" autocomplete=\"off\" action=\"#\">\r\n    <!--PERSONAL DETAILS -->\r\n    <div>\r\n        <p>\r\n            DETAILS\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":6,"column":12},"end":{"line":12,"column":23}}})) != null ? stack1 : "")
    + "        </p>\r\n        <div class=\"row\">\r\n            <div class=\"col-2\">Full Name:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"fullName") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">First Name:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"firstName") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row pt-2\">\r\n            <div class=\"col-2\">Middle Name:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"middleName") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Last Name:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"lastName") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row pt-2\">\r\n            <div class=\"col-2\">Occupation:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"occupation") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Date of Birth:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"birthDate") : stack1),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(6, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":30,"column":48},"end":{"line":31,"column":93}}})) != null ? stack1 : "")
    + "</div>\r\n        </div>\r\n        <div class=\"row pt-2\">\r\n            <div class=\"col-2\">Nationality:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"nationality") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Country of Birth:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"countryBirth") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row\">\r\n            <div class=\"col-12 d-flex justify-content-end\">\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(8, data, 0, blockParams, depths),"inverse":container.program(13, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":41,"column":16},"end":{"line":58,"column":27}}})) != null ? stack1 : "")
    + "            </div>\r\n        </div>\r\n        <hr class=\"mt-3\"/>\r\n    </div>\r\n\r\n    <!--IDENTIFICATION DETAILS -->\r\n    <div>\r\n        <p>\r\n            IDENTIFICATION\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(15, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":68,"column":12},"end":{"line":74,"column":23}}})) != null ? stack1 : "")
    + "        </p>\r\n        <div class=\"row\">\r\n            <div class=\"col-2\">Type of Identification:</div>\r\n            <div class=\"col-4 font-weight-bold\" style=\"text-transform: capitalize;\">\r\n                "
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"identification") : stack1)) != null ? lookupProperty(stack1,"identificationType") : stack1), depth0))
    + "\r\n            </div>\r\n            <div class=\"col-2\">Country of Issue:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"identification") : stack1)) != null ? lookupProperty(stack1,"issueCountry") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row pt-2\">\r\n            <div class=\"col-2\">Expiry Date:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"identification") : stack1)) != null ? lookupProperty(stack1,"expiryDate") : stack1),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(6, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":86,"column":48},"end":{"line":87,"column":93}}})) != null ? stack1 : "")
    + "</div>\r\n        </div>\r\n        <!-- CHECK VALIDATE -->\r\n        <div class=\"row\">\r\n            <div class=\"col-12 d-flex justify-content-end\">\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(17, data, 0, blockParams, depths),"inverse":container.program(13, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":92,"column":16},"end":{"line":109,"column":27}}})) != null ? stack1 : "")
    + "            </div>\r\n        </div>\r\n        <hr class=\"mt-3\"/>\r\n    </div>\r\n\r\n    <!--PRINCIPAL ADDRESS -->\r\n    <div>\r\n        <p>\r\n            PRINCIPAL ADDRESS\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(19, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":119,"column":12},"end":{"line":125,"column":23}}})) != null ? stack1 : "")
    + "        </p>\r\n        <div class=\"row\">\r\n            <div class=\"col-2\">Address - 1st Line:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"primaryAddress") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Address - 2nd Line:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"secondaryAddress") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row pt-2\">\r\n            <div class=\"col-2\">Country:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"country") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">State:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"state") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row pt-2\">\r\n            <div class=\"col-2\">City:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"city") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Postal Code:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"principalAddress") : stack1)) != null ? lookupProperty(stack1,"postalCode") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <!-- CHECK VALIDATE -->\r\n        <div class=\"row\">\r\n            <div class=\"col-12 d-flex justify-content-end\">\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(21, data, 0, blockParams, depths),"inverse":container.program(13, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":148,"column":16},"end":{"line":165,"column":27}}})) != null ? stack1 : "")
    + "            </div>\r\n        </div>\r\n        <hr class=\"mt-3\"/>\r\n    </div>\r\n\r\n    <!--MAILING ADDRESS-->\r\n    <div>\r\n        <p>\r\n            MAILING ADDRESS\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(23, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":175,"column":12},"end":{"line":181,"column":23}}})) != null ? stack1 : "")
    + "        </p>\r\n        <div class=\"row\">\r\n            <div class=\"col-2\">Address - 1st Line:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"primaryAddress") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Address - 2nd Line:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"secondaryAddress") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row pt-2\">\r\n            <div class=\"col-2\">Country:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"country") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">State:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"state") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row pt-2\">\r\n            <div class=\"col-2\">City:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"city") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Postal Code:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"postalCode") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <!-- CHECK VALIDATE -->\r\n        <div class=\"row\">\r\n            <div class=\"col-12 d-flex justify-content-end\">\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(25, data, 0, blockParams, depths),"inverse":container.program(13, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":204,"column":16},"end":{"line":221,"column":27}}})) != null ? stack1 : "")
    + "            </div>\r\n        </div>\r\n        <hr class=\"mt-3\"/>\r\n    </div>\r\n\r\n    <!--TAX ADVICE -->\r\n    <div>\r\n        <p>\r\n            TAX ADVICE\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(27, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":231,"column":12},"end":{"line":237,"column":23}}})) != null ? stack1 : "")
    + "        </p>\r\n        <div class=\"row\">\r\n            <div class=\"col-6\">Confirmation Regarding Legal / Tax Advice:</div>\r\n            <div class=\"col-6 font-weight-bold\">\r\n                "
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"taxResidence") : stack1)) != null ? lookupProperty(stack1,"confirmation") : stack1),{"name":"if","hash":{},"fn":container.program(29, data, 0, blockParams, depths),"inverse":container.program(31, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":242,"column":16},"end":{"line":242,"column":98}}})) != null ? stack1 : "")
    + "\r\n            </div>\r\n            <div class=\"col-6\">Tax Residence:</div>\r\n            <div class=\"col-6 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"taxResidence") : stack1)) != null ? lookupProperty(stack1,"taxResidence") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <!-- CHECK VALIDATE -->\r\n        <div class=\"row\">\r\n            <div class=\"col-12 d-flex justify-content-end\">\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(33, data, 0, blockParams, depths),"inverse":container.program(13, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":250,"column":16},"end":{"line":267,"column":27}}})) != null ? stack1 : "")
    + "            </div>\r\n        </div>\r\n        <hr class=\"mt-3\"/>\r\n    </div>\r\n\r\n    <!--ADVISOR DETAILS-->\r\n    <div>\r\n        <p>\r\n            ADVISOR DETAILS\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(35, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":277,"column":12},"end":{"line":283,"column":23}}})) != null ? stack1 : "")
    + "        </p>\r\n        <div class=\"row\">\r\n            <div class=\"col-2\">First Name:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"firstName") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Middle Name:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"middleName") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row\">\r\n            <div class=\"col-2\">Last Name:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"lastName") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Name of Firm:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"firmName") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row\">\r\n            <div class=\"col-2\">Phone:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"phone") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">E-mail:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"email") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row\">\r\n            <div class=\"col-2\">Nationality:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"nationality") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Country of Incorporation:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"advisorDetails") : stack1)) != null ? lookupProperty(stack1,"incorporationCountry") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <!-- CHECK VALIDATE -->\r\n        <div class=\"row\">\r\n            <div class=\"col-12 d-flex justify-content-end\">\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(37, data, 0, blockParams, depths),"inverse":container.program(13, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":312,"column":16},"end":{"line":329,"column":27}}})) != null ? stack1 : "")
    + "            </div>\r\n        </div>\r\n        <hr class=\"mt-3\"/>\r\n    </div>\r\n\r\n    <!--PRINCIPAL ADVISOR ADDRESS -->\r\n    <div>\r\n        <p>\r\n            PRINCIPAL ADVISOR ADDRESS\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(39, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":339,"column":12},"end":{"line":345,"column":23}}})) != null ? stack1 : "")
    + "        </p>\r\n        <div class=\"row\">\r\n            <div class=\"col-2\">Address - 1st Line:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"primaryAddress") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Address - 2nd Line:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"secondaryAddress") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row pt-2\">\r\n            <div class=\"col-2\">Country:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"country") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">State:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"state") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <div class=\"row pt-2\">\r\n            <div class=\"col-2\">City:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"city") : stack1), depth0))
    + "</div>\r\n            <div class=\"col-2\">Postal Code:</div>\r\n            <div class=\"col-4 font-weight-bold\">"
    + alias4(alias3(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"residentialAddress") : stack1)) != null ? lookupProperty(stack1,"postalCode") : stack1), depth0))
    + "</div>\r\n        </div>\r\n        <!-- CHECK VALIDATE -->\r\n        <div class=\"row\">\r\n            <div class=\"col-12 d-flex justify-content-end\">\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(41, data, 0, blockParams, depths),"inverse":container.program(13, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":368,"column":16},"end":{"line":385,"column":27}}})) != null ? stack1 : "")
    + "            </div>\r\n        </div>\r\n        <hr class=\"mt-3\"/>\r\n    </div>\r\n    <!-- WORLD CHECK -->\r\n    <div>\r\n        <p>\r\n            WORLD CHECK\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(43, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":394,"column":12},"end":{"line":400,"column":23}}})) != null ? stack1 : "")
    + "        </p>\r\n        <!-- CHECK VALIDATE -->\r\n        <div class=\"row\">\r\n            <div class=\"col-12 d-flex justify-content-end\">\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||alias2).call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? lookupProperty(stack1,"lockedByFileReview") : stack1),"==",(depth0 != null ? lookupProperty(depth0,"reviewId") : depth0),{"name":"ifCond","hash":{},"fn":container.program(45, data, 0, blockParams, depths),"inverse":container.program(13, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":405,"column":16},"end":{"line":422,"column":27}}})) != null ? stack1 : "")
    + "            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div>\r\n        <hr class=\"mt-3\"/>\r\n        <p>FILES</p>\r\n        <table class=\"table\">\r\n            <thead>\r\n            <tr>\r\n                <th>Name</th>\r\n                <th style=\"width: 10%\">Group</th>\r\n                <th style=\"width: 10%\">Present</th>\r\n                <th>Explanation</th>\r\n                <th style=\"width: 10%\">Download</th>\r\n                <th style=\"width: 10%\">Validate</th>\r\n            </tr>\r\n            </thead>\r\n            <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(alias1,(depth0 != null ? lookupProperty(depth0,"positionFiles") : depth0),{"name":"each","hash":{},"fn":container.program(47, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":442,"column":12},"end":{"line":498,"column":21}}})) != null ? stack1 : "")
    + "            </tbody>\r\n        </table>\r\n    </div>\r\n</form>\r\n\r\n\r\n\r\n";
},"useData":true,"useDepths":true});
})();
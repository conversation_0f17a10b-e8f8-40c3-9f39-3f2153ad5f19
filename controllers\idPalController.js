const ConfigurationModel = require('../models/configuration');

const fetch = require("node-fetch");

exports.generateUuid = async function (idPalAccountId) {
  try {

    const configuration = await ConfigurationModel.findOne({});
    const idPalURL = process.env.ID_PAL_SEND_APPLICATION_URL;
    const idPalClientKey = process.env.ID_PAL_CLIENT_KEY;
    const idPalAccessKey = process.env.ID_PAL_ACCESS_KEY;
    const idPalProfileId = process.env.ID_PAL_PROFILE_ID;

    if (!configuration || (configuration && !configuration.idPalConfiguration)) {
      return {status: 500, message: "ID-PAL token configuration not found"};
    }

    if (!idPalURL || !idPalClientKey || !idPalAccessKey || !idPalProfileId || !idPalAccountId) {
      return {status: 500, message: "ID-PAL has missing values"};
    }

    let body = {
      "client_key": idPal<PERSON><PERSON><PERSON>ey,
      "access_key": idPalAccess<PERSON>ey,
      "profile_id": idPalProfileId,
      "account_id": idPalAccountId,
      "language": "en",
    };

    let options = {
      method: 'post',
      body: JSON.stringify(body),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': "Bearer " + configuration.idPalConfiguration.accessToken
      },
    };

    let response = await fetch(idPalURL, options);
    let content = await response.json();

    if (!response.status || response.status !== 200) {
      if (response.status === 401) {
        const responseToken = await generateAuthenticationToken(configuration);
        if (responseToken.status === 200) {
          configuration.idPalConfiguration.accessToken = responseToken.accessToken;
          configuration.idPalConfiguration.refreshToken = responseToken.refreshToken;
          await configuration.save();
          options.headers.Authorization = "Bearer " + responseToken.accessToken;
          let response2 = await fetch(idPalURL, options);
          content = await response2.json();
          if (response2.status !== 200) {
            return {status: response2.status || 500, message: content}
          }
        } else {
          return responseToken
        }
      } else {
        return {status: response.status || 500, message: content}
      }
    }

    if (content && (content.status === 0 || content.status === 25)) {
      return {
        status: 200,
        message: "UUID generated",
        uuid: content.uuid
      }
    } else {
      return {
        status: 500,
        message: content.message,
      }
    }

  }catch (e) {
    console.log(e);
    return {
      status: 500,
      message: "Internal Server Error"
    }
  }
};

exports.cancelUuid = async function (uuid) {
  try {

    const configuration = await ConfigurationModel.findOne({});
    const idPalURL = process.env.ID_PAL_CANCEL_UUID;
    const idPalClientKey = process.env.ID_PAL_CLIENT_KEY;
    const idPalAccessKey = process.env.ID_PAL_ACCESS_KEY;

    if (!configuration || (configuration && !configuration.idPalConfiguration)) {
      return {status: 500, message: "ID-PAL token configuration not found"};
    }

    if (!idPalURL || !idPalClientKey || !idPalAccessKey ) {
      return {status: 500, message: "ID-PAL has missing values"};
    }

    let body = {
      "client_key": idPalClientKey,
      "access_key": idPalAccessKey,
      "uuid": uuid,
    };

    let options = {
      method: 'post',
      body: JSON.stringify(body),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': "Bearer " + configuration.idPalConfiguration.accessToken
      },
    };

    let response = await fetch(idPalURL, options);
    let content = await response.json();

    console.log("cancel  ", content);

    if (content && content.success === true) {
      return {
        status: 200,
        message: "UUID Canceled",
        uuid: uuid
      }
    } else {
      return {
        status: 500,
        message: content.message,
      }
    }

  }catch (e) {
    console.log(e);
    return {
      status: 500,
      message: "Internal Server Error"
    }
  }
};

async function generateAuthenticationToken(configuration) {
  try {
    let idPalGetTokenURL = process.env.ID_PAL_GET_TOKEN_URL;
    let idPalClientId = process.env.ID_PAL_CLIENT_ID;
    const idPalClientKey = process.env.ID_PAL_CLIENT_KEY;
    const idPalAccessKey = process.env.ID_PAL_ACCESS_KEY;
    let body = {
      "client_key": idPalClientKey,
      "access_key": idPalAccessKey,
      "client_id": idPalClientId,
      "refresh_token": configuration.idPalConfiguration.refreshToken
    };

    let options = {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const responseToken = await fetch(idPalGetTokenURL, options);
    const content = await responseToken.json();
    console.log("token response body", content);
    if (!responseToken.status || responseToken.status !== 200) {
      return {status: 500, message: "Error generating token"}
    }

    if (content["access_token"]){
      return {
        status: 200,
        accessToken: content["access_token"],
        refreshToken: content["refresh_token"]
      }
    }
    else{
      return {
        status:400,
        message: content
      }
    }
  } catch (e) {
    console.log("error generating token: ", e);
    return {
      status: 500,
      message: "Error generating token"
    }
  }
}

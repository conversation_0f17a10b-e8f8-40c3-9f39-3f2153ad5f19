<main class="">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <h1>{{title}}</h1>

            <!-- FILTERS -->
            <form method='GET' id="substanceSearchForm"
              action="?page={{pagination.pageNumber}}&pageSize={{pagination.pageSize}}">
              <div class="row">
                <div class="col-12 col-md-6">
                  <div class="row gx-1">
                    <div class="col-12 col-md-4 px-2">
                      <label for="filterCompany">Company</label>
                      <input class='form-control filter' type='text' name='company' id='filterCompany'
                        value="{{filters.company}}" />
                    </div>

                    <div class="col-12 col-md-4 px-2">
                      <label for="filterMasterclient">Masterclient</label>
                      <input class='form-control filter' type='text' name='masterclientcode' id='filterMasterclient'
                        value="{{filters.masterclientcode}}" />
                    </div>

                    <div class="col-12 col-md-4 px-2">
                      <label for="filterReferralOffice">Referral Office</label>
                      <input class='form-control filter' type='text' name='referralOffice' id='filterReferralOffice'
                        value="{{filters.referralOffice}}" />
                    </div>
                  </div>
                  <div class="row my-1">
                    <div class="col-12 col-md-4 px-2">
                      <label for="filterStatus">Status</label> <br>
                      <select id="filterStatus" class="form-control filter filter-array-field w-100" name="status[]"
                        multiple>
                        <option value="ALL" disabled>Select status</option>
                        <option value="SAVED" {{#ifContains 'SAVED' filters.status }} selected {{/ifContains}}>SAVED
                        </option>
                        <option value="SCHEDULED" {{#ifContains 'SCHEDULED' filters.status }} selected {{/ifContains}}>
                          SCHEDULED</option>
                        <option value="SUBMITTED" {{#ifContains 'SUBMITTED' filters.status }} selected {{/ifContains}}>
                          SUBMITTED</option>
                        <option value="PAID" {{#ifContains 'PAID' filters.status }} selected {{/ifContains}}>PAID
                        </option>
                        <option value="RE-OPEN" {{#ifContains 'RE-OPEN' filters.status }} selected {{/ifContains}}>
                          RE-OPEN</option>
                        <option value="INFORMATION REQUEST" {{#ifContains 'INFORMATION REQUEST' filters.status }}
                          selected {{/ifContains}}>INFORMATION REQUEST</option>
                      </select>
                    </div>
                    <div class="col-12 col-md-4 px-2">
                      <label for="filterAlreadySubmitted">Show Submitted?</label> <br>
                      <select id="filterAlreadySubmitted" class="form-control filter w-100" name="alreadySubmitted">
                        <option value="" {{#ifEquals filters.alreadySubmitted '' }} selected {{/ifEquals}}>
                          ALL
                        </option>
                        <option value="YES" {{#ifEquals filters.alreadySubmitted 'YES' }} selected {{/ifEquals}}>YES
                        </option>
                        <option value="NO" {{#ifEquals filters.alreadySubmitted 'NO' }} selected {{/ifEquals}}>NO
                        </option>
                      </select>
                    </div>
                    <div class="col-12 col-md-4 px-2">
                      <label for="filterAllowReopen">Allow Re-Open?</label> <br>
                      <select id="filterAllowReopen" class="form-control filter w-100" name="allowReopen">
                        <option value="" {{#ifEquals filters.allowReopen '' }} selected {{/ifEquals}}>ALL
                        </option>
                        <option value="YES" {{#ifEquals filters.allowReopen 'YES' }} selected {{/ifEquals}}>YES</option>
                        <option value="NO" {{#ifEquals filters.allowReopen 'NO' }} selected {{/ifEquals}}>NO</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-md-4">
                  <div class="row">
                    <label for="filter_relevant_activities">Relevant Activities</label>
                  </div>
                  <div class="row">
                    <div class="col-6">
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input filter" name="relevantActivities[]" id="none"
                          value="none" {{#ifContains 'none' filters.relevantActivities }} checked {{/ifContains}} />
                        <label class="custom-control-label" for="none">None</label>
                      </div>
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input filter" name="relevantActivities[]"
                          id="banking_business" value="banking_business" {{#ifContains 'banking_business'
                          filters.relevantActivities }} checked {{/ifContains}} />
                        <label class="custom-control-label" for="banking_business">Banking
                          Business</label>
                      </div>
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input filter" name="relevantActivities[]"
                          id="insurance_business" value="insurance_business" {{#ifContains 'insurance_business'
                          filters.relevantActivities }} checked {{/ifContains}} />
                        <label class="custom-control-label" for="insurance_business">Insurance
                          Business</label>
                      </div>
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input filter" name="relevantActivities[]"
                          id="fund_management_business" value="fund_management_business"
                          {{#ifContains 'fund_management_business' filters.relevantActivities }} checked
                          {{/ifContains}} />
                        <label class="custom-control-label" for="fund_management_business">Fund
                          Management Business</label>
                      </div>
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input filter" name="relevantActivities[]"
                          id="finance_leasing_business" value="finance_leasing_business"
                          {{#ifContains 'finance_leasing_business' filters.relevantActivities }} checked
                          {{/ifContains}} />
                        <label class="custom-control-label" for="finance_leasing_business">Finance/Leasing
                          Business</label>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input filter" name="relevantActivities[]"
                          id="headquarters_business" value="headquarters_business" {{#ifContains 'headquarters_business'
                          filters.relevantActivities }} checked {{/ifContains}} />
                        <label class="custom-control-label" for="headquarters_business">Headquarters
                          Business</label>
                      </div>
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input filter" name="relevantActivities[]"
                          id="shipping_business" value="shipping_business" {{#ifContains 'shipping_business'
                          filters.relevantActivities }} checked {{/ifContains}} />
                        <label class="custom-control-label" for="shipping_business">Shipping
                          Business</label>
                      </div>
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input filter" name="relevantActivities[]"
                          id="holding_business" value="holding_business" {{#ifContains 'holding_business'
                          filters.relevantActivities }} checked {{/ifContains}} />
                        <label class="custom-control-label" for="holding_business">Holding
                          Business</label>
                      </div>
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input filter" name="relevantActivities[]"
                          id="intellectual_property_business" value="intellectual_property_business"
                          {{#ifContains 'intellectual_property_business' filters.relevantActivities }} checked
                          {{/ifContains}} />
                        <label class="custom-control-label" for="intellectual_property_business">Intellectual Property
                          Business</label>
                      </div>
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input filter" name="relevantActivities[]"
                          id="service_centre_business" value="service_centre_business"
                          {{#ifContains 'service_centre_business' filters.relevantActivities }} checked
                          {{/ifContains}} />
                        <label class="custom-control-label" for="service_centre_business">Service Centre
                          Business</label>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <label for="filter_other">Other</label>
                  </div>
                  <div class="row">
                    <div class="col-md-6">
                      <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" name="residentOutsideBvi"
                          id="residentOutsideBvi" value="YES" {{#if filters.residentOutsideBvi }} checked {{/if}} />
                        <label class="custom-control-label" for="residentOutsideBvi">Tax
                          residency claim outside BVI</label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-12 col-md-4">
                  <div class="row my-1">
                    <div class="col-6">
                      <label for="submittedDateRangeStart">Submitted after:</label>
                      <input class='form-control filter' type='date' name='submittedDateRangeStart'
                        id='submittedDateRangeStart' value="{{filters.submittedDateRangeStart}}" />
                    </div>
                    <div class="col-6">
                      <label for="submittedDateRangeEnd">Submitted before:</label>
                      <input class='form-control filter' type='date' name='submittedDateRangeEnd'
                        id='submittedDateRangeEnd' value="{{filters.submittedDateRangeEnd}}" />
                    </div>
                  </div>
                  <div class="row my-1">
                    <div class="col-6">
                      <label for="incorporatedDateRangeStart">Company incorporated after:</label>
                      <input class='form-control filter' type='date' name='incorporatedDateRangeStart'
                        id='incorporatedDateRangeStart' value="{{filters.incorporatedDateRangeStart}}" />
                    </div>
                    <div class="col-6">
                      <label for="incorporatedDateRangeEnd">Company incorporated before:</label>
                      <input class='form-control filter' type='date' name='incorporatedDateRangeEnd'
                        id='incorporatedDateRangeEnd' value="{{filters.incorporatedDateRangeEnd}}" />
                    </div>
                  </div>
                  <div class="row my-1">
                    <div class="col-6">
                      <label for="financialPeriodEndDateRangeStart">Financial period end after:</label>
                      <input class='form-control filter' type='date' name='financialPeriodEndDateRangeStart'
                        id='financialPeriodEndDateRangeStart' value="{{filters.financialPeriodEndDateRangeStart}}" />
                    </div>
                    <div class="col-6">
                      <label for="financialPeriodEndDateRangeEnd">Financial period end before:</label>
                      <input class='form-control filter' type='date' name='financialPeriodEndDateRangeEnd'
                        id='financialPeriodEndDateRangeEnd' value="{{filters.financialPeriodEndDateRangeEnd}}" />
                    </div>
                  </div>
                  <div class="row my-1">
                    <div class="col-6">
                      <label for="paymentDateRangeStart">Date paid after:</label>
                      <input class='form-control filter' type='date' name='paymentDateRangeStart'
                        id='paymentDateRangeStart' value="{{filters.paymentDateRangeStart}}" />
                    </div>
                    <div class="col-6">
                      <label for="paymentDateRangeEnd">Date paid before:</label>
                      <input class='form-control filter' type='date' name='paymentDateRangeEnd' id='paymentDateRangeEnd'
                        value="{{filters.paymentDateRangeEnd}}" />
                    </div>
                  </div>
                </div>
              </div>

              <div class="row mt-2">
                <div class="col-12">
                  <button type="submit" form="substanceSearchForm" class='btn btn-primary waves-effect mr-2'>Search
                  </button>
                  <button type="button" form="substanceSearchForm" id="clearFormBtn"
                    class='btn btn-secondary waves-effect '>Reset filters </button>
                  <div class="btn-group" role="group">
                    <button type="button" id="selectAllTableBtn" class='btn btn-secondary waves-effect '>Select All On
                      Page</button>
                    <button type="button" id="unselectAllTableBtn" class='btn btn-secondary waves-effect '
                      title='unselect all'>
                      <i class="far fa-square"></i>
                    </button>
                  </div>

                </div>
              </div>

            </form>
            <br /><br />

            <!-- TABLE RESULTS -->

            {{>shared/table-pagination pagination=result formName="substanceSearchForm" tableId="entriesTable"
            searching="true"}}

            <table id="entriesTable" class="table w-100 nowrap">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Email</th>
                  <th>Entity Name</th>
                  <th>Entity Number</th>
                  <th>Master Client Code</th>
                  <th>Company Number</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th>Submitted</th>
                  <th>Re-open</th>
                  <th>Re-submitted</th>
                  <th>Incorporation Date</th>
                  <th>Date Paid</th>
                  <th>Payment Ref</th>
                  <th>Financial Period Enddate</th>
                  <th>Referral Office</th>
                  <th>None</th>
                  <th title='Banking Business'>BB</th>
                  <th title='Insurance Business'>IB</th>
                  <th title='Fund Management Business'>FMB</th>
                  <th title='Finance/Leasing Business'>FLB</th>
                  <th title='Headquarters Business'>HQ</th>
                  <th title='Shipping Business'>SB</th>
                  <th title='Holding Business'>HB</th>
                  <th title='Intellectual Property Business'>IP</th>
                  <th title='Service Centre Business'>SC</th>
                  <th>Reset Submission</th>
                  {{#if authentication.isSubsSuperUser}}
                  <th>Change Financial Period</th>
                  {{/if}}
                  <th>Summary</th>
                  <th>View Information</th>
                  {{#if authentication.isSubsSuperUser}}
                  <th>Download support attachments</th>
                  {{/if}}
                </tr>
              </thead>
              <tbody>
                {{#each result.data}}
                <tr data-allow-select="{{canResetToSaved}}">
                  <td>{{_id}}</td>
                  <td>{{email}}</td>
                  <td>{{entity_name}}</td>
                  <td>{{code}}</td>
                  <td>{{masterclientcode}}</td>
                  <td>{{incorporationcode}}</td>
                  <td>{{status}}</td>
                  <td data-sort="{{formatDate createdAt 'YYYY-MM-DD HH:mm'}}">
                    {{formatDate createdAt ../STANDARD_DATE_FORMAT}}
                  </td>
                  <td data-sort="{{formatDate submitted_at 'YYYY-MM-DD HH:mm'}}">
                    {{formatDate submitted_at ../STANDARD_DATE_FORMAT}}
                  </td>
                  <td data-sort="{{formatDate reopened_at 'YYYY-MM-DD HH:mm'}}">
                    {{formatDate reopened_at ../STANDARD_DATE_FORMAT}}
                  </td>
                  <td data-sort="{{formatDate resubmitted_at 'YYYY-MM-DD HH:mm'}}">
                    {{formatDate resubmitted_at ../STANDARD_DATE_FORMAT}}
                  </td>
                  <td data-sort="{{formatDate incorporationdate 'YYYY-MM-DD HH:mm'}}">
                    {{formatDate incorporationdate ../STANDARD_DATE_FORMAT}}
                  </td>
                  <td data-sort="{{formatDate date_paid 'YYYY-MM-DD HH:mm'}}">
                    {{formatDate date_paid ../STANDARD_DATE_FORMAT}}
                  </td>
                  <td>{{payment_reference}}</td>
                  <td data-sort="{{formatDate financial_period_ends 'YYYY-MM-DD HH:mm'}}">
                    {{formatDate financial_period_ends ../STANDARD_DATE_FORMAT}}
                  </td>
                  <td>{{referral_office}}</td>
                  <td>{{#if relevant_activities.none.selected}}Yes{{else}}No{{/if}}</td>
                  <td>{{#if relevant_activities.banking_business.selected}}Yes{{else}}No{{/if}}</td>
                  <td>{{#if relevant_activities.insurance_business.selected}}Yes{{else}}No{{/if}}</td>
                  <td>{{#if
                    relevant_activities.fund_management_business.selected}}Yes{{else}}No{{/if}}
                  </td>
                  <td>{{#if
                    relevant_activities.finance_leasing_business.selected}}Yes{{else}}No{{/if}}
                  </td>
                  <td>{{#if relevant_activities.headquarters_business.selected}}Yes{{else}}No{{/if}}
                  </td>
                  <td>{{#if relevant_activities.shipping_business.selected}}Yes{{else}}No{{/if}}</td>
                  <td>{{#if relevant_activities.holding_business.selected}}Yes{{else}}No{{/if}}</td>
                  <td>{{#if
                    relevant_activities.intellectual_property_business.selected}}Yes{{else}}No{{/if}}
                  </td>
                  <td>{{#if relevant_activities.service_centre_business.selected}}Yes{{else}}No{{/if}}
                  </td>
                  <td>
                    {{#if ../authentication.isSubsSuperUser}}
                    {{#ifEquals status 'PAID'}}
                    <input type="button" class="btn btn-primary waves-effect waves-light border-white"
                      onclick="showConfirmation('{{_id}}', '{{company}}', '{{status}}',
                                                               '{{formatDate financial_period_begins ../STANDARD_DATE_FORMAT}}',
                                                               '{{formatDate financial_period_ends ../STANDARD_DATE_FORMAT}}')" value="Reset to saved">
                    {{/ifEquals}}
                    {{#ifEquals status 'SUBMITTED'}}
                    <input type="button" class="btn btn-primary waves-effect waves-light border-white"
                      onclick="showConfirmation('{{_id}}', '{{company}}', '{{status}}',
                                                               '{{formatDate financial_period_begins ../STANDARD_DATE_FORMAT}}',
                                                               '{{formatDate financial_period_ends ../STANDARD_DATE_FORMAT}}')" value="Reset to saved">
                    {{/ifEquals}}
                    {{/if}}

                  </td>
                  {{#if ../authentication.isSubsSuperUser}}
                  <td>
                    {{#ifCond status '!==' 'NOT STARTED'}}
                    <input type="button" class="btn btn-primary waves-effect waves-light border-white"
                      onclick="showFinancialPeriodModal('{{_id}}', '{{company}}', '{{formatDate financial_period_begins ../STANDARD_DATE_FORMAT}}',
                                                              '{{formatDate financial_period_ends ../STANDARD_DATE_FORMAT}}')" value="Change">
                    {{/ifCond}}
                  </td>
                  {{/if}}
                  <td>

                    {{#ifEquals status 'PAID'}}
                    <a href="/substance/{{_id}}/submission.pdf" target="_blank"
                      class="btn btn-primary waves-effect waves-light  text-white border-white">Download</a>
                    {{/ifEquals}}
                    {{#ifEquals status 'SUBMITTED'}}
                    <a href="/substance/{{_id}}/submission.pdf" target="_blank"
                      class="btn btn-primary waves-effect waves-light  text-white border-white">Download</a>
                    {{/ifEquals}}

                    {{#ifEquals status 'SCHEDULED'}}
                    <a href="/substance/{{_id}}/submission.pdf" target="_blank"
                      class="btn btn-primary waves-effect waves-light  text-white border-white">Download</a>
                    {{/ifEquals}}
                  </td>
                  <td>

                    {{#if ../authentication.isSubsSuperUser}}
                    {{#if show_info_details}}
                    <button class="btn btn-primary waves-effect waves-light border-white" data-toggle="modal"
                      data-target="#showInformationModal" data-entry-id="{{_id}}"> Show Info
                    </button>
                    {{/if}}
                    {{/if}}
                  </td>
                  {{#if ../authentication.isSubsSuperUser}}
                  <td style="vertical-align: middle;">
                    {{#if show_attachments_download}}
                    <input type="button" name="downloadFilesBtn" id="download_files_{{_id}}"
                      class="btn btn-primary waves-effect waves-light"
                      onclick="event.stopPropagation(); downloadFiles('{{_id}}')" value="Download">
                    {{/if}}
                  </td>
                  {{/if}}
                </tr>
                {{/each}}

              </tbody>
            </table>


            <br>
            <!--FOOTER BUTTONS-->
            <div class="row">
              <div class="col-12 mb-3 d-flex flex-row">
                <div class="mr-2 ">
                  <a href='/substance/' class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                </div>

                {{#if authentication.isSubsSuperUser}}
                <div data-toggle="tooltip" data-placement="top" title="Re-open all selected table entries">
                  <button id="resetToSaveMultipleBtn" class="btn btn-primary width-lg " style="display: none;"
                    data-toggle="modal" data-target="#rtsBulkModal">
                    Reset to saved
                  </button>
                </div>
                {{/if}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {{>substance/change-financial-period-modal}}
  {{>substance/reset-to-saved-modal}}
  {{>substance/reset-to-saved-bulk-modal}}
  {{>substance/show-information-modal}}
</main>

<script type="text/javascript">
  $('[data-toggle="tooltip"]').tooltip({
    container: 'body',
    boundary: 'window'
  });
  let selectedId;
  let selectedCompany;
  let isSelectedEntryPaid = false;
  let $entriesTable;

  $(document).ready(function () {
    $('#filterStatus').select2();


    $entriesTable = $("#entriesTable").DataTable({
      dom: "lrtip",
      columnDefs: [{ "visible": false, "targets": [0] }],
      scrollX: !0,
      select: { style: "multi" },
      paging: false,
      info: false,
      sort: false
    });



    $entriesTable.on('select', function (e, dt, type, indexes) {
      if (type === 'row') {
        const selectedRowData = $entriesTable.row(indexes).node();;
        const allowSelectRow = $(selectedRowData).data('allow-select')

        if (allowSelectRow !== true) {
          $(selectedRowData).removeClass('selected');
          e.preventDefault();
          return false;

        }

        if ($entriesTable.rows('.selected').data().length) {
          $("#resetToSaveMultipleBtn").show();
        } else {
          $('#resetToSaveMultipleBtn').hide();
        }
      }
    });
    $entriesTable.on('deselect', function (e, dt, type, indexes) {
      if (type === 'row') {
        if ($entriesTable.rows('.selected').data().length) {
          $("#resetToSaveMultipleBtn").show();
        } else {
          $('#resetToSaveMultipleBtn').hide();
        }
      }
    });


    $("#selectAllTableBtn").on('click', function () {
      let showButton = false;
      $entriesTable.rows({ page: 'current' }).nodes().each((node) => {
        if ($(node).data('allow-select') === true) {
          showButton = true;
          $(node).addClass('selected');
        }
      })

      if (showButton) {
        $("#resetToSaveMultipleBtn").show();
      }

    })

    $("#unselectAllTableBtn").on('click', function () {
      $entriesTable.rows({ page: 'current' }).deselect();
      $("#resetToSaveMultipleBtn").hide();
    })

  });

  function showConfirmation(id, company, status, financialPeriodStart, financialPeriodEnd) {
    selectedId = id;
    selectedCompany = company;
    isSelectedEntryPaid = status === "PAID";
    $("#currentStartDate").html('Current: ' + financialPeriodStart);
    $("#currentEndDate").html('Current: ' + financialPeriodEnd);
    $("#newFinancialStartDate").flatpickr({
      dateFormat: "Y-m-d",
      altInput: true,
      altFormat: "m/d/Y",
      autoclose: true,
      monthSelectorType: "dropdown",
      allowInput: true,
      defaultDate: moment(financialPeriodStart, 'MM/DD/YYYY').toDate()
    })

    $("#newFinancialEndDate").flatpickr({
      dateFormat: "Y-m-d",
      altInput: true,
      altFormat: "m/d/Y",
      autoclose: true,
      monthSelectorType: "dropdown",
      allowInput: true,
      defaultDate: moment(financialPeriodEnd, 'MM/DD/YYYY').toDate()
    })
    $("#confirmation_modal").modal();

  }

  function showFinancialPeriodModal(id, company, financialPeriodStart, financialPeriodEnd) {
    selectedId = id;
    selectedCompany = company;
    isSelectedEntryPaid = status === "PAID";
    $("#financialStartDateControl").datepicker("setDate", financialPeriodStart);
    $("#financialEndDateControl").datepicker("setDate", financialPeriodEnd);
    $("#financial-period-modal").modal();

  }

  function downloadFiles(id) {
    window.location.href = `/substance/${id}/support-attachments`;
  }

  function saveFinancialPeriod() {
    $('#confirmFinancialPeriodButton').prop('disabled', true);

    const newFinancialStartDate = $("#financialStartDateControl").val();
    const newFinancialEndDate = $("#financialEndDateControl").val();



    if (!newFinancialStartDate || !newFinancialEndDate) {
      toastr["warning"]('Please select financial period start/end dates');
      return;
    }

    $.ajax({
      type: "PUT",
      url: "./search/update-financial-period",
      contentType: "application/json; charset=utf-8",
      data: JSON.stringify({
        id: selectedId,
        company: selectedCompany,
        newFinancialStartDate: newFinancialStartDate,
        newFinancialEndDate: newFinancialEndDate,
      }),
      success: function (data) {
        if (data.success) {
          toastr.success('Submission updated successfully');
          window.setTimeout(function () {
            document.location.reload();
          }, 200)
        } else {
          toastr["error"]('Sorry, there was an error updating the submission.');
          $('#confirmFinancialPeriodButton').prop('disabled', false);
        }
      },
      error: (err) => {
        if (err.responseJSON?.error) {
          toastr["error"](err.responseJSON?.error);
          $('#confirmFinancialPeriodButton').prop('disabled', false);
        }
        else {
          toastr["error"]('Sorry, there was an error updating the submission.');
          $('#confirmFinancialPeriodButton').prop('disabled', false);
        }
      },

    });
  }


  $("#clearFormBtn").on('click', () => {
    $("#substanceSearchForm")[0].reset();
    $("#substanceSearchForm input").val('');
    $('#substanceSearchForm input[type=checkbox]').prop('checked', false);
    $("#substanceSearchForm select").val('');
  })


</script>
const EntryModel = require("../models/entry").EntryModel;
const mongoose = require('mongoose');
const dotenv = require('dotenv');
dotenv.config();

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });
    
    await updateFilesMimeType();

    console.log('FINISHED');
  } catch (error) {
    console.error('ERROR', error);
  } finally {
    mongoose.disconnect();
  }
}

async function updateFilesMimeType() {
  try {
    const entries = await EntryModel.find(
      {
        "client_returned_information.details": { "$exists": true, "$ne": [] },
      }, { _id: 1, client_returned_information: 1 });

    console.log("Possible entries to update: ", entries.length);
    for (const entry of entries) {
      // Update files mimetype of each detail
      const details = entry.client_returned_information.details;
      let entryUpdated = false;
      for (const detail of details) {
        if (detail.files && detail.files.length > 0) {
          for (const file of detail.files) {
            if (!file.mimetype) {
              entryUpdated = true;
              file.mimetype = "application/pdf";
            }
          }
        }
      }
      if (entryUpdated) {
        await EntryModel.updateOne({ _id: entry._id }, {
          $set: {
            client_returned_information: {
              details: details
            }
          }
        }, { timestamps: false });
      }
    }
  } catch (e) {
    console.log("Error updating the file review date: ", e);
    return []
  }

}

runScript();



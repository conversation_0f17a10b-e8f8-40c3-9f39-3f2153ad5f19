<main class="">
  <div class="container">
    <div class="row">
      <div class="div col-7 mx-auto">
        <div class="card-box">
          <div class="card-title">
            <h3>
              Company Name:<span class="font-weight-bold ml-1">{{ file.companyName }}</span>
            </h3>
            <div class="card-body">
              <div class="row mx-0">
                <div class="col-12 px-0">
                  We have determined that the type should be a
                  <span class="font-weight-bold" style="text-transform: capitalize;">
                    {{ file.companyType }}.
                  </span>
                </div>
              </div>
              <div class="row mx-0 mt-3">
                <div class="col-12 px-0">
                  <form
                    action="/file-reviewer/confirm-type/{{ id }}"
                    id="confirmTypeForm"
                    method="POST"
                  >
                    <div class="row">
                      <div class="col-6 mb-2">
                        <span class="font-weight-bold d-block mb-2">Is the type correct?</span>
                        <div class="custom-control custom-radio custom-control-inline">
                          <input
                            type="radio"
                            class="custom-control-input"
                            id="optNo"
                            name="optTypeCorrect"
                            value="no"
                            onchange="showSelection()"
                            onclick="blockNext()"
                          />
                          <label class="custom-control-label" for="optNo">
                            No
                          </label>
                        </div>
                        <div class="custom-control custom-radio custom-control-inline">
                          <input
                            type="radio"
                            class="custom-control-input"
                            id="optYes"
                            name="optTypeCorrect"
                            value="yes"
                            onchange="showSelection()"
                            onclick="enableNext()"
                            checked
                          />
                          <label class="custom-control-label" for="optYes">
                            Yes
                          </label>
                        </div>
                      </div>
                      <div class="col-6" id="selectedType" style="display: none;">
                        <label>Please select the correct type</label>
                        <select
                          name="newType"
                          id="typeSelection"
                          class="custom-select"
                          onchange="enableNext()"
                        >
                          <option id="opt0" value="" selected disabled hidden>Select...</option>
                          <option id="opt1" value="managed">Managed Company</option>
                          <option id="opt2" value="other">Other</option>
                        </select>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
            <div class="row mt-2 justify-content-between">
              <div class="col-md-2">
                <a href="/file-reviewer/file-review-list" class="btn btn-secondary width-lg waves-effect waves-light">
                  Back
                </a>
              </div>
              <div class="col-md-2">
                <button
                  id="btn-Next"
                  type="submit"
                  form="confirmTypeForm"
                  class="btn solid royal-blue w-100"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>

<script>
  $(document).ready(function () {
    $('#opt0').prop('selected', true);
    $('#optYes').prop('checked', true);
  });

  function showSelection() {
    if ($('#optNo').is(':checked')) {
      $('#selectedType').show();
    } else {
      $('#selectedType').hide();
      $('#opt0').prop('selected', true);
    }
  }

  function blockNext() {
    $('#btn-Next').attr('disabled', true);
  }

  function enableNext() {
    $('#btn-Next').attr('disabled', false);
  }
</script>

<!-- NOFITY MODAL -->
<div class="modal fade" id="cancelRequestClientModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">CANCEL REQUESTED FILES FROM CLIENT</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mb-1">
                    <div class="col-md-12 text-center">
                        <h3 class="font-weight-bold text-center">Are you sure?</h3>
                    </div>
                </div>

                <div class="row m-1">
                    <div class="col-md-12 text-center">
                        <span>The current request will be cancelled.</span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 px-4">

                        <label for="cancelRequestFilesComment"><small></small></label> <br>
                        <textarea class="form-control" name="cancelRequestFilesComment" id="cancelRequestFilesComment"
                                  placeholder="Add comment..." rows="3"></textarea>

                    </div>
                </div>

            </div>
            <div class="modal-footer justify-content-between">
                <div class="col-6">
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                        Close
                    </button>
                </div>
                <div class="d-flex col-6 justify-content-end">
                    <button
                            id="confirmCancelRequestButton"
                            type="button"
                            form="fileReviewForm"
                            class="btn solid royal-blue"
                    >
                        Confirm
                    </button>

                    <button id="spinnerCancelBtn" class="btn solid royal-blue" type="button" disabled
                            style="display: none">
                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        Sending...
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

    let cancelRequestId = '';
    $('#cancelRequestClientModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget);

        cancelRequestId = button.data('id');
    });

    $('#confirmCancelRequestButton').click(function (e) {
        e.preventDefault();
        $(this).prop('disabled', true);
        $(this).hide();
        $("#spinnerCancelBtn").show();
        let comment = $('#cancelRequestFilesComment').val();
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/reviews/' + cancelRequestId + '/cancel-request-client-files',
            data: {
                comment: comment
            },
            success: function (res) {
                $("#spinnerCancelBtn").hide();
                Swal.fire('Success', 'Request has been cancelled successfully', 'success').then(() => {
                    location.href = '/file-reviewer/file-review-list';
                });

            },
            error: function (err) {
                $("#spinnerCancelBtn").hide();
                $("#confirmCancelRequestButton").prop('disabled', false);
                $("#confirmCancelRequestButton").show();
                Swal.fire('Error', 'Error cancelling the request... Try again later', 'error');
            },
        });
    });

    $('#cancelRequestClientModal').on('hidden.bs.modal', function (event) {
        $("#requested-files").html('');
        $("#confirmCancelRequestButton").prop('disabled', true);
        $("#confirmCancelRequestButton").show();
        $("#spinnerCancelBtn").hide();
    });
</script>
<!-- NOFITY MODAL END -->

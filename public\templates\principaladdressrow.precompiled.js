(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['principaladdressrow'] = template({"1":function(container,depth0,helpers,partials,data) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <tr>\r\n        <td>\r\n            Certified legible copy of passport/government issued ID\r\n        </td>\r\n        <td class=\"text-center\">\r\n            <div class=\"custom-control custom-checkbox\">\r\n                <input\r\n                        type=\"checkbox\"\r\n                        class=\"custom-control-input\"\r\n                        name=\"principal-7-"
    + alias4(((helper = (helper = lookupProperty(helpers,"newRow") || (depth0 != null ? lookupProperty(depth0,"newRow") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"newRow","hash":{},"data":data,"loc":{"start":{"line":11,"column":42},"end":{"line":11,"column":52}}}) : helper)))
    + "-generalpartnerid-present-file-corporate\"\r\n                        id=\"principal-7-"
    + alias4(((helper = (helper = lookupProperty(helpers,"newRow") || (depth0 != null ? lookupProperty(depth0,"newRow") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"newRow","hash":{},"data":data,"loc":{"start":{"line":12,"column":40},"end":{"line":12,"column":50}}}) : helper)))
    + "-generalpartnerid-present-file-corporate\"\r\n                />\r\n                <label\r\n                        class=\"custom-control-label\"\r\n                        for=\"principal-7-"
    + alias4(((helper = (helper = lookupProperty(helpers,"newRow") || (depth0 != null ? lookupProperty(depth0,"newRow") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"newRow","hash":{},"data":data,"loc":{"start":{"line":16,"column":41},"end":{"line":16,"column":51}}}) : helper)))
    + "-generalpartnerid-present-file-corporate\"\r\n                ></label>\r\n            </div>\r\n        </td>\r\n        <td class=\"text-center\">\r\n            <button\r\n                    type=\"button\"\r\n                    class=\"btn solid royal-blue\"\r\n                    data-toggle=\"modal\"\r\n                    data-target=\"#upload-temp-modal\"\r\n                    data-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":26,"column":29},"end":{"line":26,"column":37}}}) : helper)))
    + "\"\r\n                    data-field=\"Certified legible copy of passport/government issued ID\"\r\n                    data-row=\"7\"\r\n            >\r\n                Upload\r\n            </button>\r\n        </td>\r\n        <td>\r\n        <textarea\r\n                class=\"form-control\"\r\n                name=\"principal-7-"
    + alias4(((helper = (helper = lookupProperty(helpers,"newRow") || (depth0 != null ? lookupProperty(depth0,"newRow") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"newRow","hash":{},"data":data,"loc":{"start":{"line":36,"column":34},"end":{"line":36,"column":44}}}) : helper)))
    + "-generalpartnerid-explanation-file-corporate\"\r\n                id=\"principal-7-"
    + alias4(((helper = (helper = lookupProperty(helpers,"newRow") || (depth0 != null ? lookupProperty(depth0,"newRow") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"newRow","hash":{},"data":data,"loc":{"start":{"line":37,"column":32},"end":{"line":37,"column":42}}}) : helper)))
    + "-generalpartnerid-explanation-file-corporate\"\r\n                rows=\"1\"\r\n        ></textarea>\r\n        </td>\r\n    </tr>\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <tr>\r\n        <td>Certified legible copy of Proof of Address</td>\r\n        <td class=\"text-center\">\r\n            <div class=\"custom-control custom-checkbox\">\r\n                <input\r\n                        type=\"checkbox\"\r\n                        class=\"custom-control-input\"\r\n                        name=\"principal-8-"
    + alias4(((helper = (helper = lookupProperty(helpers,"newRow") || (depth0 != null ? lookupProperty(depth0,"newRow") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"newRow","hash":{},"data":data,"loc":{"start":{"line":50,"column":42},"end":{"line":50,"column":52}}}) : helper)))
    + "-generalpartneraddressproof-present-file-corporate\"\r\n                        id=\"principal-8-"
    + alias4(((helper = (helper = lookupProperty(helpers,"newRow") || (depth0 != null ? lookupProperty(depth0,"newRow") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"newRow","hash":{},"data":data,"loc":{"start":{"line":51,"column":40},"end":{"line":51,"column":50}}}) : helper)))
    + "-generalpartneraddressproof-present-file-corporate\"\r\n                />\r\n                <label\r\n                        class=\"custom-control-label\"\r\n                        for=\"principal-8-"
    + alias4(((helper = (helper = lookupProperty(helpers,"newRow") || (depth0 != null ? lookupProperty(depth0,"newRow") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"newRow","hash":{},"data":data,"loc":{"start":{"line":55,"column":41},"end":{"line":55,"column":51}}}) : helper)))
    + "-generalpartneraddressproof-present-file-corporate\"\r\n                ></label>\r\n            </div>\r\n        </td>\r\n        <td class=\"text-center\">\r\n            <button\r\n                    type=\"button\"\r\n                    class=\"btn solid royal-blue\"\r\n                    data-toggle=\"modal\"\r\n                    data-target=\"#upload-temp-modal\"\r\n                    data-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":65,"column":29},"end":{"line":65,"column":37}}}) : helper)))
    + "\"\r\n                    data-field=\"Certified legible copy of Proof of Address\"\r\n                    data-row=\"8\"\r\n            >\r\n                Upload\r\n            </button>\r\n        </td>\r\n        <td>\r\n        <textarea\r\n                class=\"form-control\"\r\n                name=\"principal-8-"
    + alias4(((helper = (helper = lookupProperty(helpers,"newRow") || (depth0 != null ? lookupProperty(depth0,"newRow") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"newRow","hash":{},"data":data,"loc":{"start":{"line":75,"column":34},"end":{"line":75,"column":44}}}) : helper)))
    + "-generalpartneraddressproof-explanation-file-corporate\"\r\n                id=\"principal-8-"
    + alias4(((helper = (helper = lookupProperty(helpers,"newRow") || (depth0 != null ? lookupProperty(depth0,"newRow") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"newRow","hash":{},"data":data,"loc":{"start":{"line":76,"column":32},"end":{"line":76,"column":42}}}) : helper)))
    + "-generalpartneraddressproof-explanation-file-corporate\"\r\n                rows=\"1\"\r\n        ></textarea>\r\n        </td>\r\n    </tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||container.hooks.helperMissing).call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"rowType") : depth0),"1",{"name":"ifEquals","hash":{},"fn":container.program(1, data, 0),"inverse":container.program(3, data, 0),"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":81,"column":13}}})) != null ? stack1 : "")
    + "\r\n";
},"useData":true});
})();
{{!-- CONFIRM MODAL --}}
<div class="modal fade" id="updateIncorporationStatusModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div  class="modal-body p-3 text-justify">
                <div id="message_modal">

                </div>
                <div id="divInputComment" class="mt-1 text-justify" style="display: none">
                    <label for="inputCommentModal"><small></small></label> <br>
                    <textarea class="form-control" name="inputCommentModal" id="inputCommentModal" placeholder="Add comment..." rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="button" class="btn solid royal-blue confirm-status-btn" id="updateStatusButton"
                        style="display: none">
                    Confirm
                </button>

                <button type="button" class="btn solid royal-blue confirm-status-btn" id="declineStatusButton"
                        style="display: none">
                    Decline
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    const modalInfoValidate = {
        "approve-for-incorporation": {
            "modalMessage": '<strong>You are about to approve for incorporate the application. Are you sure?</strong> <br> ' +
                    'This will put the application in the approved state and the client will know the incorporation is in progress.',
            "successMessage": 'The application has been approved successfully.',
            "errorMessage": 'There was an error approving the application.'
        },
        "approve": {
            "modalMessage": '<strong>You are about to approve the application. Are you sure?</strong> <br> ' +
                    'This will put the application in the approved state and the client will know the incorporation process is completed.',
            "successMessage": 'The application has been approved successfully.',
            "errorMessage": 'There was an error approving the application.'
        },
        "decline": {
            "modalMessage": '<strong>You are about to decline the application. Are you sure?</strong> <br>' +
                    'Please provide a comment in the text area below, this comment will be visible for the client.',
            "successMessage": 'The application has been declined successfully.',
            "errorMessage": 'There was an error rejecting the application.'
        },
    };

    let status = '';
    let incorporationId2 = '';

    $('#updateIncorporationStatusModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        status = button.data('status');
        incorporationId2 = button.data('id');
        $('#message_modal').html(modalInfoValidate[status].modalMessage);

        if (status === "decline"){
            $("#declineStatusButton").show();
        }else{
            $("#updateStatusButton").show();
        }

        $("#divInputComment").show();
    });

    $('#updateIncorporationStatusModal').on('hide.bs.modal', function (event) {
        $("#updateStatusButton").hide();
        $("#declineStatusButton").hide();
        $("#divInputComment").hide();
        $('#message_modal').html('');
        $(".confirm-status-btn").prop('disabled', false);
    });

    $('.confirm-status-btn').on('click', function (event) {
        event.preventDefault();
        $(this).prop('disabled', true);
        comment = $('#inputCommentModal').val();
        let companyCode;
        let incorporationNr;

        if (status === "approve"){
          companyCode = $("#companyCode").val();
          incorporationNr = $("#incorporationNr").val();

          if (companyCode === "" || incorporationNr === ""){
              $("#companyCode").toggleClass('is-invalid', companyCode === "");
              $("#incorporationNr").toggleClass('is-invalid', incorporationNr === "");
              toastr["warning"]('The company code and incorporation number cannot be empty');
              $('#updateIncorporationStatusModal').modal('hide');
              return false;
          }
        }

        $.ajax({
            type: 'POST',
            url: './'+ incorporationId2 + '/update-status',
            data: { status: status, comment: comment, companyCode: companyCode, incorporationNr: incorporationNr },
            success: () => {
                $('#updateIncorporationStatusModal').modal('hide');
                Swal.fire('Success', modalInfoValidate[status].successMessage, 'success').then(() => {
                    location.href = '/client-incorporation/list';
                });
            },
            error: (err) => {
                Swal.fire('Error', modalInfoValidate[status].errorMessage, 'error').then(() => {
                    $('#updateIncorporationStatusModal').modal('hide');
                });
            },
        });
    });
</script>

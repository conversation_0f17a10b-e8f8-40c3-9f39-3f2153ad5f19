{{#ifEquals relation.type 'natural'}}
        <!-- PEP DETAILS -->
        {{#if relation.pep}}
        <div>
            <div class="alert alert-warning" role="alert">This person is a PEP</div>
            <p>PEP DETAILS
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                {{#if relationInformation.pepDetails.complete}}
                <span class="badge badge-success">Complete</span>
                {{else}}
                <span class="badge badge-warning text-dark">Incomplete</span>
                {{/if}}
                {{/ifCond}}
            </p>
            <div class="row">
                <div class="col-3">PEP Information:</div>
                <div class="col-9 font-weight-bold">{{relation.pepDetails.information}}</div>
                <div class="col-3">Addtional news check completed?</div>
                <div class="col-9 font-weight-bold">{{#if relation.pepDetails.confirmAdditionalComments}} Yes {{else}} No{{/if}}</div>
                <div class="col-3">News Comments:</div>
            <div class="col-9 font-weight-bold">{{relation.pepDetails.additionalComments}}</div>
            </div>
        <hr class="mt-3">
    {{/if}}

    {{#if electronicIdInfo.comments}}
        <div class="row">
            <div class="col-md-12">
                {{#each  electronicIdInfo.comments}}
                    <span>
                        ({{#formatDate date "MM/DD/YYYY"}} {{/formatDate }}): {{comment}}
                    </span>
                {{/each}}
            </div>
        </div>
    {{/if}}
    <p>
        <!-- DETAILS -->
        DETAILS
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.details.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-2">Full Name:</div>
        <div class="col-4 font-weight-bold">{{ relation.details.fullName }}</div>
        <div class="col-2">First Name:</div>
        <div class="col-4 font-weight-bold">{{ relation.details.firstName }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">Middle Name:</div>
        <div class="col-4 font-weight-bold">{{ relation.details.middleName }}</div>
        <div class="col-2">Last Name:</div>
        <div class="col-4 font-weight-bold">{{ relation.details.lastName }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">Occupation:</div>
        <div class="col-4 font-weight-bold">{{ relation.details.occupation }}</div>
        <div class="col-2">Date of Birth:</div>
        <div class="col-4 font-weight-bold">{{#formatDate relation.details.birthDate "MM/DD/YYYY"}} {{/formatDate }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">Nationality:</div>
        <div class="col-4 font-weight-bold">{{ relation.details.nationality }}</div>
        <div class="col-2">Country of Birth:</div>
        <div class="col-4 font-weight-bold">{{relation.details.countryBirth}}</div>
    </div>
    <hr class="mt-3"/>
    <p>
        <!-- IDENTIFICATION -->
        IDENTIFICATION
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.identification.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-2">Type of Identification:</div>
        <div class="col-4 font-weight-bold" style="text-transform: capitalize;">
            {{ relation.identification.identificationType }}
        </div>
        <div class="col-2">Country of Issue:</div>
        <div class="col-4 font-weight-bold">{{ relation.identification.issueCountry }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">Expiry Date:</div>
        <div class="col-4 font-weight-bold">{{#formatDate relation.identification.expiryDate "MM/DD/YYYY"}} {{/formatDate }}</div>
    </div>
    <hr class="mt-3"/>
    <p>
        <!-- PRINCIPAL ADDRESS -->
        PRINCIPAL ADDRESS
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.principalAddress.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-2">Address - 1st Line:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.primaryAddress }}</div>
        <div class="col-2">Address - 2nd Line:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.secondaryAddress }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">Country:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.country }}</div>
        <div class="col-2">State:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.state }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">City:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.city }}</div>
        <div class="col-2">Postal Code:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.postalCode }}</div>
    </div>
    <hr class="mt-3"/>
    <p>
        <!-- MAILING ADDRESS -->
        MAILING ADDRESS
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.mailingAddress.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-2">Address - 1st Line:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.primaryAddress }}</div>
        <div class="col-2">Address - 2nd Line:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.secondaryAddress }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">Country:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.country }}</div>
        <div class="col-2">State:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.state }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">City:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.city }}</div>
        <div class="col-2">Postal Code:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.postalCode }}</div>
    </div>
    <hr class="mt-3"/>
    <p>
        <!-- TAX ADVICE -->
        TAX ADVICE
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.taxResidence.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-6">Confirmation Regarding Legal / Tax Advice:</div>
        <div class="col-6 font-weight-bold">
            {{#if relation.taxResidence.confirmation}} Confirmed {{else}} Not confirmed{{/if}}
        </div>
        <div class="col-6">Tax Residence:</div>
        <div class="col-6 font-weight-bold">{{ relation.taxResidence.taxResidence }}</div>
    </div>
    <hr class="mt-3"/>
    <p>
        <!-- ADVISOR DETAILS -->
        ADVISOR DETAILS
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.advisorDetails.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-2">First Name:</div>
        <div class="col-4 font-weight-bold">{{ relation.advisorDetails.firstName }}</div>
        <div class="col-2">Middle Name:</div>
        <div class="col-4 font-weight-bold">{{ relation.advisorDetails.middleName }}</div>
    </div>
    <div class="row">
        <div class="col-2">Last Name:</div>
        <div class="col-4 font-weight-bold">{{ relation.advisorDetails.lastName }}</div>
        <div class="col-2">Name of Firm:</div>
        <div class="col-4 font-weight-bold">{{ relation.advisorDetails.firmName }}</div>
    </div>
    <div class="row">
        <div class="col-2">Phone:</div>
        <div class="col-4 font-weight-bold">{{ relation.advisorDetails.phone }}</div>
        <div class="col-2">E-mail:</div>
        <div class="col-4 font-weight-bold">{{ relation.advisorDetails.email}}</div>
    </div>
    <div class="row">
        <div class="col-2">Nationality:</div>
        <div class="col-4 font-weight-bold">{{ relation.advisorDetails.nationality }}</div>
        <div class="col-2">Country of Incorporation:</div>
        <div class="col-4 font-weight-bold">{{ relation.advisorDetails.incorporationCountry }}</div>
    </div>
    <hr class="mt-3"/>
    <p>
        <!-- PRINCIPAL ADVISOR ADDRESS -->
        PRINCIPAL ADVISOR ADDRESS
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.residentialAddress.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-2">Address - 1st Line:</div>
        <div class="col-4 font-weight-bold">{{ relation.residentialAddress.primaryAddress }}</div>
        <div class="col-2">Address - 2nd Line:</div>
        <div class="col-4 font-weight-bold">{{ relation.residentialAddress.secondaryAddress }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">Country:</div>
        <div class="col-4 font-weight-bold">{{ relation.residentialAddress.country }}</div>
        <div class="col-2">State:</div>
        <div class="col-4 font-weight-bold">{{ relation.residentialAddress.state }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">City:</div>
        <div class="col-4 font-weight-bold">{{ relation.residentialAddress.city }}</div>
        <div class="col-2">Postal Code:</div>
        <div class="col-4 font-weight-bold">{{ relation.residentialAddress.postalCode }}</div>
    </div>

    <hr class="mt-3"/>
    <p>
        <!-- WORLD CHECK -->
        WORLD CHECK
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.worldCheck.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>

{{!-- ORGANIZATIONS --}}
{{else}}
{{!-- DETAILS --}}
    <p>
        DETAILS
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.details.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-2">Organization Name:</div>
        <div class="col-4 font-weight-bold">{{ relation.details.organizationName }}</div>
        <div class="col-2">Incorporation / Formation Number:</div>
        <div class="col-4 font-weight-bold">{{ relation.details.incorporationNumber }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">Tax Residence:</div>
        <div class="col-4 font-weight-bold">{{ relation.details.taxResidence }}</div>
        <div class="col-2">Business Registration Number:</div>
        <div class="col-4 font-weight-bold">{{ relation.details.businessNumber }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">Date of Incorporation:</div>
        <div class="col-4 font-weight-bold">{{#formatDate relation.details.incorporationDate "MM/DD/YYYY"}} {{/formatDate }}</div>
        <div class="col-2">Incorporation Country:</div>
        <div class="col-4 font-weight-bold">{{relation.details.incorporationCountry }}</div>
    </div>
    <hr class="mt-3"/>
    {{!-- PRINCIPAL ADDRESS --}}
    <p>
        PRINCIPAL ADDRESS
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.principalAddress.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-2">Address - 1st line:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.primaryAddress }}</div>
        <div class="col-2">Address - 2nd line:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.secondaryAddress }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">Country:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.country }}</div>
        <div class="col-2">State:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.state }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">City:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.city }}</div>
        <div class="col-2">Postal Code:</div>
        <div class="col-4 font-weight-bold">{{ relation.principalAddress.postalCode}}</div>
    </div>
    <hr class="mt-3"/>
    {{!-- MAILING ADDRESS --}}
    <p>
        MAILING ADDRESS
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.mailingAddress.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-2">Address - 1st line:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.primaryAddress }}</div>
        <div class="col-2">Address - 2nd line:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.secondaryAddress }}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">Country:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.country }}</div>
        <div class="col-2">State:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.state}}</div>
    </div>
    <div class="row pt-2">
        <div class="col-2">City:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.city }}</div>
        <div class="col-2">Postal Code:</div>
        <div class="col-4 font-weight-bold">{{ relation.mailingAddress.postalCode }}</div>
    </div>
    {{#ifCond relation.type '!=' 'trust'}}
    <hr class="mt-3"/>
    {{!-- LISTED --}}
    <p>
        LISTED COMPANY
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.listedCompanyDetails.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-4">Stock Code / Ticker Symbol:</div>
        <div class="col-6 font-weight-bold">{{relation.listedCompanyDetails.stockCode  }}</div>
    </div>
    <hr class="mt-3"/>
    {{!-- LIMITED --}}
    <p>
        LIMITED COMPANY
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
        {{#if relationInformation.limitedCompanyDetails.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>
    <div class="row">
        <div class="col-2">Registration Number:</div>
        <div class="col-4 font-weight-bold">{{ relation.limitedCompanyDetails.registrationNumber }}</div>
        <div class="col-2">Registration Date:</div>
        <div class="col-4 font-weight-bold">{{#formatDate relation.limitedCompanyDetails.registrationDate "MM/DD/YYYY"}} {{/formatDate }}</div>
    </div>
    {{/ifCond}}
    {{!-- FOUNDATION --}}
     {{#ifCond relation.type '==' 'foundation'}}
        <hr class="mt-3"/>
        <p>
            FOUNDATION
            {{#ifCond relation.lockedByFileReview '==' reviewId}}
            {{#if relationInformation.foundation.complete}}
                <span class="badge badge-success">Complete</span>
            {{else}}
                <span class="badge badge-warning text-dark">Incomplete</span>
            {{/if}}
            {{/ifCond}}
        </p>
        <div class="row">
            <div class="col-2">Country:</div>
            <div class="col-4 font-weight-bold">{{ relation.foundation.country }}</div>
        </div>
     {{/ifCond}}
    {{!-- MUTUALFUND --}}
    <hr class="mt-3"/>
    <p>
        REGULATED (Mutual Fund)
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
            {{#if relationInformation.mutualFundDetails.complete}}
                <span class="badge badge-success">Complete</span>
            {{else}}
                <span class="badge badge-warning text-dark">Incomplete</span>
            {{/if}}
        {{/ifCond}}
    </p>

    <hr class="mt-3"/>
    <p>
        <!-- WORLD CHECK -->
        WORLD CHECK
        {{#ifCond relation.lockedByFileReview '==' reviewId}}
            {{#if relationInformation.worldCheck.complete}}
                <span class="badge badge-success">Complete</span>
            {{else}}
                <span class="badge badge-warning text-dark">Incomplete</span>
            {{/if}}
        {{/ifCond}}
    </p>
    {{#if relation.positions}}
        <hr class="mt-3"/>
        <p>Positions</p>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 25%;">Name</th>
                    <th style="width: 20%;">Country</th>
                    <th style="width: 15%;">Position</th>
                    <th style="width: 14%;"></th>
                    <th style="width: 13%;"></th>
                    <th style="width: 13%;"></th>
                </tr>
            </thead>
            <tbody>
            {{#each relation.positions}}
                <tr>
                    <td style="text-transform: capitalize;">{{referenceId.details.fullName}}</td>
                    <td style="text-transform: capitalize;">{{referenceId.details.countryBirth}}</td>
                    <td style="text-transform: capitalize;">{{ type }}</td>
                    <td class="text-center align-middle">
                        <button class="btn btn-xs solid royal-blue open-position" type="button" data-dismiss="modal"
                                data-target="#openPositionModal" data-toggle="modal" data-review-id="{{../reviewId}}"
                                data-org="{{ ../relation._id }}" data-name="{{ referenceId.details.fullName }}"
                                data-row="{{ _id }}">Open
                        </button>
                    </td>
                    <td class="text-center align-middle">
                        {{#ifCond referenceId.lockedByFileReview '==' ../reviewId}}
                            <a href="/file-reviewer/open-file-review/{{ ../reviewId }}/relations/{{ ../relation._id }}/positions/{{ _id }}/update"
                               class="btn btn-xs btn-outline-secondary"><i class="fa fa-pen"></i></a>
                        {{else}}
                            <i class="fa fa-lock"></i>
                        {{/ifCond}}

                    </td>
                    <td class="text-center align-middle">
                    {{#ifCond referenceId.lockedByFileReview '==' ../reviewId}}
                        <button class="btn btn-xs btn-danger delete-position"
                                type="button" data-review-id="{{../reviewId}}"
                                data-position-type="{{type}}"
                                data-org="{{../relation._id}}"
                                data-id="{{_id}}">
                            <i class="fa fa-trash"></i>
                        </button>
                    {{else}}
                        <button
                                type="button"
                                class="btn btn-outline-secondary"
                                id="locked-{{ _id }}"
                                data-locked-id="{{ referenceId.lockedByFileReview  }}"
                                data-toggle="modal"
                                data-target="#lockedReviewModal"
                        >
                            <i class="fa fa-info-circle fa-lg" ></i>
                        </button>
                    {{/ifCond}}
                    </td>
                </tr>
            {{/each}}
            </tbody>
        </table>

        {{!-- DELETE POSITION --}}
        <script type="text/javascript">
            $('.delete-position').click(function () {
                let button = $(this); // Button that triggered the modal
                let reviewId = button.data('review-id');
                let positionId = button.data('id');
                let orgId = button.data('org');
                let positionType = button.data('position-type');
                swal({
                    title: 'Confirmation',
                    text: "Are you sure you want to delete this position?",
                    icon: 'danger',
                    showCancelButton: true,
                    showCloseButton: true,
                    reverseButtons: true,
                    confirmButtonColor: '#f1556c',
                    confirmButtonText: 'Delete',
                    cancelButtonText: 'Close'
                }).then((result) => {
                    if (result.value) {
                        $.ajax({
                            type: 'DELETE',
                            url: '/file-reviewer/open-file-review/'+ reviewId +   '/relations/' + orgId + '/positions/' + positionId,
                            data: {
                                type: positionType
                            },
                            success: function () {
                                location.reload();
                            },
                            error: function () {
                                Swal.fire('Error', 'There was an error while trying to delete the position', 'error')
                            }
                        })
                    }
                });
            });
        </script>
    {{/if}}
{{/ifEquals}}
    <div>
        <hr class="mt-3"/>
        <p>FILES</p>
        <table class="table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Group</th>
                    <th>Download</th>
                </tr>
            </thead>
            <tbody>
            {{#each relationFiles}}
                <tr>
                    <td style="text-transform: capitalize;">{{external}}</td>
                    <td style="text-transform: capitalize;">{{fileGroup}}</td>
                    <td class="text-center align-middle">
                        <button class="btn solid royal-blue download-button"
                                type="button"
                                data-toggle="modal"
                                data-target="#downloadFileModal"
                                data-review-id="{{../reviewId }}"
                                data-relation-id="{{../relation._id}}"
                                data-file-id="{{ id }}"
                                data-file-group="{{../relation.type}}"
                            {{#unless uploadFiles}} disabled {{/unless}}
                        >Download
                        </button>
                    </td>
                </tr>
            {{/each}}
            </tbody>
        </table>
    </div>
</div>

<div id="reopenedInformation" class="mb-2">
    <div class="row">
        <div class="col-12">
            <h4><b>Information on Reopened submissions</b></h4>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            {{#each entryData.reopenedInfo}}
                {{#if resubmitted_at }}
                   <span>
                        {{#formatDate resubmitted_at "MM/DD/YYYY"}} {{/formatDate }} | {{resubmitted_by}} | <b>RESUBMITTED</b>
                    </span>
                    <br>
                {{/if}}
                <span>
                    {{#formatDate date_reopened "MM/DD/YYYY"}} {{/formatDate }} | {{reopened_by}} | <b>Reason:</b> {{reason}}
                    {{#ifEquals change_financial_period_dates true}}
                        <br>
                        <b>Financial period changed:</b> OLD period from {{#formatDate old_start_date "MM/DD/YYYY"}} {{/formatDate }} to {{#formatDate old_end_date "MM/DD/YYYY"}} {{/formatDate }},
                        NEW period from {{#formatDate new_start_date "MM/DD/YYYY"}} {{/formatDate }} to {{#formatDate new_end_date "MM/DD/YYYY"}} {{/formatDate }}
                    {{/ifEquals}}

                </span>
                <br>
            {{/each}}
        </div>
    </div>
</div>
<hr>
<br>
<div id="financialPeriodChangesInformation" class="mb-2">
    <div class="row">
        <div class="col-12">
            <h4><b>Financial period changes</b></h4>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            {{#each entryData.financialPeriodChangesInfo}}
                <span>
                    {{formatDate date_changed "MM/DD/YYYY"}} | {{changed_by}}
                    <br>
                    <b>Financial period changed:</b> OLD period from {{formatDate old_start_date "MM/DD/YYYY"}} to {{formatDate old_end_date "MM/DD/YYYY"}},
                    NEW period from {{formatDate new_start_date "MM/DD/YYYY"}} to {{formatDate new_end_date "MM/DD/YYYY"}}
                </span>
                <br>
            {{/each}}
        </div>
    </div>
</div>
<hr>
<br>
<div id="accordion">
    <div class="row mb-2">
        <div class="col-12">
            <h4><b>Requests for Information</b></h4>
        </div>
    </div>

    {{#if entryData.requestedInfo}}
        <div class="table-responsive">
            <table id="table-request"   class="table w-100">
                <thead>
                <tr>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                {{#each entryData.requestedInfo}}
                    <tr>
                        <td class="m-0 p-0">
                            <div class="card mb-0">
                                <div id="request-{{id}}">
                                    <button class="btn btn-outline-secondary border-white text-left font-16 w-100 " data-toggle="collapse" data-target="#response-{{id}}" aria-expanded="false" aria-controls="response-{{id}}">
                                        {{#each reminders}}
                                            {{#formatDate reminder_date "MM/DD/YYYY"}} {{/formatDate }} | {{description}} | <b>REMINDER SENT</b> <br>
                                        {{/each}}
                                        <i class="fa fa-angle-down" aria-hidden="true"></i>
                                        {{#formatDate requested_at "MM/DD/YYYY"}} {{/formatDate }} | {{username}} | <b>{{status}}</b>
                                        <br> <b>Reason:</b> {{comment}}

                                    </button>
                                </div>

                                <div id="response-{{id}}" class="collapse" aria-labelledby="request-{{id}}" data-parent="#accordion">
                                    {{#each client_response}}
                                        <div class="card-body">
                                    <span >
                                        {{#formatDate returned_at "MM/DD/YYYY"}} {{/formatDate }} | {{username}} <br>
                                        <b>Response</b>: {{comment}}
                                    </span>
                                            <hr>
                                            <span >
                                        <b>FILES:</b>
                                    </span>
                                            <br>
                                            {{#each files}}
                                                <a href="/substance/{{../../../entryData.entryId}}/download/{{../request_id}}/{{_id}}"
                                                   target="_blank" >
                                                    <span class="small"> &#8226; {{originalname}} </span>
                                                </a>
                                                <br>
                                            {{else}}

                                                <span class="small">
                                                &#8226; No files uploaded
                                            </span>


                                            {{/each}}
                                        </div>

                                    {{else}}
                                        <div class="card-body">
                                    <span >
                                        No response from the client yet
                                    </span>
                                        </div>
                                    {{/each}}
                                </div>
                            </div>
                        </td>
                    </tr>

                {{/each}}
                </tbody>
            </table>
        </div>
    {{/if}}



</div>


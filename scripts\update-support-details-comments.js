const EntryModel = require("../models/entry").EntryModel;
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config();


const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const result = await updateSupportDetailsComments();

    console.log('Script ejecutado correctamente', result);
  } catch (error) {
    console.error('Error en el script:', error);
  } finally {
    mongoose.disconnect();
  }
}


async function updateSupportDetailsComments() {
  try {

    let updateLog = [['Entry ID', 'Company', 'Update date', 'Action']];

    const entriesToUpdate = await EntryModel.find({ version: { $ne: "5.0" }, "relevant_activities.none.selected": true, "relevant_activities.none_remarks": {"$nin": [null, ""]} },
      { _id: 1, company: 1, relevant_activities: 1, supporting_details: 1 });

    console.log("entriesToUpdate  ", entriesToUpdate.length);
    if (entriesToUpdate.length > 0) {
      for (let i = 0; i < entriesToUpdate.length; i++) {
        console.log('processing ' + i + '  from ' + entriesToUpdate.length)

        const entry = entriesToUpdate[i];

        try {
          
          let supportingDetails = {}

          if (!entry.supporting_details) {
            supportingDetails = {
              support_comment: entry.relevant_activities?.none_remarks,
              support_attachments: entry.relevant_activities?.evidence_none_activities || []
            }
          } 
          else {
            supportingDetails = {
              support_comment: entry.supporting_details?.support_comment ?
                entry.supporting_details.support_comment + "\n" + entry.relevant_activities?.none_remarks :
                entry.relevant_activities?.none_remarks,
              support_attachments: [
                ...entry.supporting_details?.support_attachments || [],
                ...entry.relevant_activities?.evidence_none_activities || []
              ]
            }
          }

          entry.relevant_activities.none_remarks = "";
          entry.relevant_activities.evidence_none_activities = [];

          
          const result = await EntryModel.updateOne({ _id: entry._id }, {
            $set: {
              relevant_activities: entry.relevant_activities,
              supporting_details: supportingDetails
            }
          });
          


          if (result.nModified > 0) {
            updateLog.push([entry._id?.toString(), entry.company, new Date(), 'SUCCESS']);
          } else {
            updateLog.push([entry._id?.toString(), entry.company, new Date(), 'ERROR: NOT FOUND']);
          }
        } catch (error) {
          console.error('Error:', error.message);
          updateLog.push([entry._id?.toString(), entry.company, new Date(), 'ERROR UPDATING']);
        }
      }
    }


    // create entities bo
    console.log("entries updated ", updateLog.length - 1);

    const filename = 'update_entries_support_details_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'entries ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalRows": updateLog.length - 1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}


runScript();
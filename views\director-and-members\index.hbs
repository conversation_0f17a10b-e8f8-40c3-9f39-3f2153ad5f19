<main class="">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="card-box">
          <div class="card-title">
            <h1>{{title}}</h1>
            <p>Welcome {{user.name}}</p>
          </div>
          <div class="card-body">
            <div class="row">
              {{#if hasProductionOfficeGroups}}
              <div class="col-md-4">
                <div class="card bg-primary text-white">
                  <div class="card-body">
                    <h5 class="card-title text-white">Search</h5>
                    <a href="/director-and-members/search" class="btn btn-light btn-sm waves-effect">Search Data</a>
                  </div>
                </div>
              </div>
              {{/if}}

              {{#if showImportDataModule}}
              <div class="col-md-4">
                <div class="card bg-primary text-white">
                  <div class="card-body">
                    <h5 class="card-title text-white">Import</h5>
                    <a href="/director-and-members/import-data" class="btn btn-light btn-sm waves-effect">
                      Import Data
                    </a>
                  </div>
                </div>
              </div>
              {{/if}}
            </div>


            <div class="row mt-2">
              <div class="col-md-2">
                <a href="/" class="btn solid royal-blue w-100">
                  Back
                </a>
              </div>
            </div>

          </div>


        </div>
      </div>
    </div>
  </div>
</main>
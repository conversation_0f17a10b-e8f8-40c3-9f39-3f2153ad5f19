const fileTypeSchema = require('./file');

const mongoose = require('mongoose');

const addressSchema = new mongoose.Schema({
  primaryAddress: { type: String, required: false },
  secondaryAddress: { type: String, required: false },
  country: { type: String, required: false },
  state: { type: String, required: false },
  postalCode: { type: String, required: false },
  city: { type: String, required: false },
});

const organizationSchema = new mongoose.Schema({
  type: { type: String, required: true, default: 'corporate' },
  lockedByFileReview: { type: mongoose.Schema.Types.ObjectId, ref: 'filereview', required: false },
  percentage: { type: String, required: false },
  groups: [String],
  ownerShip:  { type: String, required: false },
  details: {
    isTridentClient:  { type: Boolean, required: false },
    organizationName: { type: String, required: false },
    incorporationNumber: { type: String, required: false },
    taxResidence: { type: String, required: false },
    businessNumber: { type: String, required: false },
    incorporationDate: { type: Date, required: false },
    incorporationCountry: { type: String, required: false },
    files: [fileTypeSchema],
  },
  detailsPartner: {
    files: [fileTypeSchema]
  },
  principalAddress: addressSchema,
  isSamePrincipalAddress: {type: Boolean, required: false, default: false},
  mailingAddress: addressSchema,
  listedCompanyDetails: {
    active: { type: Boolean, required: false },
    stockCode: { type: String, required: false },
  },
  limitedCompanyDetails: {
    active: { type: Boolean, required: false },
    registrationNumber: { type: String, required: false },
    registrationDate: { type: Date, required: false },
  },
  mutualFundDetails: {
    active: { type: Boolean, required: false },
    files: [fileTypeSchema],
  },
  foundation: {
    active: { type: Boolean, required: false },
    isFoundation: { type: Boolean, required: false, default: false },
    files: [fileTypeSchema],
    country: { type: String, required: false },
  },
  worldCheck: {
    files: [fileTypeSchema]
  },
  positions: [{
    referenceId: { type: mongoose.Schema.Types.ObjectId, ref: 'naturalperson', required: false},
    type: { type: String, required: false },
  }],
  partitionkey: { type: String, required: false, default: 'organization' },
  createdAt: { type: Date, required: false},
  updatedAt: { type: Date, required: false },
});

module.exports = mongoose.model('organization', organizationSchema);


<div class="row justify-content-between mt-2">
  <div class="d-flex col-12 justify-content-left px-0">
    <input type="text" hidden readonly id="paginationOptions" name="paginationOptions" value=""
      data-total-items="{{pagination.totalItemCount}}"
      data-page-count="{{pagination.pageCount}}"
      data-form-name="{{formName}}"
    />
    <input type="text" hidden id="pageNumberInput" name="page" value="1" form="{{formName}}"
      data-page-number="{{pagination.pageNumber}}"
    />
    <input type="text" hidden id="pageSizeInput" name="pageSize" value="10" form="{{formName}}"
      data-original-page-size="{{pagination.pageSize}}"
    />
    <div class="col-12 d-flex flex-wrap">
      <div class="col-12 col-md-12 col-lg-8 col-xl-6 d-flex flex-wrap justify-content-start p-0">
        <div class="w-100 col-sm-12 col-md-6 col-lg-4 col-xl-3 p-0">
          <div class="input-group flex-nowrap w-100 pr-md-2">
            <div class="input-group-prepend">
              <label id="pageSizeLabel" class="input-group-text" for="pageSizeControl">Page size</label>
            </div>
            <select class="form-control" style="cursor: pointer;" name="pageSize" id="pageSizeControl" >
              <option {{#ifEquals pagination.pageSize 10}}selected{{/ifEquals}} value="10">10</option>
              <option {{#ifEquals pagination.pageSize 25}}selected{{/ifEquals}} value="25">25</option>
              <option {{#ifEquals pagination.pageSize 50}}selected{{/ifEquals}} value="50">50</option>
              <option {{#ifEquals pagination.pageSize 100}}selected{{/ifEquals}} value="100">100</option>
            </select>
          </div>
        </div>
        <div class="col-sm-12 col-md-6 col-lg-8 col-xl-5 p-0">
            <div class="input-group flex-nowrap w-100">
              <div class="input-group-prepend">
                <label id="pageSortControlLabel" class="input-group-text w-100" for="pageSortControl" >Sort by</label>
              </div>
              <select class="form-control w-100" style="cursor: pointer;" name="sortBy" id="pageSortControl" form="{{formName}}">
                {{#each pagination.sorting.options}}
                <option {{#ifEquals ../pagination.sorting.sortBy code}}selected{{/ifEquals}} value="{{code}}">{{name}}</option>
                {{/each}}
              </select>

              <select class="form-control w-50" style="cursor: pointer;" name="sortType" id="pageSortTypeControl" form="{{formName}}">
                <option {{#ifEquals pagination.sorting.type 1}}selected{{/ifEquals}}  value="1">ASC</option>
                <option {{#ifEquals pagination.sorting.type -1}}selected{{/ifEquals}} {{#unless pagination.sorting.type}} selected {{/unless}} value="-1">DESC</option>
              </select>
            </div>
        </div>
        <div class="w-100 col-sm-12 col-lg-6 col-xl-4 p-0">
          <div class="input-group w-100 pl-xl-2 pb-md-2">
            <input type="text" id="pageSearch" class="form-control" value="" placeholder="Search" data-table-id="{{tableId}}"
              {{#ifCond searching '!==' "true"}}hidden{{/ifCond}}
            />
          </div>
        </div>
      </div>

      <div class="col-12 col-md-12 col-lg-4 col-xl-6">
        <div class="row flex flex-inline justify-content-end">
            <div class="mr-3 pt-1 pb-2">
              <span id="paginationInfo">Showing 0 - 10 of {{pagination.totalItemCount}} entries</span>
            </div>
            <nav>
              <ul class="pagination justify-content-sm-end mr-2">
                <li class="page-item">
                  <select class="form-control" style="cursor: pointer; min-width: 80px; max-width:150px" name="selectPage"
                    id="selectPageControl">
                    <option disabled value="">Select page</option>
                  </select>
                </li>
                <!--PREV PAGE BUTTON-->
                <li class="page-item {{#unless pagination.hasPrevious}}disabled{{/unless}}" style="cursor: pointer;">
                  <a class="page-link pagination-step-change"
                    data-page="{{subtract pagination.pageNumber 1}}"><span>Previous</span></a>
                </li>
            
                {{#ifCond pagination.pageNumber '>' 2}}
                <li class="page-item" style="cursor: pointer;">
                  <a class="page-link pagination-step-change" data-page="{{subtract pagination.pageNumber 2}}">
                    {{subtract pagination.pageNumber 2}}
                  </a>
                </li>
                {{/ifCond}}
            
                {{#ifCond pagination.pageNumber '>' 1}}
                <li class="page-item" style="cursor: pointer;">
                  <a class="page-link pagination-step-change" data-page="{{subtract pagination.pageNumber 1}}">
                    {{subtract pagination.pageNumber 1}}</a>
                </li>
                {{/ifCond}}
            
                <!-- CURRENT PAGE NUMBER -->
                <li class="page-item active" style="cursor: pointer;">
                  <a class="page-link pagination-step-change" data-page="{{pagination.pageNumber}}">{{pagination.pageNumber}}</a>
                </li>
            
            
                {{#ifCond (add pagination.pageNumber 1) '<=' pagination.pageCount}} <li class="page-item" style="cursor: pointer;">
                  <a class="page-link pagination-step-change" data-page="{{add pagination.pageNumber 1}}">{{add
                    pagination.pageNumber 1}}</a>
                  </li>
                  {{/ifCond}}
                  {{#ifCond (add pagination.pageNumber 2) '<=' pagination.pageCount}} <li class="page-item"
                    style="cursor: pointer;">
                    <a class="page-link pagination-step-change" data-page="{{add pagination.pageNumber 2}}">{{add
                      pagination.pageNumber
                      2}}</a>
                    </li>
                    {{/ifCond}}
            
                    <!--NEXT PAGE BUTTON-->
                    <li class="page-item {{#unless pagination.hasNext}}disabled{{/unless}}" style="cursor: pointer;">
                      <a class="page-link pagination-step-change" data-page="{{add pagination.pageNumber 1}}"><span>Next</span> </a>
                    </li>
              </ul>
            </nav>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  let $filterForm = $(`#${$("#paginationOptions").data('form-name')}`);
  const totalItems = Number($("#paginationOptions").data('total-items'));
  const pageCount = Number($("#paginationOptions").data('page-count'));
  let pageNumber = Number($("#pageNumberInput").data('page-number'));
  const originalPageSize = Number($("#pageSizeInput").data('original-page-size'));
  $(document).ready(function () {
    $('.filter').trigger('change');
    $("#pageSizeControl").select2();
    $("#pageSortControl").select2();
    $("#pageSortTypeControl").select2();
    initSelectPageNumber();
    calculatePaginationText()

    $('.search-on-enter').on('keypress', function (e) {
      if (e.which === 13) {
        e.preventDefault();
        search();
      }
    });

  });

  function search() {
    // Serializa el formulario
    const filters = {
      page: pageNumber,
      pageSize: $('#pageSizeControl').val(),
    };
    $("input[name='page']").val(filters.page);
    $("input[name='pageSize']").val(filters.pageSize);

    // Filters form 
    $filterForm.submit();
  }

  $('#pageSizeControl').on('change', function () {
    if (originalPageSize !== Number($(this).val())) {
      pageNumber = 1;
    }
    search();
  });
  
  $('#selectPageControl').on('change', function () {
    const newPage = Number($(this).val())
    if (newPage !== pageNumber) {
      pageNumber = newPage;
    }
    search();
  });

  $('#pageSortControl').on('change', function () {
    pageNumber = 1;
    search();
  });


  $('#pageSortTypeControl').on('change', function () {
    pageNumber = 1;
    search();
  });


  $(".pagination-step-change").on('click', function() {
    const page = $(this).data('page')
    pageNumber = page;
    search();
  })

  $('#pageSearch').on('input', function () {
    const searchTerm = $(this).val();
    const tableId= $(this).data('table-id');
    const table = $(`#${tableId}`);
    table.DataTable().search(searchTerm).draw();

  });


  function calculatePaginationText() {
    const itemsPerPage = Number($('#pageSizeControl').val());
    const startIndex = (pageNumber - 1) * itemsPerPage;

    const endIndex = Math.min(startIndex + itemsPerPage - 1, totalItems - 1);
    const itemsOnPage = endIndex - startIndex + 1;
    const paginationText = `Showing ${startIndex + 1} - ${startIndex + itemsOnPage} of ${totalItems} entries`;
    $("#paginationInfo").text(paginationText)
    return ;
  }

  function initSelectPageNumber(){
    let $select = $("#selectPageControl");

    const maxPage = pageCount || 1;
    for (let i = 1; i <= maxPage; i++) {
      const $option = $("<option>", { value: i, text: i });
      if (i === pageNumber) {
        $option.attr('selected', 'selected');
      }
      $select.append($option);
    }

    $select.select2();
  }

</script>
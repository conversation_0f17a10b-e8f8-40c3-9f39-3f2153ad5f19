const CompanyModel = require("../models/company").schema;
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config();


const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const result = await enableCompanyModules();

    console.log('Script executed successfully', result);
  } catch (error) {
    console.error('Error in the script:', error);
  } finally {
    mongoose.disconnect();
  }
}


async function enableCompanyModules() {
  try {

    let updateLog = [['ID', 'Company Code', 'Update date', 'Action']];
    const companiesToUpdate = await CompanyModel.find({ "substanceModule.active": { $exists: false }},
      { _id: 1, code: 1, isDeleted: 1});

    console.log("companiesToUpdate  ", companiesToUpdate.length);
    if (companiesToUpdate.length > 0) {
      for (let i = 0; i < companiesToUpdate.length; i++) {
        console.log('processing ' + i + '  from ' + companiesToUpdate.length)

        const company = companiesToUpdate[i];

        try {
          
          const isActive = company.isDeleted !== true;

          const result = await CompanyModel.updateOne({ _id: company._id }, {
            $set: {
              "substanceModule.active": isActive,
              "accountingRecordsModule.active": false,
              "dirboModule.active": isActive
            },
          });
          

          if (result.nModified > 0) {
            updateLog.push([company._id?.toString(), company.code, new Date(), 'SUCCESS']);
          } else {
            updateLog.push([company._id?.toString(), company.code, new Date(), 'ERROR: NOT FOUND']);
          }
        } catch (error) {
          console.error('Error:', error.message);
          updateLog.push([company._id?.toString(), company.code, new Date(), 'ERROR UPDATING']);
        }
      }
    }


    // create entities bo
    console.log("companies updated ", updateLog.length - 1);

    const filename = 'update_company_modules_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'companies ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalRows": updateLog.length - 1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}


runScript();
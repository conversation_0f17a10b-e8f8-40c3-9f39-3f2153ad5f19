<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h1>{{ title }}</h1>
                    <p>Welcome {{ user.name }}</p>

                    <div class="row">
                        {{#ifCond authentication.isSubsManagers '||' authentication.isSubsSuperUser }}
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title text-white">Substance Management</h5>
                                    <a href="/substance/" class="btn btn-light btn-sm waves-effect"
                                    >Manage the substance submission data</a
                                    >
                                </div>
                            </div>
                        </div>
                        {{/ifCond}}
                        {{#if isFilewReviewer}}
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title text-white">TBVI File Review</h5>
                                    <a href="/file-reviewer/dashboard" class="btn btn-light btn-sm waves-effect"
                                    >Manage the TBVI file reviews</a
                                    >
                                </div>
                            </div>
                        </div>
                        {{/if}}
                        {{#if isClientManagement}}
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title text-white">Client Management</h5>
                                    <a href="/client-management/" class="btn btn-light btn-sm waves-effect"
                                    >Manage Clients</a
                                    >
                                </div>
                            </div>
                        </div>
                        {{/if}}
                    </div>
                    <div class="row">
                        {{#ifCond authentication.isIncorporationOfficer '||' authentication.isIncorporationSuperAdmin}}
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title text-white">TBVI Client Incorporation</h5>
                                    <a href="/client-incorporation/" class="btn btn-light btn-sm waves-effect">
                                        Manage incorporation requests
                                    </a>
                                </div>
                            </div>
                        </div> 
                        {{/ifCond}}
                        {{#if hasDirectorAccess}} 
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Director and BO</h5>
                                        <a href="/director-and-bo/" class="btn btn-light btn-sm waves-effect">
                                            Manage Director/BO data
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {{/if}}
                        {{#if hasDirectorAccess}} 
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Company Information Management</h5>
                                        <a href="/director-and-members/" class="btn btn-light btn-sm waves-effect">
                                            Manage Director/Member/Partner/BO data
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {{/if}}
                        {{#if isAccountingUser}}
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title text-white">Financial Return Management</h5>
                                    <a href="/financial-report-management/" class="btn btn-light btn-sm waves-effect">
                                        Manage Submissions and Reporting
                                    </a>
                                </div>
                            </div>
                        </div>
                        {{/if}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

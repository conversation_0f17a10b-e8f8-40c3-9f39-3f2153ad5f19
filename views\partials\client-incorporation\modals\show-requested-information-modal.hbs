{{!-- CONFIRM CLIENT MODAL --}}
<div class="modal fade" id="showRequestedInformationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Show Requested information</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">


                <div class="row showRequestInfo" id="showInfo" style="display: none">
                    <div class="col-md-12">
                        <h5>Previously required information</h5>
                        <div class="table-responsive">
                            <table id="requestInfoTable" class="table table-sm  w-100 table-striped">
                                <thead>
                                <tr>
                                    <th style="width: 25%">Requested At</th>
                                    <th style="width: 75%">Question</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- RESPONSIVE TABLE END -->
                </div>



                <div class="row showRequestInfo" id="showFiles" style="display: none">
                    <div class="col-md-12">
                        <h5>Required Files</h5>
                        <div class="table-responsive">
                            <table id="requestedFilesTable" class="table table-sm table-striped">
                                <thead>
                                <tr>
                                    <th style="width: 25%">Requested At</th>
                                    <th scope="col" style="width: 65%">File Name</th>
                                    <th scope="col" style="width: 10%">Download</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>


    $('#showRequestedInformationModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        let incorporationId = button.data('submission-id');

        $.ajax({
            type: "GET",
            url: "./" + incorporationId + '/request-information',
            data: $(this).serialize(),
            success: function (data) {
                if (data.status === 200) {
                    if (data.requestInformation) {
                        if (data.requestInformation.questions && data.requestInformation.questions.length > 0) {
                            for (let i = 0; i < data.requestInformation.questions.length; i++) {
                                const row = '<tr><td>' + data.requestInformation.questions[i].requestedAt + '</td>' +
                                        '<td>'+ data.requestInformation.questions[i].question +'</td></tr>';
                                $('#requestInfoTable > tbody:last-child').append(row);
                            }
                        }
                        else{
                            const row = '<tr><td colspan="2" class="text-center font-italic"> There are no questions </td> </tr>';
                            $('#requestInfoTable > tbody:last-child').append(row);
                        }
                        if (data.requestInformation.files && data.requestInformation.files.length > 0) {
                            for (let i = 0; i < data.requestInformation.files.length; i++) {
                                const row = '<tr> <td>'+data.requestInformation.files[i].requestedAt +
                                        '</td> + <td>' + data.requestInformation.files[i].originalName + '</td>' +
                                        '<td class="pl-2 py-1 text-center align-middle">' + '<a href="/client-incorporation/' + incorporationId + '/download-document/' +
                                        data.requestInformation.files[i].fileId + '?requestDataId=' + data.requestInformation.files[i].requestId +
                                        '" target="_blank"> <i class="fa fa-download" aria-hidden="true"></i></a></td></tr>';
                                $('#requestedFilesTable > tbody:last-child').append(row);
                            }
                        }
                        else{
                            const row = '<tr><td colspan="3" class="text-center font-italic"> There are no required files</td> </tr>';
                            $('#requestedFilesTable > tbody:last-child').append(row);
                        }
                        $(".showRequestInfo").show(200);
                    }
                } else {
                    toastr["warning"]('Sorry, Error getting the request information. Try again later...');
                }
            },
            error: function (err) {
                toastr["error"]('Sorry,  Error getting the request information. Try again later...');
            }
        });
    });

    $('#showRequestedInformationModal').on('hide.bs.modal', function (event) {
        $(".showRequestInfo").hide();
        $('#requestedFilesTable > tbody').html('');
        $('#requestInfoTable > tbody').html('');

    });


</script>

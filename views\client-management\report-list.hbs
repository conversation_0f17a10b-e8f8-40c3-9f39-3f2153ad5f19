<div class="card">
    <div class="card-body">

        <div class="row">
            <div class="col-3"></div>
            <div class="col-6">
                <h1>{{title}}</h1>
                <br/><br/>
                <table id="scroll-horizontal-datatable" class="table w-100 nowrap">
                    <thead>
                    <tr class="text-center">
                        <th>REPORT NAME</th>
                        <th>CREATED AT</th>
                        <th>DOWNLOAD</th>
                    </tr>
                    </thead>
                    <tbody>
                    {{#each reports}}
                        <tr class="text-center">
                            <td>{{name}}</td>
                            <td>{{createdAt}}</td>
                            <td>
                                <a href="/client-management/report-invoices/download/{{name}}" target="_blank"
                                   class="btn btn-primary waves-effect waves-light">
                                    Download
                                </a>
                            </td>
                        </tr>
                    {{/each}}
                    </tbody>
                </table>
            </div>
            <div class="col-3"></div>
        </div>
        <br>
        <div class="row">
            <div class="col-3"></div>
            <div class="col-6">

                <div class="row">
                    <div class="mb-3 ml-3">
                        <a href='/client-management/' class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                    </div>
                </div>
            </div>
            <div class="col-3"></div>
        </div>
    </div>
</div>

<script type="text/javascript">
    let table;
    $(document).ready(function () {

        table = $("#scroll-horizontal-datatable").DataTable({
            "pageLength": 50,
            "order": [[1, "des"]],
            scrollX: !0,
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>"
                }
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
            }
        });
    });
</script>

const excel = require("node-excel-export");
const moment = require("moment");
const { STANDARD_DATE_FORMAT } = require('../../utils/constants');
const {
  REPORT_STATUS, 
  ACCOUNTING_SERVICE_TYPES
} = require('../../utils/financialReportConstants');

exports.generatePaginatedSearchXLSX = async function (exportData) {
  try {
    const styles = {
      headerTable: {
        fill: {
          fgColor: {
            rgb: "ffffff",
          },
        },
        border: {
          top: { style: "thin", color: "000000" },
          bottom: { style: "thin", color: "000000" },
          left: { style: "thin", color: "000000" },
          right: { style: "thin", color: "000000" },
        },
        font: {
          color: {
            rgb: "000000",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
    };

    const specification = {
      Email: {
        displayName: "Email",
        headerStyle: styles.headerTable,
        width: 120,
      },
      EntityName: {
        displayName: "Entity Name",
        headerStyle: styles.headerTable,
        width: 120,
      },
      RegistrationCode: {
        displayName: "Registration Code",
        headerStyle: styles.headerTable,
        width: 120,
      },
      MasterClientCode: {
        displayName: "Master Client Code",
        headerStyle: styles.headerTable,
        width: 160,
      },
      IncorporationNumber: {
        displayName: "Incorporation Number",
        headerStyle: styles.headerTable,
        width: 120,
      },
      IncorporationDate: {
        displayName: "Incorporation Date",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Status: {
        displayName: "Status",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Created: {
        displayName: "Created",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Submitted: {
        displayName: "Submitted",
        headerStyle: styles.headerTable,
        width: 120,
      },
      FPStart: {
        displayName: "Financial Period Start Date",
        headerStyle: styles.headerTable,
        width: 180,
      },
      FPEnd: {
        displayName: "Financial Period End Date",
        headerStyle: styles.headerTable,
        width: 180,
      },
      ReferralOffice: {
        displayName: "Referral Office",
        headerStyle: styles.headerTable,
        width: 120,
      },
      ExemptCompany: {
        displayName: "Exempt Company",
        headerStyle: styles.headerTable,
        width: 120,
      }
    };

    const dataset = exportData.map((item) =>{
      let reportStatusLbl =item.status;

      if (item.reportDetails &&
        (item.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_COMPLETE || 
         item.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE) &&
        item.status === REPORT_STATUS.CONFIRMED) {
      
        reportStatusLbl = "COMPLETED";
    }
      
      return {
        Email: item.createdBy,
        EntityName: item.companyData.name,
        RegistrationCode: item.companyData.code,
        MasterClientCode: item.masterClientCode,
        IncorporationNumber: item.companyData.incorporationcode,
        IncorporationDate: item.companyData.incorporationdate ? moment(item.companyData.incorporationdate).format("YYYY-MM-DD") : "",
        Status: reportStatusLbl,
        Created: moment(item.createdAt).format("YYYY-MM-DD"),
        Submitted: item.submittedAt ? moment(item.submittedAt).format("YYYY-MM-DD") : '',
        FPStart: item.financialPeriod ? moment(item.financialPeriod.start).format("YYYY-MM-DD") : "",
        FPEnd: item.financialPeriod ? moment(item.financialPeriod.end).format("YYYY-MM-DD") : "",
        ReferralOffice: item.companyData.referral_office,
        ExemptCompany: item.reportDetails?.isExemptCompany ? "Yes" : "No"
      }
    });

    const searchXlsx = excel.buildExport([
      {
        name: "Financial Reports",
        specification: specification,
        data: dataset,
      },
    ]);


    return searchXlsx
  } catch (error) {
    console.log("Error generating the financial report paginated search export xlsx: ", error);
    return null
  }
};


exports.generateAccountingCompanyDashboardSearchXLSX = async function (exportData) {
  try {
    const styles = {
      headerTable: {
        fill: {
          fgColor: {
            rgb: "ffffff",
          },
        },
        border: {
          top: { style: "thin", color: "000000" },
          bottom: { style: "thin", color: "000000" },
          left: { style: "thin", color: "000000" },
          right: { style: "thin", color: "000000" },
        },
        font: {
          color: {
            rgb: "000000",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
    };

    const specification = {
      EntityName: {
        displayName: "Entity Name",
        headerStyle: styles.headerTable,
        width: 200,
      },
      ReferralOffice: {
        displayName: "Referral Office",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Status: {
        displayName: "Status",
        headerStyle: styles.headerTable,
        width: 200,
      },
      MasterClientCode: {
        displayName: "Master Client Code",
        headerStyle: styles.headerTable,
        width: 120,
      },
      IncorporationNumber: {
        displayName: "Incorporation Number",
        headerStyle: styles.headerTable,
        width: 200,
      },
      IncorporationDate: {
        displayName: "Incorporation Date",
        headerStyle: styles.headerTable,
        width: 120,
      },
      InitialFPEndDate: {
        displayName: "Initial FP End Date",
        headerStyle: styles.headerTable,
        width: 120,
      },
      ConfirmedPeriod: {
        displayName: "Current Period",
        headerStyle: styles.headerTable,
        width: 120,
      },
      ExemptCompany: {
        displayName: "Exempt Company",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Deadline: {
        displayName: "Deadline",
        headerStyle: styles.headerTable,
        width: 120,
      },
      DaysToDeadline: {
        displayName: "Days to Deadline",
        headerStyle: styles.headerTable,
        width: 120,
      },
      Penalty: {
        displayName: "Penalty",
        headerStyle: styles.headerTable,
        width: 120,
      },
    };
    const dataset = exportData.map((item) => {
      return {
        EntityName: item.name,
        ReferralOffice: item.referralOffice,
        Status: item.status,
        MasterClientCode: item.masterClientCode,
        IncorporationNumber: item.incorporationCode,
        IncorporationDate: item.incorporationDate ? moment(item.incorporationDate).format(STANDARD_DATE_FORMAT) : "",
        InitialFPEndDate: item.initialFPDate ? moment(item.initialFPDate).format(STANDARD_DATE_FORMAT) : "",
        ConfirmedPeriod: item.confirmedPeriod ? 
          (item.confirmedPeriod !== "NOT STARTED" ? moment(item.confirmedPeriod).format(STANDARD_DATE_FORMAT) : item.confirmedPeriod) 
          : "",
        ExemptCompany: item.isExemptCompany,
        Deadline: item.deadline ? moment(item.deadline).format(STANDARD_DATE_FORMAT) : "",
        DaysToDeadline: item.daysToDeadline,
        Penalty: item.penalty     
      }
    });

    const searchXlsx = excel.buildExport([
      {
        name: "Companies Deadlines",
        specification: specification,
        data: dataset,
      },
    ]);


    return searchXlsx
  } catch (error) {
    console.log("Error generating the companies dashboard search export xlsx: ", error);
    return null
  }
};

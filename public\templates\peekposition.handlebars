<p>
    DETAILS
    {{#ifCond position.lockedByFileReview '==' reviewId}}
    {{#if positionInformation.details.complete}}
        <span class="badge badge-success">Complete</span>
    {{else}}
        <span class="badge badge-warning text-dark">Incomplete</span>
    {{/if}}
    {{/ifCond}}
</p>
<div class="row">
    <div class="col-2">Full Name:</div>
    <div class="col-4 font-weight-bold">{{ position.details.fullName }}</div>
    <div class="col-2">First Name:</div>
    <div class="col-4 font-weight-bold">{{ position.details.firstName }}</div>
</div>
<div class="row pt-2">
    <div class="col-2">Middle Name:</div>
    <div class="col-4 font-weight-bold">{{ position.details.middleName }}</div>
    <div class="col-2">Last Name:</div>
    <div class="col-4 font-weight-bold">{{ position.details.lastName }}</div>
</div>
<div class="row pt-2">
    <div class="col-2">Occupation:</div>
    <div class="col-4 font-weight-bold">{{ position.details.occupation }}</div>
    <div class="col-2">Date of Birth:</div>
    <div class="col-4 font-weight-bold">{{#formatDate position.details.birthDate "MM/DD/YYYY"}} {{/formatDate }}</div>
</div>
<div class="row pt-2">
    <div class="col-2">Nationality:</div>
    <div class="col-4 font-weight-bold">{{ position.details.nationality }}</div>
    <div class="col-2">Country of Birth:</div>
    <div class="col-4 font-weight-bold">{{ position.details.countryBirth }}</div>
</div>
<hr class="mt-3"/>
<p>
    IDENTIFICATION
    {{#ifCond position.lockedByFileReview '==' reviewId}}
    {{#if positionInformation.identification.complete}}
        <span class="badge badge-success">Complete</span>
    {{else}}
        <span class="badge badge-warning text-dark">Incomplete</span>
    {{/if}}
    {{/ifCond}}
</p>
<div class="row">
    <div class="col-2">Type of Identification:</div>
    <div class="col-4 font-weight-bold" style="text-transform: capitalize;">
        {{  position.identification.identificationType }}
    </div>
    <div class="col-2">Country of Issue:</div>
    <div class="col-4 font-weight-bold">{{  position.identification.issueCountry }}</div>
</div>
<div class="row pt-2">
    <div class="col-2">Expiry Date:</div>
    <div class="col-4 font-weight-bold">{{#formatDate position.identification.expiryDate "MM/DD/YYYY"}} {{/formatDate }}</div>
</div>
<hr class="mt-3"/>
<p>
    PRINCIPAL ADDRESS
    {{#ifCond position.lockedByFileReview '==' reviewId}}
    {{#if positionInformation.principalAddress.complete}}
        <span class="badge badge-success">Complete</span>
    {{else}}
        <span class="badge badge-warning text-dark">Incomplete</span>
    {{/if}}
    {{/ifCond}}
</p>
<div class="row">
    <div class="col-2">Address - 1st Line:</div>
    <div class="col-4 font-weight-bold">{{  position.principalAddress.primaryAddress }}</div>
    <div class="col-2">Address - 2nd Line:</div>
    <div class="col-4 font-weight-bold">{{ position.principalAddress.secondaryAddress}}</div>
</div>
<div class="row pt-2">
    <div class="col-2">Country:</div>
    <div class="col-4 font-weight-bold">{{ position.principalAddress.country }}</div>
    <div class="col-2">State:</div>
    <div class="col-4 font-weight-bold">{{ position.principalAddress.state }}</div>
</div>
<div class="row pt-2">
    <div class="col-2">City:</div>
    <div class="col-4 font-weight-bold">{{ position.principalAddress.city }}</div>
    <div class="col-2">Postal Code:</div>
    <div class="col-4 font-weight-bold">{{ position.principalAddress.postalCode}}</div>
</div>
<hr class="mt-3"/>
<p>
    MAILING ADDRESS
    {{#ifCond position.lockedByFileReview '==' reviewId}}
    {{#if positionInformation.mailingAddress.complete}}
        <span class="badge badge-success">Complete</span>
    {{else}}
        <span class="badge badge-warning text-dark">Incomplete</span>
    {{/if}}
    {{/ifCond}}
</p>
<div class="row">
    <div class="col-2">Address - 1st Line:</div>
    <div class="col-4 font-weight-bold">{{ position.mailingAddress.primaryAddress }}</div>
    <div class="col-2">Address - 2nd Line:</div>
    <div class="col-4 font-weight-bold">{{ position.mailingAddress.secondaryAddress }}</div>
</div>
<div class="row pt-2">
    <div class="col-2">Country:</div>
    <div class="col-4 font-weight-bold">{{ position.mailingAddress.country }}</div>
    <div class="col-2">State:</div>
    <div class="col-4 font-weight-bold">{{ position.mailingAddress.state }}</div>
</div>
<div class="row pt-2">
    <div class="col-2">City:</div>
    <div class="col-4 font-weight-bold">{{ position.mailingAddress.city }}</div>
    <div class="col-2">Postal Code:</div>
    <div class="col-4 font-weight-bold">{{ position.mailingAddress.postalCode}}</div>
</div>
<hr class="mt-3"/>
<p>
    TAX ADVICE
    {{#ifCond position.lockedByFileReview '==' reviewId}}
    {{#if positionInformation.taxResidence.complete}}
        <span class="badge badge-success">Complete</span>
    {{else}}
        <span class="badge badge-warning text-dark">Incomplete</span>
    {{/if}}
    {{/ifCond}}
</p>
<div class="row">
    <div class="col-6">Confirmation Regarding Legal / Tax Advice:</div>
    <div class="col-6 font-weight-bold">
        {{#if position.taxResidence.confirmation}}Confirmed {{else}} Not Confirmed {{/if}}
    </div>
    <div class="col-6">Tax Residence:</div>
    <div class="col-6 font-weight-bold">{{ position.taxResidence.taxResidence }}</div>
</div>
<hr class="mt-3"/>
<p>
    ADVISOR DETAILS
    {{#ifCond position.lockedByFileReview '==' reviewId}}
    {{#if positionInformation.advisorDetails.complete}}
        <span class="badge badge-success">Complete</span>
    {{else}}
        <span class="badge badge-warning text-dark">Incomplete</span>
    {{/if}}
    {{/ifCond}}
</p>
<div class="row">
    <div class="col-2">First Name:</div>
    <div class="col-4 font-weight-bold">{{ position.advisorDetails.firstName }}</div>
    <div class="col-2">Middle Name:</div>
    <div class="col-4 font-weight-bold">{{ position.advisorDetails.middleName }}</div>
</div>
<div class="row">
    <div class="col-2">Last Name:</div>
    <div class="col-4 font-weight-bold">{{ position.advisorDetails.lastName }}</div>
    <div class="col-2">Name of Firm:</div>
    <div class="col-4 font-weight-bold">{{ position.advisorDetails.firmName }}</div>
</div>
<div class="row">
    <div class="col-2">Phone:</div>
    <div class="col-4 font-weight-bold">{{ position.advisorDetails.phone }}</div>
    <div class="col-2">E-mail:</div>
    <div class="col-4 font-weight-bold">{{ position.advisorDetails.email }}</div>
</div>
<div class="row">
    <div class="col-2">Nationality:</div>
    <div class="col-4 font-weight-bold">{{ position.advisorDetails.nationality }}</div>
    <div class="col-2">Country of Incorporation:</div>
    <div class="col-4 font-weight-bold">{{ position.advisorDetails.incorporationCountry }}</div>
</div>
<hr class="mt-3"/>
<p>
    PRINCIPAL ADVISOR ADDRESS
    {{#ifCond position.lockedByFileReview '==' reviewId}}
    {{#if positionInformation.residentialAddress.complete}}
        <span class="badge badge-success">Complete</span>
    {{else}}
        <span class="badge badge-warning text-dark">Incomplete</span>
    {{/if}}
    {{/ifCond}}
</p>
<div class="row">
    <div class="col-2">Address - 1st Line:</div>
    <div class="col-4 font-weight-bold">{{ position.residentialAddress.primaryAddress }}</div>
    <div class="col-2">Address - 2nd Line:</div>
    <div class="col-4 font-weight-bold">{{ position.residentialAddress.secondaryAddress }}</div>
</div>
<div class="row pt-2">
    <div class="col-2">Country:</div>
    <div class="col-4 font-weight-bold">{{ position.residentialAddress.country }}</div>
    <div class="col-2">State:</div>
    <div class="col-4 font-weight-bold">{{ position.residentialAddress.state }}</div>
</div>
<div class="row pt-2">
    <div class="col-2">City:</div>
    <div class="col-4 font-weight-bold">{{ position.residentialAddress.city }}</div>
    <div class="col-2">Postal Code:</div>
    <div class="col-4 font-weight-bold">{{ position.residentialAddress.postalCode }}</div>
</div>

<hr class="mt-3"/>
    <p>
        <!-- WORLD CHECK -->
        WORLD CHECK
        {{#ifCond position.lockedByFileReview '==' reviewId}}
        {{#if positionInformation.worldCheck.complete}}
            <span class="badge badge-success">Complete</span>
        {{else}}
            <span class="badge badge-warning text-dark">Incomplete</span>
        {{/if}}
        {{/ifCond}}
    </p>

<div>
    <hr class="mt-3"/>
    <p>FILES</p>
    <table class="table">
        <thead>
        <tr>
            <th >Name</th>
            <th style="width: 10%">Group</th>
            <th style="width: 10%">Present</th>
            <th >Explanation</th>
            <th style="width: 10%">Download</th>
        </tr>
        </thead>
        <tbody>
        {{#each positionFiles}}
            <tr>
                <td style="text-transform: capitalize;">{{external}}</td>
                <td style="text-transform: capitalize;">{{fileGroup}}</td>
                <td class="text-center" style="text-transform: capitalize;">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox"
                               disabled
                               class="custom-control-input"
                               id="standardFilePresent-{{ @key }}"
                            {{#if present}}
                               checked
                            {{/if}}
                        />
                        <label
                                class="custom-control-label"
                                for="standardFilePresent-{{ @key }}"
                        ></label>
                    </div>
                </td>
                <td style="text-transform: capitalize;">{{ explanation }}</td>
                <td class="text-center align-middle">
                    <button class="btn solid royal-blue download-button"
                            id="standardFileDownload-{{ @key }}"
                            type="button"
                            data-toggle="modal"
                            data-target="#downloadFileModal"
                            data-review-id="{{../reviewId }}"
                            data-relation-id="{{../position._id}}"
                            data-file-id="{{ id }}"
                            data-file-group="{{../position.type}}"
                        {{#unless present}} disabled {{/unless}}
                    >Download
                    </button>
                </td>
            </tr>
        {{/each}}
        </tbody>
    </table>
</div>

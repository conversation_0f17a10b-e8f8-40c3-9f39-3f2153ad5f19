<main>
    <div class="container-fluid">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <h1>{{title}}</h1>
                        <form method="POST" id="submitForm">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="filter_masterclient">Masterclient</label>
                                    <input class='form-control' type='text' name='filter_masterclient'
                                        id='filter_masterclient' />
                                </div>
                                <div class="col-md-4" style="padding-top:25px">
                                    <input type='SUBMIT' class='btn btn-light btn-sm waves-effect' value='Search' />
                                </div>
                            </div>
                        </form>
                        <br /><br />
                        <table id="scroll-horizontal-datatable" class="table w-100 nowrap">
                            <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>Master Client Code</th>
                                    <th>Owners</th>
                                    <th>Custom payment?</th>
                                    <th>Invitation Date</th>
                                    <th>Clear Invitation Date</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each result}}
                                <tr>
                                    <td>{{_id}}</td>
                                    <td>{{code}}</td>
                                    <td>{{owners}}</td>
                                    <td>{{#if customIncorporationPayment}}Yes{{else}}No{{/if}}</td>
                                    <td>{{formatDate date_invited ../STANDARD_DATE_FORMAT}}</td>
                                    <td>
                                        {{#if date_invited}}
                                          {{#if ../updateAllowed}}                                          
                                            <input type="button" class="btn btn-primary waves-effect waves-light"
                                                onclick="event.stopPropagation(); clearInvitation('{{_id}}', '{{code}}')"
                                                value="Clear">
                                            {{/if}}
                                        {{/if}}
                                    </td>
                                    <td>
                                        <input type="button" class="btn btn-primary waves-effect waves-light" value="Edit">
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="row">
                            <div class="mb-3 ml-3">
                                <a href='/client-management/'
                                    class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<div class="modal hide fade edit-modal-xl" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel"
    id="edit_modal" style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myLargeModalLabel">Edit Master Client</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div class="col-12">
                    <label>Use custom incorporation payment amounts?</label>
                    <div class="custom-control custom-radio">
                        <input type="radio" class="custom-control-input" id="useCustomYes" name="useCustom"
                            value="true" {{#ifCond updateAllowed '==' false}}disabled{{/ifCond}}>
                        <label class="custom-control-label" for="useCustomYes">Yes</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" class="custom-control-input" id="useCustomNo" name="useCustom"
                            value="false" {{#ifCond updateAllowed '==' false}}disabled{{/ifCond}}>
                        <label class="custom-control-label" for="useCustomNo">No</label>
                    </div>
                    <div id="customPayment">
                        <hr>
                        <button type="button" class="btn btn-primary my-1 btn-block" id="resetValuesBtn"
                            onclick="resetDefaultValues()">Reset default to values</button>


                        <div id="feesModalRow">

                        </div>

                        <hr>
                    </div>
                </div>
                <div class="col-12 mt-3">
                    <label>Owners</label>
                    <div class="row mb-3">
                        <div class="col-12" id="ownersList">

                        </div>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        {{#if authentication.isClientManagementSuperUser}}
                        <button type="button" class="btn btn-primary my-1" id="addButton"
                            onclick="addOwner()">Add</button>
                        {{/if}}
                        {{#if updateAllowed}}
                        <button type="button" class="btn btn-primary my-1" id="saveButton"
                            onclick="saveMasterclient()">Save</button>
                        {{/if}}
                    </div>
                </div>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>

<script type='text/javascript' src='/templates/masterclients/paymentfeelist.precompiled.js'></script>
<script type="text/javascript">
    let table;
    let ownerIndex = 0;
    let disabled = {{#if authentication.isClientManagementSuperUser}}''{{else}}'disabled'{{/if}};
    let masterclientId;
    let masterclientCode;
    let useCustomPayment;

    let currentConfig;
    let defaultConfig;
    $(document).ready(function () {

        table = $("#scroll-horizontal-datatable").DataTable({ "pageLength": 50, "columnDefs": [{ "visible": false, "targets": [0] }], "order": [[1, "asc"]], scrollX: !0, language: { paginate: { previous: "<i class='mdi mdi-chevron-left'>", next: "<i class='mdi mdi-chevron-right'>" } }, drawCallback: function () { $(".dataTables_paginate > .pagination").addClass("pagination-rounded") } });
        $("#scroll-horizontal-datatable").on('click', 'tr', function () {
            let data = table.rows(this).data()[0];
            masterclientId = data[0];

            $.ajax({
                type: "GET",
                url: "./search-masterclients/" + masterclientId,
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data.success) {
                        masterclientCode = data.masterClient.code;
                        useCustomPayment = data.masterClient.customIncorporationPayment;
                        $('#useCustomYes').prop('checked', data.masterClient.customIncorporationPayment);
                        $('#useCustomNo').prop('checked', !data.masterClient.customIncorporationPayment);
                        defaultConfig = data.configuration.incorporation;
                        if (data.masterClient.customIncorporationPayment) {
                            $('#customPayment').show();
                            currentConfig = data.masterClient.incorporation;

                        } else {
                            $('#customPayment').hide();
                            currentConfig = defaultConfig;
                        }


                        let template = Handlebars.templates.paymentfeelist;
                        let d = {
                            fees: currentConfig.fees,
                            disbursements: currentConfig.disbursements
                        };
                        let html = template(d);
                        $('#feesModalRow').html(html);


                        ownersHtml = '';
                        ownerIndex = 0;
                        for (let owner of data.masterClient.owners) {
                            ownersHtml += `<div class="d-flex justify-content-between mb-1" id="ownerRow${ownerIndex}"> <input type="text" class="form-control mr-2" name="editOwners" id="editOwner${ownerIndex}" value="${owner}" ${disabled}><button type="button" class="btn btn-danger" id="editButton${ownerIndex}" ${disabled} onclick="deleteOwner(${ownerIndex})">Delete</button></div>`;
                            ownerIndex++;
                        }

                        $("#ownersList").html(ownersHtml);
                        $("#edit_modal").modal();
                    } else {
                        toastr["warning"]('Sorry, there was an error finding the Master Client.');
                        $('#saveButton').prop('disabled', false);
                    }
                }
            });
        });
    });

    function clearInvitation(id, code) {
        $.ajax({
            type: "POST",
            url: "./search-masterclients/clear-invitation",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({ id, code }),
            success: function (data) {
                if (data.success) {
                    toastr.success('Master Client updated successfully');
                    $('#edit_modal').modal('hide');
                    window.setTimeout(function () {
                        document.location.reload();
                    }, 200)
                } else {
                    toastr["warning"]('Sorry, there was an error updating the Master Client.');
                    $('#saveButton').prop('disabled', false);
                }
            }
        });
    }

    $('input[name="useCustom"]').change(function () {
        if ($('#useCustomYes').is(':checked')) {
            $('#customPayment').show(200);
        } else {
            $('#customPayment').hide(200);
        }
    });

    function resetDefaultValues() {

        let template = Handlebars.templates.paymentfeelist;
        let d = {
            fees: defaultConfig.fees,
            disbursements: defaultConfig.disbursements
        };
        let html = template(d);
        $('#feesModalRow').html(html);
    }

    function deleteOwner(index) {
        $(`#ownerRow${index}`).remove();
    }

    function addOwner() {
        $("#ownersList").append(`<div class="d-flex justify-content-between mb-1" id="ownerRow${ownerIndex}"> <input type="text" class="form-control mr-2" name="editOwners" id="editOwner${ownerIndex}"><button type="button" class="btn btn-danger" id="editButton${ownerIndex}" onclick="deleteOwner(${ownerIndex})">Delete</button></div>`);
        ownerIndex++;
    }

    function saveMasterclient() {
        $('#saveButton').prop('disabled', true);
        const ownersArray = [];
        let emptyOwners = false;
        $("input[name=editOwners]").each(function () {
            const ownerEmail = $(this).val();

            if(ownerEmail === ""){
                emptyOwners = true;
            }
            ownersArray.push(ownerEmail);
        });

        if(emptyOwners){
            toastr["warning"]('Email owners cannot be empty');
            $('#saveButton').prop('disabled', false);
            return false
        }

        const newValues = {
            fees: [],
            disbursements: []
        }
        const customIncorporationPayment = $('#useCustomYes').is(':checked');
        if (customIncorporationPayment) {
            for (const fee of currentConfig.fees) {
                newValues.fees.push({
                    '_id': fee._id,
                    'title': fee.title || "",
                    'description': fee.description,
                    'value': $('#fee' + fee._id).val()
                });
            }
            for (const disbursement of currentConfig.disbursements) {
                newValues.disbursements.push({
                    '_id': disbursement._id,
                    'title': disbursement.title || "",
                    'description': disbursement.description,
                    'value': $('#disbursement' + disbursement._id).val()
                });
            }
        }
        $.ajax({
            type: "POST",
            url: "./search-masterclients/update",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({
                id: masterclientId,
                owners: ownersArray,
                code: masterclientCode,
                incorporation: newValues,
                customIncorporationPayment
            }),
            success: function (data) {
                if (data.success) {
                    toastr.success('Master Client updated successfully');
                    $('#edit_modal').modal('hide');
                    window.setTimeout(function () {
                        document.location.reload();
                    }, 200)
                } else {
                    toastr["warning"](data.message || 'Sorry, there was an error updating the Master Client.');
                    $('#saveButton').prop('disabled', false);
                }
            }
        });
    }

</script>

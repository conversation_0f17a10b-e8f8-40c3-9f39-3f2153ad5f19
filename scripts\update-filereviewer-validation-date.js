const { filereview: FileReviewModel } = require('../models/filereview');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
dotenv.config();


try {
  mongoose.connect(process.env.MONGODB, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    useFindAndModify: false,
  }, function (err) {
    throw err;
  });
  const db = mongoose.connection;
  db.on('error', console.error.bind(console, 'MongoDB connection error:'));
  // Start proccess - Process submissions from excel file
  updateSubmissionsFileReviewDate().then(r => console.log("FINISH, total updated: ", r.length));
  // Search imported submissions in database, update if found
  // and create new if not
} catch (e) {
  console.log(e);
}

async function updateSubmissionsFileReviewDate() {
  try {
    const reviewsUpdated = [];
    const reviews = await FileReviewModel.find(
      {
        "status.code": {"$in": [
          'IN PROGRESS BY QA', 'VALIDATED QA', 'COMPLIANCE', 'VALIDATED BY CO', 'SEND TO FILE REVIEW OFFICER BY QA', 'SEND TO CLIENT BY QA',
            'SEND TO QA BY CO', 'ASSIGNED QA BY CO', 'COMPLIANCE BY QA'
          ]
        },
      "fileReview.validatedDate": {
          "$exists": false
        }
        }, {_id:1, status: 1, fileReview: 1, comments: 1});

    if (reviews && reviews.length > 0){
      for (let review of reviews) {
        if (!review.fileReview.validatedDate && review.comments.length > 0){
          const commentsDate = review.comments.filter((c) => c.role === "FR" && c.from === "FR" && c.to === "QA");

          if (commentsDate.length > 0){
            const validatedDate = commentsDate[commentsDate.length -1].date;

            await FileReviewModel.findByIdAndUpdate(review._id, {"fileReview.validatedDate": validatedDate});
            reviewsUpdated.push({
              "id": review._id,
              "status": review.status.code
            })

          }
        }
      }
    }

    return reviewsUpdated;
  } catch (e) {
    console.log("Error updating the file review date: ", e);
    return []
  }

}




<main class="">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h1>{{title}}</h1>
                        <form method='POST' id="generalForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="row" style="row-gap: 15px;">
                                        <div class="col-md-4">
                                            <label for="filter_name">Entity Name</label>
                                            <input class='form-control' type='text' name='filter_name' id='filter_name'
                                                value="{{filters.filter_name}}" />
                                        </div>
                                        <div class="col-md-4">
                                            <label for="filter_director_name">Director/BO Name</label>
                                            <input class='form-control' type='text' name='filter_director_name'
                                                id='filter_director_name' value="{{filters.filter_director_name}}" />
                                        </div>
                                        <div class="col-md-4">
                                            <label for="filter_masterclient">Master Client Code</label>
                                            <input class='form-control' type='text' name='filter_masterclient' id='filter_masterclient'
                                                value="{{filters.filter_masterclient}}" />
                                        </div>
                                        <div class="col-md-4">
                                            <label for="filter_entity_number">VP Entity Number</label>
                                            <input class='form-control' type='text' name='filter_entity_number' id='filter_entity_number'
                                                value="{{filters.filter_entity_number}}" />
                                        </div>
                                                                                <div class="col-md-4">
                                            <label for="filter_referral">Referral Office</label>
                                            <input class='form-control' type='text' name='filter_referral' id='filter_referral'
                                                value="{{filters.filter_referral}}" />
                                        </div>
                                        <div class="col-md-4">
                                            <label for="filter_position">Position</label>
                                            <select
                                                    name="filter_position"
                                                    id='filter_position'
                                                    class="custom-select"
                                            >
                                                <option id="all" value="all" {{#ifCond filters.filter_position '===' ''}} selected {{/ifCond}}>Select...</option>
                                                <option id="Director" {{#ifCond filters.filter_position '===' 'Director'}} selected {{/ifCond}} value="Director"> Director
                                                </option>
                                                <option id="BO" {{#ifCond filters.filter_position '===' 'Owner/Controller'}} selected {{/ifCond}} value="Owner/Controller">BO
                                                </option>
                                                <label for="filter_position">Position</label>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="filter_production">Production Office</label>
                                            <select 
                                                    name="filter_production"
                                                    id='filter_production'
                                                    class="custom-select"
                                                    data-value="{{filters.filter_production}}"
                                            >
                                                <option selected value="">Select...</option> 
                                                {{#each productionOfficeOptions}}
                                                <option value="{{this}}">{{this}}</option>
                                                {{/each}}
                                                <label for="filter_production">Production Office</label>
                                            </select>
                                        </div>
                                    </div>
                                </div> 
                                <div class="col-md-6">
                                    <div class="row">
                                        <div class="col">
                                            <div class="row">
                                                <label for="filter_referral">Status</label>
                                            </div>
                                            <div class="row">
                                                <div class="col">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input"
                                                            name="status" id="confirmed"
                                                            value="CONFIRMED" {{#ifContains 'CONFIRMED' filters.status }} checked {{/ifContains}} />
                                                        <label class="custom-control-label" for="confirmed">CONFIRMED</label>
                                                    </div>
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input"
                                                            name="status" id="pending"
                                                            value="PENDING UPDATE REQUEST" {{#ifContains 'PENDING UPDATE REQUEST' filters.status }} checked {{/ifContains}} />
                                                        <label class="custom-control-label" for="pending">PENDING UPDATE REQUEST</label>
                                                    </div>
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input"
                                                            name="status" id="initial"
                                                            value="INITIAL" {{#ifContains 'INITIAL' filters.status }} checked {{/ifContains}} />
                                                        <label class="custom-control-label" for="initial">INITIAL</label>
                                                        <span class="fa-stack tooltip-wrapper" style="margin-top: -15px" data-toggle="tooltip"
                                                        data-placement="right"
                                                        title="Data was never updated or confirmed">
                                                            <i class="fa fa-info-circle fa-stack text-info fa-lg ml-1"></i>
                                                        </span>
                                                    </div>
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input"
                                                            name="status" id="refreshed"
                                                            value="REFRESHED" {{#ifContains 'REFRESHED' filters.status }} checked {{/ifContains}} />
                                                        <label class="custom-control-label" for="refreshed">REFRESHED</label>
                                                        <span class="fa-stack tooltip-wrapper" style="margin-top: -15px" data-toggle="tooltip"
                                                        data-placement="right"
                                                        title="Data updated and pending for confirmation without client requesting update through the portal">
                                                            <i class="fa fa-info-circle fa-stack text-info fa-lg ml-1"></i>
                                                        </span>
                                                    </div>
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input"
                                                            name="status" id="data_received"
                                                            value="VP DATA RECEIVED" {{#ifContains 'VP DATA RECEIVED' filters.status }} checked {{/ifContains}} />
                                                        <label class="custom-control-label" for="data_received">SUBSEQUENT</label>
                                                        <span class="fa-stack tooltip-wrapper" style="margin-top: -15px" data-toggle="tooltip"
                                                        data-placement="right"
                                                        title="After client requests update through the portal, Data updated and pending for confirmation">
                                                            <i class="fa fa-info-circle fa-stack text-info fa-lg ml-1"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="row">
                                                <label>Specifics</label>
                                            </div>
                                            <div class="row">
                                                <div class="col">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input"
                                                            name="subStatus" id="missing_information"
                                                            value="MISSING INFORMATION" {{#ifContains 'MISSING INFORMATION' filters.subStatus }} checked {{/ifContains}} />
                                                        <label class="custom-control-label" for="missing_information">MISSING INFORMATION</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input"
                                                            name="subStatus" id="no_director_and_bo"
                                                            value="NO DIRECTOR" {{#ifContains 'NO DIRECTOR' filters.subStatus }} checked {{/ifContains}} />
                                                        <label class="custom-control-label" for="no_director_and_bo">NO DIRECTOR & BO</label>
                                                    </div>
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input"
                                                            name="subStatus" id="has_director_and_bo"
                                                            value="HAS DIRECTOR" {{#ifContains 'HAS DIRECTOR' filters.subStatus }} checked {{/ifContains}} />
                                                        <label class="custom-control-label" for="has_director_and_bo">HAS DIRECTOR OR BO</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row my-4">
                                <div class="col-md-2">
                                    <label for="confrimedDate">Confirmed after:</label>
                                    <input class='form-control' type='date' name='filter_confirmed_range_start'
                                        id='confrimedDateStart' value="{{filters.filter_confirmed_range_start}}" />
                                </div>
                                <div class="col-md-2">
                                    <label for="confrimedDate">Confirmed before:</label>
                                    <input class='form-control' type='date' name='filter_confirmed_range_end'
                                        id='confrimedDateEnd' value="{{filters.filter_confirmed_range_end}}" />
                                </div> 
                                <div class="row" style="padding-top:30px; padding-left: 30px">
                                    <div>
                                        <input type='SUBMIT' class='btn btn-light btn-sm waves-effect' value='Search'/>
                                    </div>
                                    <div class="row ml-2" style="align-items: center;">  
                                         {{#ifCond hasFilter '===' false}}
                                            <label style="color: #f1556c;" id="criteriaText">Please enter search criteria</label>
                                            {{else}}<div></div>
                                         {{/ifCond}}
                                    </div>
                                </div>
                            </div>
                        </form>
                        <br /><br />
                        <table id="scroll-horizontal-datatable" class="table w-100 nowrap">
                            <thead>
                                <tr>
                                    <th>Entity Name</th>
                                    <th>VP Entity Number</th>
                                    <th>Entity Portal Code</th>
                                    <th>Master Client Code</th>
                                    <th>Director/BO Name</th>
                                    <th>Position</th>
                                    <th>Referral Office</th>
                                    <th>Production Office</th>
                                    <th>Status</th>
                                    <th>Specifics</th>
                                    <th>Request Update Date</th>
                                    <th>Confirmed Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each vpDirectors}}
                                <tr>
                                    <td>{{EntityName}}</td>
                                    <td>{{EntityCode}}</td>
                                    <td>{{CompanyNumber}}</td>
                                    <td>{{MasterClientCode}}</td>
                                    <td>{{Name}}</td>
                                    {{#ifCond RelationType '===' 'Owner/Controller'}}
                                        <td>BO</td>
                                    {{else}}
                                        <td>{{RelationType}}</td>
                                    {{/ifCond}}
                                    <td>{{ReferralOffice}}</td>
                                    <td>{{ProductionOffice}}</td>
                                    {{#ifCond Status '!==' undefined}}
                                        {{#ifCond Status '===' 'VP DATA RECEIVED'}}
                                            <td>SUBSEQUENT</td>
                                        {{else}}
                                            <td>{{Status}}</td>
                                        {{/ifCond}}
                                    {{else}}
                                        <td></td>
                                    {{/ifCond}}
                                    {{#ifCond Status '===' "CONFIRMED"}}
                                        <td></td>
                                    {{else ifCond Status '===' "PENDING UPDATE REQUEST"}}
                                        <td></td>
                                    {{else ifCond MissingInfo '===' 1}}
                                        <td>MISSING INFORMATION</td>
                                    {{else}}
                                        <td></td>
                                    {{/ifCond}}

                                    <td data-sort="{{formatDate UpdateRequestDate 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate UpdateRequestDate 'DD/MM/YYYY'}}
                                    </td>
                                    {{#ifCond Status '===' 'VP DATA RECEIVED'}}
                                    <td></td>
                                    {{else ifCond Status '===' "REFRESHED"}}
                                    <td></td>
                                    {{else}}
                                    <td data-sort="{{formatDate ConfirmedDate 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate ConfirmedDate 'DD/MM/YYYY'}}
                                    </td>
                                    {{/ifCond}}
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <form method="POST" action="./export-search-xls" id="submitFormXls" name="submitFormXls">
                        <input type="hidden" name="entryIds" value="" />
                    </form>
                    <div class="row">
                        <div class="col-12 text-sm-center form-inline">
                            <div class="ml-3 mr-2  mb-3">
                                <a href='/director-and-bo/'
                                   class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                                <button class="btn btn-primary width-lg waves-effect waves-light"
                                        id="btn-export-xls">Export xls</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<script type="text/javascript">

    $(document).ready(function () {

       
        if($('#missing_information').prop('checked')) {
            $('#no_director_and_bo').prop('disabled', true)
        } else {
            $('#no_director_and_bo').prop('disabled', false)
        }
        
        if($('#no_director_and_bo').prop('checked')) {
            $('#missing_information').prop('disabled', true)
        } else {
            $('#missing_information').prop('disabled', false)
        }
        

        if($('input[name="status"]:checked').length > 0) {
            $('#no_director_and_bo').prop('disabled', true)
        } else {
            $('#no_director_and_bo').prop('disabled', false)
        }


        $("#confirmedDateStart").datepicker({
            format: 'mm/dd/yyyy',
            autoclose: true
        }).on('hide', function (event) {
            event.preventDefault();
            event.stopPropagation();
        });

        $("#confirmedDateEnd").datepicker({
            format: 'mm/dd/yyyy',
            autoclose: true
        }).on('hide', function (event) {
            event.preventDefault();
            event.stopPropagation();
        });

        if ($('#missing_information').prop('checked') || $('#has_director_and_bo').prop('checked')) {
            $('#no_director_and_bo').prop('checked', false)
            $('#no_director_and_bo').prop('disabled', true)
        }

        if ($('#no_director_and_bo').prop('checked')) {
            $('#has_director_and_bo').prop('checked', false)
            $('#has_director_and_bo').prop('disabled', true)
            $('#missing_information').prop('checked', false)
            $('#missing_information').prop('disabled', true)
        }


        $("#scroll-horizontal-datatable").DataTable({
            scrollX: !0,
            select: false,
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>" }
            },
            drawCallback: function () { $(".dataTables_paginate > .pagination").addClass("pagination-rounded") }
        });
         
        if($('#filter_production').data('value') !== ''){
            $('#filter_production option[value="'+$('#filter_production').data('value')+'"]').prop('selected', true)
        }
        
    });

    
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body',
        boundary: 'window'
    });

    let selectedId;
    let selectedCompany;
    let isSelectedEntryPaid = false;
     
    $('#missing_information, #has_director_and_bo').change(function () {
        if($(this).prop('checked')) {
            $('#no_director_and_bo').prop('disabled', true)
        } else {
            $('#no_director_and_bo').prop('disabled', false)
        } 

    })

    $('#filter_position').on('change', function (){
     
    if($(this).val() !== 'all') {
        $('#has_director_and_bo').prop('checked', true)
     } else {
         $('#has_director_and_bo').prop('checked', false)
    }
  })

    $('#confrimedDateEnd, #confrimedDateStart').on('change', function () {
     
    if($(this).val()) {
        $('#has_director_and_bo').prop('checked', true)
     } else {
         $('#has_director_and_bo').prop('checked', false)
    }
  })


    $('#filter_production').on('change', function (){
         
        if($(this).val() !== 'all') {
            $('#has_director_and_bo').prop('checked', true)
        } else {
            $('#has_director_and_bo').prop('checked', false)
        }
    })
    
    $('#filter_entity_number').on('input', function () {
         
        if($(this).val().trim() !== '') {
            $('#has_director_and_bo').prop('checked', true)
         } else {
            $('#has_director_and_bo').prop('checked', false)
        }
    })

    $('#filter_director_name').on('input', function () {
         
        if($(this).val().trim() !== '') {
            $('#has_director_and_bo').prop('checked', true)
         } else {
            $('#has_director_and_bo').prop('checked', false)
        }
    })



    $('#no_director_and_bo').change(function () {
    if($(this).prop('checked')) {
        $('#missing_information').prop('disabled', true)
        $('#has_director_and_bo').prop('disabled', true)
        $('#missing_information').prop('checked', false)
        $('#has_director_and_bo').prop('checked', false)
    } else {
        $('#missing_information').prop('disabled', false)
        $('#has_director_and_bo').prop('disabled', false)
    }
    
  })


    $('input[name="status"]').change(function () {
     var allDisabled = true;
  
    $("input[name='status']").each(function() {
        if ($(this).prop('checked')) {
        allDisabled = false;
        return false;
        }
    });

    if (allDisabled) {
        $("#no_director_and_bo").prop("disabled", false)
        $("#has_director_and_bo").prop('checked', false)
    } else {
        $("#missing_information").prop("disabled", false)
        $("#no_director_and_bo").prop("disabled", true)
        $("#no_director_and_bo").prop('checked', false)
        $("#has_director_and_bo").prop('checked', true)
    }
    
  })

    $('#btn-export-xls').click(function () {
        let elements = $('input:checked');
        let status = [];
        let subStatus = [];
        elements.each(function() {
            if ($(this).attr('name') === 'status') {
                status.push($(this).val());
            } else {
                subStatus.push($(this).val());
            }
        });


        var dataToSend = {
            'filter_name': $('#filter_name').val(),
            'filter_director_name': $('#filter_director_name').val(),
            'filter_masterclient': $('#filter_masterclient').val(),
            'filter_entity_number': $('#filter_entity_number').val(),
            'filter_referral': $('#filter_referral').val(),
            'filter_production': $('#filter_production').val(),
            'filter_confirmed_range_start': $('#confrimedDateStart').val(),
            'filter_confirmed_range_end': $('#confrimedDateEnd').val(),
            'filter_position': $('#filter_position').val(),
            'status': status,
            'subStatus': subStatus
        };

        $.ajax({
            url: "./export-search-xls",
            data: dataToSend,
            method: 'POST',
            xhrFields: {
                responseType: 'blob' // to avoid binary data being mangled on charset conversion
            },
            success: function (blob, status, xhr) {
                // check for a filename
                var filename = "";
                var disposition = xhr.getResponseHeader('Content-Disposition');
                if (disposition && disposition.indexOf('attachment') !== -1) {
                    var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    var matches = filenameRegex.exec(disposition);
                    if (matches != null && matches[1]) filename = matches[1].replace(/['"]/g, '');
                }

                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    // IE workaround for "HTML7007: One or more blob URLs were revoked by closing the blob for which they were created. These URLs will no longer resolve as the data backing the URL has been freed."
                    window.navigator.msSaveBlob(blob, filename);
                } else {
                    var URL = window.URL || window.webkitURL;
                    var downloadUrl = URL.createObjectURL(blob);

                    if (filename) {
                        // use HTML5 a[download] attribute to specify filename
                        var a = document.createElement("a");
                        // safari doesn't support this yet
                        if (typeof a.download === 'undefined') {
                            window.location.href = downloadUrl;
                        } else {
                            a.href = downloadUrl;
                            a.download = filename;
                            document.body.appendChild(a);
                            a.click();
                        }
                    } else {
                        window.location.href = downloadUrl;
                    }

                    setTimeout(function () { URL.revokeObjectURL(downloadUrl); }, 100); // cleanup
                }
            }
        })
        //xlsForm.submit();
    });

</script>

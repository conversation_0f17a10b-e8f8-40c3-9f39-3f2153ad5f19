<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h2>{{ title }}</h2>
                    </div>
                    <!-- CONTENT -->
                    <div class="card-body">
                        <div class="row">
                            <h5 class="pl-1 pb-1">HISTORY:</h5>
                            <div class="table-responsive">
                                <table id="import-directors-datatable"
                                    class="table table-striped  w-100 nowrap ">
                                    <thead>
                                        <tr>
                                            <th style="width: 50%;">FILE</th>
                                            <th style="width: 30%;">CREATED AT</th>
                                            <th style="width: 20%;">DOWNLOAD</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {{#each importFiles}}
                                            <tr class="text-left">
                                                <td>{{name}}</td>
                                                <td>
                                                     {{createdAt}}
                                                </td>
                                                <td>
                                                    <a href="/director-and-bo/import-directors/download/{{filename}}?pathname={{name}}" target="_blank"
                                                        class="btn btn-primary waves-effect waves-light">
                                                        Download
                                                    </a>
                                                </td>
                                            </tr>
                                        {{else}}
                                            <tr>
                                                <td colspan="3" class="text-center font-italic">There are no files imported
                                                </td>
                                            </tr>
                                        {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                    </div>
                    <!-- CONTENT END -->
                    <div class="row mt-2">
                        <div class="col-md-12 d-flex justify-content-between">
                            <a href="/director-and-bo" class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>
                            
                            {{#if showNewImportButton}}
                            <a href="/director-and-bo/import-directors/new" class="btn  solid royal-blue">New
                                Import</a>
                            {{/if}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>


<script type="text/javascript">

    let table;
    $(document).ready(function () {

        table = $("#import-directors-datatable").DataTable({
            "pageLength": 50,
            "order": [[1, "des"]],
            scrollX: !0,
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>"
                }
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
            }
        });
    });
</script>

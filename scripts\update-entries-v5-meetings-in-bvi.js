const EntryModel = require("../models/entry").EntryModel;
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config();


const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const result = await updateEntriesMeetingInBVIField();

    console.log('Script run success', result);
  } catch (error) {
    console.error('Error en el script:', error);
  } finally {
    mongoose.disconnect();
  }
}


async function updateEntriesMeetingInBVIField() {
  try {
    let updateLog = [['Entry ID', 'Company', 'Update date', 'Action']];

    const entriesToUpdate = await EntryModel.find({
      version: "5.0",
      "$or": [
        { "banking_business.number_or_board_meetings_outside_bvi": { "$gte": 0 }},
        { "insurance_business.number_or_board_meetings_outside_bvi": { "$gte": 0 } },
        { "fund_management_business.number_or_board_meetings_outside_bvi": { "$gte": 0 } },
        { "finance_leasing_business.number_or_board_meetings_outside_bvi": { "$gte": 0 } },
        { "headquarters_business.number_or_board_meetings_outside_bvi": { "$gte": 0 } },
        { "shipping_business.number_or_board_meetings_outside_bvi": { "$gte": 0 } },
        { "holding_business.number_or_board_meetings_outside_bvi": { "$gte": 0 } },
        { "intellectual_property_business.number_or_board_meetings_outside_bvi": { "$gte": 0 } },
        { "service_centre_business.number_or_board_meetings_outside_bvi": { "$gte": 0 } },
      ]
    });

    for (let index = 0; index < entriesToUpdate.length; index++) {
      const entry = entriesToUpdate[index];
      const updateValues = {}

      if (entry.banking_business && parseFloat(entry.banking_business?.number_or_board_meetings_outside_bvi) >= 0){
        updateValues["banking_business.number_of_board_meetings_in_bvi"] = entry.banking_business.number_or_board_meetings_outside_bvi;
        updateValues["banking_business.number_or_board_meetings_outside_bvi"] = null;
      }

      if (entry.insurance_business && parseFloat(entry.insurance_business?.number_or_board_meetings_outside_bvi) >= 0) {
        updateValues["insurance_business.number_of_board_meetings_in_bvi"] = entry.insurance_business.number_or_board_meetings_outside_bvi;
        updateValues["insurance_business.number_or_board_meetings_outside_bvi"] = null;
      }

      if (entry.fund_management_business && parseFloat(entry.fund_management_business?.number_or_board_meetings_outside_bvi) >= 0) {
        updateValues["fund_management_business.number_of_board_meetings_in_bvi"] = entry.fund_management_business.number_or_board_meetings_outside_bvi;
        updateValues["fund_management_business.number_or_board_meetings_outside_bvi"] = null;
      }

      if (entry.finance_leasing_business && parseFloat(entry.finance_leasing_business?.number_or_board_meetings_outside_bvi) >= 0) {
        updateValues["finance_leasing_business.number_of_board_meetings_in_bvi"] = entry.finance_leasing_business.number_or_board_meetings_outside_bvi;
        updateValues["finance_leasing_business.number_or_board_meetings_outside_bvi"] = null;
      }

      if (entry.headquarters_business && parseFloat(entry.headquarters_business?.number_or_board_meetings_outside_bvi) >= 0) {
        updateValues["headquarters_business.number_of_board_meetings_in_bvi"] = entry.headquarters_business.number_or_board_meetings_outside_bvi;
        updateValues["headquarters_business.number_or_board_meetings_outside_bvi"] = null;
      }

      if (entry.shipping_business && parseFloat(entry.shipping_business?.number_or_board_meetings_outside_bvi) >= 0) {
        updateValues["shipping_business.number_of_board_meetings_in_bvi"] = entry.shipping_business.number_or_board_meetings_outside_bvi;
        updateValues["shipping_business.number_or_board_meetings_outside_bvi"] = null;
      }
      

      if (entry.holding_business && parseFloat(entry.holding_business?.number_or_board_meetings_outside_bvi) >= 0) {
        updateValues["holding_business.number_of_board_meetings_in_bvi"] = entry.holding_business.number_or_board_meetings_outside_bvi;
        updateValues["holding_business.number_or_board_meetings_outside_bvi"] = null;
      }

      if (entry.intellectual_property_business && parseFloat(entry.intellectual_property_business?.number_or_board_meetings_outside_bvi) >= 0) {
        updateValues["intellectual_property_business.number_of_board_meetings_in_bvi"] = entry.intellectual_property_business.number_or_board_meetings_outside_bvi;
        updateValues["intellectual_property_business.number_or_board_meetings_outside_bvi"] = null;
      }

      if (entry.service_centre_business && parseFloat(entry.service_centre_business?.number_or_board_meetings_outside_bvi) >= 0) {
        updateValues["service_centre_business.number_of_board_meetings_in_bvi"] = entry.service_centre_business.number_or_board_meetings_outside_bvi;
        updateValues["service_centre_business.number_or_board_meetings_outside_bvi"] = null;
      }

      try {
        const result = await EntryModel.updateOne({ _id: entry._id }, {
          $set: updateValues
        });

        if (result.nModified > 0) {
          updateLog.push([entry._id?.toString(), entry.company, new Date(), 'SUCCESS']);
        } else {
          updateLog.push([entry._id?.toString(), entry.company, new Date(), 'ERROR: NOT FOUND']);
        }
      } catch (error) {
        console.error('Error:', error.message);
        updateLog.push([entry._id?.toString(), entry.company, new Date(), 'ERROR UPDATING']);
      }
    }

    // create entities bo
    console.log("entries updated ", updateLog.length - 1);

    const filename = 'set_entries_n_meeting_bvi_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'entries ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalRows": updateLog.length - 1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}


runScript();
const CompanyIncorporationModel = require('../../models/clientIncorporation');
const pdfController = require('../pdfController');
const uploadController = require('../client-incorporation/uploadClientDocumentsController');
const idPalController = require('../idPalController');
const mailController = require('../mailController');
const mailFormatter = require('../mailFormatController');
const {v4: uuidv4} = require('uuid');
const moment = require("moment");
const { STANDARD_DATE_FORMAT, STANDARD_DATETIME_FORMAT } = require('../../utils/constants');


exports.getDashboard = function (req, res) {
  res.render("client-incorporation/index", {
    user: req.session.user,
    title: "Client Incorporation Dashboard",
    STANDARD_DATE_FORMAT
  });
};


exports.getIncorporationRequestList = async function (req, res) {
  try {
    let searchedSubmissions = [];
    const newSubmissions = await CompanyIncorporationModel.find({
        "$or": [{status: {"$in": ["SUBMITTED", "PAID"]}, incorporationStatus: {"$in": ["NOT STARTED", null, ""]}},
          {nameReservationStatus: 'IN PROGRESS'}]},
      {_id: 1, name: 1, masterClientCode: 1, submittedAt: 1, status:1,incorporationStatus: 1, nameReservationStatus: 1}).limit(100);
    const submissionsInProgress = await CompanyIncorporationModel.find({
      "$or": [
        {
          incorporationStatus: {"$in": ["IN REVIEW", "APPROVED FOR INCORPORATION"]},
          status: {"$in": ["SUBMITTED", "PAID"]}
          },
        {
          nameReservationStatus: 'IN REVIEW'}],
        "user.username": req.session.user.username.toLowerCase() },
      {_id: 1, name: 1, masterClientCode: 1, submittedAt: 1,status:1, nameReservationStatus:1 ,  incorporationStatus: 1}).limit(100);
    const pendingClientSubmissions = await CompanyIncorporationModel.find({
        "incorporationStatus": "PENDING INFORMATION", "user.username": req.session.user.username.toLowerCase()},
      {_id: 1, name: 1, masterClientCode: 1, status:1, lastRequestInformationDate: 1, incorporationStatus:1}).limit(100);
    const submissionsReturnedByClient = await CompanyIncorporationModel.find({
        "incorporationStatus": "INFORMATION PROVIDED", "user.username": req.session.user.username.toLowerCase()},
      {_id: 1, name: 1, masterClientCode: 1, lastReturnedInformationDate: 1, status: 1, incorporationStatus:1}).limit(100);

    let searchQuery = [];
    if (req.query.filter && req.query.filter.length > 2) {
      const filter = req.query.filter;
      searchQuery.push({
        $or: [
          {'name': {$regex: filter, $options: 'i'}},
          {'code': {$regex: filter, $options: 'i'}},
          {'masterClientCode': {$regex:filter, $options: 'i'}}
        ],
      });

    }

    if (req.query.notShowCompleted === "YES") {
      searchQuery.push({"status": {"$ne": "COMPLETED"}});
    }

    if (searchQuery.length > 0){
      searchedSubmissions = await CompanyIncorporationModel.find({ "$and": searchQuery
      }, {_id: 1, name: 1, masterClientCode: 1, status:1, incorporationStatus:1, nameReservationStatus: 1, user: 1}).limit(1000);
    }


    const currentDate = moment();


    if (newSubmissions.length > 0){
      newSubmissions.forEach((submission) => {
        if(submission.nameReservationStatus === 'IN PROGRESS') {
          submission.incorporationStatus = "NAME REVIEW";
        }
        if (submission.submittedAt){
          const submissionDate =  moment(submission.submittedAt, "YYYY-MM-DD");
          submission.daysOpen = moment.duration(submissionDate.diff(currentDate)).humanize();
        }
      })
    }

    if (submissionsInProgress.length > 0){
      submissionsInProgress.forEach((submission) => {
        if (submission.submittedAt){
          const submissionDate =  moment(submission.submittedAt, "YYYY-MM-DD");
          submission.daysOpen = moment.duration(submissionDate.diff(currentDate)).humanize();
        }
      })
    }

    if (pendingClientSubmissions.length > 0){
      pendingClientSubmissions.forEach((pendingSubmission) => {
        if(pendingSubmission.lastRequestInformationDate) {
          const requestDate = moment(pendingSubmission.lastRequestInformationDate, "YYYY-MM-DD");
          pendingSubmission.daysOpen = moment.duration(requestDate.diff(currentDate)).humanize();
        }
      })
    }


    if (submissionsReturnedByClient.length > 0){
      submissionsReturnedByClient.forEach((returnedSubmission) => {
        if(returnedSubmission.lastReturnedInformationDate) {
          const returnedDate = moment(returnedSubmission.lastReturnedInformationDate, "YYYY-MM-DD");
          returnedSubmission.daysOpen = moment.duration(returnedDate.diff(currentDate)).humanize();
        }
      })
    }

    res.render('client-incorporation/incorporation-list',
      {
        user: req.session.user,
        title: "Dashboard",
        authentication: req.session.authentication,
        STANDARD_DATE_FORMAT,
        submissions: {
          newSubmissions: newSubmissions ? newSubmissions : [],
          inProgress: submissionsInProgress ? submissionsInProgress : [],
          pendingClient: pendingClientSubmissions ? pendingClientSubmissions : [],
          returnedByClient: submissionsReturnedByClient ? submissionsReturnedByClient : [],
          searchedSubmissions: searchedSubmissions ? searchedSubmissions : []
        },
        filters: {
          search: req.query.filter ? req.query.filter : '',
          showIncomplete:  req.query.notShowCompleted === "YES",
        }
      });
  }catch (e) {
    console.log("Error ", e);
    res.redirect("/");
  }

};


exports.getSubmissionView = async function (req, res) {
  try {
    req.session[req.params.incorporationId] = {};
    const companyIncorporation = await CompanyIncorporationModel.findById(req.params.incorporationId,
      {_id: 1, name: 1, status:1, files: 1, relations: 1, requestInformation: 1, incorporationStatus: 1,
        clientReturnedInformation: 1, nameReservationStatus: 1, nameReservationInfo: 1,pendingElectronicIds: 1, submittedAt: 1 });
    let companyFiles = [];
    let lastClientReturnedFiles = [];
    let clientRequestAnswers = [];
    let pendingElectronicInfo = [];
    if (companyIncorporation && companyIncorporation.files) {
      const fileGroup = {
        structureChartFiles: "Structure charts",
        passportFiles: "Passport",
        addressProofFiles: "Proof of Address",
        otherDeclarationFiles: "Other Declaration",
      };

      for (const [key, items] of Object.entries(companyIncorporation.files.toObject())) {
        if (items){
          for (let i = 0; i < items.length; i++) {
            const mimeType =items[i].mimeType.split('/');
            companyFiles.push({
              fileGroup: fileGroup[key],
              mimeType: mimeType[1] ? mimeType[1].toUpperCase() : items[i].mimeType,
              originalName: items[i].originalName,
              fileId: items[i].fileId
            })
          }
        }
      }
    }
    if (companyIncorporation.clientReturnedInformation && companyIncorporation.clientReturnedInformation.length > 0){
      companyIncorporation.clientReturnedInformation.forEach((clientInfo) => {

        if (clientInfo.comment){
          clientRequestAnswers.push({
            returnedAt: clientInfo.returnedAt,
            username: clientInfo.username.toLowerCase(),
            comment: clientInfo.comment
          })
        }

        if (clientInfo.files && clientInfo.files.length > 0){
          for (let i = 0; i < clientInfo.files.length; i++) {
            const mimeType =clientInfo.files[i].mimeType.split('/');
            lastClientReturnedFiles.push({
              fileGroup:  clientInfo.files[i].fieldName,
              mimeType: mimeType[1] ? mimeType[1].toUpperCase() : clientInfo.files[i].mimeType,
              originalName: clientInfo.files[i].originalName,
              fileId: clientInfo.files[i].fileId,
              fileTypeId: clientInfo._id
            })
          }
        }
      });
    }

    if (companyIncorporation.pendingElectronicIds){
      const naturalRelations = companyIncorporation.relations.filter((relation) =>
        relation.type && relation.type.toLowerCase() === "natural" && relation.electronicIdInfo
        && relation.electronicIdInfo.electronicIdInvitationSent &&
        (relation.electronicIdInfo.status === "IN PROGRESS" || relation.electronicIdInfo.status === "NOT SENT"));

      if (naturalRelations){
        const emails = naturalRelations.map((r) => {
          return  r.electronicIdInfo.email
        });

        pendingElectronicInfo = {
          invitationDate: companyIncorporation.submittedAt,
          emails: emails.join(' ,')
        }
      }
    }

    // let incorporationUploadedFiles = await downloadController.listIncorporationFiles(companyIncorporation._id.toString());
    res.render('client-incorporation/open-company-incorporation',
      {
        user: req.session.user,
        title: "Dashboard",
        authentication: req.session.authentication,
        STANDARD_DATE_FORMAT,
        companyIncorporation: companyIncorporation,
        companyFiles: companyFiles,
        readOnly: companyIncorporation.incorporationStatus === "PENDING INFORMATION" || companyIncorporation.nameReservationStatus === "IN REVIEW" ,
        clientReturnedInformation: lastClientReturnedFiles,
        clientRequestAnswers: clientRequestAnswers,
        pendingElectronicInfo: pendingElectronicInfo,
        isNameReview:  companyIncorporation.nameReservationStatus === "IN REVIEW" ||
          companyIncorporation.incorporationStatus === "NAME IN REVIEW" ||
          (!companyIncorporation.nameReservationInfo || !companyIncorporation.nameReservationInfo.approved),
      });
  }catch (e) {
    console.log("Error ", e);
    res.redirect("/");
  }

};

// Controller function for ajax call to pick a submitted file
exports.pickClientIncorporationSubmission = async function (req, res) {
  try {
    const submission = await CompanyIncorporationModel.findById(req.body.id);
    if (submission) {

      if (submission.nameReservationStatus === "IN PROGRESS"){
        submission.nameReservationStatus = "IN REVIEW";
        submission.incorporationStatus = "NAME IN REVIEW";
      }
      else{
        submission.incorporationStatus = "IN REVIEW";
      }

      submission.user = {
        username: req.session.user.username.toLowerCase(),
        name: req.session.user.name,
        dateAssigned: new Date(),
      };
      await  submission.save();
      return res.status(200).end();
    } else {
      return res.status(400).end();
    }
  } catch (error) {
    console.log(error);
    return res.status(500).end();
  }
};

exports.sendToRequestInformation = async function (req, res) {
  try {
    const submission = await CompanyIncorporationModel.findById(req.params.incorporationId);
    if (submission) {
      let sessData = req.session;
      let requestedFiles = [];
      submission.incorporationStatus = "PENDING INFORMATION";
      submission.lastRequestInformationDate = new Date();
      const incorporationFiles = sessData[submission._id];
      const files = incorporationFiles["requested-files"];

      if(files){
        const fileName = "requested-file";
        const fileTypeId = uuidv4();

        requestedFiles = moveUploadFiles(files, submission._id, fileName, fileTypeId);
      }
      submission.requestInformation.push({
        username: req.session.user.username.toLowerCase(),
        requireProvideFiles: req.body.needsProvideDocuments === 'true',
        requestedAt: new Date(),
        comment: req.body.questions ? req.body.questions : '',
        files: requestedFiles
      });

      if (submission.submittedBy){
        let email = mailFormatter.generateClientRequestInformationEmail();
        await mailController.asyncSend(
          submission.submittedBy,
          'Trident Trust BVI Client Portal notification',
          email.textString,
          email.htmlString
        );
      }

      await  submission.save();
      return res.status(200).json({status: 200, message: "Send back to client successfully"});
    } else {
      return res.status(400).json({status: 404, message: "Information not found"});
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({status: 500, message: "Internal Server Error"});
  }
};


exports.updateStatus = async function (req, res) {
  try {
    const submission = await CompanyIncorporationModel.findById(req.params.incorporationId);
    if (submission) {
      const status = {
        "approve-for-incorporation": "APPROVED FOR INCORPORATION",
        "approve": "APPROVED",
          "decline": "DECLINED",
      };
      submission.incorporationStatus = status[req.body.status];

      if (req.body.status === "approve"){
        let officialFiles = [];
        submission.companyCode = req.body.companyCode;
        submission.incorporationNr = req.body.incorporationNr;
        const incorporationFiles = req.session[submission._id];
        const files = incorporationFiles["official-files"];

        if(files){
          const fileName = "Official Document";
          const fileTypeId = uuidv4();

          officialFiles = moveUploadFiles(files, submission._id, fileName, fileTypeId);

        }

        if (officialFiles.length > 0){
          submission.files.officialIncorporationFiles = officialFiles;
        }
      }

      const statusComment = {
        username: req.session.user.username.toLowerCase(),
        comment: req.body.comment ? req.body.comment : '',
        date: new Date(),
        status: status[req.body.status],
      };
      submission.comments.push(statusComment);
      await  submission.save();
      return res.status(200).end();
    } else {
      return res.status(400).end();
    }
  }catch (e) {
    console.log(e);
    return res.status(500).json({status: 500, message: "Internal Server Error"});
  }
};

exports.getRelationFileList = async function (req, res) {
  try {
    const submission = await CompanyIncorporationModel.findById(req.params.incorporationId);
    if (submission) {
      let files = [];
        const relation = submission.relations.find((r) => r._id && r._id.toString() === req.params.relationId);
        if (relation){
          let relationFiles;
          if (relation.type === "natural") {
            const identificationFiles = relation.identification.files ? relation.identification.files : [];
            const pepFiles = relation.pepDetails.files ? relation.pepDetails.files : [];
            const worldCheckFiles = relation.worldCheck.files ? relation.worldCheck.files : [];
            const electronicIdFiles = relation.electronicIdInfo && relation.electronicIdInfo.files ? relation.electronicIdInfo.files : [];
            relationFiles = [...identificationFiles,...electronicIdFiles, ...pepFiles, ...worldCheckFiles];
          } else {
            const detailsFiles = relation.details.files ? relation.details.files : [];
            const detailsPartnerFiles = relation.detailsPartner.files ? relation.detailsPartner.files : [];
            const limitedFiles = relation.limitedCompanyDetails.files ? relation.limitedCompanyDetails.files : [];
            const mutualFundFiles = relation.mutualFundDetails.files ? relation.mutualFundDetails.files : [];
            const foundationFiles = relation.foundation.files ? relation.foundation.files : [];
            const worldCheckFiles = relation.worldCheck.files ? relation.worldCheck.files : [];
            relationFiles = [...detailsFiles, ...detailsPartnerFiles, ...limitedFiles, ...mutualFundFiles, ...foundationFiles,
              ...worldCheckFiles];
          }

          if (relationFiles.length > 0){
            relationFiles.forEach((file) => {
              if (file.uploadFiles && file.uploadFiles.length > 0){
                let uploadFiles = file.uploadFiles.map((f) => {
                  const mimeType =f.mimeType.split('/');
                  return {
                    fileTypeId: file.id,
                    fileName: file.external,
                    mimeType:  mimeType[1] ? mimeType[1].toUpperCase() : f.mimeType,
                    fileId: f.fileId,
                    originalName: f.originalName,
                  }
                });
                files = [...files, ...uploadFiles];
              }
            })
          }
        }
      return res.status(200).json({status: 200, files: files});
    } else {
      return res.status(400).json({status: 404, message: "Client incorporation not found"});
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({status: 500, message: "Internal Server Error"});
  }
};

exports.downloadIncorporationPdf = async function (req, res, next) {
  try {
    const incorporation = await CompanyIncorporationModel.findById(req.params.incorporationId);
    await pdfController.generateIncorporationPdf(incorporation, res, next);
  } catch (error) {
    console.log("E ", error);
    return res.render('error', { status: 500, message: "Internal Server Error" });
  }
};

exports.updateReservationStatus = async function (req, res) {
  try {
    const incorporation = await CompanyIncorporationModel.findById(req.params.incorporationId);
    if (req.body.approvedName === "YES"){
      if (incorporation.incorporationStatus === "NAME IN REVIEW"){
        incorporation.incorporationStatus = "IN REVIEW";
      }

      incorporation.nameReservationStatus = "APPROVED";
      incorporation.nameReservationInfo = {
        approved: true,
        approvedName: incorporation.name,
        approvedAt: new Date(),
      }
    }
    else if(req.body.approvedName === "NO"){
      incorporation.incorporationStatus = "NAME DECLINED";
      incorporation.nameReservationStatus = "DECLINED";
      const suggestions =  req.body.suggestions.filter(Boolean);
      incorporation.nameReservationInfo = {
        approved: false,
        declinedAt: new Date(),
        suggestions: suggestions,
      }
    }
    await incorporation.save();
    return res.status(200).json({status: 200, message: "Client company information success."});
  } catch (e) {
    console.log(e);
    return res.status(500).json({status: 500, message: "Internal Server Error"});
  }
};


exports.updateAssignedOfficer = async function (req, res) {
  try {
    const incorporation = await CompanyIncorporationModel.findById(req.params.incorporationId);
    const newOfficer = req.body.newReviewer;

    if (newOfficer){
      if (incorporation.user.name){
        incorporation.previousOfficer = req.body.currentReviewer.toLowerCase();
      }

      incorporation.user = {
        name: 'Re assigned officer',
        username: newOfficer.toLowerCase(),
        dateAssigned: new Date(),
      }
    }
    else{
      return res.status(400).json({status: 400, message: "Error, new officer is required."});
    }

    await incorporation.save();
    return res.status(200).json({status: 200, message: "Client company information success."});
  } catch (e) {
    console.log(e);
    return res.status(500).json({status: 500, message: "Internal Server Error"});
  }
};

exports.getRequestInformation = async function(req, res) {
  try {
    req.session[req.params.incorporationId] = {};
    const submission = await CompanyIncorporationModel.findById(req.params.incorporationId, {_id: 1, requestInformation: 1});
    if (submission) {
      let requestedFiles = [];
      let requestedQuestions = [];
      if (submission.requestInformation && submission.requestInformation.length > 0){
        submission.requestInformation.forEach((r) => {
          const requestedAt = moment(r.requestedAt).format(STANDARD_DATETIME_FORMAT);
          requestedQuestions.push({
            requestedAt: requestedAt,
            question: r.comment
          });

          if (r.files && r.files.length > 0){
            r.files.forEach((f) => {
              const file = {
                fileId: f.fileId,
                requestId: r._id.toString(),
                originalName: f.originalName,
                requestedAt: requestedAt
              };
              requestedFiles.push(file);
            })
          }
        })
      }
      if (requestedQuestions.length > 0){
        requestedQuestions.sort((a,b) => {
          return new Date(b.requestedAt) - new Date(a.requestedAt);
        });
      }

      if (requestedFiles.length > 0){
        requestedFiles.sort((a,b) => {
          return new Date(b.requestedAt) - new Date(a.requestedAt);
        });
      }


      return res.status(200).json({status: 200, requestInformation: {questions: requestedQuestions, files: requestedFiles}});
    } else {
      return res.status(400).json({status: 404, message: "Client incorporation not found"});
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({status: 500, message: "Internal Server Error"});
  }

};

exports.sendElectronicInvitation = async function(req, res){
  try {
    const incorporation = await CompanyIncorporationModel.findById(req.params.incorporationId);
    let status;
    let message;
    if (!incorporation){
      return res.status(404).json({status: 404, message: "Incorporation not found"});
    }

    const relationIndex = incorporation.relations.findIndex((r) => r._id && r._id.toString() === req.params.relationId);

    if (relationIndex === -1){
      return res.status(404).json({status: 404, message: "Relation not found"});
    }

    let relation = incorporation.relations[relationIndex];
    const electronicIdInfo = relation.electronicIdInfo;
    if (req.body.changeEmail === 'YES' && req.body.newEmailAddress){
      electronicIdInfo.email = req.body.newEmailAddress;
    }
    if (electronicIdInfo && electronicIdInfo.isElectronicId && electronicIdInfo.email){

      const palResponse = await idPalController.generateUuid(relation._id.toString());

      if (palResponse && palResponse.status === 200){
        const cancelUuidResponse = await idPalController.cancelUuid(electronicIdInfo.uuid);
        if (cancelUuidResponse && cancelUuidResponse.status === 200){
          electronicIdInfo.comments.push({
            username: req.session.user.username.toLowerCase(),
            comment: "A new electronic ID request was created, therefore the previous request with uuid "+electronicIdInfo.uuid +
              "  was automatically canceled at " + moment().format(STANDARD_DATE_FORMAT),
            date: new Date(),
            status: electronicIdInfo.status,
          });

        }

        const url = process.env.CLIENTPORTAL_APP_HOST + "/idpal?uuid=" + palResponse.uuid;
        let email = mailFormatter.generateClientIncorporationIdPalEmail(url, req.body.emailTemplate ?
          req.body.emailTemplate : 'new-invitation-template');
        let sentEmailResult = await mailController.asyncSend(
          electronicIdInfo.email,
          incorporation.name + ' - ID Verification',
          email.textString,
          email.htmlString
        );

        if (!incorporation.pendingElectronicIds) {
          incorporation.pendingElectronicIds = true;
        }
        electronicIdInfo.uuid =palResponse.uuid;
        if (sentEmailResult.accepted) {

          electronicIdInfo.electronicIdInvitationSent = true;
          electronicIdInfo.status = "IN PROGRESS";
          electronicIdInfo.invitationDate = new Date();
          electronicIdInfo.comments.push({
            username: req.session.user.username.toLowerCase(),
            comment: req.session.user.username + " has send a reminder to " + electronicIdInfo.email + " at" + moment().format(STANDARD_DATETIME_FORMAT),
            date: new Date(),
            status: electronicIdInfo.status,
          });
          status = 200;
          message= "Success";
        }
        else{
          electronicIdInfo.electronicIdInvitationSent = false;
          electronicIdInfo.status = "NOT SENT";
          electronicIdInfo.comments.push({
            username: req.session.user.username.toLowerCase(),
            comment: "Error sending email invitation",
            date: new Date(),
            status: electronicIdInfo.status,
          });

          status = 400;
          message= "Error sending email to id-pal invitation";
        }

      }

      else{
        message = "Error sending invitation";
        status = 500;
        electronicIdInfo.electronicIdInvitationSent = false;
        electronicIdInfo.status = "NOT SENT";
        electronicIdInfo.comments.push({
          username:req.session.user.username.toLowerCase(),
          comment: "Error sending invitation: " + (palResponse.message && palResponse.message.error ?
            palResponse.message.error : palResponse.message) ,
          date: new Date(),
          status: electronicIdInfo.status,
        });
      }
    }
    else{
      status = 404;
      message = "Error sending id-pal invitation: Email not found";
      electronicIdInfo.comments.push({
        username:req.session.user.username.toLowerCase(),
        comment: "Error sending id-pal invitation: Email not found",
        date: new Date(),
        status: "NOT SENT"
      })
    }
    relation.electronicIdInfo = electronicIdInfo;
    incorporation.relations[relationIndex] = relation;
    incorporation.markModified('relations');
    await incorporation.save();

    res.json({status: status, message: message});

  }catch (e) {
    console.log(e);
    return res.status(500).json({status: 500, message: "Internal Server Error"});
  }
};

function moveUploadFiles(files, id, fileName, fileTypeId){
  let uploadFiles = [];
  if (files.length > 0){
    files.forEach((file) => {
      uploadController.moveIncorporationUpload(id, file, fileName).catch((reason => {
        if (reason) {
          console.log(reason);
        }
      }));
      file = {
        fileId: uuidv4(),
        fileTypeId: fileTypeId,
        fieldName: file.fieldname.replace(/Document/i, fileName),
        originalName: file.originalname,
        encoding: file.encoding,
        mimeType: file.mimetype,
        blobName: file.blobName.replace(/Document/i, fileName),
        container: file.container,
        blob: file.blob.replace(/Document/i, fileName),
        blobType: file.blobType,
        size: file.size,
        etag: file.etag,
        url: file.url.replace(/Document/i, id + "/" + fileName),
      };
      uploadFiles.push(file)
    });
  }
  return uploadFiles;
}


(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['showinformationmodalcontent'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            <div class=\"text-left\"> \r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"resubmittedAt") : depth0),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":12,"column":16},"end":{"line":17,"column":23}}})) != null ? stack1 : "")
    + "                <span>\r\n                    "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"reopenedAt") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":19,"column":20},"end":{"line":19,"column":76}}})) != null ? stack1 : "")
    + " | "
    + alias4(((helper = (helper = lookupProperty(helpers,"reopenedBy") || (depth0 != null ? lookupProperty(depth0,"reopenedBy") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"reopenedBy","hash":{},"data":data,"loc":{"start":{"line":19,"column":79},"end":{"line":19,"column":93}}}) : helper)))
    + " | <b>Reason:</b> "
    + alias4(((helper = (helper = lookupProperty(helpers,"reason") || (depth0 != null ? lookupProperty(depth0,"reason") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"reason","hash":{},"data":data,"loc":{"start":{"line":19,"column":111},"end":{"line":19,"column":121}}}) : helper)))
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"change_financial_period_dates") : depth0),true,{"name":"ifEquals","hash":{},"fn":container.program(5, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":20,"column":20},"end":{"line":23,"column":33}}})) != null ? stack1 : "")
    + "                </span>\r\n                <br>\r\n            </div>\r\n\r\n";
},"2":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <span>\r\n                    "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"resubmittedAt") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":14,"column":20},"end":{"line":14,"column":79}}})) != null ? stack1 : "")
    + " | "
    + container.escapeExpression(((helper = (helper = lookupProperty(helpers,"resubmittedBy") || (depth0 != null ? lookupProperty(depth0,"resubmittedBy") : depth0)) != null ? helper : alias2),(typeof helper === "function" ? helper.call(alias1,{"name":"resubmittedBy","hash":{},"data":data,"loc":{"start":{"line":14,"column":82},"end":{"line":14,"column":99}}}) : helper)))
    + " | <b>RESUBMITTED</b>\r\n                </span>\r\n                <br>\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    return " ";
},"5":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <br>\r\n                    <b>Financial period changed:</b> OLD period from "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"oldStartDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":22,"column":69},"end":{"line":22,"column":127}}})) != null ? stack1 : "")
    + " to "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"oldEndDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":22,"column":131},"end":{"line":22,"column":187}}})) != null ? stack1 : "")
    + ", NEW period from "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"newStartDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":22,"column":205},"end":{"line":22,"column":263}}})) != null ? stack1 : "")
    + " to "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"newEndDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":22,"column":267},"end":{"line":22,"column":323}}})) != null ? stack1 : "")
    + "\r\n";
},"7":function(container,depth0,helpers,partials,data) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <span>\r\n                    "
    + alias3((lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"dateChanged") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"data":data,"loc":{"start":{"line":45,"column":20},"end":{"line":45,"column":59}}}))
    + " | "
    + alias3(((helper = (helper = lookupProperty(helpers,"changedBy") || (depth0 != null ? lookupProperty(depth0,"changedBy") : depth0)) != null ? helper : alias2),(typeof helper === "function" ? helper.call(alias1,{"name":"changedBy","hash":{},"data":data,"loc":{"start":{"line":45,"column":62},"end":{"line":45,"column":75}}}) : helper)))
    + "\r\n                    <br>\r\n                    <b>Financial period changed:</b> OLD period from "
    + alias3((lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"oldStartDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"data":data,"loc":{"start":{"line":47,"column":69},"end":{"line":47,"column":109}}}))
    + " to "
    + alias3((lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"oldEndDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"data":data,"loc":{"start":{"line":47,"column":113},"end":{"line":47,"column":151}}}))
    + ",\r\n                    NEW period from "
    + alias3((lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"newStartDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"data":data,"loc":{"start":{"line":48,"column":36},"end":{"line":48,"column":76}}}))
    + " to "
    + alias3((lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"newEndDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"data":data,"loc":{"start":{"line":48,"column":80},"end":{"line":48,"column":118}}}))
    + "\r\n                </span>\r\n                <br>\r\n";
},"9":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "        <div class=\"table-responsive\">\r\n            <table id=\"table-request\"   class=\"table w-100\">\r\n                <thead>\r\n                <tr>\r\n                    <th></th>\r\n                </tr>\r\n                </thead>\r\n                <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = (depth0 != null ? lookupProperty(depth0,"reportData") : depth0)) != null ? lookupProperty(stack1,"requestedInfo") : stack1),{"name":"each","hash":{},"fn":container.program(10, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":73,"column":16},"end":{"line":129,"column":25}}})) != null ? stack1 : "")
    + "                </tbody>\r\n            </table>\r\n        </div>\r\n";
},"10":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <tr>\r\n                        <td class=\"m-0 p-0\">\r\n                            <div class=\"card mb-0\">\r\n                                <div id=\"request-"
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":77,"column":49},"end":{"line":77,"column":55}}}) : helper)))
    + "\">\r\n                                    <button class=\"btn btn-outline-secondary border-white text-left font-16 w-100 \" data-toggle=\"collapse\" data-target=\"#response-"
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":78,"column":162},"end":{"line":78,"column":168}}}) : helper)))
    + "\" aria-expanded=\"false\" aria-controls=\"response-"
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":78,"column":216},"end":{"line":78,"column":222}}}) : helper)))
    + "\">\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(alias1,(depth0 != null ? lookupProperty(depth0,"reminders") : depth0),{"name":"each","hash":{},"fn":container.program(11, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":79,"column":40},"end":{"line":81,"column":49}}})) != null ? stack1 : "")
    + "                                        <i class=\"fa fa-angle-down\" aria-hidden=\"true\"></i>\r\n                                        "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"requestedAt") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":83,"column":40},"end":{"line":83,"column":97}}})) != null ? stack1 : "")
    + " | "
    + alias4(((helper = (helper = lookupProperty(helpers,"username") || (depth0 != null ? lookupProperty(depth0,"username") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"username","hash":{},"data":data,"loc":{"start":{"line":83,"column":100},"end":{"line":83,"column":112}}}) : helper)))
    + " | <b>"
    + alias4(((helper = (helper = lookupProperty(helpers,"status") || (depth0 != null ? lookupProperty(depth0,"status") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"status","hash":{},"data":data,"loc":{"start":{"line":83,"column":118},"end":{"line":83,"column":128}}}) : helper)))
    + "</b>\r\n                                        <br> <b>Reason:</b> "
    + alias4(((helper = (helper = lookupProperty(helpers,"comment") || (depth0 != null ? lookupProperty(depth0,"comment") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"comment","hash":{},"data":data,"loc":{"start":{"line":84,"column":60},"end":{"line":84,"column":71}}}) : helper)))
    + "\r\n\r\n                                    </button>\r\n                                </div>\r\n\r\n                                <div id=\"response-"
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":89,"column":50},"end":{"line":89,"column":56}}}) : helper)))
    + "\" class=\"collapse\" aria-labelledby=\"request-"
    + alias4(((helper = (helper = lookupProperty(helpers,"id") || (depth0 != null ? lookupProperty(depth0,"id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"id","hash":{},"data":data,"loc":{"start":{"line":89,"column":100},"end":{"line":89,"column":106}}}) : helper)))
    + "\" data-parent=\"#accordion\">\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(alias1,(depth0 != null ? lookupProperty(depth0,"client_response") : depth0),{"name":"each","hash":{},"fn":container.program(13, data, 0, blockParams, depths),"inverse":container.program(18, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":90,"column":36},"end":{"line":123,"column":45}}})) != null ? stack1 : "")
    + "                                </div>\r\n                            </div>\r\n                        </td>\r\n                    </tr>\r\n\r\n";
},"11":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                                            "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"reminderDate") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":80,"column":44},"end":{"line":80,"column":102}}})) != null ? stack1 : "")
    + " | "
    + container.escapeExpression(((helper = (helper = lookupProperty(helpers,"description") || (depth0 != null ? lookupProperty(depth0,"description") : depth0)) != null ? helper : alias2),(typeof helper === "function" ? helper.call(alias1,{"name":"description","hash":{},"data":data,"loc":{"start":{"line":80,"column":105},"end":{"line":80,"column":120}}}) : helper)))
    + " | <b>REMINDER SENT</b> <br>\r\n";
},"13":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                                        <div class=\"card-body\">\r\n                                    <span >\r\n                                        "
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"returnedAt") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(3, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":93,"column":40},"end":{"line":93,"column":96}}})) != null ? stack1 : "")
    + " | "
    + alias4(((helper = (helper = lookupProperty(helpers,"username") || (depth0 != null ? lookupProperty(depth0,"username") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"username","hash":{},"data":data,"loc":{"start":{"line":93,"column":99},"end":{"line":93,"column":111}}}) : helper)))
    + " <br>\r\n                                        <b>Response</b>: "
    + alias4(((helper = (helper = lookupProperty(helpers,"comment") || (depth0 != null ? lookupProperty(depth0,"comment") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"comment","hash":{},"data":data,"loc":{"start":{"line":94,"column":57},"end":{"line":94,"column":68}}}) : helper)))
    + "\r\n                                    </span>\r\n                                            <hr>\r\n                                            <span >\r\n                                        <b>FILES:</b>\r\n                                    </span>\r\n                                            <br>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(alias1,(depth0 != null ? lookupProperty(depth0,"files") : depth0),{"name":"each","hash":{},"fn":container.program(14, data, 0, blockParams, depths),"inverse":container.program(16, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":101,"column":44},"end":{"line":114,"column":53}}})) != null ? stack1 : "")
    + "                                        </div>\r\n\r\n";
},"14":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), alias4=container.hooks.helperMissing, alias5="function", lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                                                <a href=\"/financial-report-management/"
    + alias2(alias1(((stack1 = (depths[3] != null ? lookupProperty(depths[3],"reportData") : depths[3])) != null ? lookupProperty(stack1,"reportId") : stack1), depth0))
    + "/download/"
    + alias2(alias1((depths[1] != null ? lookupProperty(depths[1],"requestId") : depths[1]), depth0))
    + "/"
    + alias2(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":102,"column":145},"end":{"line":102,"column":152}}}) : helper)))
    + "\"\r\n                                                   target=\"_blank\" >\r\n                                                    <span class=\"small\"> &#8226; "
    + alias2(((helper = (helper = lookupProperty(helpers,"originalName") || (depth0 != null ? lookupProperty(depth0,"originalName") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"originalName","hash":{},"data":data,"loc":{"start":{"line":104,"column":81},"end":{"line":104,"column":97}}}) : helper)))
    + " </span>\r\n                                                </a>\r\n                                                <br>\r\n";
},"16":function(container,depth0,helpers,partials,data) {
    return "\r\n                                                <span class=\"small\">\r\n                                                &#8226; No files uploaded\r\n                                            </span>\r\n\r\n\r\n";
},"18":function(container,depth0,helpers,partials,data) {
    return "                                        <div class=\"card-body\">\r\n                                    <span >\r\n                                        No response from the client yet\r\n                                    </span>\r\n                                        </div>\r\n";
},"20":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            <h3 class=\"ml-1\"><b>Current Officer: </b> "
    + container.escapeExpression(container.lambda(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"reportData") : depth0)) != null ? lookupProperty(stack1,"officerDetails") : stack1)) != null ? lookupProperty(stack1,"currentOfficer") : stack1), depth0))
    + " </h3>\r\n";
},"22":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "        <div class=\"table-responsive ml-1\">\r\n            <table id=\"table-request\"   class=\"table w-100\">\r\n                <thead>\r\n                <tr>\r\n                    <th>Username</th>\r\n                    <th>Assigned By</th>\r\n                    <th>Assigned At</th>\r\n                </tr>\r\n                </thead>\r\n                <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"reportData") : depth0)) != null ? lookupProperty(stack1,"officerDetails") : stack1)) != null ? lookupProperty(stack1,"details") : stack1),{"name":"each","hash":{},"fn":container.program(23, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":162,"column":16},"end":{"line":168,"column":25}}})) != null ? stack1 : "")
    + "                </tbody>\r\n            </table>\r\n        </div>\r\n";
},"23":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <tr>\r\n                        <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"username") || (depth0 != null ? lookupProperty(depth0,"username") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"username","hash":{},"data":data,"loc":{"start":{"line":164,"column":28},"end":{"line":164,"column":40}}}) : helper)))
    + "</td>\r\n                        <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"assignedBy") || (depth0 != null ? lookupProperty(depth0,"assignedBy") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"assignedBy","hash":{},"data":data,"loc":{"start":{"line":165,"column":28},"end":{"line":165,"column":42}}}) : helper)))
    + "</td>\r\n                        <td>"
    + ((stack1 = (lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"assignedAt") : depth0),"MM/DD/YYYY",{"name":"formatDate","hash":{},"fn":container.program(24, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":166,"column":28},"end":{"line":166,"column":83}}})) != null ? stack1 : "")
    + "</td>\r\n                    </tr>\r\n";
},"24":function(container,depth0,helpers,partials,data) {
    return "";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, alias1=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<div id=\"reopenedInformation\" class=\"mb-2\">\r\n    <div class=\"row\">\r\n        <div class=\"col-12\">\r\n            <h4><b>Information on Reopened submissions</b></h4>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"row\">\r\n        <div class=\"col-12\">\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"reportData") : depth0)) != null ? lookupProperty(stack1,"reopenedInfo") : stack1),{"name":"each","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":10,"column":12},"end":{"line":28,"column":21}}})) != null ? stack1 : "")
    + "        </div>\r\n    </div>\r\n</div>\r\n<hr>\r\n<br>\r\n<div id=\"financialPeriodChangesInformation\" class=\"mb-2\">\r\n    <div class=\"row\">\r\n        <div class=\"col-12\">\r\n            <h4><b>Financial period changes</b></h4>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"row\">\r\n        <div class=\"col-12\">\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"reportData") : depth0)) != null ? lookupProperty(stack1,"financialPeriodChangesInfo") : stack1),{"name":"each","hash":{},"fn":container.program(7, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":43,"column":12},"end":{"line":51,"column":21}}})) != null ? stack1 : "")
    + "        </div>\r\n    </div>\r\n</div>\r\n<hr>\r\n<br>\r\n<div id=\"accordion\">\r\n    <div class=\"row mb-2\">\r\n        <div class=\"col-12\">\r\n            <h4><b>Requests for Information</b></h4>\r\n        </div>\r\n    </div>\r\n\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,((stack1 = (depth0 != null ? lookupProperty(depth0,"reportData") : depth0)) != null ? lookupProperty(stack1,"requestedInfo") : stack1),{"name":"if","hash":{},"fn":container.program(9, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":64,"column":4},"end":{"line":133,"column":11}}})) != null ? stack1 : "")
    + "\r\n\r\n\r\n</div>\r\n<hr>\r\n<br>\r\n<div id=\"officerDetails\">\r\n    <div class=\"row mb-2\">\r\n        <div class=\"col-12\">\r\n            <h4><b>Officer Details</b></h4>\r\n            <br>\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"reportData") : depth0)) != null ? lookupProperty(stack1,"officerDetails") : stack1)) != null ? lookupProperty(stack1,"details") : stack1),{"name":"if","hash":{},"fn":container.program(20, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":145,"column":12},"end":{"line":147,"column":19}}})) != null ? stack1 : "")
    + "        </div>\r\n    </div>\r\n\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"reportData") : depth0)) != null ? lookupProperty(stack1,"officerDetails") : stack1)) != null ? lookupProperty(stack1,"details") : stack1),{"name":"if","hash":{},"fn":container.program(22, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":151,"column":4},"end":{"line":172,"column":11}}})) != null ? stack1 : "")
    + "\r\n</div>\r\n\r\n";
},"useData":true,"useDepths":true});
})();
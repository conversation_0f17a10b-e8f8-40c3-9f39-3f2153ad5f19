

exports.getUserRiskGroup = function(session) {
    const riskGroup = [];
    if (session.authentication.isFileReviewer) {
        if (session.fileReviewRisks.isHighRisk) {
            riskGroup.push("High");
        }
        if (session.fileReviewRisks.isMediumRisk) {
            riskGroup.push("Medium");
        }
        if (session.fileReviewRisks.isLowRisk) {
            riskGroup.push("Low");
        }
    }
    return riskGroup;
};

/**
 * Create list of years to display in edit company model.
 * The min year is 2020 and the max year is one year after current year.
 * @function getAvailablePaymentYearsList
 * @return Array of strings with the years
 */
exports.getAvailablePaymentYearsList = function() {
    const years = [];

    const maxYear =  new Date().getUTCFullYear() + 1;

    let startYear = 2020;
    while ( startYear <= maxYear ) {
        years.push(startYear.toString());
        startYear++;
    }
    return years;
};

/**
 * Validate by regex if a email has a valid format
 * @function validateEmailFormat
 * @return boolean
 */
exports.validateEmailFormat = function(email) {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

/**
 * Parse string to number
 * @function parseStringNumberToFloat
 * @return float
 */
exports.parseStringNumberToFloat = function(value) {
    return value ? parseFloat(value.replace(/,/g, '')) : 0;
}


/**
 * check if a values is a invalid number 
 * @function isInvalidNumber
 * @return boolean
 */
exports.isInvalidNumber = function (value) {
    if (typeof value === "string" && value !== "") {
        value = parseFloat(value.replace(/,/g, ''))
    }
    return (value === "" || value === null || isNaN(value)) ? true : false
}
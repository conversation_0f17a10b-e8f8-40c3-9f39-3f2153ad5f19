<div id="{{group}}-{{tableId}}" class="table-responsive mt-2">
    <table class="table table-striped">
        <thead>
        <tr>
            <th style="width: 45%;">Files Required</th>
            <th class="text-center" style="width: 15%;">Present</th>
            <th style="width: 15%;"></th>
            <th style="width: 25%;">Explanation</th>
        </tr>
        </thead>
        <tbody>
        {{#each files}}
            <tr>
                <td>{{external}}</td>
                <td class="text-center">
                    <div class="custom-control custom-checkbox">
                        <input
                                type="checkbox"
                                class="custom-control-input"
                                name="{{../name}}[files][{{@key}}][present]"
                                id="{{../name}}-files-{{@key}}-present"
                                {{#if present}} checked {{/if}}
                        />
                        <label
                                class="custom-control-label"
                                for="{{../name}}-files-{{@key}}-present"
                        ></label>
                    </div>
                </td>
                <td class="text-center">
                    <button
                            type="button"
                            class="btn solid royal-blue"
                            data-toggle="modal"
                            data-target="#upload-temp-modal"
                            id="btn-{{../name}}-{{@key}}"
                            data-id="{{ id }}"
                            data-row="{{@key}}"
                            data-review-id="{{ ../id}}"
                            data-relation-id="{{ ../relationId}}"
                            data-file-type="{{ ../group}}"
                            data-field="{{ internal }}"
                            data-file-group="{{ ../name}}"
                            {{#if present}}style="background-color:#0AC292;border-color: #0AC292;"{{/if}}
                    >
                        {{#if present}}Modify{{else}}Upload{{/if}}
                    </button>
                </td>
                <td>
                <textarea
                        class="form-control"
                        name="{{../name}}[files][{{ @key}}][explanation]"
                        id="{{../group}}-{{../name}}-{{@key}}-explanation-file"
                        rows="1"
                >{{explanation}}</textarea>
                    <label for="{{../group}}-{{../name}}-{{@key}}-explanation-file" hidden></label>
                </td>
            </tr>
        {{/each}}
        </tbody>
    </table>
</div>

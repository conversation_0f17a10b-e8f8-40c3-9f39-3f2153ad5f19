const express = require("express");
const router = express.Router();

const monitorController = require("../controllers/monitorController");

router.get("/", ensureAuthenticated, monitorController.verifyPerformance);


function ensureAuthenticated(req, res, next) {
  if (req.session.is_authenticated ) {
    if (req.session.authentication.isSubsManagers || req.session.authentication.isSubsSuperUser) {
      return next();
    } else {
      res.redirect("/not-authorized");
    }
  } else {
    res.redirect("/login");
  }
}

module.exports = router;

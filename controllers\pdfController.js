const PdfPrinter = require('pdfmake');
const moment = require('moment');
const { countries, CIGA_CODES, SE_BUSINESS_TYPES } = require('../utils/constants');


exports.generatePdf = function (entry, res) {
  const printer = new PdfPrinter({
    Helvetica: {
      normal: 'Helvetica',
      bold: 'Helvetica-Bold',
      italics: 'Helvetica-Oblique',
      bolditalics: 'Helvetica-BoldOblique'
    }
  });

  const docDefinition = {
    watermark: {
      text: !entry.payment?.payment_received_at || entry.status === "SAVED" || (entry.status === "SUBMITTED" && !entry.reopened?.details) ? "DRAFT" :
        (entry.status === "PAID" || entry.status === "SUBMITTED") && entry.reopened?.details?.length > 0 ? "RESUBMITTED" :
          entry.status === "PAID" ? "SUBMITTED" : entry.status,
      color: '#0081B4',
      opacity: 0.3,
      bold: true,
      italics: false
    },
    info: {
      author: 'Trident Trust Company (BVI) Limited',
      creator: 'Trident Trust Company (BVI) Limited',
      producer: 'Trident Trust Company (BVI) Limited',
    },
    footer: function () {
      // you can apply any logic and return any valid pdfmake element

      return [

        {
          canvas: [
            {
              type: 'polyline',
              lineWidth: 2,
              color: "#0081B4",
              closePath: true,
              points: [{ x: 0, y: 0 }, { x: 800, y: 0 }]
            },

          ]
        }, {
          text: ' ',
          fontSize: 6
        }, {
          text: 'Address: Trident Chambers, Wickhams Cay, PO Box 146, Road Town, Tortola, British Virgin Islands ',
          fontSize: 10,
          alignment: 'center'
        },
        { text: 'Telephone: ****** 494 2434  Website: www.tridenttrust.com', fontSize: 10, alignment: 'center' }
      ]
    },
    content: [
      {
        // under NodeJS (or in case you use virtual file system provided by pdfmake)
        // you can also pass file names here
        image: 'data:image/png;base64,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',
        width: 350,
      },
      {
        style: 'table',
        table: {
          widths: [300, '*'],
          body: createCompanyInfo(entry)
        },
        //pageBreak: "after"
      },
      {
        style: 'table',
        table: {
          widths: [300, '*'],
          body: createTableEntityDetails(entry)
        },
        //pageBreak: "after"
      },

    ],
    styles: {
      header: {
        fontSize: 18,
        bold: true,
        margin: [0, 0, 0, 10]
      },
      subheader: {
        fontSize: 16,
        bold: true,
        margin: [0, 10, 0, 5]
      },
      table: {
        margin: [0, 5, 0, 15]
      },
      tableHeader: {
        bold: true,
        color: 'black'
      }
    },
    defaultStyle: {
      font: 'Helvetica'
    }
  };

  const entryVersion = parseFloat(entry.version || 1);

  if (entryVersion && entryVersion >= 5) {
    if (entry.entity_details.ultimateParents?.length > 0) {
      createParentEntityTable(docDefinition, entry.entity_details.ultimateParents, 'Ultimate Parents')
    }

    if (entry.entity_details.immediateParents?.length > 0) {
      createParentEntityTable(docDefinition, entry.entity_details.immediateParents, 'Immediate Parents')
    }
  }

  if (entryVersion && entryVersion >= 4) {
    if (entry.relevant_activities) {
      createTableRelevantActivities(docDefinition, entry);
    }

    if (entry.relevant_activities?.none.selected !== true) {
      createTableTaxResidency(docDefinition, entry);
    }
  } else {
    createTableTaxResidency(docDefinition, entry);
  }


  if (entry.relevant_activities ) {
    if (entryVersion && entryVersion < 4) {
      createTableRelevantActivities(docDefinition, entry)
    }

    if (entry.tax_residency?.resident_in_BVI === true){
      if (entryVersion < 5) {
        if (entry.relevant_activities.banking_business.selected && entry.banking_business) {
          createTableBusiness(docDefinition, entry.banking_business, 'Banking Business');
        }
        if (entry.relevant_activities.insurance_business.selected && entry.insurance_business) {
          createTableBusiness(docDefinition, entry.insurance_business, 'Insurance Business');
        }
        if (entry.relevant_activities.fund_management_business.selected && entry.fund_management_business) {
          createTableBusiness(docDefinition, entry.fund_management_business, 'Fund Management Business',
            entry.version);
        }
        if (entry.relevant_activities.finance_leasing_business.selected && entry.finance_leasing_business) {
          createTableBusiness(docDefinition, entry.finance_leasing_business, 'Finance and Leasing Business',
            entry.version);
        }
        if (entry.relevant_activities.headquarters_business.selected && entry.headquarters_business) {
          createTableBusiness(docDefinition, entry.headquarters_business, 'Headquarters Business', entry.version);
        }
        if (entry.relevant_activities.shipping_business.selected && entry.shipping_business) {
          createTableBusiness(docDefinition, entry.shipping_business, 'Shipping Business', entry.version);
        }

        if (entry.relevant_activities.holding_business.selected && entry.holding_business) {
          createTableBusiness(docDefinition, entry.holding_business,
            'Holding Business (Pure Equity Holding entities)', entry.version);
        }
        if (entry.relevant_activities.intellectual_property_business.selected && entry.intellectual_property_business) {
          createTableBusiness(docDefinition, entry.intellectual_property_business,
            'Intellectual Property Business', entry.version);
        }
        if (entry.relevant_activities.service_centre_business.selected && entry.service_centre_business) {
          createTableBusiness(docDefinition, entry.service_centre_business,
            'Distribution and Service Centre Business', entry.version);
        }
      } else {
        const entryGeneralCurrency = entry.entity_details?.totalAnnualGrossCurrency || "USD";
        if (entry.relevant_activities.banking_business.selected && entry.banking_business) {
          createTableBusinessV5(docDefinition, entry.banking_business, entryGeneralCurrency,
            SE_BUSINESS_TYPES.BANKING, 'Banking Business');
        }
        if (entry.relevant_activities.insurance_business.selected && entry.insurance_business) {
          createTableBusinessV5(docDefinition, entry.insurance_business, entryGeneralCurrency,
            SE_BUSINESS_TYPES.INSURANCE, 'Insurance Business');
        }
        if (entry.relevant_activities.fund_management_business.selected && entry.fund_management_business) {
          createTableBusinessV5(docDefinition, entry.fund_management_business, entryGeneralCurrency,
            SE_BUSINESS_TYPES.FUND_MANAGEMENT, 'Fund Management Business');
        }
        if (entry.relevant_activities.finance_leasing_business.selected && entry.finance_leasing_business) {
          createTableBusinessV5(docDefinition, entry.finance_leasing_business, entryGeneralCurrency,
            SE_BUSINESS_TYPES.FINANCE_LEASING, 'Finance and Leasing Business');
        }
        if (entry.relevant_activities.headquarters_business.selected && entry.headquarters_business) {
          createTableBusinessV5(docDefinition, entry.headquarters_business, entryGeneralCurrency,
            SE_BUSINESS_TYPES.HEADQUARTERS, 'Headquarters Business');
        }
        if (entry.relevant_activities.shipping_business.selected && entry.shipping_business) {
          createTableBusinessV5(docDefinition, entry.shipping_business, entryGeneralCurrency,
            SE_BUSINESS_TYPES.SHIPPING, 'Shipping Business');
        }

        if (entry.relevant_activities.holding_business.selected && entry.holding_business) {
          createTableBusinessV5(docDefinition, entry.holding_business, entryGeneralCurrency,
            SE_BUSINESS_TYPES.HOLDING, 'Holding Business (Pure Equity Holding entities)');
        }
        if (entry.relevant_activities.intellectual_property_business.selected && entry.intellectual_property_business) {
          createTableBusinessV5(docDefinition, entry.intellectual_property_business, entryGeneralCurrency,
            SE_BUSINESS_TYPES.INTELLECTUAL_PROPERTY, 'Intellectual Property Business');
        }
        if (entry.relevant_activities.service_centre_business.selected && entry.service_centre_business) {
          createTableBusinessV5(docDefinition, entry.service_centre_business, entryGeneralCurrency,
            SE_BUSINESS_TYPES.DISTRIBUTION_SERVICE_CENTRE, 'Distribution and Service Centre Business');
        }
      }
    }
  }

  const supportingDetailsTable = [];
  supportingDetailsTable.push([{ text: 'Supporting Details', style: 'tableHeader', colSpan: 2 }, {}]);
  supportingDetailsTable.push(['Please provide any comment to support your Economic Substance Declaration',
    entry.supporting_details?.support_comment || ""]);

  docDefinition.content.push({
    style: 'table',
    table: {
      headerRows: 0,
      widths: ['50%', '*'],

      body: supportingDetailsTable
    },
  });

  createTableConfirmation(docDefinition, entry);

  const pdfDoc = printer.createPdfKitDocument(docDefinition);

  const chunks = [];
  let result;

  pdfDoc.on('data', function (chunk) {
    chunks.push(chunk)
  });
  pdfDoc.on('end', function () {
    result = Buffer.concat(chunks)

    res.contentType('application/pdf');
    res.setHeader('Content-Disposition', 'inline; filename=SUB-' + entry.company_data.masterclientcode + '-' + entry.company_data.code + '-' + formatDate(entry.submitted_at, "YYYYMMDD") + '.pdf');
    res.send(result)
  });
  pdfDoc.end()
}


exports.generateIncorporationPdf = async function (incorporation, res, next, isZip=false) {

  const pdf = await createIncorporationPdf(incorporation);

  if (pdf && pdf.success){
    if(!isZip){
      res.contentType('application/pdf');
      res.setHeader('Content-Disposition', 'inline; filename=incorporation.pdf');
      res.send(pdf.result);
    }
    else{
      return pdf;
    }

  }
  else{
    const err = new Error('Error generating pdf');
    err.status = 400;
    return next(err);
  }
};


function createIncorporationPdf(incorporation) {
  try {
    let chunks = [];
    let result;

    let printer = new PdfPrinter({
      Helvetica: {
        normal: 'Helvetica',
        bold: 'Helvetica-Bold',
        italics: 'Helvetica-Oblique',
        bolditalics: 'Helvetica-BoldOblique'
      }
    });

    let docDefinition = {
      watermark: {
        text: incorporation.status === 'IN PROGRESS' ? 'DRAFT' : incorporation.status,
        color: '#0081B4',
        opacity: 0.3,
        bold: true,
        italics: false
      },
      info: {
        author: 'Trident Trust Company (BVI) Limited',
        creator: 'Trident Trust Company (BVI) Limited',
        producer: 'Trident Trust Company (BVI) Limited',
      },
      footer: function () {
        // you can apply any logic and return any valid pdfmake element
        return [

          {
            canvas: [
              {
                type: 'polyline',
                lineWidth: 2,
                color: "#0081B4",
                closePath: true,
                points: [{ x: 0, y: 0 }, { x: 800, y: 0 }]
              },

            ]
          }, {
            text: ' ',
            fontSize: 6
          }, {
            text: 'Address: Trident Chambers, Wickhams Cay, PO Box 146, Road Town, Tortola, British Virgin Islands ',
            fontSize: 10,
            alignment: 'center'
          },
          { text: 'Telephone: ****** 494 2434  Website: www.tridenttrust.com', fontSize: 10, alignment: 'center' }
        ]
      },
      content: [
        {
          // under NodeJS (or in case you use virtual file system provided by pdfmake)
          // you can also pass file names here
          image: 'data:image/png;base64,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',
          width: 350,
        },
        {
          style: 'table',
          table: {
            widths: [300, '*'],
            body: createIncorporationStep1Info(incorporation)
          },
        },
        ...createIncorporationStep2Info(incorporation),
        {
          style: 'table',
          table: {
            widths: [300, '*'],
            body: createIncorporationStep3Info(incorporation)
          },
        },
        ...createIncorporationStep4Info(incorporation),
        {
          style: 'table',
          table: {
            widths: [300, '*'],
            body: createIncorporationStep5Info(incorporation)
          },
        },
        {
          style: 'table',
          table: {
            widths: [300, '*'],
            body: createIncorporationStep6Info(incorporation)
          },
        },
        {
          style: 'table',
          table: {
            widths: [300, '*'],
            body: createIncorporationStep7Info(incorporation)
          },
        },
      ],
      styles: {
        header: {
          fontSize: 18,
          bold: true,
          margin: [0, 0, 0, 10]
        },
        subheader: {
          fontSize: 16,
          bold: true,
          margin: [0, 10, 0, 5]
        },
        table: {
          margin: [0, 5, 0, 15]
        },
        tableHeader: {
          bold: true,
          color: 'black'
        }
      },
      defaultStyle: {
        font: 'Helvetica'
      }
    };
    return new Promise(resolve => {
      let pdfDoc = printer.createPdfKitDocument(docDefinition);
      pdfDoc.on('data', function (chunk) {
        chunks.push(chunk)
      });

      pdfDoc.on('error', (error) => {
        resolve({success: false, error: error});
      });

      pdfDoc.on('end', function () {
        result = Buffer.concat(chunks);
        resolve({success:true, result: result})
      });

      pdfDoc.end()
    });
  }catch (e) {
    console.log("error  ", e);
    return {success: false, error: "error generating pdf"}
  }
}

function createIncorporationStep1Info(incorporation) {
  let tableBody = [];

  tableBody.push([{ text: 'Entity Name', style: 'tableHeader' }, { text: incorporation.name }]);
  tableBody.push([{ text: 'Type of entity', style: 'tableHeader' }, { text: incorporation.type }]);
  if (incorporation.type === 'Special Instructions') {
    tableBody.push([{ text: 'Special Instructions', style: 'tableHeader' }, { text: incorporation.typeSpecialInstructions }]);
  }
  tableBody.push([{ text: 'Is the entity part of a group/structure?', style: 'tableHeader' }, { text: formatBoolean(incorporation.partOfGroup) }]);
  tableBody.push([{ text: 'Date submitted', style: 'tableHeader' }, { text: formatDate(incorporation.submittedAt, "YYYY-MM-DD") }]);

  return tableBody;
}

function createIncorporationStep2Info(incorporation) {
  const tables = [];
  for (const relation of incorporation.relations) {
    const relationTable = {
      style: 'table',
      table: {
        widths: [300, '*'],
        body: [],
      },
    };
    const relationsTypes = {
      natural: 'Natural',
      corporate: 'Corporate',
      foundation: 'Foundation',
      trust: 'Trust',
      limited: 'Limited Partnership',
    };

    relationTable.table.body.push([
      { text: 'Is the company owned by a Natural person or a Corporate entity?', style: 'tableHeader' },
      { text: relation.type === "natural" ? 'Natural' : 'Corporate' }]);
    relationTable.table.body.push([{ text: 'Type of relation', style: 'tableHeader' }, { text: relation.groups.join(', ') }]);


    if (relation.type === 'natural') {
      relationTable.table.body.push(...createNaturalRelation(relation));
    } else {
      relationTable.table.body.push([{ text: 'Type of corporation', style: 'tableHeader' }, { text: relationsTypes[relation.ownerShip] }]);
      if (relation.type === 'corporate') {
        relationTable.table.body.push(...createCorporateRelation(relation));
      } else if (relation.type === 'foundation') {
        relationTable.table.body.push(...createFoundationRelation(relation));
      } else if (relation.type === 'trust') {
        relationTable.table.body.push(...createTrustRelation(relation));
      } else if (relation.type === 'limited') {
        relationTable.table.body.push(...createLimitedPartnershipRelation(relation));
      }
    }
    if (relation.groups.includes('Shareholder')) {
      relationTable.table.body.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

      relationTable.table.body.push([{ text: 'ADDITIONAL SHAREHOLDER DETAILS', style: 'tableHeader', colSpan: 2 }, {}]);
      relationTable.table.body.push([{ text: 'Share Percentage', style: 'tableHeader' }, { text: relation.percentage }]);
    }
    tables.push(relationTable);
  }
  return tables;
}

function createIncorporationStep3Info(incorporation) {
  let tableBody = [];

  tableBody.push([{ text: 'Principal business activities', style: 'tableHeader' }, { text: incorporation.principalBusinessActivity }]);
  if (incorporation.principalBusinessActivity === 'Other') {
    tableBody.push([{ text: 'Principal business activities', style: 'tableHeader' }, { text: incorporation.principalBusinessActivityOther }]);
  }
  tableBody.push([{ text: 'Jurisdiction where activities take place', style: 'tableHeader' }, { text: incorporation.activityJurisdiction }]);
  tableBody.push([{ text: 'Tax Residence', style: 'tableHeader' }, { text: incorporation.taxResidence }]);
  if (incorporation.taxResidence === 'Foreign') {
    tableBody.push([{ text: 'Jurisdiction of Tax Residency', style: 'tableHeader' }, { text: incorporation.taxResidencyJurisdiction }]);
  }

  return tableBody;
}

function createIncorporationStep4Info(incorporation) {
  let tables = [];
  const firstQuestion = {
    style: 'table',
    table: {
      widths: [300, '*'],
      body: [],
    },
  };
  firstQuestion.table.body.push([{ text: 'Will the company own any assets legally or benefically?', style: 'tableHeader' }, { text: formatBoolean(incorporation.ownAssets) }]);
  tables.push(firstQuestion);
  if (incorporation.ownAssets) {
    const assetsTable = {
      style: 'table',
      table: {
        widths: [300, '*'],
        body: [],
      },
    };
    assetsTable.table.body.push([{ text: 'ASSETS', style: 'tableHeader', colSpan: 2 }, {}]);
    assetsTable.table.body.push([{ text: 'Asset Type', style: 'tableHeader' }, { text: 'Information', style: 'tableHeader' }]);
    for (const asset of incorporation.assets) {
      let informationColumn = '';
      console.log(asset)
      if (asset.type === 'Aircraft' || asset.type === 'Vessel (ship/yacht)') {
        informationColumn = `Registration Number: ${asset.registrationNumber}\nJurisdiction of Registration: ${asset.jurisdictionOfRegistration}`;
      } else if (asset.type === 'Intellectual property rights'
        || asset.type === 'Shares/equity participations'
        || asset.type === 'Debt'
        || asset.type === 'Other') {
        informationColumn = `Details: ${asset.details}`;
      } else if (asset.type === 'Investment portfolio') {
        informationColumn = `Name of institution: ${asset.nameOfInstitution}\nAddress of institution: ${asset.addressOfInstitution}`;
      } else if (asset.type === 'Bank account') {
        informationColumn = `Name of bank: ${asset.nameOfBank} \nAddress of bank: ${asset.addressOfBank}`;
      } else if (asset.type === 'Trust assets') {
        informationColumn = `Name of trust: ${asset.nameOfTrust}`;
      } else if (asset.type === 'Real estate') {
        informationColumn = `Type: ${asset.realEstateType}\nLocation: ${asset.location}`;
      }
      assetsTable.table.body.push([{ text: asset.type }, { text: informationColumn }]);
    }
    assetsTable.table.body.push([{text: 'Estimated value of assets:', style: 'tableHeader'}, {
      text: formatToCurrencyLocaleNumber(incorporation.estimated_value_of_assets, '')}]);
    
    tables.push(assetsTable);
    
  }
  const fundsTable = {
    style: 'table',
    table: {
      widths: [300, '*'],
      body: [],
    },
  };
  fundsTable.table.body.push([{ text: 'SOURCE OF FUNDS', style: 'tableHeader', colSpan: 2 }, {}]);
  fundsTable.table.body.push([{ text: 'Payment for Trident Services will be made from bank account owned by' }, { text: incorporation.bankAccountOwner }]);
  fundsTable.table.body.push([{ text: 'Bank name' }, { text: incorporation.bankName }]);
  fundsTable.table.body.push([{ text: 'Bank address' }, { text: incorporation.bankAddress }]);
  fundsTable.table.body.push([{ text: 'FUNDS', style: 'tableHeader', colSpan: 2 }, {}]);
  fundsTable.table.body.push([{ text: 'Source Type', style: 'tableHeader' }, { text: 'Information', style: 'tableHeader' }]);
  for (const fund of incorporation.funds) {
    let informationColumn = '';
    if (fund.type === 'Loan') {
      informationColumn = `Name of Financial Institution: ${fund.nameOfFinancialInstitution}`;
    } else if (fund.type === 'Sale of assets' || fund.type === 'Other') {
      informationColumn = `Details: ${fund.details}`;
    } else if (fund.type === 'Business income') {
      informationColumn = `Average annual turnover/profit: ${fund.profit}`;
    } else if (fund.type === 'Dividend from subsidiary') {
      informationColumn = `Name of subsidiary: ${fund.nameOfSubsidiary}\nJurisdiction of operation: ${fund.jurisdictionOfOperation}`;
    }
    fundsTable.table.body.push([{ text: fund.type }, { text: informationColumn }]);
  }
  tables.push(fundsTable);
  return tables;
}

function createIncorporationStep5Info(incorporation) {
  let tableBody = [];

  tableBody.push([{ text: 'Record Keeping Details', style: 'tableHeader', colSpan: 2 }, {}]);
  tableBody.push([{ text: 'Record Holder' }, { text: incorporation.records.recordHolder }]);
  tableBody.push([{ text: 'Address - 1st Line' }, { text: incorporation.records.primaryAddress }]);
  tableBody.push([{ text: 'Address - 2st Line' }, { text: incorporation.records.secondaryAddress }]);
  tableBody.push([{ text: 'State' }, { text: incorporation.records.state }]);
  tableBody.push([{ text: 'City' }, { text: incorporation.records.city }]);
  tableBody.push([{ text: 'Postal Code' }, { text: incorporation.records.postalCode }]);
  tableBody.push([{ text: 'E-mail' }, { text: incorporation.records.email }]);
  tableBody.push([{ text: 'Country of Operation' }, { text: incorporation.records.operationCountry }]);

  return tableBody;
}

function createIncorporationStep6Info(incorporation) {
  let tableBody = [];

  tableBody.push([{ text: 'Would you like to request additional services?', style: 'tableHeader' }, { text: formatBoolean(incorporation.requestAdditionalServices) }]);
  if (incorporation.requestAdditionalServices) {
    tableBody.push([{ text: 'Additional services requested', style: 'tableHeader' }, { text: incorporation.additionalServices.join(', ') }]);
    if (incorporation.additionalServices.includes('SIBA licence')) {
      tableBody.push([{ text: 'SIBA licence type', style: 'tableHeader' }, { text: incorporation.sibaLicence }]);
    }
    if (incorporation.additionalServices.includes('Trust licence')) {
      tableBody.push([{ text: 'Trust licence type', style: 'tableHeader' }, { text: incorporation.trustLicence }]);
    }
    if (incorporation.additionalServices.includes('Other')) {
      tableBody.push([{ text: 'Other services requested', style: 'tableHeader' }, { text: incorporation.otherServices }]);
    }
  }

  return tableBody;
}

function createIncorporationStep7Info(incorporation) {
  let tableBody = [];
  if (incorporation.status !== 'IN PROGRESS') {

    tableBody.push([{ text: 'Declaration', style: 'tableHeader', colSpan: 2 }, {}]);
    tableBody.push([{ text: 'The information provided in this and any form provided to Trident in conjunction with this form is, to the best of my knowledge and belief, true and correct.' }, { text: formatBoolean(incorporation.declaration.information) }]);
    tableBody.push([{ text: 'The assets to be introduced into the entity/structure and the funds used to pay for Trident’s services in relation thereto are from lawful sources.' }, { text: formatBoolean(incorporation.declaration.assets) }]);
    tableBody.push([{ text: 'I have read, understood and accept Trident’s Terms of Business and agree to comply with all of the terms and conditions outlined therein.' }, { text: formatBoolean(incorporation.declaration.termsAndConditions) }]);
    tableBody.push([{ text: 'Name' }, { text: incorporation.declaration.name }]);
    tableBody.push([{ text: 'Date' }, { text: formatDate(incorporation.declaration.date, 'YYYY-MM-DD') }]);
    tableBody.push([{ text: 'Relation to entity' }, { text: incorporation.declaration.relationToEntity }]);
    if (incorporation.declaration.relationToEntity === 'Other') {
      tableBody.push([{ text: 'Other relation to entity' }, { text: incorporation.declaration.relationToEntityOther }]);
    }
    tableBody.push([{ text: 'Phone' }, { text: incorporation.declaration.phone }]);
  } else {
    tableBody.push([]);
  }


  return tableBody;
}

function createNaturalRelation(relation) {
  const table = [];
  // Relation details
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'RELATION DETAILS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'First name', style: 'tableHeader' }, { text: relation.details.firstName }]);
  table.push([{ text: 'Middlename/Initial', style: 'tableHeader' }, { text: relation.details.middleName }]);
  table.push([{ text: 'Last Name', style: 'tableHeader' }, { text: relation.details.lastName }]);
  table.push([{ text: 'Occupation', style: 'tableHeader' }, { text: relation.details.occupation }]);
  table.push([{ text: 'Source of Income', style: 'tableHeader'}, {text: relation.details.source_of_income}]);
  table.push([{ text: 'Date of Birth', style: 'tableHeader' }, { text: formatDate(relation.details.birthDate, 'YYYY-MM-DD') }]);
  table.push([{ text: 'Nationality', style: 'tableHeader' }, { text: relation.details.nationality }]);
  table.push([{ text: 'Country of birth', style: 'tableHeader' }, { text: relation.details.countryBirth }]);
  // Identification
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'IDENTIFICATION', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'Electronic ID', style: 'tableHeader' }, { text: relation.electronicIdInfo &&
    relation.electronicIdInfo.isElectronicId === true ? 'Yes' : 'No' }]);
  table.push([{ text: 'Type of Identification', style: 'tableHeader' }, { text: relation.identification.identificationType }]);
  table.push([{ text: 'Country of Issue', style: 'tableHeader' }, { text: relation.identification.issueCountry }]);
  table.push([{ text: 'Expiry Date', style: 'tableHeader' }, { text: formatDate(relation.identification.expiryDate, 'YYYY-MM-DD') }]);
  table.push([{ text: 'Valid Identification', style: 'tableHeader' }, { text: formatBoolean(relation.identification.valid) }]);
  // Principal Address
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'PRINCIPAL ADDRESS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push(...createAddressTable(relation.principalAddress));
  // Mailing Address
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'MAILING ADDRESS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'Is mailing address the same as Principal address?', style: 'tableHeader' }, { text: relation.isSamePrincipalAddress }]);
  table.push(...createAddressTable(relation.mailingAddress));
  // Tax Advice
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'TAX ADVICE', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'Confirmation Regarding Legal / Tax Advice', style: 'tableHeader' }, { text: formatBoolean(relation.taxResidence.confirmation) }]);
  table.push([{ text: 'Tax Residence', style: 'tableHeader' }, { text: relation.taxResidence.taxResidence }]);
  if (relation.taxResidence.confirmation) {
    // Advisor Details
    table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

    table.push([{ text: 'ADVISOR DETAILS', style: 'tableHeader', colSpan: 2 }, {}]);
    table.push([{ text: 'First Name', style: 'tableHeader' }, { text: relation.advisorDetails.firstName }]);
    table.push([{ text: 'Middle Name', style: 'tableHeader' }, { text: relation.advisorDetails.middleName }]);
    table.push([{ text: 'Last Name', style: 'tableHeader' }, { text: relation.advisorDetails.lastName }]);
    table.push([{ text: 'Name of Firm', style: 'tableHeader' }, { text: relation.advisorDetails.firmName }]);
    table.push([{ text: 'Phone', style: 'tableHeader' }, { text: relation.advisorDetails.phone }]);
    table.push([{ text: 'E-mail', style: 'tableHeader' }, { text: relation.advisorDetails.email }]);
    table.push([{ text: 'Nationality', style: 'tableHeader' }, { text: relation.advisorDetails.nationality }]);
    table.push([{ text: 'Country of Incorporation', style: 'tableHeader' }, { text: relation.advisorDetails.incorporationCountry }]);

    // Principal Advisor Address
    table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

    table.push([{ text: 'PRINCIPAL ADVISOR ADDRESS', style: 'tableHeader', colSpan: 2 }, {}]);
    table.push(...createAddressTable(relation.principalAddress));
  }

  // PEP Information
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'Is this relation a PEP?', style: 'tableHeader' }, { text: formatBoolean(relation.pep) }]);
  return table;
}

function createCorporateRelation(relation) {
  const table = [];
  // Relation details
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'RELATION DETAILS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'Is the company already a TridentTrust client?', style: 'tableHeader' },
    { text: formatBoolean(relation.details.isTridentClient) }]);
  table.push([{ text: 'Organization name', style: 'tableHeader' }, { text: relation.details.organizationName }]);
  table.push([{ text: 'Incorporation / Formation Number', style: 'tableHeader' }, { text: relation.details.incorporationNumber }]);
  if (!relation.details.isTridentClient) {
    table.push([{ text: 'Tax Residence', style: 'tableHeader' }, { text: relation.details.taxResidence }]);
    table.push([{ text: 'Business Registration Number (if applicable)', style: 'tableHeader' }, { text: relation.details.businessNumber }]);
    table.push([{ text: 'Date of Incorporation', style: 'tableHeader' }, { text: formatDate(relation.details.incorporationDate, 'YYYY-MM-DD') }]);
    table.push([{ text: 'Country of Incorporation', style: 'tableHeader' }, { text: relation.details.incorporationCountry }]);
  }

  // Principal Address
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'PRINCIPAL ADDRESS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push(...createAddressTable(relation.principalAddress));
  // Mailing Address
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'MAILING ADDRESS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'Is mailing address the same as Principal address?', style: 'tableHeader' }, { text: relation.isSamePrincipalAddress }]);
  table.push(...createAddressTable(relation.mailingAddress));

  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'Listed Company Details', style: 'tableHeader' }, { text: formatBoolean(relation.listedCompanyDetails.active) }]);
  table.push([{ text: 'Regulated Limited Company Details', style: 'tableHeader' }, { text: formatBoolean(relation.limitedCompanyDetails.active) }]);
  table.push([{ text: 'Fund Details', style: 'tableHeader' }, { text: formatBoolean(relation.mutualFundDetails.active) }]);

  return table;
}

function createFoundationRelation(relation) {
  const table = [];
  // Relation details
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'RELATION DETAILS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'Is the company already a TridentTrust client?', style: 'tableHeader' },
    { text: formatBoolean(relation.details.isTridentClient) }]);
  table.push([{ text: 'Organization name', style: 'tableHeader' }, { text: relation.details.organizationName }]);
  table.push([{ text: 'Incorporation / Formation Number', style: 'tableHeader' }, { text: relation.details.incorporationNumber }]);
  if (!relation.details.isTridentClient) {
    table.push([{ text: 'Tax Residence', style: 'tableHeader' }, { text: relation.details.taxResidence }]);
    table.push([{ text: 'Business Registration Number (if applicable)', style: 'tableHeader' }, { text: relation.details.businessNumber }]);
    table.push([{ text: 'Date of Incorporation', style: 'tableHeader' }, { text: formatDate(relation.details.incorporationDate, 'YYYY-MM-DD') }]);
    table.push([{ text: 'Country of Incorporation', style: 'tableHeader' }, { text: relation.details.incorporationCountry }]);
  }


  // Principal Address
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'PRINCIPAL ADDRESS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push(...createAddressTable(relation.principalAddress));
  // Mailing Address
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'MAILING ADDRESS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'Is mailing address the same as Principal address?', style: 'tableHeader' }, { text: relation.isSamePrincipalAddress }]);
  table.push(...createAddressTable(relation.mailingAddress));

  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'Listed Company Details', style: 'tableHeader' }, { text: formatBoolean(relation.listedCompanyDetails.active) }]);
  table.push([{ text: 'Regulated Limited Company Details', style: 'tableHeader' }, { text: formatBoolean(relation.limitedCompanyDetails.active) }]);
  table.push([{ text: 'Foundation Details', style: 'tableHeader' }, { text: formatBoolean(relation.foundation.active) }]);
  table.push([{ text: 'Fund Details', style: 'tableHeader' }, { text: formatBoolean(relation.mutualFundDetails.active) }]);

  return table;
}

function createTrustRelation(relation) {
  const table = [];
  // Relation details
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'RELATION DETAILS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'Is the company already a TridentTrust client?', style: 'tableHeader' },
    { text: formatBoolean(relation.details.isTridentClient) }]);
  table.push([{ text: 'Trust Name', style: 'tableHeader' }, { text: relation.details.organizationName }]);
  table.push([{ text: 'Trustee Name', style: 'tableHeader' }, { text: relation.details.incorporationNumber }]);
  if (!relation.details.isTridentClient) {
    table.push([{ text: 'Tax Residence', style: 'tableHeader' }, { text: relation.details.taxResidence }]);
    table.push([{ text: 'Business Registration Number (if applicable)', style: 'tableHeader' }, { text: relation.details.businessNumber }]);
    table.push([{ text: 'Date of Trust Establishment', style: 'tableHeader' }, { text: formatDate(relation.details.incorporationDate, 'YYYY-MM-DD') }]);
    table.push([{ text: 'Country of Incorporation', style: 'tableHeader' }, { text: relation.details.incorporationCountry }]);
  }

  // Principal Address
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'PRINCIPAL ADDRESS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push(...createAddressTable(relation.principalAddress));
  // Mailing Address
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'MAILING ADDRESS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'Is mailing address the same as Principal address?', style: 'tableHeader' }, { text: relation.isSamePrincipalAddress }]);
  table.push(...createAddressTable(relation.mailingAddress));

  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'Regulated Limited Company Details', style: 'tableHeader' }, { text: formatBoolean(relation.limitedCompanyDetails.active) }]);

  return table;
}

function createLimitedPartnershipRelation(relation) {
  const table = [];
  // Relation details
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'RELATION DETAILS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'Is the company already a TridentTrust client?', style: 'tableHeader' },
    { text: formatBoolean(relation.details.isTridentClient) }]);
  table.push([{ text: 'Organization name', style: 'tableHeader' }, { text: relation.details.organizationName }]);
  table.push([{ text: 'Incorporation / Formation Number', style: 'tableHeader' }, { text: relation.details.incorporationNumber }]);
  if (!relation.details.isTridentClient) {
    table.push([{ text: 'Tax Residence', style: 'tableHeader' }, { text: relation.details.taxResidence }]);
    table.push([{ text: 'Business Registration Number (if applicable)', style: 'tableHeader' }, { text: relation.details.businessNumber }]);
    table.push([{ text: 'Date of Incorporation', style: 'tableHeader' }, { text: formatDate(relation.details.incorporationDate, 'YYYY-MM-DD') }]);
    table.push([{ text: 'Country of Incorporation', style: 'tableHeader' }, { text: relation.details.incorporationCountry }]);
  }

  // Principal Address
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'PRINCIPAL ADDRESS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push(...createAddressTable(relation.principalAddress));
  // Mailing Address
  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'MAILING ADDRESS', style: 'tableHeader', colSpan: 2 }, {}]);
  table.push([{ text: 'Is mailing address the same as Principal address?', style: 'tableHeader' }, { text: relation.isSamePrincipalAddress }]);
  table.push(...createAddressTable(relation.mailingAddress));

  table.push([{ text: " ", style: 'tableHeader', colSpan: 2 }, {}]);

  table.push([{ text: 'Listed Company Details', style: 'tableHeader' }, { text: formatBoolean(relation.listedCompanyDetails.active) }]);
  table.push([{ text: 'Regulated Limited Company Details', style: 'tableHeader' }, { text: formatBoolean(relation.limitedCompanyDetails.active) }]);
  table.push([{ text: 'Fund Details', style: 'tableHeader' }, { text: formatBoolean(relation.mutualFundDetails.active) }]);

  return table;
}

function createAddressTable(address) {
  const table = [];
  table.push([{ text: 'Address - 1st Line', style: 'tableHeader' }, { text: address.primaryAddress }]);
  table.push([{ text: 'Address - 2nd Line', style: 'tableHeader' }, { text: address.secondaryAddress }]);
  table.push([{ text: 'Country', style: 'tableHeader' }, { text: address.country }]);
  table.push([{ text: 'State/Province', style: 'tableHeader' }, { text: address.state }]);
  table.push([{ text: 'City', style: 'tableHeader' }, { text: address.postalCode }]);
  table.push([{ text: 'Postal Code', style: 'tableHeader' }, { text: address.city }]);
  return table;
}

function formatDate(date, format) {
    if (date) {
      if(typeof(date) === "string"){
        return moment(date).format(format);
      }
      else{
        date = new Date(date.getTime() +  ( date.getTimezoneOffset() * 60000 ) )
        return moment(date).format(format);
      }
    } else {
      return '';
    }
  }

function formatBoolean(boolValue) {
  if (boolValue == null || boolValue == undefined) {
    return "";
  }
  return (boolValue ? "Yes" : "No")
}

function formatBusinessAddress(businessObj) {
  let addressComponent = [];
  addressComponent.push(businessObj.address_line1);
  addressComponent.push(businessObj.address_line2);
  addressComponent.push(businessObj.city);
  addressComponent.push(businessObj.country);
  addressComponent.push(businessObj.postalcode);

  return addressComponent.filter(function (val) {return val;}).join(', ');
}

function formatToCurrencyLocaleNumber(number, currency) {
  let val = number && Number(number) ? number : 0;
  return currency + " " + Number(val).toLocaleString('en', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}


function createCompanyInfo(entry) {
  let tableBody = [];

  tableBody.push([{text: 'Client Company Name', style: 'tableHeader'}, {text: entry.company_data.name}]);
  tableBody.push([{text: 'Client Company Number', style: 'tableHeader'}, {text: entry.company_data.code}]);
  tableBody.push([{text: 'Registered Agent', style: 'tableHeader'}, {text: "Trident Trust Company (BVI) Limited"}]);
  tableBody.push([{text: 'Master Client Code', style: 'tableHeader'}, {text: entry.company_data.masterclientcode}]);
  tableBody.push([{
    text: 'Date submitted',
    style: 'tableHeader'
  }, {text: formatDate(entry.initial_submit_date ? entry.initial_submit_date : entry.submitted_at, "YYYY-MM-DD")}]);

  if (entry.reopened?.details?.length > 0){
    let reopenedCount = 0;
    entry.reopened.details.forEach((reopened) =>{
      if (reopened.resubmitted_at){
        reopenedCount++;
        tableBody.push([{
          text: `Date resubmitted (${reopenedCount})`,
          style: 'tableHeader'
        }, {text: formatDate(reopened.resubmitted_at, "YYYY-MM-DD")}]);
      }
    });
  }

  return tableBody;
}

function createTableEntityDetails(entry) {
  let tableBody = [];

  const entryVersion = entry.version ? parseFloat(entry.version) : 0;

  if (entryVersion >= 4) {
    tableBody.push([{
      text: 'Has an application been made and confirmed with ITA to change your financial period?',
      style: 'tableHeader'
    }, formatBoolean(entry.entity_details.financial_period_changed)]);
  }
  tableBody.push([{
    text: 'Financial period begins',
    style: 'tableHeader'
  }, { text: formatDate(entry.entity_details.financial_period_begins, "YYYY-MM-DD") }]);

  tableBody.push([{
    text: 'Financial period ends',
    style: 'tableHeader'
  }, formatDate(entry.entity_details.financial_period_ends, "YYYY-MM-DD")]);

  if (entryVersion < 4) {
    tableBody.push([{
      text: 'Has the financial period changed',
      style: 'tableHeader'
    }, formatBoolean(entry.entity_details.financial_period_changed)]);
  }

  if (entry.entity_details.financial_period_changed) {
    tableBody.push([{
      text: 'Previous end date',
      style: 'tableHeader'
    }, formatDate(entry.entity_details.previous_financial_period_ends, "YYYY-MM-DD")]);
    tableBody.push([{
      text: 'Date of application to ITA under Rule 13, 15 or 16',
      style: 'tableHeader'
    }, formatDate(entry.entity_details.date_of_application_ITA, "YYYY-MM-DD")]);
  }

  if (entryVersion >= 5) {
    tableBody.push([{
      text: 'TIN',
      style: 'tableHeader'
    }, entry.entity_details.TIN ?? ""]);
  

    tableBody.push([{
      text: 'Gross total annual income of the entity',
      style: 'tableHeader'
    }, (entry.entity_details.totalAnnualGrossCurrency || 'USD') + " " + (entry.entity_details.totalAnnualGross ?? "")]);

    tableBody.push([{
      text: 'Business address is same as registered address?',
      style: 'tableHeader'
    }, formatBoolean(entry.entity_details.isSameBusinessAddress)]);

    tableBody.push([{
      text: 'Business Address Line 1 (street #, name & city)',
      style: 'tableHeader'
    }, entry.entity_details.businessAddress.address_line1]);

    tableBody.push([{
      text: 'Business Address Line 2',
      style: 'tableHeader'
    }, entry.entity_details.businessAddress.address_line2]);

    const businessCountry = countries.find((c) => c.alpha_3_code === entry.entity_details.businessAddress?.country)
    tableBody.push([{
      text: 'Business Address Country',
      style: 'tableHeader'
    }, businessCountry?.name || ""]);

    tableBody.push([{
      text: 'Name of the MNE Group (if applicable)',
      style: 'tableHeader'
    }, entry.entity_details.nameOfMNEGroup || ""]);


    tableBody.push([{
      text: 'Does entity have an ultimate parent entity?',
      style: 'tableHeader'
    }, formatBoolean(entry.entity_details.hasUltimateParents)]);

    tableBody.push([{
      text: 'Does entity have an immediate parent entity?',
      style: 'tableHeader'
    }, formatBoolean(entry.entity_details.hasImmediateParents)]);


  }

  return tableBody;
}

function createTableTaxResidency(docDefinition, entry) {

  let tableBody = [];
  tableBody.push([{ text: 'Tax Residency', style: 'tableHeader', colSpan: 2 }, {}]);

  if (entry.version && parseFloat(entry.version) < 4) {
    tableBody.push(['Are you a resident in the BVI?', formatBoolean(entry.tax_residency.resident_in_BVI)])
    if (!entry.tax_residency.resident_in_BVI) {
      tableBody.push(['Entity’s jurisdiction of tax residency', entry.tax_residency.entity_jurisdiction])
      tableBody.push(['Foreign Tax ID Number', entry.tax_residency.foreign_tax_id_number])


      tableBody.push(['Attachments submitted with Substance Filing', ((entry.tax_residency.evidence_non_residency && entry.tax_residency.evidence_non_residency.length > 0) ||
        (entry.tax_residency.evidence_provisional_treatment_non_residency != null && entry.tax_residency.evidence_provisional_treatment_non_residency.length > 0) ? "Yes" : "No")]);

      if (entry.version && parseFloat(entry.version) >= 3) {

        tableBody.push(['Does the entity have a parent entity?', entry.tax_residency.have_parent_entity_not_answered ?
          'Not Answered' : formatBoolean(entry.tax_residency.have_parent_entity)]);
        if (entry.tax_residency.have_parent_entity && !entry.tax_residency.have_parent_entity_not_answered) {
          tableBody.push(['Parent entity name', (entry.tax_residency.parent_entity_name && entry.tax_residency.parent_entity_name.length > 0 ? entry.tax_residency.parent_entity_name : "N/A")]);
          tableBody.push(['Parent entity alternative name', (entry.tax_residency.parent_entity_alternative_name && entry.tax_residency.parent_entity_alternative_name.length > 0 ? entry.tax_residency.parent_entity_alternative_name : "N/A")]);
          tableBody.push(["Parent Entity's jurisdiction of formation", (entry.tax_residency.parent_entity_jurisdiction && entry.tax_residency.parent_entity_jurisdiction.length > 0 ? entry.tax_residency.parent_entity_jurisdiction : "N/A")]);
          tableBody.push(["Parent Entity’s Incorporation/ Formation Number", (entry.tax_residency.parent_entity_incorporation_number && entry.tax_residency.parent_entity_incorporation_number.length > 0 ? entry.tax_residency.parent_entity_incorporation_number : "N/A")]);
        }
      }
    }
  } else {
    tableBody.push(['Does the entity intend to make a claim of tax residency outside the Virgin Islands under rule 2?',
      formatBoolean(!entry.tax_residency.resident_in_BVI)]);
    if (!entry.tax_residency.resident_in_BVI) {
      tableBody.push(['Entity’s jurisdiction of tax residency', entry.tax_residency.entity_jurisdiction]);
      tableBody.push(['Taxpayer identification number (“TIN”) or other identification reference number', entry.tax_residency.foreign_tax_id_number]);

      if (parseFloat(entry.version) < 5) {
        tableBody.push(['Name of MNE group', entry.tax_residency.MNE_group_name]);

        tableBody.push(['Does the entity have a parent entity?', entry.tax_residency.have_parent_entity_not_answered ?
          'Not Answered' : formatBoolean(entry.tax_residency.have_parent_entity)]);
        if (entry.tax_residency.have_parent_entity && !entry.tax_residency.have_parent_entity_not_answered) {
          tableBody.push(['Parent entity name', (entry.tax_residency.parent_entity_name && entry.tax_residency.parent_entity_name.length > 0 ? entry.tax_residency.parent_entity_name : "N/A")]);
          tableBody.push(['Parent entity alternative name', (entry.tax_residency.parent_entity_alternative_name && entry.tax_residency.parent_entity_alternative_name.length > 0 ? entry.tax_residency.parent_entity_alternative_name : "N/A")]);
          tableBody.push(["Parent Entity's jurisdiction of formation", (entry.tax_residency.parent_entity_jurisdiction && entry.tax_residency.parent_entity_jurisdiction.length > 0 ? entry.tax_residency.parent_entity_jurisdiction : "N/A")]);
          tableBody.push(["Parent Entity’s Incorporation/ Formation Number", (entry.tax_residency.parent_entity_incorporation_number && entry.tax_residency.parent_entity_incorporation_number.length > 0 ? entry.tax_residency.parent_entity_incorporation_number : "N/A")]);
        }
      }

      tableBody.push(['Evidence type', entry.tax_residency.evidence_type === "non residency" ?
        "Evidence of Tax Residency in another jurisdiction which meets ITA Rule 3" :
        "Provisional treatment as non-resident under ITA Rule 6 which meets conditions in Rule 10"
      ]);
      tableBody.push(['Attachments submitted with Substance Filing', ((entry.tax_residency.evidence_non_residency && entry.tax_residency.evidence_non_residency.length > 0) ||
        (entry.tax_residency.evidence_provisional_treatment_non_residency != null && entry.tax_residency.evidence_provisional_treatment_non_residency.length > 0) ? "Yes" : "No")]);

    }
  }

  docDefinition.content.push(
    {
      style: 'table',
      table: {
        widths: [300, '*'],
        body: tableBody
      }
    });

  return tableBody;
}

function createTableRelevantActivities(docDefinition, entry) {

  if (entry.relevant_activities) {

    let tableBody = [];
    tableBody.push([{text: 'Relevant Activities', style: 'tableHeader', colSpan: 5}, {}, {}, {}, {}]);
    tableBody.push([{text: 'Relevant Activity Period'}, {
      text: 'Start date',
      style: 'tableHeader'
    }, {text: formatDate(entry.entity_details.financial_period_begins, "YYYY-MM-DD")}, {
      text: 'End date',
      style: 'tableHeader'
    }, {text: formatDate(entry.entity_details.financial_period_ends, "YYYY-MM-DD")}]);
    tableBody.push([{text: 'Relevant Activity or Activities Conducted', colSpan: 5}, {}, {}, {}, {}]);
    if (entry.relevant_activities.banking_business.selected) {
      tableBody.push([{
        text: 'Banking Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.banking_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.banking_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      //addFinancialPeriods(tableBody, entry.relevant_activities.banking_business.financial_periods)
    }

    if (entry.relevant_activities.insurance_business.selected) {
      tableBody.push([{
        text: 'Insurance Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.insurance_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.insurance_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.insurance_business.financial_periods)
    }
    if (entry.relevant_activities.fund_management_business.selected) {
      tableBody.push([{
        text: 'Fund Management Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.fund_management_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.fund_management_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.fund_management_business.financial_periods)
    }
    if (entry.relevant_activities.finance_leasing_business.selected) {
      tableBody.push([{
        text: 'Finance and Leasing Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.finance_leasing_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.finance_leasing_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.finance_leasing_business.financial_periods)
    }
    if (entry.relevant_activities.headquarters_business.selected) {
      tableBody.push([{
        text: 'Headquarters business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.headquarters_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.headquarters_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.headquarters_business.financial_periods)
    }
    if (entry.relevant_activities.shipping_business.selected) {
      tableBody.push([{
        text: 'Shipping Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.shipping_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.shipping_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.shipping_business.financial_periods)
    }
    if (entry.relevant_activities.holding_business.selected) {
      tableBody.push([{
        text: 'Holding Business (Pure Equity Holding entities)',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.holding_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.holding_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.holding_business.financial_periods)
    }
    if (entry.relevant_activities.intellectual_property_business.selected) {
      tableBody.push([{
        text: 'Intellectual Property Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.intellectual_property_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.intellectual_property_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.intellectual_property_business.financial_periods)
    }
    if (entry.relevant_activities.service_centre_business.selected) {
      tableBody.push([{
        text: 'Distribution and Service Centre Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.service_centre_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.service_centre_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.service_centre_business.financial_periods)
    }
    if (entry.relevant_activities.none.selected) {
      tableBody.push([{
        text: 'None',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.none.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.none.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.none.financial_periods)
      if (entry.relevant_activities.none_remarks && entry.relevant_activities.none_remarks.length > 0) {
        tableBody.push([{text: entry.relevant_activities.none_remarks, colSpan: 5}, {}, {}, {}, {}]);
      }
    }

    docDefinition.content.push(
      {
        style: 'table',
        table: {
          widths: [200, '*', '*', '*', '*'],
          body: tableBody
        }
      });

    return tableBody;
  }
}

function addFinancialPeriods(tableBody, financial_periods) {
  if (financial_periods.length > 1) {
    for (let index = 1; index < financial_periods.length; index++) {
      tableBody.push([{
        text: '',
        colspan: 2
      }, {}, {text: formatDate(financial_periods[index].financial_period_begins, "YYYY-MM-DD")}, {}, {text: formatDate(financial_periods[index].financial_period_ends, "YYYY-MM-DD")}]);
    }
  }
}


function createTableBusiness(docDefinition, businessObj, businessTitle, entryVersion) {

  let tableBody = [];
  if (businessTitle == 'Intellectual Property Business') {
    tableBody.push(['Is the entity high risk?', formatBoolean(businessObj.high_risk_ip)]);
    tableBody.push(['Can the company provide evidence that would allow it to contest  the  rebuttable  presumption  introduced  by ESA section  9(2)(b)?', formatBoolean(businessObj.evidence_high_risk_ip)]);
    tableBody.push(['Attachments submitted with Substance Filing', (businessObj.high_risk_ip_evidence && businessObj.high_risk_ip_evidence.length > 0 ? "Yes" : "No")]);
  }
  if (businessTitle != 'Holding Business (Pure Equity Holding entities)') {
    tableBody.push(['Is the relevant activity directed and managed in the BVI?', formatBoolean(businessObj.management_in_bvi)]);
    tableBody.push(['Number of board meetings the entity held during the financial period', businessObj.number_of_board_meetings]);
    tableBody.push(['How many board meetings were held outside of the BVI', businessObj.number_or_board_meetings_outside_bvi]);
    tableBody.push(['Total turnover for the relevant activity during the financial period', businessObj.total_turnover]);
    tableBody.push(['Total expenditure incurred on the relevant activity during the financial period', businessObj.total_expenditure]);
    tableBody.push(['Total expenditure incurred in the BVI on the relevant activity during the financial period', businessObj.total_expenditure_bvi]);
  } else {
    tableBody.push(['Is the entity in compliance with its statutory obligations?', formatBoolean(businessObj.compliant_with_statutory_obligations)]);
    tableBody.push(['Does the entity manage its equity participations?', formatBoolean(businessObj.manage_equity_participations)]);
  }
  if (entryVersion && parseFloat(entryVersion) >= 3) {
    if ((businessTitle != 'Holding Business (Pure Equity Holding entities)' && businessTitle != "Intellectual Property Business") ||
      (businessTitle == "Intellectual Property Business" && (businessObj.high_risk_ip == false || businessObj.evidence_high_risk_ip == true)) ||
      (businessTitle == 'Holding Business (Pure Equity Holding entities)' && businessObj.manage_equity_participations)) {
      tableBody.push(['Total number of employees', businessObj.full_total_employees]);
      tableBody.push(['Total number of suitably qualified employees engaged in the relevant activity in the BVI', businessObj.total_employees]);
      tableBody.push(['Is the total number suitably qualified employees engaged in the relevant activity in the BVI adequate?', formatBoolean(businessObj.total_employees_adequate)]);
    }
  }
  if (businessTitle != 'Holding Business (Pure Equity Holding entities)' || businessObj.manage_equity_participations) {
    if (entryVersion && parseFloat(entryVersion) < 3) {
      tableBody.push(['Total number of suitably qualified employees engaged in the relevant activity in the BVI', businessObj.total_employees]);
      tableBody.push(['Is this number adequate?', formatBoolean(businessObj.total_employees_adequate)]);
    }
    if (businessTitle != 'Holding Business (Pure Equity Holding entities)') {
      tableBody.push(['Are premises appropriate for income generating activities in the BVI?', formatBoolean(businessObj.income_generating_premises)]);
    } else {
      tableBody.push(['Are premises appropriate for carrying out the management of the entity’s equity participations?', formatBoolean(businessObj.income_generating_premises)]);
    }
    if (businessTitle == 'Intellectual Property Business') {
      tableBody.push(['Is any equipment located within the BVI that is used in connection with the relevant activity?', formatBoolean(businessObj.equipment_in_bvi)]);
      tableBody.push(['Attachments submitted with Substance Filing', (businessObj.evidence_equipment && businessObj.evidence_equipment.length > 0 ? "Yes" : "No")]);
    }

    docDefinition.content.push({ text: businessTitle, fontSize: 15, margin: [0, 20, 0, 20] });
    docDefinition.content.push(
      {
        style: 'table', // optional
        table: {
          headerRows: 0,
          widths: [400, '*'],

          body: tableBody
        },
        //pageBreak: "after"
      });
    if (businessTitle != 'Holding Business (Pure Equity Holding entities)') {
      let tableDirectors = [];
      tableDirectors.push([{ text: 'Name', style: 'tableHeader' },
      { text: 'Date of Birth / Date of Incorporation', style: 'tableHeader' },
      { text: 'Address', style: 'tableHeader' },
      { text: 'Resident in BVI', style: 'tableHeader' },
      { text: 'Position held', style: 'tableHeader' }]);

      for (let manager of businessObj.managers) {
        tableDirectors.push([manager.first_name + "  " + (manager.middle_name ? manager.middle_name + " " : "") + manager.last_name,
        formatDate(manager.date_of_birth, "YYYY-MM-DD"),
        formatBusinessAddress(manager),
        formatBoolean(manager.resident_in_bvi),
        manager.position_held
        ]);
      }

      docDefinition.content.push({ text: "Direction and management", fontSize: 12, margin: [0, 20, 0, 20] });

      docDefinition.content.push(
        {
          style: 'table', // optional
          table: {
            headerRows: 0,
            widths: ['*', '*', '*', '*', '*'],

            body: tableDirectors
          },
          //pageBreak: "after"
        });
    }
    let tablePremises = [];
    tablePremises.push(['Address line 1', 'Address line 2', 'Island', 'Country', 'Postalcode']);

    for (let premises of businessObj.premises) {
      tablePremises.push([premises.address_line1,
      premises.address_line2,
      premises.city || "",
      premises.country,
      premises.postalcode || ""
      ]);
    }

    docDefinition.content.push({ text: "Premises", fontSize: 12, margin: [0, 20, 0, 20] });

    docDefinition.content.push(
      {
        style: 'table', // optional
        table: {
          headerRows: 0,
          widths: ['*', '*', '*', '*', '*'],

          body: tablePremises
        },
        //pageBreak: "after"
      });
  } else {
    docDefinition.content.push({ text: businessTitle, fontSize: 15, margin: [0, 20, 0, 20] });
    docDefinition.content.push(
      {
        style: 'table', // optional
        table: {
          headerRows: 0,
          widths: [400, '*'],

          body: tableBody
        },
        //pageBreak: "after"
      });
  }

  if (businessTitle != 'Holding Business (Pure Equity Holding entities)') {
    let tableOutsourcing = [];
    docDefinition.content.push({ text: "Outsourcing", fontSize: 12, margin: [0, 20, 0, 20] });
    tableOutsourcing.push(['Has any core income generating activity (CIGA) been outsourced?', formatBoolean(businessObj.core_income_generating_outsourced)])
    if (businessObj.core_income_generating_outsourced) {
      tableOutsourcing.push(['Has the outsourced activity been undertaken in the BVI?', formatBoolean(businessObj.outsourced_activity_undertaken_in_BVI)])
      if (!businessObj.outsourced_activity_undertaken_in_BVI) {

        tableOutsourcing.push([{ text: 'Explanation', colSpan: 2 }, {}]);
        tableOutsourcing.push([{ text: businessObj.explanation_outsourced_activity_undertaken_in_BVI, colSpan: 2 }, {}]);

      }
      tableOutsourcing.push(['Is the legal entity able to demonstrate that it is monitoring the outsourced activity?', formatBoolean(businessObj.demonstrate_monitoring_outsourced_activity)])
      if (businessObj.demonstrate_monitoring_outsourced_activity) {
        tableOutsourcing.push([{ text: 'Explanation', colSpan: 2 }, {}]);
        tableOutsourcing.push([{
          text: businessObj.explanation_demonstrate_monitoring_outsourced_activity,
          colSpan: 2
        }, {}]);
      }
      if (businessObj.outsourcing_evidence != null && businessObj.outsourcing_evidence.length > 0) {
        tableOutsourcing.push(['Attachments submitted with Substance Filing', (businessObj.outsourcing_evidence && businessObj.outsourcing_evidence.length > 0 ? "Yes" : "No")]);

      }
    }


    docDefinition.content.push(
      {
        style: 'table',
        table: {
          widths: [400, '*'],
          body: tableOutsourcing
        }
      });
  }
}

function createTableBusinessV5(docDefinition, businessObj, entryCurrency, activityType, title) {
  let tableBody = [];
  tableBody.push(['Total Gross Income for the relevant activity during the financial period',
    entryCurrency + " " + businessObj.gross_income_total]);
  tableBody.push(['Type of gross income in relation to the relevant activity', businessObj.gross_income_type]);

  // COMMON FIELDS FOR ACTIVITIES != HOLDING 
  if (activityType !== SE_BUSINESS_TYPES.HOLDING) {
    tableBody.push(['Amount and type of assets and premises held in the course of carrying out the relevant activity',
      `${businessObj.activity_assets_amount} ${businessObj.activity_assets_type}`]);
    tableBody.push(['Net book values of tangible assets held in the course of carrying out the relevant activity',
      businessObj.activity_netbook_values]);

    //direction and management
    tableBody.push(['Is the relevant activity directed and managed in the Virgin Islands?', formatBoolean(businessObj.management_in_bvi)]);

    tableBody.push(['Number of board meetings the entity held during the financial period', businessObj.number_of_board_meetings]);
    tableBody.push(['Of those board meetings, how many were held in the Virgin Islands where a quorum of directors was physically present?',
      businessObj.number_of_board_meetings_in_bvi]);
    if (businessObj.number_of_board_meetings_in_bvi > 0) {
      tableBody.push(['Are the minutes for these board meetings being held in the Virgin Islands?', formatBoolean(businessObj.are_minutes_for_board_meetings)]);
    }


    tableBody.push(['Quorum of board meetings', businessObj.quorum_of_board_meetings]);
    tableBody.push(['Quorum of directors physically present in the Virgin Islands?', formatBoolean(businessObj.are_quorum_of_directors)]);
    tableBody.push(['Total expenditure incurred on the relevant activity during the financial period',
      entryCurrency + " " + businessObj.total_expenditure]);
    tableBody.push(['Total expenditure incurred in the BVI on the relevant activity during the financial period',
      entryCurrency + " " + businessObj.total_expenditure_bvi]);
    tableBody.push(['Total number of employees', businessObj.full_total_employees]);
    tableBody.push(['Total number of employees engaged in the relevant activity', businessObj.total_employees_engaged]);
    tableBody.push(['Total number of suitably qualified employees engaged in the relevant activity in the BVI', businessObj.total_employees]);
  }
  else {
    //  FIELDS FOR HOLDING ACTIVITY
    tableBody.push(['Total expenditure incurred on the relevant activity during the financial period',
      entryCurrency + " " + businessObj.total_expenditure]);
    tableBody.push(['Total expenditure incurred in the BVI on the relevant activity during the financial period',
      entryCurrency + " " + businessObj.total_expenditure_bvi]);
    tableBody.push(['Does the entity actively manage its equity participation?', formatBoolean(businessObj.manage_equity_participations)]);

    if (businessObj.manage_equity_participations === true) {
      tableBody.push(['Total number of employees', businessObj.full_total_employees]);
      tableBody.push(['Total number of employees engaged in the relevant activity', businessObj.total_employees_engaged]);
      tableBody.push(['Total number of suitably qualified employees engaged in the relevant activity in the BVI', businessObj.total_employees]);
    } else {
      tableBody.push(['Does the entity comply with its statutory obligations under the BVI Business Companies Act, 2004 or the Limited Partnership Act, 2017(whichever is relevant)?',
        formatBoolean(businessObj.compliant_with_statutory_obligations)]);

    }
  }

  // FIELDS FOR OTHER TYPES OF ACTIVITIES 
  if (activityType !== SE_BUSINESS_TYPES.HOLDING && activityType !== SE_BUSINESS_TYPES.INTELLECTUAL_PROPERTY) {
    const activityCore = CIGA_CODES.find((cigaCode) => cigaCode.code === businessObj.activity_ciga_core);

    tableBody.push(['Core Income Generating Activities conducted carried out in the Virgin Islands for the relevant activity',
      activityCore?.description || businessObj.activity_ciga_core]);
    if (businessObj.activity_ciga_core == '0.2') {
      tableBody.push(['Core Income Other',
        businessObj.activity_ciga_core_other]);
    }

    tableBody.push(['Has any core income generating activity(CIGA) been outsourced to another entity?',
      formatBoolean(businessObj.core_income_generating_outsourced)]);
    tableBody.push(['Total expenditure incurred on outsourcing in the Virgin Islands during the financial period?',
      entryCurrency + " " + businessObj.outsourcing_total_expenditure]);
  }

  // FIELDS FOR INTELLECTUAL PROPERTY
  if (activityType === SE_BUSINESS_TYPES.INTELLECTUAL_PROPERTY) {
    tableBody.push(['Is the entity high risk?', formatBoolean(businessObj.high_risk_ip)]);
    tableBody.push(['Does the entity wish to provide evidence to rebut the presumption as set out in ESA section 9(4)?', formatBoolean(businessObj.evidence_high_risk_ip)]);
    tableBody.push(['Attachments submitted with Substance Filing', (businessObj.high_risk_ip_evidence && businessObj.high_risk_ip_evidence.length > 0 ? "Yes" : "No")]);

    if (businessObj.high_risk_ip === true) {
      tableBody.push(['Gross income through Royalties, if applicable',
        entryCurrency + " " + businessObj.high_risk_gross_income_total]);
      tableBody.push(['Gross income through Gains from sale of IP Assets, if applicable',
        entryCurrency + " " + businessObj.high_risk_gross_income_assets]);
      tableBody.push(['Gross income through others',
        entryCurrency + " " + businessObj.high_risk_gross_income_others]);
      tableBody.push(['The relevant tangible asset which the corporate and legal entity holds', businessObj.tangible_assets_name]);
      tableBody.push(['Explanation of how that tangible asset is being used to generate income', businessObj.tangible_assets_explanation]);
      tableBody.push(['Identify the decisions for which each employee is responsible for in respect of the generation of income from the intangible asset',
        businessObj.intangible_assets_decisions]);
      tableBody.push(['The nature and history of strategic decisions (if any) taken by the entity in the Virgin Islands',
        businessObj.intangible_assets_nature]);
      tableBody.push(['The nature and history of the trading activities (if any carried out in the Virgin Islands by which) the intangible assets is exploited for the purpose of generating income from third parties',
        businessObj.intangible_assets_trading_nature]);
    } else {
      tableBody.push(['Does the legal entity conduct CIGA other than those outlined in section 7(h) of the ESA?',
        formatBoolean(businessObj.is_other_ciga_legal_entity)]);
      if (businessObj.is_other_ciga_legal_entity === true) {
        tableBody.push(['Does the entity wish to provide evidence to rebut the presumption as set out in ESA section 9(3)?',
          formatBoolean(businessObj.has_other_ciga_evidences)]);
        tableBody.push(['Identify the relevant IP asset which it holds', businessObj.other_ciga_ip_asset]);
        tableBody.push(['Provide the detail business plans which explain the commercial rationale of holding the intellectual property assets in the Virgin Islands',
          businessObj.other_ciga_business_details]);
        tableBody.push(['Identify the decisions for which each employee is responsible in respect of the generation of income from the intangible asset',
          businessObj.other_ciga_decisions]);
        tableBody.push(['Provide concrete evidence that decision making is taking place within the Virgin Islands, including but not limited to, minutes of meetings which have taken place in the Virgin Islands',
          businessObj.other_ciga_evidence_details]);
      } else {
        const activityCore = CIGA_CODES.find((cigaCode) => cigaCode.code === businessObj.activity_ciga_core);
        tableBody.push(['Core Income Generating Activities conducted carried out in the Virgin Islands for the relevant activity',
          activityCore?.description || businessObj.activity_ciga_core]);
        if (businessObj.activity_ciga_core == '0.2') {
          tableBody.push(['Core Income Other',
            businessObj.activity_ciga_core_other]);
        }
      }
    }

    tableBody.push(['Has any core income generating activity(CIGA) been outsourced to another entity?',
      formatBoolean(businessObj.core_income_generating_outsourced)]);
    tableBody.push(['Total expenditure incurred on outsourcing in the Virgin Islands during the financial period?',
      entryCurrency + " " + businessObj.outsourcing_total_expenditure]);

    tableBody.push(['Description of the nature of any equipment located within the Virgin Islands used in connection with the relevant activity',
      businessObj.equipment_nature_description]);

  }

  docDefinition.content.push({ text: title, fontSize: 15, margin: [0, 10, 0, 10] });
  docDefinition.content.push({
    style: 'table',
    table: {
      headerRows: 0,
      widths: [400, '*'],

      body: tableBody
    },
  });



  if (activityType !== SE_BUSINESS_TYPES.HOLDING) {
    // DIRECTORS TABLE
    let tableDirectors = [];
    tableDirectors.push([{ text: 'Name', style: 'tableHeader' },
    { text: 'Corporate director?', style: 'tableHeader' },
    { text: 'Resident in Virgin Islands', style: 'tableHeader' },
    { text: 'Relation to Entity', style: 'tableHeader' }]);

    if (businessObj.managers.length > 0) {
      for (let manager of businessObj.managers) {
        tableDirectors.push([
          manager.first_name + "  " + (manager.middle_name ? manager.middle_name + " " : "") + manager.last_name,
          formatBoolean(manager.is_corporate_director),
          formatBoolean(manager.resident_in_bvi),
          manager.position_held
        ]);
      }
    }
    docDefinition.content.push({ text: "Direction and management", fontSize: 12, margin: [0, 10, 0, 10] });

    docDefinition.content.push({
      style: 'table',
      table: {
        headerRows: 0,
        widths: ['*', '*', '*', '*'],
        body: tableDirectors
      },

    });

    // BOARD MEETINGS
    if (businessObj.number_of_board_meetings_in_bvi > 0 && businessObj.board_meetings?.length > 0) {
      let tableBoardMeetings = [];
      tableBoardMeetings.push([
        { text: 'Meeting #', style: 'tableHeader' },
        { text: 'Name', style: 'tableHeader' },
        { text: 'Physically Present', style: 'tableHeader' },
        { text: 'Relation to Entity', style: 'tableHeader' },
        { text: 'Qualification', style: 'tableHeader' }
      ]);

      for (let boardMeeting of businessObj.board_meetings) {
        tableBoardMeetings.push([
          boardMeeting.meeting_number,
          boardMeeting.name,
          formatBoolean(boardMeeting.physically_present),
          boardMeeting.relation_to_entity,
          boardMeeting.qualification
        ]);
      }

      docDefinition.content.push({ text: "Board Meetings", fontSize: 12, margin: [0, 10, 0, 10] });
      docDefinition.content.push({
        style: 'table',
        table: {
          headerRows: 0,
          widths: ['15%', '*', '15%', '*', '*'],

          body: tableBoardMeetings
        },
      });
    }
  }

  // EMPLOYEES TABLE
  if (businessObj.total_employees > 0 && businessObj.employees?.length > 0) {
    let tableEmployees = [];
    tableEmployees.push([
      { text: 'Name', style: 'tableHeader' },
      { text: 'Qualification', style: 'tableHeader' },
      { text: 'Years of relevant experience', style: 'tableHeader' }
    ]);

    for (let employee of businessObj.employees) {
      tableEmployees.push([
        employee.name,
        employee.qualification,
        employee.experience_years
      ]);
    }

    docDefinition.content.push({ text: "Employees", fontSize: 12, margin: [0, 10, 0, 10] });
    docDefinition.content.push({
      style: 'table',
      table: {
        headerRows: 0,
        widths: ['*', '*', '*'],

        body: tableEmployees
      },
    });
  }

  // PREMISES TABLE
  let tablePremises = [];
  tablePremises.push([
    { text: 'Address line 1', style: 'tableHeader' },
    { text: 'Address line 2', style: 'tableHeader' },
    { text: 'Country', style: 'tableHeader' }
  ]);

  if (businessObj.premises?.length > 0) {
    for (let premises of businessObj.premises) {
      const country = countries.find((c) => c.alpha_3_code === premises.country);
      tablePremises.push([premises.address_line1,
      premises.address_line2,
      (country ? country.name : premises.country)
      ]);
    }
  }

  docDefinition.content.push({ text: "Premises", fontSize: 12, margin: [0, 10, 0, 10] });
  docDefinition.content.push({
    style: 'table',
    table: {
      headerRows: 0,
      widths: ['*', '*', '*',],

      body: tablePremises
    },
  });

  if (businessObj.core_income_generating_outsourced === true) {
    let tableOutsourcing = [];
    tableOutsourcing.push([
      { text: 'Entity Name', style: 'tableHeader' },
      { text: 'Details of Resources', style: 'tableHeader' },
      { text: 'Number of staff', style: 'tableHeader' },
      { text: 'Hours per month', style: 'tableHeader' },
      { text: 'Monitoring and Control', style: 'tableHeader' },
    ]);

    if (businessObj.outsourcing_providers?.length > 0) {
      for (let provider of businessObj.outsourcing_providers) {
        tableOutsourcing.push([
          provider.entity_name,
          provider.resource_details,
          provider.staff_count,
          provider.hours_per_month,
          formatBoolean(provider.monitoring_control)
        ]);
      }
    }

    docDefinition.content.push({ text: "Outsourcing", fontSize: 12, margin: [0, 10, 0, 10] });
    docDefinition.content.push({
      style: 'table',
      table: {
        headerRows: 0,
        widths: ['*', '30%', '15%', '15%', '15%'],

        body: tableOutsourcing
      },
    });
  }

}


function createTableConfirmation(docDefinition, entry) {
  if (entry.confirmation) {
    let tableBody = [];
    tableBody.push([{text: 'Confirmation', colSpan: 2, style: 'tableHeader'},{}]);
    tableBody.push( [ 'I confirm that the information provided above is true and accurate to the best of my knowledge and belief and that by submission of this information to the Registered Agent, I have provided all the information required to complete the economic substance self-assessment.', formatBoolean(entry.confirmation.confirmed)])
    tableBody.push( [ 'Please confirm you have the authority to act on behalf of the company.', formatBoolean(entry.confirmation.confirmed_authority)])
    tableBody.push( [ 'I confirm and acknowledge that the Registered Agent has a legitimate interest for processing any personal data provided above; specifically, in order to ensure the Registered Agent’s and the Entity’s compliance with relevant BVI law. I further acknowledge that the processing of such personal data may include its transfer to BVI competent authorities and that the Registered Agent’s processing of any personal data will be done in accordance with the https://tridenttrust.com/legal-pages/data-protection/ (Trident Trust Data Privacy Policy), which I have read and understood', formatBoolean(entry.confirmation.confirmed_conditions)])
    if (entry.company_data.amount && entry.company_data.amount > 0) {
      tableBody.push( [ 'I confirm and acknowledge that the submission fee in the amount of US$'+ entry.company_data.amount +' is due and payable in order to complete the submission process', formatBoolean(entry.confirmation.confirmed_payment)])
    }
    tableBody.push( [ 'Relation to entity', entry.confirmation.relation_to_entity]);
    tableBody.push( [ 'Relation to entity (other)', (entry.confirmation.relation_to_entity_other && entry.confirmation.relation_to_entity_other.length > 0 ? entry.confirmation.relation_to_entity_other : "N/A")]);
    tableBody.push( [ 'Name of the person stating the declaration', entry.confirmation.user_fullname]);
    tableBody.push( [ 'Phone number', entry.confirmation.user_phonenumber]);

    if (!entry.version || (entry.version && parseFloat(entry.version) < 3)){
      tableBody.push(['Ultimate parent entity name', (entry.confirmation.ultimate_parent_entity_name && entry.confirmation.ultimate_parent_entity_name.length > 0 ? entry.confirmation.ultimate_parent_entity_name : "N/A")]);
      tableBody.push(['Ultimate parent entity address', (entry.confirmation.ultimate_parent_entity_address && entry.confirmation.ultimate_parent_entity_address.length > 0 ? entry.confirmation.ultimate_parent_entity_address : "N/A")]);
      tableBody.push(['Entity jurisdiction', (entry.confirmation.entity_jurisdiction && entry.confirmation.entity_jurisdiction.length > 0 ? entry.confirmation.entity_jurisdiction : "N/A")]);
      tableBody.push(['Incorporation number', (entry.confirmation.incorporation_number && entry.confirmation.incorporation_number.length > 0 ? entry.confirmation.incorporation_number : "N/A")]);
    }


    docDefinition.content.push(
    {
      style: 'table',
      table: {
        widths: [ 300, '*'],
        body: tableBody
      }
    });
  }
}

function createParentEntityTable(docDefinition, parents, title) {
  let tableParents = [];
  tableParents.push(['Name', 'Alternative Name', 'Jurisdiction of Formation', 'Incorporation Number', 'TIN or other number']);

  for (let parent of parents) {
    const jurisdiction = countries.find((c) => c.alpha_3_code === parent.jurisdiction)
    tableParents.push([
      parent.parentName,
      parent.alternativeName,
      jurisdiction.name || "",
      parent.incorporationNumber,
      parent.TIN
    ]);
  }

  docDefinition.content.push({ text: title, fontSize: 12, margin: [0, 20, 0, 20] });

  docDefinition.content.push({
    style: 'table',
    table: {
      headerRows: 0,
      widths: ['20%', '20%', '20%', '20%', '20%'],

      body: tableParents
    },
  });
}
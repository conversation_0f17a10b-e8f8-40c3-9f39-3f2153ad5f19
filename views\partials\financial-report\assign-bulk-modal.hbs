<div class="modal hide fade confirmation-modal-md" tabindex="-1" role="dialog" 
  id="assignBulkModal" style="display: none;" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="assignBulkModalTitle">Confirmation</h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-12">
            <p>
              This will assign/change the officer assigned of the financial reports. Are you sure? 
            </p>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <div class="form-group">
              <a id='downloadAssignBulkLink' href="#" style="display: none;" download></a>
              <label class="mt-1" for="newOfficerBulk">New Officer:</label>
              <input type="email" name="newOfficerBulk" id="newOfficerBulk" class="form-control" required />
            </div>
          </div>
        </div>

        <div class="row mt-2 mb-0">
          <div class="col-12">
            <p class="m-0">Total reports: <b id="totalReports">0</b></p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
          Close
        </button>
        
        <button id="submitAssignBulkButton" type="button" class="btn btn-primary waves-effect waves-light">
          Submit
        </button>
        
        <button id="submitAssignBulkSpinner" style="display: none;" class="btn btn-primary waves-effect waves-light" type="button"
          disabled>
          <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
          Submit...
        </button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div>
<script type="text/javascript">

  const $submitAssignBulkSpinner = $("#submitAssignBulkSpinner");
  let assignBulkReports = [];

  $('#assignBulkModal').on('shown.bs.modal', function (event) {
    let button = $(event.relatedTarget); 
    const data = $('#financialReportsTable').DataTable().rows('.selected').data();
    for (let idx = 0; idx < data.length; idx++) {
      assignBulkReports.push(data[idx][0])
    }
    $("#totalReports").text(assignBulkReports.length);

  });


  $('#assignBulkModal').on('hide.bs.modal', function () {
    $("#newOfficerBulk").val('');
    assignBulkReports = [];
  });

  $("#submitAssignBulkButton").on('click', function (event) {
    $(this).prop('disabled', true);
    $(this).hide();
    $submitAssignBulkSpinner.show();
    event.stopPropagation();

    const newOfficer = $("#newOfficerBulk").val();

    if (!newOfficer || newOfficer === ''  || !validateEmailFormat(newOfficer)) {
      toastr["warning"]("Please enter a valid email");
      $submitAssignBulkSpinner.hide();
      $(this).prop('disabled', false).show();
      return false;
    }

    $.ajax({
      type: "POST",
      url: `/financial-report-management/bulk-assign`,
      contentType: "application/json; charset=utf-8",
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      data: JSON.stringify({
        newOfficer: newOfficer,
        reportIds: assignBulkReports
      }),
      success: function (response) {
        if (response.status === 200) {
          if(response.data?.xlsxData){
            const url = 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,' + response.data?.xlsxData;
            $('#downloadAssignBulkLink').attr('href', url);
            $('#downloadAssignBulkLink').attr('download', response.data?.filename);
            document.getElementById('downloadAssignBulkLink').click();
          }
          Swal.fire('Success', response.message, 'success').then(() => {
            location.reload();
          });
        } else if (response.status === 400) {
          Swal.fire('Error', response.error, 'error');
          $("#submitAssignBulkButton").prop('disabled', false).show();
          $submitAssignBulkSpinner.hide();

        }
      },
      error: (err) => {
        if (err.responseJSON?.error) {
          toastr["error"](err.responseJSON?.error);
        }
        else {
          toastr["error"]('Sorry, There was an error with the bulk assign');
        }
         $submitAssignBulkSpinner.hide();
        $("#submitAssignBulkButton").prop('disabled', false).show();
      },

    });
  });
</script>
<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">  
                    <form method="POST" id="submitForm">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3 qa-questions">
                                    <label for="QuestionQenA">Question</label>
                                    {{#each questions}}
                                        <input type="hidden" name="originalQuestion" value="{{this}}" />
                                        <textarea class="form-control" id="QuestionQenA" name="QuestionQenA" rows="5">{{this}}</textarea>
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="cbDeleteQuestion{{@index}}" name="cbDeleteQuestion" value="{{@index}}">
                                            <label class="custom-control-label" for="cbDeleteQuestion{{@index}}">Mark this question for deletion</label>
                                        </div>
                                        <br/><br/>
                                    {{/each}}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <button type="button" class="btn btn-blue width-lg waves-effect waves-light" id="AddQuestion" name="AddQuestion" onclick="addQuestion()">Add Question</button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label class="mb-2" for="AnswerQenA">Answer</label>
                                    <textarea class="form-control" id="AnswerQenA" name="AnswerQenA" rows="5">{{answer}}</textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label class="mb-2" for="AnswerQenA">If you want to sent an confirmation to the person that submitted this question, please fill in the e-mail adress below.</label>
                                    <input type="text" id="recipient" name="recipient" class="form-control">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-8"></div>
                            <div class="col-md-2">
                                <div class="form-group mb-3">
                                    <a href='../list' class="btn btn-danger width-lg waves-effect waves-light">Cancel</a>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group mb-3">                                    
                                    <button class="btn btn-success width-lg" type="button" id="submitButton"><span class="spinner-grow spinner-grow-sm mr-1" style="display:none" role="status" aria-hidden="true"></span>Save</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript">
$(document).ready(function() {
    $("#submitButton").click(function(){        

        $(".spinner-grow").show();
        $("#submitButton").attr("disabled", true);
        $("#submitForm").submit(); // Submit the form
    });
});

function addQuestion() {
    $(".qa-questions").append('<textarea class="form-control" id="QuestionQenA" name="QuestionQenA" rows="5"></textarea><br/><br/>')
}
</script>
<main class="">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card">
                    <form method="POST" id="submitForm">
                        <div class="card-body">
                            <h1>{{title}}</h1>
                            <div id="import-table">

                            </div>
                            <div class="row" id="uploadRow">
                                <div class="col-md-12">
                                    <p>
                                        Maximum of 1 file, XLSX only. File must not
                                        be password
                                        protected.
                                    </p>
                                    <p>
                                        Dowload Template <a href="/AccRecordsImportTemplate.xlsx" target="_blank" class="text-danger underline">(Template)</a>
                                        <span class="fa-stack tooltip-wrapper ml-1" style="margin-top: -10px" data-toggle="tooltip" data-container="body"
                                                data-placement="top" data-html="true" title="
                                                In the provided template, you will find an illustrative example showcasing the accepted formulas and formats.</small>">
                                                <i class="fa text-primary fa-info-circle fa-stack fa-lg"></i>
                                        </span>
                                        
                                    </p>
                                    <br>
                                    <div id="uploadFile" class="dropzone">
                                        <div class="fallback">
                                            <input name="fileUploaded" type="file" multiple />
                                        </div>
                                        <div class="dz-message needsclick">
                                            <i class="h1 text-muted dripicons-cloud-upload"></i>
                                            <h3>Drop files here or click to upload.</h3>
                                            <span class="text-muted">Files will be automatically uploaded</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="">
                                <div class="row mb-3 mx-2 justify-content-between">
                                    <a href='/financial-report-management/import-files'
                                        class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>


<script type="text/javascript">
    $('[data-toggle="tooltip"]').tooltip({
        trigger: 'hover',
        container: 'body'

    });
    let importedData;
    Dropzone.autoDiscover = false;
    $(function () {
        let field = '';
        var myDropZone = new Dropzone('#uploadFile', {
            url: './load-file',
            acceptedFiles: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            uploadMultiple: false,
            autoProcessQueue: true,
            parallelUploads: 1,
            maxFiles: 1,
            maxFilesize: 25,
            paramName: function () {
                return 'fileUploaded';
            },
            init: function () {
                this.on('processing', function () {
                    this.options.url = './load-file';
                });
                this.on("success", function (file, response) {
                    if (response.status === 200) {
                        loadFileSuccess(response.message);

                    } else {
                        toastr["warning"](response.error);
                        if (this.files.length != 0) {
                            for (i = 0; i < this.files.length; i++) {
                                this.files[i].previewElement.remove();
                            }
                            this.files.length = 0;
                        }
                    }
                })
                this.on("sending", function (file, xhr, formData) {
                    $("#btnSubmit").prop('disabled', true);
                    if (!formData.has('filetype')) {
                        formData.append("filetype", field);
                    }
                })

                this.on('maxfilesexceeded', function (file) { });

                this.on('resetFiles', function () {
                    if (this.files.length != 0) {
                        for (i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                        this.files.length = 0;
                    }
                    $('#maxUpload').text(this.options.maxFiles);
                });
            },
        });
    });


    function loadFileSuccess(message){
        Swal.fire('Success', message, 'success').then(() => {
            location.href = '/financial-report-management/import-files';
        });
    }
</script>

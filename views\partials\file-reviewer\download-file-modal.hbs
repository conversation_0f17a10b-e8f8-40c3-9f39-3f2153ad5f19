<div
        id="downloadFileModal"
        class="modal fade "
        tabindex="-1"
        role="dialog"
        aria-labelledby="downloadFileModal"
        style="display: none; z-index: 2000; border: #1a1e24 2px solid;"
        aria-hidden="true"
>
    <div class="modal-dialog modal-md contour container-fluid">
        <div class="modal-content border rounded border-secondary" style="">
            <div class="modal-header">
                <h4 class="modal-title" id="download-modal-title">
                    Download file <span id="download-modal-file-label" class="font-weight-bold"></span>
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">

                            <div id="downloadFiles" class=" m-2 text-left text-muted">
                                <p style="text-align: center">Searching files...</p>
                            </div>
                        </div>
                        <!-- end card-body-->
                        <div class="modal-footer justify-content-end pb-0">
                            <button type="button" class="btn solid royal-blue" data-dismiss="modal">
                                <i class="mdi mdi-send mr-1"></i>Close
                            </button>
                        </div>
                    </div>
                    <!-- end card-->
                </div>
                <!-- end col -->
            </div>
            <!-- end row -->
        </div>
    </div>
</div>
<script type="text/javascript">
    let reviewIdBtn;
    let fileRowBtn;
    let fileGroup;
    $('#downloadFileModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        reviewIdBtn = button.data('review-id');
        const relationId = button.data('relation-id');
        fileRowBtn =  button.data('file-id');
        fileGroup =  button.data("file-group");
        const id = fileGroup === "standard" ? reviewIdBtn : relationId;
        $.ajax({
            type: 'GET',
            url: '/file-reviewer/company-file-review/uploaded-files',
            data: {reviewId: id,  fileId: fileRowBtn, type: fileGroup},
            timeout: 5000,
            success: (data) => {
                if (data.success === false){
                    toastr["warning"](data.message);
                }
                else{
                    let strFileLinks = "";
                    if (data.files.length > 0) {
                        for (let idx = 0; idx < data.files.length; idx++) {
                            if (fileGroup === "standard"){
                                strFileLinks += "<a style='font-size: x-large' href='/file-reviewer/reviews/" + reviewIdBtn +
                                        "/download/" + fileGroup + "/" + fileRowBtn+ "/" + data.files[idx].fileId + "' target='_blank'>" +  '<i class="fa fa-arrow-circle-down" aria-hidden="true"></i>  '+
                                        data.files[idx].originalName + "</a><br> <hr>"
                            }
                            else{
                                const url = "/file-reviewer/reviews/" + reviewIdBtn + "/relations/" + relationId + "/download/"+ fileGroup  +"/" + fileRowBtn + "/" + data.files[idx].fileId;
                                strFileLinks += "<a style='font-size: x-large' href='"+ url + "' target='_blank'>" +  '<i class="fa fa-arrow-circle-down" aria-hidden="true"></i>  '+
                                        data.files[idx].originalName + "</a><br> <hr>"
                            }

                        }
                        if (strFileLinks){
                            $("#downloadFiles").html(strFileLinks);
                        }
                    }
                    else{
                        $("#downloadFiles").html("<p style='text-align: center'>Files not found</p>");
                    }

                }
            },
            error: (err) => {
                console.log(err);
                Swal.fire('Error', 'There was an error downloading the file', 'error').then(()=>{
                    $('#downloadFileModal').modal('hide');
                });

            },
        });
    });

    $('#downloadFileModal').on('hide.bs.modal', function (event) {
        $("#downloadFiles").html("<p style='text-align: center'>Searching files...</p>");
        $('.modal').css('overflow-y', 'auto');
    });

</script>

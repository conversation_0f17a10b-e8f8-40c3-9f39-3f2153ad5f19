const CompanyModel = require("../models/company").schema;
const EntryModel = require("../models/entry").EntryModel;
const { filereview: FileReviewModel } = require('../models/filereview');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const xlsx = require('xlsx');
const fs = require('fs');
const moment = require('moment');

dotenv.config();


async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const db = mongoose.connection;
    db.on('error', console.error.bind(console, 'MongoDB connection error:'));
    // Start process - Read file (change file name as needed)
    const data = new Uint8Array(fs.readFileSync('scripts/UpdateCompanies.xlsx'));
    const workbook = xlsx.read(data, {
      type: "array",
      cellText: false,
      cellDates: true,
    });

    const result = await updateCompanyNames(workbook);

    console.log('Script result: ', result);
  } catch (error) {
    console.error('Error script:', error);
  } finally {
    mongoose.disconnect();
  }
}




function getCompaniesRows(workbook) {
  try {
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const companies = [];

    for (let cell in worksheet) {
      const cellAsString = cell.toString();
      const rowNumber = Number(cellAsString.replace(/\D/g, ''));
      const dataStartRow = 2;
      const companyIndex = rowNumber - dataStartRow;
      const rowName = cellAsString.replace(/[0-9]/g, '');

      if (companyIndex >= 0 && worksheet[cell].v) {
        if (!companies[companyIndex]) {
          companies.push({
            code: '',
            newName: '',
          })
        }
        if (rowName === "B") {
          companies[companyIndex].newName = worksheet[cell].v;
        }
        if (rowName === "C") {
          companies[companyIndex].code = worksheet[cell].v;
        }
      }
    }
    return companies;
  } catch (e) {
    console.log("Error processing xlsx data: ", e);
    return []
  }

}



async function updateCompanyNames(workbook) {
  try {

    let updateCompanyLog = [['ID', 'Company Code', 'Update date', 'Action']];

    // Get company + bo info
    const companies = getCompaniesRows(workbook);
    console.log("companies length ", companies.length);


    for (let i = 0; i < companies.length; i++) {
      console.log('processing ' + i + '  from ' + companies.length)

      const company = companies[i];
      const dbCompany = await CompanyModel.findOne({code: company.code});

      // create companies

      if(!dbCompany){
        updateCompanyLog.push([company.id, company.code, new Date(), 'COMPANY NOT FOUND'])
        continue;
      }

      // skip update if has the same name in db
      if (dbCompany.name === company.newName) {
        updateCompanyLog.push([dbCompany._id.toString(), dbCompany.code, new Date(), 'SKIPPED'])
        continue;
      }

      // update name in company
      dbCompany.name = company.newName;
      await dbCompany.save();

      // update name in submissions
      await EntryModel.updateMany({
        "company_data.code": dbCompany.code,
        "company_data.masterclientcode": dbCompany.masterclientcode
      }, 
      { "$set": {"company_data.name": dbCompany.name}},
      { timestamps: false})

      // update name in submissions
      await FileReviewModel.updateMany({
        "companyCode": dbCompany.code,
        "masterClientCode": dbCompany.masterclientcode
      },
        { "$set": { "companyName": dbCompany.name } },
        { timestamps: false })


      updateCompanyLog.push([dbCompany._id.toString(), dbCompany.code, new Date(), 'UPDATED'])
    }
    // create entities bo
    console.log("companies updated ", updateCompanyLog.length -1);

    const filename = 'update_company_name_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateCompanyLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'companies ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalRows": updateCompanyLog.length -1 };
  } catch (e) {
    console.log(e);
    return { "success": false };
  }
}

runScript()



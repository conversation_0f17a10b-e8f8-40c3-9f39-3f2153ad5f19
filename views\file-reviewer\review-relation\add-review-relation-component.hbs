<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            <span class="font-weight-bold">{{title }}</span>
                        </h3>
                    </div>
                    <div class="card-body">
                        <form id="reviewRelationForm" method="POST" autocomplete="off">
                            <div class="form-group">
                                <label for="relationType">Select Type of Relation</label>
                                <select data-toggle="tooltip" data-placement="top"
                                        title="A Corporate, Foundation, Trust or Limited Partnership can only be the beneficial owner if it is listed or regulated. Otherwise it must be a Natural Person"
                                        class="custom-select" id="relationType" name="relationType">
                                    <option id="empty-option" value="" disabled {{#ifEquals group ""}}
                                            selected {{/ifEquals}}>Select Type
                                    </option>
                                    <option id="natural-option" value="natural" {{#ifEquals group "natural"}}
                                            selected {{/ifEquals}}>Natural
                                    </option>
                                    <option id="corporate-option" value="corporate" {{#ifEquals group "corporate"}}
                                            selected {{/ifEquals}}>Corporate
                                    </option>
                                    <option id="foundation-option" value="foundation" {{#ifEquals group "foundation"}}
                                            selected {{/ifEquals}}>Foundation
                                    </option>
                                    <option id="trust-option" value="trust" {{#ifEquals group "trust"}}
                                            selected {{/ifEquals}}>Trust
                                    </option>
                                    <option id="limited-option" value="limited" {{#ifEquals group "limited"}}
                                            selected {{/ifEquals}}>Limited Partnership
                                    </option>
                                </select>
                            </div>

                            <div>
                                {{#if group}}
                                    <h4 id="relationTitle">
                                        Relation Details
                                    </h4>
                                {{/if}}
                                <!-- TITLE -->

                                {{#ifEquals group "natural"}}
                                    {{>file-reviewer/review-relations/natural-form-component showPep="true"
                                            relation=relation relationInformation=relationInformation}}
                                {{/ifEquals}}

                                {{#ifEquals group "corporate"}}
                                    {{>file-reviewer/review-relations/corporate-form-component relation=relation
                                            relationInformation=relationInformation}}
                                {{/ifEquals}}

                                {{#ifEquals group "foundation"}}
                                    {{>file-reviewer/review-relations/foundation-form-component relation=relation
                                            relationInformation=relationInformation}}
                                {{/ifEquals}}

                                {{#ifEquals group "trust"}}
                                    {{>file-reviewer/review-relations/trust-form-component relation=relation
                                            relationInformation=relationInformation}}
                                {{/ifEquals}}

                                {{#ifEquals group "limited"}}
                                    {{>file-reviewer/review-relations/limited-partnership-form-component relation=relation
                                            relationInformation=relationInformation}}
                                {{/ifEquals}}

                                {{#ifContains 'shareholder' reviewGroup}}
                                    {{#if group}}
                                        <hr class="mt-2"/>
                                        <div id="shareholderAdditionalForm">
                                            <!-- ADITIONAL REQUIRED INFO FOR SHAREHOLDERS  -->
                                            <h4>Additional Shareholder Details</h4>
                                            <div class="row mt-4">
                                                <div class="col-2">
                                                    <label for="additional-percentage">Share Percentage</label>
                                                </div>
                                                <div class="col-4 input-group mb-3">
                                                    <input
                                                            name="additional[percentage]"
                                                            id="additional-percentage"
                                                            type="number"
                                                            max="100"
                                                            min="0"
                                                            class="form-control"
                                                            value="{{relation.additional.percentage}}"
                                                    />
                                                    <div class="input-group-append">
                                                        <span class="input-group-text">%</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="table-responsive pl-2" id="shareholderInfo" hidden>
                                                <table class="table table-striped">
                                                    <thead>
                                                    <tr>
                                                        <th style="width: 34%;">Files Required</th>
                                                        <th style="width: 13%;" class="text-center">Present</th>
                                                        <th style="width: 20%;" class="text-center">Action</th>
                                                        <th style="width: 33%;">Explanation</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr>
                                                        <td>Certified legible copy of passport/government issued ID</td>
                                                        <td class="text-center">
                                                            <div class="custom-control custom-checkbox">
                                                                <input
                                                                        type="checkbox"
                                                                        class="custom-control-input"
                                                                        name="additional-20-id-present-file"
                                                                        id="additional-20-id-present-file"
                                                                />
                                                                <label
                                                                        class="custom-control-label"
                                                                        for="additional-20-id-present-file"
                                                                ></label>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <button
                                                                    type="button"
                                                                    class="btn solid royal-blue"
                                                                    data-toggle="modal"
                                                                    data-target="#upload-temp-modal"
                                                                    data-id="{{ id }}"
                                                                    data-field="Certified legible copy of passport/government issued ID"
                                                                    data-row="20"
                                                            >
                                                                Upload
                                                            </button>
                                                        </td>
                                                        <td>
                                                      <textarea
                                                              class="form-control"
                                                              name="additional-20-id-explanation-file"
                                                              id="additional-20-id-explanation-file"
                                                              rows="1"
                                                      ></textarea>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>Certified legible copy of Proof of Address</td>
                                                        <td class="text-center">
                                                            <div class="custom-control custom-checkbox">
                                                                <input
                                                                        type="checkbox"
                                                                        class="custom-control-input"
                                                                        name="additional-21-proofaddress-present-file"
                                                                        id="additional-21-proofaddress-present-file"
                                                                />
                                                                <label
                                                                        class="custom-control-label"
                                                                        for="additional-21-proofaddress-present-file"
                                                                ></label>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <button
                                                                    type="button"
                                                                    class="btn solid royal-blue"
                                                                    data-toggle="modal"
                                                                    data-target="#upload-temp-modal"
                                                                    data-id="{{ id }}"
                                                                    data-field="Certified legible copy of Proof of Address"
                                                                    data-row="21"
                                                            >
                                                                Upload
                                                            </button>
                                                        </td>
                                                        <td>
                                                      <textarea
                                                              class="form-control"
                                                              name="additional-21-proofaddress-explanation-file"
                                                              id="additional-21-proofaddress-explanation-file"
                                                              rows="1"
                                                      ></textarea>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <!-- SHAREHOLDER TABLE END -->
                                            <div class="row pt-3">
                                                <div class="col-12 d-flex justify-content-end">
                                                    <div class="custom-control custom-checkbox">
                                                        <input
                                                                type="checkbox"
                                                                class="custom-control-input completeCheck"
                                                                name="additional[correct]"
                                                                id="correctAdditional"
                                                            {{#if relationInformation.additional.correct}}
                                                                checked {{/if}}
                                                        />
                                                        <label class="custom-control-label" for="correctAdditional"
                                                        >Complete Information</label
                                                        >
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <hr class="mt-2"/>
                                    {{/if}}

                                {{/ifContains}}

                            </div>

                        </form>
                    </div>
                    <div class="row mt-4 justify-content-between">
                        <div class="col-2">
                            <a
                                    href="/file-reviewer/open-file-review/{{ id }}/beneficial-owners"
                                    class="btn btn-secondary width-lg waves-effect waves-light"
                            >
                                Back
                            </a>
                        </div>
                        <div class="col-4 d-flex justify-content-end">
                            {{#if group}}
                                <div class="custom-control custom-checkbox pr-2 pt-2">
                                    <input  type="checkbox" name="allCompleteCheck"
                                            class="custom-control-input completeCheck"
                                            id="allCompleteCheck"
                                            form="reviewRelationForm"
                                    />
                                    <label class="custom-control-label" for="allCompleteCheck">Mark all as complete</label>
                                </div>
                            {{/if}}


                            <button
                                    id="submitRelationBtn"
                                    type="submit"
                                    form="reviewRelationForm"
                                    class="btn solid royal-blue px-4"
                                    data-id="{{ id }}"
                                    data-group="{{reviewGroup}}"
                            >
                                Submit
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

{{>file-reviewer/upload-temp-modal}}

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/showrelationtypeform.precompiled.js"></script>
<script type="text/javascript" src="/templates/addpartnerfilerow.precompiled.js"></script>
<script>
    let existingSelected = "";
    let typeSelected = $('#relationType option:selected').val();
    let relationGroup = '{{reviewGroup}}';


    $('#relationType').on('change', function () {
        typeSelected = $('#relationType option:selected').val();
        existingSelected = '';

        $('.relation-form-type').hide();
        if (typeSelected) {
            $('#worldCheckSection').show();
            $('#relationTitle').show();
        } else {
            $('#worldCheckSection').hide();
        }

        $('#empty-relation-option').prop('selected', true);
        window.location.href = window.location.origin + window.location.pathname + "?group=" + typeSelected;

    });


    $('#reviewRelationForm').submit(function (event) {
        const submitBtn = $("#submitRelationBtn");
        submitBtn.prop('disabled', true);
        event.preventDefault();

        const myForm = $("#reviewRelationForm")[0];
        const reviewId = $('button[type=submit]').data('id');
        // const relationGroup = $('button[type=submit]').data('group');

        if (typeSelected) {
            if (!myForm.checkValidity()) {
                if (myForm.reportValidity) {
                    myForm.reportValidity();
                    return;
                }
            }
            let formData = typeSelected === 'natural' ? $('input[name="details[fullName]"]') :
                    $('input[name="details[organizationName]"]');
            if (!formData.val()) {
                formData.addClass('is-invalid');
                formData.focus();
                setTimeout(function () {
                    submitBtn.prop('disabled', false);
                }, 0);
            } else {
              console.log(window.location.origin + window.location.pathname);
                $.ajax({
                    url: window.location.origin + window.location.pathname,
                    type: 'POST',
                    data: $(this).serialize(),
                    timeout: 3000,
                    success: function () {
                        location.href = '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
                    },
                    error: function () {
                        Swal.fire('Error', 'There was an error creating the beneficial owner', 'error').then(
                                () => {
                                    location.href =
                                            '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
                                }
                        );
                    },
                });
            }
        } else {
            setTimeout(function () {
                submitBtn.prop('disabled', false);
            }, 0);
            $('#existing').addClass('is-invalid');
            $('#beneficialType').addClass('is-invalid');
        }


    });

    $('#allCompleteCheck').on('change', function (){
        const checked = $(this).is(':checked');
        if(checked){
            $('.completeCheck').prop('checked', true);
        }
        else {
            $('.completeCheck').prop('checked', false);
        }
    });

</script>
<script>
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body',
        boundary: 'window'
    });
    $('.toggle-section-check').change(function () {
        const name = "content-" + $(this).attr('id');
        $('#' + name).toggle(200);
    });

    $('#world-check-confirmation').change(function () {
        $('#worldCheckBody').toggle(200);
    });

    $('.addCertificatePartnerRow').on('click', function () {
        const tableId = $(this).data("table-id");
        const reviewId = $(this).data("review-id");
        const relationId = $(this).data("relation-id");
        const group = $(this).data("group");
        const fileRow = $(this).data("row");
        const rowCount = $('#' + tableId + ' tr').length - 1;
        $.ajax({
            url: '/file-reviewer/get-template-files',
            type: 'GET',
            timeout: 1500,
            data: {
                fieldToSearch: 'relationFiles',
                group: group,
                fileType: 'detailsPartner',
                row: fileRow,
                newFile: true
            },
            success: function (response) {
                if (response.success) {
                    let template = Handlebars.templates.addpartnerfilerow;
                    let d = {
                        file: response.data,
                        row: rowCount,
                        group: group,
                        reviewId: reviewId,
                        relationId: relationId
                    };
                    let html = template(d);
                    $("#" + tableId + " > table > tbody").append(html);
                } else {
                    Swal.fire('Error', 'There was an error adding a new row file', 'error');
                }
            },
            error: function () {
                Swal.fire('Error', 'There was an error adding a new row file', 'error');
            },
        });
    });
</script>

const appInsights = require("applicationinsights");
appInsights.setup(process.env.APPLICATIONINSIGHTS_CONNECTION_STRING);
appInsights.start();

const createError = require('http-errors');
const express = require('express');
const path = require('path');
const cookieParser = require('cookie-parser');
const expressSession = require('express-session');
const fileupload = require("express-fileupload");
const redis = require("redis");
const redisStore = require('connect-redis')(expressSession);
const logger = require('morgan');
const indexRouter = require('./routes/index');
const fileReviewRouter = require('./routes/fileReviewer');
const substanceRouter = require('./routes/substance');
const clientManagementRouter = require('./routes/clientManagement');
const monitorRouter = require("./routes/monitor");
const fetch = require("node-fetch");
const clientRouter = require("./routes/clientIncorporation");
const directorAndBoRouter = require("./routes/directorAndBo");
const directorAndMembersRouter = require("./routes/directorAndMembers");
const financialReportRouter = require("./routes/financialReportManagement");
const config = require('./config');
const hbs = require('express-handlebars');
const handlebars = require('handlebars');
const { allowInsecurePrototypeAccess } = require('@handlebars/allow-prototype-access');
const bodyParser = require('body-parser');
const moment = require('moment');
const mongoose = require('mongoose');
const mongoDB = process.env.MONGODB;
const sqlDb = require('./models-sql');
const momentTimezone = require('moment-timezone');


require('express-async-errors');



mongoose.connect(mongoDB, { useNewUrlParser: true });
mongoose.set('useFindAndModify', false);
const db = mongoose.connection;
db.on('error', console.error.bind(console, 'MongoDB connection error:'));

//Connect to MySQL db
momentTimezone.tz.setDefault('UTC');
sqlDb.sequelize.authenticate().then(()=> {
  console.log("SQL connection has been established successfully.")
}).catch(err => {
  console.log("Unable to connect to the SQL database: ", err);
});


const app = express();

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'hbs');
app.engine(
    'hbs',
    hbs.engine({
        extname: 'hbs',
        handlebars: allowInsecurePrototypeAccess(handlebars),
        defaultView: 'default',
        layoutsDir: __dirname + '/views/layouts/',
        partialsDir: __dirname + '/views/partials/',
        helpers: {
            formatDate: function (date, format) {
                if (date) {
                    return moment(date).utc().format(format)
                } else {
                    return '';
                }
            },
            ifEquals: function (str1, str2, options) {
                return str1 === str2 ? options.fn(this) : options.inverse(this);
            },
            ifNotIn: function (element, list) {
                return list.indexOf(element) === -1;
            },
            ifContains: function (element, list, options) {
                return list && list.indexOf(element) > -1 ? options.fn(this) : options.inverse(this);
            },
            ifCond: function (v1, operator, v2, options) {
                switch (operator) {
                    case '==':
                        return (v1 == v2) ? options.fn(this) : options.inverse(this);
                    case '===':
                        return (v1 === v2) ? options.fn(this) : options.inverse(this);
                    case '!=':
                        return (v1 != v2) ? options.fn(this) : options.inverse(this);
                    case '!==':
                        return (v1 !== v2) ? options.fn(this) : options.inverse(this);
                    case '<':
                        return (v1 < v2) ? options.fn(this) : options.inverse(this);
                    case '<=':
                        return (v1 <= v2) ? options.fn(this) : options.inverse(this);
                    case '>':
                        return (v1 > v2) ? options.fn(this) : options.inverse(this);
                    case '>=':
                        return (v1 >= v2) ? options.fn(this) : options.inverse(this);
                    case '&&':
                        return (v1 && v2) ? options.fn(this) : options.inverse(this);
                    case '||':
                        return (v1 || v2) ? options.fn(this) : options.inverse(this);
                    default:
                        return options.inverse(this);
                }
            },
            concat: function () {
                let concatValue = "";
                for (let i = 0; i < arguments.length - 1; ++i) {
                    //Do your thing with each array element.
                    if (typeof (arguments[i]) === "string" || typeof (arguments[i]) === "number") {
                        concatValue = concatValue + arguments[i].toString();
                    }
                }
                //Return your results...
                return concatValue;
            },
            ternary: function (condition, v1, v2) {
                if (condition) {
                    return v1;
                } else {
                    return v2;
                }
            },
            subtract: function(n1, n2){
                const value = Number(n1) - Number(n2);
                return value;
            },
            add: function (n1, n2) {
                const value = n1 + n2;
                return value;
            }
        },
    })
);


const redisClient = redis.createClient(6380, process.env.REDIS_HOST,
    { auth_pass: process.env.REDIS_PASS, tls: { servername: process.env.REDIS_HOST } });

const cookieSettings = {
    maxAge: 7200000,  //120 mins
    secure:  process.env.SECURE_COOKIE === 'true',
    httpOnly: true
};

// Use filerUpload
app.use('/substance/import-entries/load-file', fileupload());
app.use('/substance/import-payments/load-file', fileupload());
app.use('/client-management/import/load-file', fileupload());
app.use('/client-management/delete-companies/load-file', fileupload());
app.use('/director-and-bo/import-directors/load-file', fileupload());
app.use('/financial-report-management/import-files/load-file', fileupload())

app.use(logger('dev'));
app.use(bodyParser.json({ limit: "50mb" }));
app.use(bodyParser.urlencoded({ limit: "50mb", extended: true, parameterLimit: 50000 }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));

app.use(expressSession({
    secret: process.env.REDIS_SECRET,
    resave: true,
    saveUninitialized: false,
    store: new redisStore({ client: redisClient }),
    cookie: cookieSettings
}));

// Initialize Passport!  Also use passport.session() middleware, to support
// persistent login sessions (recommended)
app.use(function (req, res, next) {
    res.setHeader('Cache-control', ['no-store', 'no-cache', 'must-revalidate']);
    res.setHeader('Pragma', 'no-cache');
    next();
});



app.use('/', indexRouter);
app.use('/substance', substanceRouter);
app.use('/client-management', clientManagementRouter);
app.use('/file-reviewer', fileReviewRouter);
app.use('/monitor', monitorRouter);
app.use('/client-incorporation', clientRouter);
app.use('/director-and-bo', directorAndBoRouter);
app.use('/director-and-members', directorAndMembersRouter);
app.use('/financial-report-management', financialReportRouter);
app.set('trust proxy', 1);

app.get('/login', async function (req, res) {
    const token = req.headers["x-ms-token-aad-access-token"];

    if (process.env.NODE_ENV === "dev") {
        req.session.user = {
            access_token: '',
            username: "<EMAIL>",
            name: "Test User 01",
        };

        req.session.authentication = {
            isCompliance: true,
            isQualityAssurance: true,
            isFileObserver: true,
            isFileReviewer: true,
            isIncorporationOfficer: true,
            isIncorporationSuperAdmin: true,
            isAnnouncementManager: true,
            isSubsManagers: false,
            isSubsSuperUser: true,
            isDirectorManager: true,
            isDirBoImportManager: true,
            isAccountingAccountant: false,
            isAccountingManager: true,
            isAccountingStandard: true,
            isAccountingSuperUser: true,
            isAccountingProductOwner: true,
            isClientManagementSuperUser: true,
        };
        req.session.fileReviewRisks = {
            isLowRisk: true,
            isMediumRisk: true,
            isHighRisk: true
        };
        req.session.productionOfficeGroups = ["TBVI",  "THKO", "TYCP", "TPANVG"]

        req.session.is_authenticated = Object.values(req.session.authentication).some((groups) => groups === true);

        res.redirect('/');
    } else {
        if (!token) {
            return res.redirect('/not-authorized');
        }

        const responseAad = await getSecurityGroups(token);
        if (!responseAad && !req.params.afterRefresh) {
            return res.redirect('/refresh-token');
        }

        const securityGroups = responseAad.value;
        const userResponse = await getUser(token);


        if (!securityGroups || !securityGroups.length || !userResponse) {
            return res.redirect('/not-authorized');
        } else {
            req.session.user = {
                username: userResponse.mail,
                name: userResponse.displayName,
            };
        }

        if (securityGroups) {
            const productionOfficeGroups = [];
            securityGroups.findIndex((group) => group === config.securityGroupTBVIProductionOffice) > -1 ? productionOfficeGroups.push('TBVI') : null;
            securityGroups.findIndex((group) => group === config.securityGroupTHKOProductionOffice) > -1 ? productionOfficeGroups.push('THKO') : null;
            securityGroups.findIndex((group) => group === config.securityGroupTCYPProductionOffice) > -1 ? productionOfficeGroups.push('TCYP') : null;
            securityGroups.findIndex((group) => group === config.securityGroupTPANVGProductionOffice) > -1 ? productionOfficeGroups.push('TPANVG') : null;

            req.session.authentication = {
                isCompliance: securityGroups.findIndex((group) => group === config.securityGroupFilereviewerCompliance)> -1,
                isQualityAssurance: securityGroups.findIndex((group) => group === config.securityGroupFilereviewerQualityAssurance) > -1,
                isFileObserver: securityGroups.findIndex((group) => group === config.securityGroupFilereviewerFileObserver) > -1,
                isFileReviewer:  (securityGroups.findIndex((group) => group === config.securityGroupFilereviewerLowRisk) > -1) ||
                  (securityGroups.findIndex((group) => group === config.securityGroupFilereviewerMediumRisk) > -1) ||
                  (securityGroups.findIndex((group) => group === config.securityGroupFilereviewerHighRisk) > -1),
                isIncorporationOfficer: securityGroups.findIndex((group) => group === config.securityGroupClientIncorporationOfficer) > -1,
                isIncorporationSuperAdmin: securityGroups.findIndex((group) => group === config.securityGroupClientIncorporationSuperAdmin) > -1,
                isAnnouncementManager: securityGroups.findIndex((group) => group === config.securityGroupAnnouncementManagers) > -1,
                isSubsManagers: securityGroups.findIndex((group) => group === config.securityGroupSubsManagers) > -1,
                isSubsSuperUser: securityGroups.findIndex((group) => group === config.securityGroupSubsSuperUser) > -1,
                isDirectorManager: securityGroups.findIndex((group) => group === config.securityGroupClientDirectorManager) > -1,
                isDirBoImportManager: securityGroups.findIndex((group) => group === config.securityGroupDirBoImportManager) > -1,
                isAccountingAccountant: securityGroups.findIndex((group) => group === config.securityGroupFinancialReportAccountant) > -1,
                isAccountingManager: securityGroups.findIndex((group) => group === config.securityGroupFinancialReportManager) > -1,
                isAccountingStandard: securityGroups.findIndex((group) => group === config.securityGroupFinancialReportStandard) > -1,
                isAccountingSuperUser: securityGroups.findIndex((group) => group === config.securityGroupFinancialReportSuperUser) > -1,
                isAccountingProductOwner: securityGroups.findIndex((group) => group === config.securityGroupFinancialReportProductOwner) > -1,
                isClientManagementSuperUser: securityGroups.findIndex((group) => group === config.securityGroupClientManagementSuperUser) > -1                
            };
            req.session.fileReviewRisks = {
                isLowRisk: securityGroups.findIndex((group) => group === config.securityGroupFilereviewerLowRisk) > -1,
                isMediumRisk: securityGroups.findIndex((group) => group === config.securityGroupFilereviewerMediumRisk) > -1,
                isHighRisk: securityGroups.findIndex((group) => group === config.securityGroupFilereviewerHighRisk) > -1
            };
            req.session.productionOfficeGroups = productionOfficeGroups

            req.session.is_authenticated = Object.values(req.session.authentication).some((groups) => groups === true);

            if ( req.session.is_authenticated === false){
                res.redirect('/not-authorized');
            }
            else{
                res.redirect('/');
            }
        } else {
            res.redirect('/not-authorized');
        }
    }
});

app.get('/logout', function (req, res) {

    req.session.destroy(function (err) {
        if(err){
            console.log("logout error ", err);
        }
        const cookie = req.cookies;
        for (var prop in cookie) {
            res.clearCookie(prop);
        }
        res.redirect(process.env.AZURE_APP_URL + "/.auth/logout");
    });
});

app.get('/session-expired', function (req, res) {
    console.log('session expired');
    res.render('session-expired', { url: process.env.AZURE_AD_ADMIN_CONSENT_URL });
});

app.get('/refresh-token', async function (req, res) {
    const sessionToken = req.cookies.AppServiceAuthSession;
    if(await refreshToken(sessionToken) == false) {
        res.redirect('/session-expired');
    } else {
        res.redirect('/login?afterRefresh=true');
    }
});


// 'logout' route, logout from passport, and destroy the session with AAD.
app.get('/not-authorized', function (req, res) {
    console.log('not authorized');
    res.render('not-authorized', { message: 'You are not authorized' });
});

// catch 404 and forward to error handler
app.use(function (req, res, next) {
    next(createError(404));
});

// error handler
// eslint-disable-next-line no-unused-vars
app.use(function (err, req, res) {
    // set locals, only providing error in development
    if (process.env.DEVELOPMENT_ERRORS === 'true') {
        res.locals.message = err.message;
        res.locals.error = req.app.get('env') === 'development' ? err : {};
    } else {
        res.locals.message = 'An error ocurred!';
        res.locals.error = {};
    }


    // render the error page
    res.status(err.status || 500);
    res.render('error');
});

async function getSecurityGroups(token) {
    console.log('get security groups for ' + token)
    try {

        let todo = {
            securityEnabledOnly: true
        };

        const options = {
            'method': 'POST',
            'body': JSON.stringify(todo),
            'headers': {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            }
        }

        const response = await fetch(process.env.AZURE_GRAPH_API_URL + '/me/getMemberObjects', options);
        if (!response.status || response.status !== 200) {
            return null;
        }
        return await response.json();
    } catch (e) {
        console.log(e);
    }
}

async function getUser(token) {
    console.log('get user for ' + token)
    try {

        const options = {
            'method': 'GET',
            'headers': {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            }
        };
        const response = await fetch(process.env.AZURE_GRAPH_API_URL + '/me', options);

        if (!response.status || response.status !== 200) {
            return null;
        }
        return await response.json();
    } catch (e) {
        console.log(e);
    }
}

async function refreshToken(token) {
    console.log('refresh ' + token)
    try {

        const options = {
            'method': 'GET',
            'headers': {
                'Content-Type': 'application/json',
                'Cookie': 'AppServiceAuthSession=' + token
            }
        };
        const response = await fetch(process.env.AZURE_APP_URL + "/.auth/refresh", options);
        console.log(response)
        if (!response.status || response.status !== 200) {
            return false;
        }
        return true;
    } catch (e) {
        console.log(e);
    }
}


app.disable('x-powered-by');
module.exports = app;

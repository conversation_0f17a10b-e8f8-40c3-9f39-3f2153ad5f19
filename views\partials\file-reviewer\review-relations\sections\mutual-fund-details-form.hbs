<div id="{{group}}-mutualFundForm">
    <div class="row">
        <div class="col-5">
            <h4>Regulated Details</h4>
        </div>
        <div class="col-7 d-flex">
            <div class="custom-control custom-switch my-auto">
                <input type="checkbox" class="custom-control-input toggle-section-check"
                       id="mutual-fund-confirmation-{{group}}"
                       name="mutualFundDetails[active]"
                    {{#if mutualFund.active}} checked {{/if}}
                >
                <label class="custom-control-label" for="mutual-fund-confirmation-{{group}}"></label>
            </div>
        </div>
    </div>
    <div id="content-mutual-fund-confirmation-{{group}}" {{#unless mutualFund.active }} style="display: none" {{/unless}}>
        {{>file-reviewer/shared/relation-file-table tableId="mutualFundTable"  name="mutualFundDetails"
                files=(ternary newRelation mutualFund mutualFund.files) relationId=relation._id}}
        <div class="row pt-3">
            <div class="col-12 d-flex justify-content-end">
                <div class="custom-control custom-checkbox">
                    <input
                            type="checkbox"
                            class="custom-control-input completeCheck"
                            name="mutualFundDetails[correct]"
                            id="{{group}}-correct-mutualFund"
                            {{#if relationInformation.mutualFundDetails.complete}} checked {{/if}}
                    />
                    <label class="custom-control-label" for="{{group}}-correct-mutualFund"
                    >Complete Information</label
                    >
                </div>
            </div>
        </div>
    </div>
</div>



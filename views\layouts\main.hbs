<!doctype html>
<html lang="en-us">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <link rel="icon" href="/images/favicon.ico" type="image/x-icon">
    <title>{{title}}</title>
    <!-- inject-head:css -->

    <link rel="stylesheet" href="/stylesheets/bootstrap.min.css">
    <link rel="stylesheet" href="/stylesheets/icons.min.css">

    <link href="/javascripts/libs/jquery-nice-select/nice-select.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/switchery/switchery.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/multiselect/multi-select.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/select2/select2.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/bootstrap-select/bootstrap-select.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/bootstrap-touchspin/jquery.bootstrap-touchspin.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/flatpickr/flatpickr.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/bootstrap-colorpicker/bootstrap-colorpicker.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/clockpicker/bootstrap-clockpicker.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/bootstrap-datepicker/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/dropzone/dropzone.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/dropify/dropify.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/sweetalert2/sweetalert2.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/datatables/dataTables.bootstrap4.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/datatables/responsive.bootstrap4.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/datatables/select.bootstrap4.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/toastr/toastr.min.css" rel="stylesheet" type="text/css" />

    <link rel="stylesheet" href="/stylesheets/app.min.css">
    <link rel="stylesheet" href="/stylesheets/style.css">

    <script src="/javascripts/vendor.js"></script>
    <script src="/javascripts/libs/jquery-nice-select/jquery.nice-select.min.js"></script>
    <script src="/javascripts/libs/switchery/switchery.min.js"></script>
    <script src="/javascripts/libs/multiselect/jquery.multi-select.js"></script>
    <script src="/javascripts/libs/select2/select2.min.js"></script>
    <script src="/javascripts/libs/jquery-mockjax/jquery.mockjax.min.js"></script>
    <script src="/javascripts/libs/autocomplete/jquery.autocomplete.min.js"></script>
    <script src="/javascripts/libs/bootstrap-select/bootstrap-select.min.js"></script>
    <script src="/javascripts/libs/bootstrap-touchspin/jquery.bootstrap-touchspin.min.js"></script>
    <script src="/javascripts/libs/bootstrap-maxlength/bootstrap-maxlength.min.js"></script>
    <script src="/javascripts/libs/flatpickr/flatpickr.min.js"></script>
    <script src="/javascripts/libs/bootstrap-colorpicker/bootstrap-colorpicker.min.js"></script>
    <script src="/javascripts/libs/clockpicker/bootstrap-clockpicker.min.js"></script>
    <script src="/javascripts/libs/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
    <script src="/javascripts/libs/dropzone/dropzone.min.js"></script>
    <script src="/javascripts/libs/toastr/toastr.min.js"></script>
    <script src="/javascripts/libs/moment/moment.min.js"></script>
    <script src="/javascripts/jquery.serializejson.js"></script>

    <script src="/javascripts/libs/sweetalert2/sweetalert2.min.js"></script>

    <script src="/javascripts/libs/datatables/jquery.dataTables.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.bootstrap4.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.responsive.min.js"></script>
    <script src="/javascripts/libs/datatables/responsive.bootstrap4.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.buttons.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.bootstrap4.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.html5.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.flash.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.print.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.keyTable.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.select.min.js"></script>
    <script src="/javascripts/libs/handlebars/handlebars.runtime.min-v4.5.3.js"></script>
    <script>
        Handlebars.registerHelper('ifEquals', function (arg1, arg2, options) {
            return arg1 === arg2 ? options.fn(this) : options.inverse(this);
        });

        Handlebars.registerHelper('ifCond', function (v1, operator, v2, options) {
            switch (operator) {
                case '==':
                    return (v1 == v2) ? options.fn(this) : options.inverse(this);
                case '===':
                    return (v1 === v2) ? options.fn(this) : options.inverse(this);
                case '!=':
                    return (v1 != v2) ? options.fn(this) : options.inverse(this);
                case '!==':
                    return (v1 !== v2) ? options.fn(this) : options.inverse(this);
                case '<':
                    return (v1 < v2) ? options.fn(this) : options.inverse(this);
                case '<=':
                    return (v1 <= v2) ? options.fn(this) : options.inverse(this);
                case '>':
                    return (v1 > v2) ? options.fn(this) : options.inverse(this);
                case '>=':
                    return (v1 >= v2) ? options.fn(this) : options.inverse(this);
                case '&&':
                    return (v1 && v2) ? options.fn(this) : options.inverse(this);
                case '||':
                    return (v1 || v2) ? options.fn(this) : options.inverse(this);
                default:
                    return options.inverse(this);
            }
        });

        Handlebars.registerHelper('formatDate', function (date, format) {
            if (date) {
                return moment(date).utc().format(format)
            } else {
                return '';
            }
        });
        Handlebars.registerHelper('json', function(context) {
            return JSON.stringify(context);
        });

        Handlebars.registerHelper('subtract', function (n1, n2) {
            const value = Number(n1) - Number(n2);
            return value;
        });

        Handlebars.registerHelper('add', function (n1, n2) {
            const value = n1 + n2;
            return value;
        });
    </script>
    <!-- endinject -->
</head>
<body>
    <header class="main-header">
        <div class="container">
            <div class="main-logo">
                <a href="/"><figure>
                    <img src="/images/trident-logo-new.svg" alt="Trident Trust">
                </figure></a>
                <!-- /.brand -->
                <a href="" class="menu-btn"><i class="icon-menu"></i></a>
            </div>
            <!-- /.main-logo -->

            <div class="navigation">
                <nav class="sub-nav">
                    <ul>
                        <li>{{user.name}}</li>
                    </ul>
                    <ul>
                        {{#if user}}<li><a href="/logout" class="btn btn-danger  width-xl">Logout</a></li>{{/if}}
                    </ul>
                </nav>
                <!-- /.sub-nav-items -->



            </div>
            <!-- /.navigation -->

        </div>
    <!-- /.container -->
    </header>
        {{{body}}}
  </body>
</html>

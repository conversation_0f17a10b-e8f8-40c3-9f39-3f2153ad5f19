{{!-- CONFIRM CLIENT MODAL --}}
<div class="modal fade" id="showAnnouncementDetails" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Show Details</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <form action="#" id="showDetailsForm">
                    <div class="row">
                        <div class="col-md-12">
                            <label for="subjectField">Subject</label>
                            <input class="form-control" id="subjectField" type="text" disabled>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-md-12">
                            <label for="contentField">Content</label>
                            <textarea  class="form-control" id="contentField" type="text" rows="5" disabled></textarea>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <label for="scheduleDate">Scheduled/Send date</label>
                            <input class='form-control' type='text' name='scheduleDate' id='scheduleDate'
                                   value=""  disabled />
                        </div>

                        <div class="col-6">
                            <label>Send message to all Master Client Codes?</label> <br>
                            <div class="custom-control custom-radio custom-control-inline pt-1">
                                <input type="radio" class="custom-control-input" id="send-to-all-yes"
                                       name="sendToAll" disabled value="YES"/>
                                <label class="custom-control-label" for="send-to-all-yes">Yes</label>
                            </div>
                            <div class="custom-control custom-radio custom-control-inline pt-1">
                                <input type="radio" class="custom-control-input" id="send-to-all-no"
                                       name="sendToAll" disabled value="NO"/>
                                <label class="custom-control-label" for="send-to-all-no">No</label>
                            </div>
                        </div>

                    </div>

                    <div class="row mt-2">
                        <div class="col-md-12">
                            <label for="mccField">Master Client Codes</label>
                            <textarea  class="form-control" id="mccField" type="text" rows="2" disabled></textarea>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-md-12">
                            <label for="mccField">Files</label>
                            <ul id="messageFilesBox">

                            </ul>
                        </div>
                    </div>

                </form>


            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    $('#showAnnouncementDetails').on('hide.bs.modal', function (event) {
        $("#showDetailsForm").trigger("reset");
        $("#messageFilesBox").html('');
    });



</script>

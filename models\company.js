const mongoose = require('mongoose');
const ObjectId = require('mongoose').Types.ObjectId;
const DocumentChangesModel = require("./documentchangelog");
const { compareSchema } = require('../utils/schemaUtils');
const { UPDATE_CHUNK_SIZE, SKIP_COMPANY_HOOKS } = require('../utils/constants');



const CompanyNameChangesSchema = new mongoose.Schema(
  {
    email: { type: String, required: true },
    originalName: { type: String, required: true },
    newName: { type: String, required: true },
    modulesChanged: [{ type: String, required: false }],
    changedAt: { type: Date, required: false },
  }
);


const CompanyCodeChangesSchema = new mongoose.Schema(
  {
    oldCode: { type: String, required: true },
    newCode: { type: String, required: true },
    changedAt: { type: Date, required: false },
  }
);

const substanceModuleSchema = new mongoose.Schema({
  active: { type: Boolean, required: true },
  hasITADate: { type: Boolean, required: false },
  approvedITAStartDate: { type: Date, required: false },
  approvedITAEndDate: { type: Date, required: false },
  applicationITADate: { type: Date, required: false },
  approval: {
    approved: { type: Boolean, required: false },
    date: { type: Date, required: false },
    by: { type: String, required: false },
  },

})

const reportedSchema = new mongoose.Schema({
  reportedBy: { type: String, required: false },
  isReported: { type: Boolean, required: false },
  reportedAt: { type: Date, required: false },
})

const deadlineRemindersSchema = new mongoose.Schema({
  reminderDay: { type: Number, required: false },
  description: { type: String, required: false },
  reminderDate: { type: Date, required: false },
  reminderDeadline: { type: Date, required: false },
  messageId: { type: ObjectId, required: false },
})

const accountingRecordsModuleSchema = new mongoose.Schema({
  active: { type: Boolean, required: false },
  selfServiceCompleteAnnualReturnAmount: { type: Number, required: false },
  selfServicePrepareAnnualReturnAmount: { type: Number, required: false },
  tridentServiceCompleteAnnualReturnAmount: { type: Number, required: false },
  tridentServiceDropAccountingRecordsAmount: { type: Number, required: false },
  firstFinancialPeriodStart: { type: Date, required: false },
  firstFinancialPeriodEnd: { type: Date, required: false },
  reportedHistory: [reportedSchema],
  currentDeadline: { type: Date, required: false }, //used to set company IN PENALTY status
  currentFilingDeadline: { type: Date, required: false }, //used to send reminders on 90/60/30/-1 days interval
  compliantStatus: { type: String, required: false },
  isReported: { type: Boolean, required: false },
  deadlineReminders: [deadlineRemindersSchema],
  inPenalty: { type: Boolean, required: false },
  currentPenaltyReport: { type: ObjectId, required: false },
  currentPenaltyAmount: { type: Number, required: false },
})

const dirboModuleSchema = new mongoose.Schema({
  active: { type: Boolean, required: true }
})

const CompanySchema = new mongoose.Schema({
  name: { type: String, required: true, max: 100 },
  address: { type: String, required: false, max: 100 },
  code: { type: String, required: true, max: 100 },
  incorporationcode: { type: String, required: true, max: 100 },
  incorporationdate: { type: Date, required: false },
  masterclientcode: { type: String, required: true, max: 100 },
  riskgroup: { type: String, required: false, max: 100 },
  filereviews: [{ type: mongoose.Schema.Types.ObjectId, ref: 'filereview' }],
  company_type: { type: String, required: false, max: 100 },
  referral_office: { type: String, required: true, max: 100 },
  amount: { type: Number, required: false },
  partitionkey: { type: String, required: true, default: "company" },
  isDeleted: { type: Boolean, required: false, default: false },
  deletedAt: { type: Date, required: false },
  companyNameChanges: [CompanyNameChangesSchema],
  companyCodeChanges: [CompanyCodeChangesSchema],
  hasITADate: { type: Boolean, required: false, default: false },
  approvedITAStartDate: { type: Date, required: false },
  approvedITAEndDate: { type: Date, required: false },
  applicationITADate: { type: Date, required: false },
  paymentYears: [String],
  createdAt: { type: Date, required: false},
  createdBy: { type: String, required: false },
  updatedAt: { type: Date, required: false },
  modifiedBy: { type: String, required: false },
  vpMasterFileCode: { type: String, required: false },
  entityStatus: { type: String, required: false },
  entitySubStatus: { type: String, required: false },
  entityStatusLabel: { type: String, required: false },
  productionOffice: { type: String, required: false },
  mccCpMasterFileCode: { type: String, required: false },
  substanceModule: substanceModuleSchema,
  accountingRecordsModule: accountingRecordsModuleSchema,
  dirboModule: dirboModuleSchema
}, { timestamps: true });


// HOOKS

CompanySchema.virtual('originalDocument').get(function () {
  return this.originalDocument;
}).set(function (value) {
  this.originalDocument = value;
});

// Hook to save old/new values in DocumentChanges collection
CompanySchema.pre('save', async function (next) {
  if(SKIP_COMPANY_HOOKS === true){
    return next()
  }
  try {
    // Verify if there is changes
    const original = await this.constructor.findById(this._id);
    let modifiedBy = "";

    if (this.isModified()) {
      // get modified by
      modifiedBy = this.get("modifiedBy");

      // get modified fields
      const changes = compareSchema(CompanySchema, '', original, this);
       
      // Save modified values in documentchanges collection
      if(changes.length>0)
        await DocumentChangesModel.create({
          documentId: this._id,
          modifiedValues: changes,
          modifiedBy: modifiedBy,
          collectionName: 'companies'
        });
    }

  } catch (error) {
    console.error('Error creating company change log on PRE save:', error);
  } finally {
    next();
  }
});

// PRE Hook to updateOne method 
CompanySchema.pre('updateOne', async function (next) {
  if(SKIP_COMPANY_HOOKS === true){
    return next()
  }

  try {
    // Get original document
    const original = await this.model.findOne(this.getFilter()).lean();
    if (original) {
      this.originalDocument = original;
    } else {
      this.originalDocument = {}
    }
  } catch (error) {
    console.error('Error getting original values on PRE updateOne:', error);
  } finally {
    next();
  }

});

// POST Hook to updateOne method 
CompanySchema.post('updateOne', async function () {
  if(SKIP_COMPANY_HOOKS === true){
    return
  }

  try {
    const updateOperation = this.getUpdate();
    const updatedValues = updateOperation.$set;
    const fieldsToUpdate = Object.keys(updatedValues);

    let modifiedBy = "";

    if (fieldsToUpdate?.length > 1) {
      // get modified by
      modifiedBy = updatedValues["modifiedBy"];

      const changes = compareSchema(CompanySchema, '', this.originalDocument, this);

      // Save modified values in documentchanges collection
      if(changes.length>0)
        await DocumentChangesModel.create({
          documentId: this.originalDocument._id,
          modifiedValues: changes,
          modifiedBy: modifiedBy || "",
          collectionName: 'companies'
        });

    }

  } catch (error) {
    console.error('Error creating financial report change logs on POST updateOne:', error);
  }
});


// PRE Hook to findOneAndUpdate method, this also works for findByIdAndUpdate
CompanySchema.pre('findOneAndUpdate', async function (next) {
  if(SKIP_COMPANY_HOOKS === true){
    return next()
  }

  try {
    const original = await this.model.findOne(this.getQuery()).lean()

    if (original) {
      this.originalDocument = original;

    } else {
      this.originalDocument = {}
    }

  } catch (error) {
    console.error('Error getting original financial report values on PRE findOneAndUpdate:', error);
  } finally {
    next()
  }
});

// POST Hook to findOneAndUpdate method
CompanySchema.post('findOneAndUpdate', async function () {
  if(SKIP_COMPANY_HOOKS === true){
    return;
  }

  try {
    const updateOperation = this.getUpdate();
    const updatedValues = { ...updateOperation.$set };
    const originalDocument = { ...this.originalDocument }
    const fieldsToUpdate = Object.keys(updatedValues);

    let modifiedBy = "";

    if (fieldsToUpdate?.length > 1) {
      // get modified by
      modifiedBy = updatedValues["modifiedBy"];

      const changes = compareSchema(CompanySchema, '', originalDocument, await this.model.findOne(this.getQuery()).lean());
      
      // Save modified values in documentchanges collection
      if(changes.length>0)
        await DocumentChangesModel.create({
          documentId: this.originalDocument._id,
          modifiedValues: changes,
          modifiedBy: modifiedBy || "",
          collectionName: 'companies'
        });

    }
  } catch (error) {
    console.error('Error creating financial report change logs on POST findOneAndUpdate:', error);
  }
});

// PRE Hook to updateMany method
CompanySchema.pre('updateMany', async function (next) {
  if(SKIP_COMPANY_HOOKS === true){
    return next();
  }
  try {
    const filter = this.getFilter();
    this._previousDocuments = await this.model.find(filter).lean();
  } catch (error) {
    console.error('Error getting original company values on PRE updateMany:', error);
  } finally {
    next();
  }
});

// POST Hook to updateMany method
CompanySchema.post('updateMany', async function () {
  if(SKIP_COMPANY_HOOKS === true){
    return;
  }

  try {
    const filter = this.getFilter();
    const updateOperation = this.getUpdate();
    const updatedValues = updateOperation.$set;
    const fieldsToUpdate = Object.keys(updatedValues);

    const previousDocuments = this._previousDocuments || [];
    const updatedDocuments = await this.model.find(filter);

    if (updatedDocuments?.length > 0 && fieldsToUpdate?.length > 1) {
      const updatedDocumentLogs = [];

      for (let i = 0; i < updatedDocuments.length; i++) {
        const doc = updatedDocuments[i];
        const originalDoc = (previousDocuments.find((pd) => pd._id?.toString() === doc._id?.toString()) || {});
        const changes = compareSchema(CompanySchema, '', originalDoc, doc);

        let modifiedValues = {};
        let modifiedBy = doc['modifiedBy'];


        if (modifiedValues) {
          updatedDocumentLogs.push({
            documentId: doc._id,
            modifiedValues: changes,
            modifiedBy: modifiedBy || "",
            collectionName: 'companies'
          });
        }
      }

      if (updatedDocumentLogs.length > 0) {
        for (let i = 0; i < updatedDocumentLogs.length; i += UPDATE_CHUNK_SIZE) {
          const updateLogsChunk = updatedDocumentLogs.slice(i, i + UPDATE_CHUNK_SIZE);

          await DocumentChangesModel.insertMany(updateLogsChunk);
        }
      }

    }

  } catch (error) {
    console.error('Error creating financial report change logs on POST updateMany:', error);
  }
});



//Export model
module.exports.schema = mongoose.model('Company', CompanySchema);
module.exports.model = CompanySchema;

<div id="reopenedInformation" class="mb-2">
    <div class="row">
        <div class="col-12">
            <h4><b>Information on Reopened submissions</b></h4>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            {{#each reportData.reopenedInfo}}
            <div class="text-left"> 
                {{#if resubmittedAt }}
                <span>
                    {{#formatDate resubmittedAt "MM/DD/YYYY"}} {{/formatDate }} | {{resubmittedBy}} | <b>RESUBMITTED</b>
                </span>
                <br>
                {{/if}}
                <span>
                    {{#formatDate reopenedAt "MM/DD/YYYY"}} {{/formatDate }} | {{reopenedBy}} | <b>Reason:</b> {{reason}}
                    {{#ifEquals change_financial_period_dates true}}
                    <br>
                    <b>Financial period changed:</b> OLD period from {{#formatDate oldStartDate "MM/DD/YYYY"}} {{/formatDate }} to {{#formatDate oldEndDate "MM/DD/YYYY"}} {{/formatDate }}, NEW period from {{#formatDate newStartDate "MM/DD/YYYY"}} {{/formatDate }} to {{#formatDate newEndDate "MM/DD/YYYY"}} {{/formatDate }}
                    {{/ifEquals}}
                </span>
                <br>
            </div>

            {{/each}}
        </div>
    </div>
</div>
<hr>
<br>
<div id="financialPeriodChangesInformation" class="mb-2">
    <div class="row">
        <div class="col-12">
            <h4><b>Financial period changes</b></h4>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            {{#each reportData.financialPeriodChangesInfo}}
                <span>
                    {{formatDate dateChanged "MM/DD/YYYY"}} | {{changedBy}}
                    <br>
                    <b>Financial period changed:</b> OLD period from {{formatDate oldStartDate "MM/DD/YYYY"}} to {{formatDate oldEndDate "MM/DD/YYYY"}},
                    NEW period from {{formatDate newStartDate "MM/DD/YYYY"}} to {{formatDate newEndDate "MM/DD/YYYY"}}
                </span>
                <br>
            {{/each}}
        </div>
    </div>
</div>
<hr>
<br>
<div id="accordion">
    <div class="row mb-2">
        <div class="col-12">
            <h4><b>Requests for Information</b></h4>
        </div>
    </div>

    {{#if reportData.requestedInfo}}
        <div class="table-responsive">
            <table id="table-request"   class="table w-100">
                <thead>
                <tr>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                {{#each reportData.requestedInfo}}
                    <tr>
                        <td class="m-0 p-0">
                            <div class="card mb-0">
                                <div id="request-{{id}}">
                                    <button class="btn btn-outline-secondary border-white text-left font-16 w-100 " data-toggle="collapse" data-target="#response-{{id}}" aria-expanded="false" aria-controls="response-{{id}}">
                                        {{#each reminders}}
                                            {{#formatDate reminderDate "MM/DD/YYYY"}} {{/formatDate }} | {{description}} | <b>REMINDER SENT</b> <br>
                                        {{/each}}
                                        <i class="fa fa-angle-down" aria-hidden="true"></i>
                                        {{#formatDate requestedAt "MM/DD/YYYY"}} {{/formatDate }} | {{username}} | <b>{{status}}</b>
                                        <br> <b>Reason:</b> {{comment}}

                                    </button>
                                </div>

                                <div id="response-{{id}}" class="collapse" aria-labelledby="request-{{id}}" data-parent="#accordion">
                                    {{#each client_response}}
                                        <div class="card-body">
                                    <span >
                                        {{#formatDate returnedAt "MM/DD/YYYY"}} {{/formatDate }} | {{username}} <br>
                                        <b>Response</b>: {{comment}}
                                    </span>
                                            <hr>
                                            <span >
                                        <b>FILES:</b>
                                    </span>
                                            <br>
                                            {{#each files}}
                                                <a href="/financial-report-management/{{../../../reportData.reportId}}/download/{{../requestId}}/{{_id}}"
                                                   target="_blank" >
                                                    <span class="small"> &#8226; {{originalName}} </span>
                                                </a>
                                                <br>
                                            {{else}}

                                                <span class="small">
                                                &#8226; No files uploaded
                                            </span>


                                            {{/each}}
                                        </div>

                                    {{else}}
                                        <div class="card-body">
                                    <span >
                                        No response from the client yet
                                    </span>
                                        </div>
                                    {{/each}}
                                </div>
                            </div>
                        </td>
                    </tr>

                {{/each}}
                </tbody>
            </table>
        </div>
    {{/if}}



</div>
<hr>
<br>
<div id="officerDetails">
    <div class="row mb-2">
        <div class="col-12">
            <h4><b>Officer Details</b></h4>
            <br>
            {{#if reportData.officerDetails.details}}
            <h3 class="ml-1"><b>Current Officer: </b> {{reportData.officerDetails.currentOfficer}} </h3>
            {{/if}}
        </div>
    </div>

    {{#if reportData.officerDetails.details}}
        <div class="table-responsive ml-1">
            <table id="table-request"   class="table w-100">
                <thead>
                <tr>
                    <th>Username</th>
                    <th>Assigned By</th>
                    <th>Assigned At</th>
                </tr>
                </thead>
                <tbody>
                {{#each reportData.officerDetails.details}}
                    <tr>
                        <td>{{username}}</td>
                        <td>{{assignedBy}}</td>
                        <td>{{#formatDate assignedAt "MM/DD/YYYY"}}{{/formatDate }}</td>
                    </tr>
                {{/each}}
                </tbody>
            </table>
        </div>
    {{/if}}

</div>


const mongoose = require('mongoose');
const ObjectId = require('mongoose').Types.ObjectId;

const addressSchema = new mongoose.Schema({
  primaryAddress: { type: String, required: false },
  secondaryAddress: { type: String, required: false },
  country: { type: String, required: false },
  state: { type: String, required: false },
  postalCode: { type: String, required: false },
  phone: { type: String, required: false },
  city: { type: String, required: false },
});

const corporationSchema = new mongoose.Schema({
  corporateName: { type: String, required: false },
  corporateNumber: { type: String, required: false },
  entityType: { type: String, required: false },
  corporateDate: { type: Date, required: false },
  corporatePlace: { type: String, required: false },
  city: { type: String, required: false },
});

const contactDetailsSchema = new mongoose.Schema({
  phoneNumber: { type: String, required: false },
  mobileNumber: { type: String, required: false },
  workPhoneNumber: { type: String, required: false },
  workEmail: { type: String, required: false },
  additionalEmail:  { type: String, required: false },
  fax: { type: String, required: false },
  faxNumber: { type: String, required: false },
});

const beneficialOwnerInformationSchema = new mongoose.Schema({
  type: { type: String, required: false },
  secondaryType: { type: String, required: false },
  note: { type: String, required: false },
  beneficialNumber:  { type: String, required: false },
  exemptedPerson: { type: String, required: false },
  registeredEntity: { type: String, required: false },
  jurisdiction: { type: String, required: false },
  regulatorName: { type: String, required: false },
  sovereignName: { type: String, required: false },
  ownershipPercent: { type: String, required: false },
  corporateStatus: { type: String, required: false },
  corporateStockCode: { type: String, required: false },
  notSend: { type: Boolean, required: false },
  stockExchange: { type: String, required: false },
  trustName: { type: String, required: false },
});


const beneficialOwnersSchema = new mongoose.Schema({
  code: { type: String, required: true },
  type: { type: String, required: false },
  companyNumber:{ type: String, required: false },
  companyName: { type: String, required: false },
  personOrEntity: { type: String, required: false },
  appointmentDate: { type: Date, required: false},
  ceasedDate: { type: Date, required: false},
  salutation: { type: String, required: false },
  firstName: { type: String, required: false },
  middleName: { type: String, required: false },
  lastName: { type: String, required: false },
  fullName: { type: String, required: false },
  email: { type: String, required: false },
  formerFirstName: { type: String, required: false },
  formerMiddleName: { type: String, required: false },
  formerLastName: { type: String, required: false },
  dateOfBirth: { type: Date, required: false },
  placeOfBirth:  { type: String, required: false },
  gender: { type: String, required: false },
  nationality:  { type: String, required: false },
  secondNationality:  { type: String, required: false },
  otherNationality:  { type: String, required: false },
  residence:  { type: String, required: false },
  secondResidence:  { type: String, required: false },
  thirdResidence:  { type: String, required: false },
  idType:  { type: String, required: false },
  nationalId:  { type: String, required: false },
  passportNumber:  { type: String, required: false },
  passportCountry:  { type: String, required: false },
  passportExpiration:  { type: Date, required: false },
  notarPassport: {type: Boolean, required: false},
  fullContact: {type: Boolean, required: false},
  indpVer: {type: Boolean, required: false},
  refLet: {type: Boolean, required: false},
  profLet: {type: Boolean, required: false},
  recordType: { type: String, required: false },
  contactInformation: contactDetailsSchema,
  principalAddress: addressSchema,
  mailingAddress:  addressSchema,
  residentialAddress: addressSchema,
  corporation: corporationSchema,
  officeType: { type: String, required: false },
  officeAddress: addressSchema,
  boInformation: beneficialOwnerInformationSchema,
  comment: { type: String, required: false },
  fileDate: { type: Date, required: false},
  createdAt: { type: Date, required: false},
  updatedAt: { type: Date, required: false },
  submittedAt: { type: Date, required: false},
  entryDate: {type: Date, required: false},
  relationReferenceId: {type: ObjectId, ref: 'naturalperson', required: false},
  partitionkey: { type: String, required: false },
});


module.exports = mongoose.model('beneficialowners', beneficialOwnersSchema);

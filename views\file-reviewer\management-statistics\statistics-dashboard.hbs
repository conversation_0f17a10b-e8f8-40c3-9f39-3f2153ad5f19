<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title">
                            <h2>{{ title }}</h2>
                        </div>
                        <br>
                        <div class="mx-auto">
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead class="thead-light">
                                    <tr>
                                        <th></th>
                                        <th>Count</th>
                                        <th>%</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>Not started</td>
                                        <td>{{allData.notStarted.amount}}</td>
                                        <td>{{allData.notStarted.percentage}} %</td>
                                    </tr>
                                    <tr>
                                        <td>Assigned to File Reviewer</td>
                                        <td>{{allData.assigned.amount}}</td>
                                        <td>{{allData.assigned.percentage}} %</td>
                                    </tr>
                                    <tr>
                                        <td>In progress by File Reviewer</td>
                                        <td>{{allData.onHold.amount}}</td>
                                        <td>{{allData.onHold.percentage}} %</td>
                                    </tr>
                                    <tr>
                                        <td>Reviewed</td>
                                        <td>{{allData.reviewed.amount}}</td>
                                        <td>{{allData.reviewed.percentage}} %</td>
                                    </tr>
                                    <tr>
                                        <td>In progress by Quality Assurance</td>
                                        <td>{{allData.inProgressByQa.amount}}</td>
                                        <td>{{allData.inProgressByQa.percentage}} %</td>
                                    </tr>
                                    <tr>
                                        <td>Validated by Quality Assurance</td>
                                        <td>{{allData.validatedByQa.amount}}</td>
                                        <td>{{allData.validatedByQa.percentage}} %</td>
                                    </tr>
                                    <tr>
                                        <td>In progress by Compliance</td>
                                        <td>{{allData.compliance.amount}}</td>
                                        <td>{{allData.compliance.percentage}} %</td>
                                    </tr>
                                    <tr>
                                        <td>Validated by Compliance</td>
                                        <td>{{allData.validatedByCo.amount}}</td>
                                        <td>{{allData.validatedByCo.percentage}} %</td>
                                    </tr>
                                    <tr>
                                        <td>Sent back to Client</td>
                                        <td>{{allData.sendToClient.amount}}</td>
                                        <td>{{allData.sendToClient.percentage}} %</td>
                                    </tr>
                                    <tr>
                                        <td>Assigned to File Reviewer by Quality Assurance or Compliance</td>
                                        <td>{{allData.sendToFileReview.amount}}</td>
                                        <td>{{allData.sendToFileReview.percentage}} %</td>
                                    </tr>

                                    <tr>
                                        <td>Assigned to Quality Assurance by Compliance</td>
                                        <td>{{allData.sendToQa.amount}}</td>
                                        <td>{{allData.sendToQa.percentage}} %</td>
                                    </tr>
                                    <tr>
                                        <td>Assigned to Compliance by File Reviewer or Quality Assurance</td>
                                        <td>{{allData.complianceBy.amount}}</td>
                                        <td>{{allData.complianceBy.percentage}} %</td>
                                    </tr>
                                    <tr>
                                        <th>Total</th>
                                        <th>{{allData.total}}</th>
                                        <th>100 %</th>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- CONTENT END -->
                        <div class="row mt-2 justify-content-between ">
                            <div class="col-md-2">
                                <a href="/file-reviewer/dashboard"
                                   class="btn btn-secondary width-lg waves-effect waves-light">
                                    Back
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>


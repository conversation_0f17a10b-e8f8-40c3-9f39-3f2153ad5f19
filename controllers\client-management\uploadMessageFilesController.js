const multer = require('multer');
const multerAzureStorage = require('../../classes/MulterAzureStorage');
const { renameBlob } = require('../../utils/azureStorage');

exports.uploadMessageFile = multer({
    storage: new multerAzureStorage({
        containerName: process.env.AZURE_STORAGE_CONTAINER_MESSAGES_UPLOAD,
        accessKey: process.env.AZURE_STORAGE_ACCESS_KEY,
        accountName: process.env.AZURE_STORAGE_ACCOUNT,
    }),
});

exports.moveMessageUpload = async function (id, file, fileType) {
    try {
      if (file) {
        const newName = id + '/' + file.blobName.replace('fileUploaded', fileType);
        await renameBlob(
          process.env.AZURE_STORAGE_ACCOUNT,
          process.env.AZURE_STORAGE_ACCESS_KEY,
          process.env.AZURE_STORAGE_CONTAINER_MESSAGES_UPLOAD, 
          file.blobName,
          newName
        );
      }
    } catch (error) {
        console.log("ERROR: ", error);
    }
};

<div id="foundationForm">

    <!-- CORPORATE DETAILS -->
    <div id="foundationDetails">
        <div class="row mt-3">
            <div class="col-2">
                <label for="foundation-details-organization-name">Organization Name*</label>
            </div>
            <div class="col-4">
                <input type="text" class="form-control"  id="foundation-details-organization-name"  name="details[organizationName]"
                       value="{{relation.details.organizationName}}" />
            </div>
            <div class="col-2">
                <label for="foundation-details-incorporation-number">Incorporation / Formation Number</label>
            </div>
            <div class="col-4">
                <input id="foundation-details-incorporation-number" class="form-control" type="text" name="details[incorporationNumber]"
                       value="{{relation.details.incorporationNumber}}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="foundation-details-tax-residence">Tax Residence</label>
            </div>
            <div class="col-4">
<!--                <input id="foundation-details-tax-residence" class="form-control" type="text" name="details[taxResidence]"-->
<!--                       value="{{relation.details.taxResidence}}"/>-->
                {{>file-reviewer/shared/select-country selectId="details[taxResidence]"
                        value=relation.details.taxResidence}}
            </div>
            <div class="col-2">
                <label for="foundation-details-registration-number"
                >Business Registration Number (if applicable)</label
                >
            </div>
            <div class="col-4">
                <input class="form-control" type="text"  id="foundation-details-registration-number" name="details[businessNumber]"
                       value="{{relation.details.businessNumber}}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="foundation-details-incorporation-date">Date of Incorporation</label>
            </div>
            <div class="col-4">
                <input class="form-control" type="date" id="foundation-details-incorporation-date" name="details[incorporationDate]"
                       value="{{#formatDate relation.details.incorporationDate "YYYY-MM-DD"}} {{/formatDate }}"/>
            </div>
            <div class="col-2">
                <label for="details[incorporationCountry]">Country of Incorporation</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[incorporationCountry]"
                        value=relation.details.incorporationCountry}}
            </div>
        </div>
        <!-- DETAILS TABLE -->
        {{>file-reviewer/shared/relation-file-table tableId="detailsTable"  name="details" group="foundation"
                files=(ternary newRelation relation.foundationFiles.details relation.details.files)
                relationId=relation._id}}

        <!-- DETAILS PARTNER TABLE -->
        {{>file-reviewer/shared/certificate-partner-table group="foundation"
                partnerFiles=(ternary newRelation relation.foundationFiles.detailsPartner relation.detailsPartner.files)
                relationId=relation._id}}

        <div class="row pt-3">
            <div class="col-12 d-flex justify-content-end">
                <div class="custom-control custom-checkbox">
                    <input
                            type="checkbox"
                            class="custom-control-input completeCheck"
                            name="details[correct]"
                            id="foundation-details-correct"
                        {{#if relationInformation.details.complete}} checked {{/if}}
                    />
                    <label class="custom-control-label" for="foundation-details-correct"
                    >Complete Information</label
                    >
                </div>
            </div>
        </div>
    </div>
    <hr class="mt-2"/>

    <!-- PRINCIPAL ADDRESS DETAILS -->
    <div id="principalAddressDetails">
        <h4>Principal Address</h4>
        {{>file-reviewer/review-relations/sections/address-details-form group="foundation"
                principalAddress=relation.principalAddress formType="principalAddress"
                relationInformation=relationInformation.principalAddress}}
    </div>
    <hr class="mt-2" />

    <!-- MAILING ADDRESS DETAILS -->
    <div id="mailingAddressDetails">
        <h4>Mailing Address</h4>
        {{>file-reviewer/review-relations/sections/address-details-form group="foundation"
                principalAddress=relation.mailingAddress formType="mailingAddress"
                relationInformation=relationInformation.mailingAddress }}
    </div>
    <hr class="mt-2" />

    <!-- LISTED COMPANY DETAILS -->
    <div class="listedCompanyDetails">
        {{>file-reviewer/review-relations/sections/listed-company-details-form group="foundation"
                listedCompany=relation.listedCompanyDetails}}
    </div>
    <hr class="mt-2" />

    <!-- LIMITED COMPANY DETAILS -->
    <div class="limitedCompanyDetails">
        {{>file-reviewer/review-relations/sections/limited-company-details-form group="foundation"
                limitedCompany=relation.limitedCompanyDetails}}
    </div>
    <hr class="mt-2" />

    <!-- FOUNDATION DETAILS -->
    <div class="foundationDetails">
        <div class="row">
            <div class="col-5">
                <h4>Foundation Details</h4>
            </div>
            <div class="col-7 d-flex">
                <div class="custom-control custom-switch my-auto">
                    <input type="checkbox" class="custom-control-input toggle-section-check"
                           id="foundation-details-confirmation"
                           name="foundation[active]"
                        {{#if relation.foundation.active}} checked {{/if}}
                    >
                    <label class="custom-control-label" for="foundation-details-confirmation"></label>
                </div>
            </div>
        </div>
        <div id="content-foundation-details-confirmation"  {{#unless relation.foundation.active }} style="display: none" {{/unless}}>
            {{>file-reviewer/shared/relation-file-table tableId="foundationTable"  name="foundation"
                    files=(ternary newRelation relation.foundationFiles.foundation relation.foundation.files)
                    relationId=relation._id}}
            <div class="row mt-2">
                <div class="col-2">
                    <label for="foundation[country]">Country</label>
                </div>
                <div class="col-4">
                    {{>file-reviewer/shared/select-country selectId="foundation[country]" value=relation.foundation.country}}
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 d-flex justify-content-end">
                    <div class="custom-control custom-checkbox">
                        <input
                                type="checkbox"
                                class="custom-control-input completeCheck"
                                name="foundation[correct]"
                                id="correct-foundation"
                                {{#if relationInformation.foundation.complete}} checked {{/if}}
                        />
                        <label class="custom-control-label" for="correct-foundation"
                        >Complete Information</label
                        >
                    </div>
                </div>
            </div>
        </div>

    </div>
    <hr class="mt-2"/>

    <!-- MUTUAL FUND DETAILS -->
    <div class="mutualFundDetails">
        {{>file-reviewer/review-relations/sections/mutual-fund-details-form group="foundation"
                mutualFund=(ternary newRelation relation.foundationFiles.mutualFund relation.mutualFundDetails) relationId=relation._id}}
    </div>
    <hr class="mt-2" />

    <!--WORLD CHECK DETAILS -->
    <div  id="worldCheckSection" >
        {{>file-reviewer/review-relations/sections/world-check-details-form group="foundation"
                worldCheck=(ternary newRelation relation.foundationFiles.worldCheck relation.worldCheck)
                relationInformation=relationInformation.worldCheck }}
    </div>


</div>

<!-- OP<PERSON> OWNER MODAL -->
<div class="modal fade" id="openRelationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modal-relation-title" class="modal-title">Type</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-relation-body" class="modal-body p-3">
                <p class="text-muted text-center">LOADING...</p>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button
                        id="submitRelationButton"
                        type="button"
                        form="qualityRelationForm"
                        class="btn solid royal-blue modal-action-btn" style="display: none"
                >
                    Submit
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="/templates/peekqualityrelation.precompiled.js"></script>
<script type="text/javascript">
    let reviewId;
    let relationId;
    let relationType;
    let relationGroup;
    $('#openRelationModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        reviewId = button.data('review-id');
        relationId = button.data('relation-id');
        relationType = button.data('type');

        let relationName = button.data('name');
        const openMode = button.data('open-mode');
        relationGroup = button.data('group');
        let title = "";
        if (relationGroup === "beneficial"){
            title = 'Beneficial Owner: ' + relationName;
        }
        else if (relationGroup === "shareholder"){
            title = 'Shareholder: ' + relationName;
        }
        else{
            title = 'Director: ' + relationName;
        }
        $('#modal-relation-title').text(title);

        $.ajax({
            type: 'GET',
            url: '/file-reviewer/peek-relation/' + reviewId,
            data: {group: relationGroup, type: relationType, relationId: relationId},
            success: function (data) {
                let template = Handlebars.templates.peekqualityrelation;
                let d = {
                    reviewId: reviewId,
                    relation: data.relation,
                    relationInformation: data.relationInformation,
                    relationFiles: data.relationFiles,
                    onlyRead: openMode === "readOnly"
                };
                if (data.relation.lockedByFileReview && data.relation.lockedByFileReview === reviewId &&
                        openMode !== "readOnly"){
                    $("#submitRelationButton").show();
                }
                else{
                    $("#submitRelationButton").hide();
                }

                let html = template(d);
                
                $("#submitRelationButton").hide();
                $('#modal-relation-body').html(html);
                $('input[type=checkbox]').attr('disabled', true);
            },
            error: function () {
                Swal.fire(
                        'Error',
                        'There was an error while trying to fetch the relation',
                        'error'
                ).then(() => {
                    $('#openRelationModal').modal('hide');
                });
            },
        });
    });


    $("#submitRelationButton").on('click', function (e) {
        const form = $("#qualityRelationForm").serializeArray();
        e.preventDefault();
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/quality-assurance-review/' + reviewId + '/relations/' + relationId,
            data: {
                type: relationType,
                relationData: form,
                group: relationGroup,
            },
            success: function (data) {
                $('#openRelationModal').modal('hide');
            },
            error: function () {
                Swal.fire(
                        'Error',
                        'There was an error while trying to save the relation',
                        'error'
                ).then(() => {
                    $('#openRelationModal').modal('hide');
                });
            },
        });
    });
</script>

{{!-- CONFIRM CLIENT MODAL --}}
<div class="modal fade" id="requestInformationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Request information</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <form id="requestInformationForm">
                    <div class="row mb-1">
                        <div class="col-md-8">
                            <label>Would you like the client to provide additional documents?</label>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex justify-content-end">
                                <div class="custom-control custom-radio ml-2">
                                    <input class="custom-control-input form-control" type="radio"
                                           id="needsProvideDocumentsYes" name="needsProvideDocuments" value="true"
                                           required>
                                    <label class="custom-control-label" for="needsProvideDocumentsYes">
                                        Yes
                                    </label>
                                </div>
                                <div class=" custom-control custom-radio ml-2">
                                    <input class="custom-control-input form-control" type="radio"
                                           id="needsProvideDocumentsNo" name="needsProvideDocuments" value="false">
                                    <label class="custom-control-label" for="needsProvideDocumentsNo">
                                        No
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-1">
                        <div class="col-12">
                            <label for="questions">Please write the question(s) that you have for the client in the text
                                area below.</label>
                            <textarea id="questions" rows="3" class="w-100 form-control" name="questions"
                                      placeholder="Add a question..."></textarea>
                        </div>
                    </div>
                    <br>
                    <div class="row" id="showUploadDocument" style="display: none">
                        <div class="col-12">
                            <div id="frmUploadModal" class="dropzone">
                                <div class="fallback">
                                    <input name="documents" type="file" multiple/>
                                </div>
                                <div class="dz-message needsclick">
                                    <i class="h1 text-muted dripicons-cloud-upload"></i>
                                    <h3>Drop files here or click to upload.</h3>
                                    <span class="text-muted font-13">Allowed filetypes: PDF</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <br>
                </form>

            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="submit" class="btn solid royal-blue" id="sendFormButton" form="requestInformationForm">
                    Submit
                </button>
            </div>
        </div>
    </div>
</div>

<script>

    let incorporationId = '';
    $('#requestInformationModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        incorporationId = button.data('submission-id');
        $("#showUploadDocument").hide();
    });

    $('#requestInformationModal').on('hide.bs.modal', function (event) {
        $("#sendFormButton").show();
        $("#showUploadDocument").hide();
        $("#questions").prop('disabled', false);
        $("#questions").val('');
        $('input[name="needsProvideDocuments"]:checked').prop('checked', false);
        $('input[name="needsProvideDocuments"]').prop('disabled', false);

    });

    Dropzone.autoDiscover = false;

    $(function () {
        const myDropZone = new Dropzone("#frmUploadModal", {
            url: "/",
            acceptedFiles: 'application/pdf',
            autoProcessQueue: true,
            parallelUploads: 1,
            maxFiles: 10,
            maxFilesize: 5,
            addRemoveLinks: true,
            paramName: function () {
                return 'Document';
            },
            uploadMultiple: true,
            init: function () {
                this.on("processing", function (file) {
                    this.options.url = '/client-incorporation/' + incorporationId + '/upload-document';
                });
                this.on("success", function () {
                    if (this.files.length) {
                        $("#sendFormButton").prop('disabled', false);
                    } else {
                        $("#sendFormButton").prop('disabled', true);
                    }
                });
                this.on("sending", function (file, xhr, formData) {
                    $("#sendFormButton").prop('disabled', true);
                    if (!formData.has('filetype')) {
                        formData.append("filetype", '');
                    }

                    if (!formData.has('fieldGroup')) {
                        formData.append("fieldGroup", 'requested-files');
                    }

                });

                this.on("errormultiple", function (files, response) {
                });

                this.on("maxfilesexceeded", function (file) {

                });

                this.on('resetFiles', function () {
                    if (this.files && this.files.length) {
                        for (let file of this.files) {
                            file.previewElement.remove();
                        }
                        this.files.length = 0;
                    }
                });

                this.on("removedfile", function (file) {
                    const name = file.name;
                    $.ajax({
                        type: 'POST',
                        url: '/client/' + incorporationId + '/delete-document',
                        data: {name: name,  group: 'requested-files'},
                        success: function (data) {

                        }
                    });
                    if (this.files && this.files.length > 0) {
                        $("#sendFormButton").prop('disabled', false);
                    } else {
                        $("#sendFormButton").prop('disabled', true);
                    }
                });
            }
        })

    })

    $("input[name='needsProvideDocuments']").on('change', function (e) {
        const value = $(this).val() === 'true';
        if (value) {
            $("#showUploadDocument").show();
        } else {
            $("#showUploadDocument").hide();
        }
    })

    $("#requestInformationForm").on('submit', function (event) {
        event.preventDefault();
        $(this).prop('disabled', true);

        $('input[required]:visible.request-information-form ').trigger('keyup');
        $("input[type='radio']:visible.request-information-form ").each(function () {
            const val = $('input:radio[name="' + this.name + '"]:checked').val();

            if (val === undefined) {
                $('input:radio[name="' + this.name + '"]').toggleClass("is-invalid", true);
            } else {
                $('input:radio[name="' + this.name + '"]').toggleClass("is-invalid", false);
            }

        });

        if ($(".is-invalid:visible").length === 0) {
            $.ajax({
                type: "POST",
                url: "./" + incorporationId + '/request-information',
                data: $(this).serialize(),
                success: function (data) {
                    if (data.status === 200) {
                        Swal.fire('Success', 'Sent back to client for request information successfully!', 'success').then(() => {
                            $("#sendFormButton").prop('disabled', false);
                            $("#requestInformationModal").modal('hide');
                            location.href = '/client-incorporation/list';
                        });
                    } else {
                        toastr["warning"]('Sorry, Error sending back to request the information. Try again later...');
                        $("#sendFormButton").removeAttr('disabled');
                    }
                },
                error: function (err) {
                    toastr["error"]('Sorry, Error sending back to request the information. Try again later...');
                    $("#sendFormButton").prop('disabled', false);
                }
            });
        } else {
            setTimeout(function () {
                $("#sendFormButton").prop('disabled', false);
            }, 1);
        }
    });

</script>

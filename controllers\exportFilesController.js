const EntryModel = require("../models/entry").EntryModel;

const excel = require("node-excel-export");
const moment = require("moment");
const { SE_RELEVANT_ACTIVITIES_FIELDS, countries } = require('../utils/constants');

exports.doExportXls = async function (req, res) {
  try {
    let entryIds = req.body["entryIds"].split(";");
    let records = await EntryModel.find().where("_id").in(entryIds).exec();

    const styles = {
      headerGreen: {
        fill: {
          fgColor: {
            rgb: "99ffeb",
          },
        },
        font: {
          color: {
            rgb: "006600",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerBluePastel: {
        fill: {
          fgColor: {
            rgb: "6f94dc",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerBlueKing: {
        fill: {
          fgColor: {
            rgb: "3333ff",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerBlueGrey: {
        fill: {
          fgColor: {
            rgb: "19334d",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerOrange: {
        fill: {
          fgColor: {
            rgb: "ff5500",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerPurple: {
        fill: {
          fgColor: {
            rgb: "6600ff",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerPurpleOutsource: {
        fill: {
          fgColor: {
            rgb: "cc0099",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerGrey: {
        fill: {
          fgColor: {
            rgb: "808080",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerGreenDark: {
        fill: {
          fgColor: {
            rgb: "009933",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerRedDark: {
        fill: {
          fgColor: {
            rgb: "cc0000",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
      headerYellowMustard: {
        fill: {
          fgColor: {
            rgb: "cc7a00",
          },
        },
        font: {
          color: {
            rgb: "ffffff",
          },
          sz: 12,
          bold: false,
          underline: false,
        },
      },
    };

    const { 
      specification_declaration,
      specification_direction,
      specification_meetings,
      specification_employees,
      specification_premises,
      specification_CIGA,
      specification_outsourcing,
      specification_parent_entities
    } = getSpecificationsColumns(styles);

    let dataset_declaration = [{}, {}];
    let dataset_direction = [{}, {}];
    let dataset_meetings = [{}, {}];
    let dataset_employees = [{}, {}];
    let dataset_premises = [{}, {}];
    let dataset_CIGA = [{}, {}];
    let dataset_outsourcing = [{}, {}];
    let dataset_parent_entities = [{}, {}];
    

    for (let i = 0; i < records.length; i++) {
      const entry = records[i];
      const entryVersion = parseFloat(entry.version || 1);
      const financialPeriodEnd = entry.entity_details?.financial_period_ends ? moment(entry.entity_details?.financial_period_ends).format("DD/MM/YYYY") : "";

      const declarationRows = createDeclarationRows(entry);
      if (declarationRows.length > 0){
        dataset_declaration = [...dataset_declaration, ...declarationRows ]
      }

      // PARENT ENTITIES
      if (entry.entity_details && entry.entity_details?.hasUltimateParents === true && entry.entity_details?.ultimateParents?.length > 0) {

        for (let i = 0; i < entry.entity_details.ultimateParents.length; i++) {
          const ultimateParentRow = createParentEntityRow(entry.company, 'Ultimate', financialPeriodEnd, entry.entity_details.ultimateParents[i]);
          dataset_parent_entities.push(ultimateParentRow);
        }
      }

      if (entry.entity_details && entry.entity_details?.hasImmediateParents === true && entry.entity_details?.immediateParents?.length > 0) {
        for (let i = 0; i < entry.entity_details.immediateParents.length; i++) {
          const immediateParentRow = createParentEntityRow(entry.company, 'Immediate', financialPeriodEnd, entry.entity_details.immediateParents[i]);
          dataset_parent_entities.push(immediateParentRow);
        }
      }

     
      let isResidentInBVI = entry.tax_residency ? entry.tax_residency?.resident_in_BVI === true : null;
      let isNoneSelected = entry.relevant_activities?.none?.selected === true;

      if (isResidentInBVI && !isNoneSelected) {
        
        SE_RELEVANT_ACTIVITIES_FIELDS.forEach((activity) => {
          const relenantActivityData = entry.relevant_activities[activity.field];
          const relevantActivityDetails = entry[activity.field];

          if (relenantActivityData && relenantActivityData?.selected === true && relevantActivityDetails){
            const activityEndDate = moment(relenantActivityData.financial_periods[0].financial_period_ends).format("DD/MM/YYYY");
            
            // DIRECTION
            if (relevantActivityDetails.managers && relevantActivityDetails.managers?.length > 0) {
              for (let i = 0; i < relevantActivityDetails.managers.length; i++) {
                const directorRow = createDirectionRow(entry.company, activity.code, activityEndDate, relevantActivityDetails.managers[i]);
                dataset_direction.push(directorRow);
              }
            }


            // BOARD MEETINGS
            if (relevantActivityDetails.board_meetings && relevantActivityDetails.board_meetings?.length > 0) {
              for (let i = 0; i < relevantActivityDetails.board_meetings.length; i++) {
                const boardMeetingRow = createBoardMeetingRow(entry.company, activity.code, activityEndDate, relevantActivityDetails.board_meetings[i]);
                dataset_meetings.push(boardMeetingRow);
              }
            }

            // EMPLOYEES
            if (relevantActivityDetails.employees && relevantActivityDetails.employees?.length > 0) {
              for (let i = 0; i < relevantActivityDetails.employees.length; i++) {
                const employeesRow = createEmployeeRow(entry.company, activity.code, activityEndDate, relevantActivityDetails.employees[i]);
                dataset_employees.push(employeesRow);
              }
            }

            // PREMISES
            if (relevantActivityDetails.premises && relevantActivityDetails.premises?.length > 0) {
              for (let i = 0; i < relevantActivityDetails.premises.length; i++) {
                const premiseRow = convertPremises(entry.company, activity.code, activityEndDate, relevantActivityDetails.premises[i]);
                dataset_premises.push(premiseRow);
              }
            }

            // CIGA
            if (relevantActivityDetails.activity_ciga_core && relevantActivityDetails.activity_ciga_core !== "") {
              const cigaRow = createCigaRow(entry.company, activity.code, activityEndDate, relevantActivityDetails);
              dataset_CIGA.push(cigaRow);
            }

            // OUTSOURCING
            if(entryVersion < 5){
              if (relevantActivityDetails.core_income_generating_outsourced === true){
                const outsourcingRow = convertOutsourcing(entry.company, activity.code, activityEndDate, relevantActivityDetails, entryVersion);
                dataset_outsourcing.push(outsourcingRow);
              }
            }
            else{
              if (relevantActivityDetails.outsourcing_providers && relevantActivityDetails.outsourcing_providers?.length > 0) {
                for (let i = 0; i < relevantActivityDetails.outsourcing_providers.length; i++) {
                  const outsourcingRow = convertOutsourcing(entry.company, activity.code, activityEndDate, relevantActivityDetails.outsourcing_providers[i], entryVersion);
                  dataset_outsourcing.push(outsourcingRow);
                }
              }
            } 
          }
        })
      }

    }

    const report = excel.buildExport([
      {
        name: "Declaration",
        specification: specification_declaration,
        data: dataset_declaration,
      },
      {
        name: "Direction",
        specification: specification_direction,
        data: dataset_direction,
      },
      {
        name: "Meetings",
        specification: specification_meetings,
        data: dataset_meetings,
      },
      {
        name: "Employees",
        specification: specification_employees,
        data: dataset_employees,
      },
      {
        name: "Premises",
        specification: specification_premises,
        data: dataset_premises,
      },
      {
        name: "CIGA",
        specification: specification_CIGA,
        data: dataset_CIGA,
      },
      {
        name: "Outsourcing",
        specification: specification_outsourcing,
        data: dataset_outsourcing,
      },
      {
        name: "AdditionalParentEntity",
        specification: specification_parent_entities,
        data: dataset_parent_entities
      }
    ]);
    let countRecords = 0;
    records.forEach(function (documentToUpdate) {
      EntryModel.findOneAndUpdate(
        {_id: documentToUpdate._id, company: documentToUpdate.company},
        {
          exported_at: new Date(),
          exported_by: req.session.user.username,
        },
        function () {
          countRecords++;
          if (countRecords === records.length) {
            res.attachment("Report.xlsx");
            return res.send(report);
          }
        }
      );
    });
  } catch (err) {
    console.log("error");
    console.log(err);
    res.send(err);
  }
};

function getSpecificationsColumns(styles){
  const specification_declaration = {
    Entity_Unique_ID: {
      displayName: "Entity Unique ID",
      headerStyle: styles.headerGreen,
      width: 140,
    },
    TIN: {
      displayName: "Entity Taxpayer Identification Number (TIN)",
      headerStyle: styles.headerGreen,
      width: 200,
    },
    EntityGrossIncome: {
      displayName: "Entity Total Income",
      headerStyle: styles.headerGreen,
      width: 200,
    },
    isSameBusinessAddress: {
      displayName: "Business Address (if different from registered address)",
      headerStyle: styles.headerGreen,
      width: 200,
    },
    BusinessAddressLine1: {
      displayName: "Address Line 1",
      headerStyle: styles.headerGreen,
      width: 200,
    },
    BusinessAddressLine2: {
      displayName: "Address Line 2",
      headerStyle: styles.headerGreen,
      width: 200,
    },
    BusinessAddressCountry: {
      displayName: "Country",
      headerStyle: styles.headerGreen,
      width: 200,
    },
    NameOfMNEGroup: {
      displayName: "MNE Group Name",
      headerStyle: styles.headerGreen,
      width: 200,
    },
    HasUltimateParents: {
      displayName: "Does entity have Ultimate parent entity",
      headerStyle: styles.headerGreen,
      width: 200,
    },
    HasImmediateParents: {
      displayName: "Does the Entity have Immediate Parent Entity",
      headerStyle: styles.headerGreen,
      width: 200,
    },
    Period_Change: {
      displayName: "Period Change (Y/N)",
      headerStyle: styles.headerPurple,
      width: 160,
    },
    Financial_Period_Start_Date: {
      displayName: "Financial Period Start Date (DD/MM/YYYY)",
      headerStyle: styles.headerPurple,
      width: 280,
    },
    Financial_Period_End_Date: {
      displayName: "Financial Period End Date (DD/MM/YYYY)",
      headerStyle: styles.headerPurple,
      width: 280,
    },
    Relevant_Activity: {
      displayName: "Relevant Activity",
      headerStyle: styles.headerBlueKing,
      width: 200,
    },
    Partial_Period: {
      displayName: "Partial Period (Y/N)",
      headerStyle: styles.headerBlueKing,
      width: 200,
    },
    Partial_Start_Date: {
      displayName: "Start Date (DD/MM/YYYY)",
      headerStyle: styles.headerBlueKing,
      width: 260,
    },
    Partial_End_Date: {
      displayName: "End Date (DD/MM/YYYY)",
      headerStyle: styles.headerBlueKing,
      width: 260,
    },
    Non_Residence_Claim: {
      displayName: "Non Residence Claim (Y/N)",
      headerStyle: styles.headerOrange,
      width: 200,
    },
    Jurisdiction_Tax_Residence: {
      displayName: "Jurisdiction of Tax Residence",
      headerStyle: styles.headerOrange,
      width: 210,
    },
    Tax_TIN: {
      displayName: "Tax Payer Identification Number(TIN)",
      headerStyle: styles.headerOrange,
      width: 210,
    },
    Has_Parent_Entity: {
      displayName: "Has Parent Entity (Y/N)",
      headerStyle: styles.headerOrange,
      width: 160,
    },
    Parent_Name: {
      displayName: "Parent Name",
      headerStyle: styles.headerOrange,
      width: 120,
    },
    Parent_Alternative_Name: {
      displayName: "Parent Alternative Name",
      headerStyle: styles.headerOrange,
      width: 190,
    },
    Parent_Jurisdiction_Formation: {
      displayName: "Parent Jurisdiction of Formation",
      headerStyle: styles.headerOrange,
      width: 240,
    },
    Parent_Incorp_Form: {
      displayName: "Parent Incorp/Form#",
      headerStyle: styles.headerOrange,
      width: 165,
    },
    IP_High_Risk: {
      displayName: "IP High Risk (Y/N)",
      headerStyle: styles.headerBlueGrey,
      width: 165,
    },
    IP_High_Risk_Rebut: {
      displayName: "IP High Risk Rebut (Y/N)",
      headerStyle: styles.headerBlueGrey,
      width: 165,
    },
    IP_High_Risk_Tangible_Asset: {
      displayName: "IP High Risk:Tangible Asset",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    IP_High_Risk_Explanation_Asset: {
      displayName: "IP High Risk:Explanation of Tangible Asset Used",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    IP_High_Risk_Decisions: {
      displayName: "IP High Risk:Decisions Each Employee Responsible",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    IP_High_Risk_Nature: {
      displayName: "IP High Risk:Nature and History of Strategic Decisions",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    IP_High_Risk_Nature_Trading: {
      displayName: "IP High Risk:Nature and History of Trading Activities",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    IP_Total_Income: {
      displayName: "IP Total Income",
      headerStyle: styles.headerBlueGrey,
      width: 165,
    },
    IP_Royalties_Income: {
      displayName: "IP Royalties Income",
      headerStyle: styles.headerBlueGrey,
      width: 165,
    },
    IP_Asset_Sales_Income: {
      displayName: "IP Asset Sales Income",
      headerStyle: styles.headerBlueGrey,
      width: 165,
    },
    IP_Others_Gross_Income: {
      displayName: "IP Gross Income Through Others",
      headerStyle: styles.headerBlueGrey,
      width: 165,
    },
    IP_Other_CIGA: {
      displayName: "IP Other CIGA (Y/N)",
      headerStyle: styles.headerBlueGrey,
      width: 185,
    },
    IP_Other_CIGA_Rebut: {
      displayName: "IP Other CIGA Rebut (Y/N)",
      headerStyle: styles.headerBlueGrey,
      width: 195,
    },
    IP_Other_CIGA_Rebut_Relevant_Asset: {
      displayName: "IP Other CIGA Rebut: Relevant IP Asset",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    IP_Other_CIGA_Rebut_Detailed_Business: {
      displayName: "IP Other CIGA Rebut: Detailed Business Plans",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    IP_Other_CIGA_Rebut_Decisions: {
      displayName: "IP Other CIGA Rebut: Decisions Each Employee Responsible",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    IP_Other_CIGA_Rebut_Evidence: {
      displayName: "IP Other CIGA Rebut: Concrete Evidence Decision Making in BVI",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Directed_Managed_in_BVI: {
      displayName: "Directed and managed in BVI (Y/N)",
      headerStyle: styles.headerBlueGrey,
      width: 220,
    },
    Num_Board_Meetings: {
      displayName: "Num Board Meetings",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Board_Meetings_in_BVI: {
      displayName: "Board Meetings in BVI",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Board_Meetings_Quorum: {
      displayName: "Board/Quoram of Meetings in BVI",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Board_Meetings_Quorum_Directors: {
      displayName: "Board/Quoram of Meetings in BVI:Quorum of Directors (Y/N)",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Minutes_Kept_in_BVI: {
      displayName: "Minutes kept in BVI (Y/N)",
      headerStyle: styles.headerBlueGrey,
      width: 220,
    },
    Reporting_Currency: {
      displayName: "Reporting Currency",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Gross_Income: {
      displayName: "Gross Income",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Turnover_Type_Income: {
      displayName: "Turnover: Gross Income",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Turnover_Amount_Type_Assets: {
      displayName: "Turnover:Amount and Type of Assets",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Turnover_Net_Book_Value: {
      displayName: "Turnover:Net book value of Tangible Assets",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Total_Expediture: {
      displayName: "Total Expenditure",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    BVI_Expenditure: {
      displayName: "BVI Expenditure",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Employees: {
      displayName: "Employees",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Total_Employees: {
      displayName: "Total Employees",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    BVI_Employees: {
      displayName: "BVI Employees",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    CIGA_Outsourced: {
      displayName: "CIGA Outsourced (Y/N)",
      headerStyle: styles.headerBlueGrey,
      width: 220,
    },
    Outsourcing_Expenditure: {
      displayName: "Outsourcing Expenditure",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    HLD_Stat_Obligations: {
      displayName: "HLD Stat Obligations (Y/N)",
      headerStyle: styles.headerBlueGrey,
      width: 220,
    },
    HLD_Active_Equity_Management: {
      displayName: "HLD Active Equity Management (Y/N)",
      headerStyle: styles.headerBlueGrey,
      width: 270,
    },
    IP_Equipment_Nature: {
      displayName: "IP Equipment Nature",
      headerStyle: styles.headerBlueGrey,
      width: 200,
    },
    Supporting_Comments: {
      displayName: "Supporting Comments",
      headerStyle: styles.headerYellowMustard,
      width: 160,
    },
  };

  const specification_direction = {
    Entity_Unique_ID: {
      displayName: "Entity Unique ID",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Relevant_Activity: {
      displayName: "Relevant Activity",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Financial_Period_End_Date: {
      displayName: "Financial Period End Date (DD/MM/YYYY)",
      headerStyle: styles.headerGreen,
      width: 260,
    },
    Name: {
      displayName: "Name",
      headerStyle: styles.headerGrey,
      width: 120,
    },
    Resident_in_BVI: {
      displayName: "Resident in BVI (Y/N)",
      headerStyle: styles.headerGrey,
      width: 170,
    },
    Relation_to_Entity: {
      displayName: "Relation to Entity",
      headerStyle: styles.headerGrey,
      width: 150,
    },
  };
  const specification_meetings = {
    Entity_Unique_ID: {
      displayName: "Entity Unique ID",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Relevant_Activity: {
      displayName: "Relevant Activity",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Financial_Period_End_Date: {
      displayName: "Financial Period End Date (DD/MM/YYYY)",
      headerStyle: styles.headerGreen,
      width: 280,
    },
    Meeting_Number: {
      displayName: "Meeting Number",
      headerStyle: styles.headerBluePastel,
      width: 120,
    },
    Person_Name: {
      displayName: "Person Name",
      headerStyle: styles.headerBluePastel,
      width: 120,
    },
    Physically_Present: {
      displayName: "Physically Present (Y/N)",
      headerStyle: styles.headerBluePastel,
      width: 180,
    },
    Relation_Company: {
      displayName: "Relation to company",
      headerStyle: styles.headerBluePastel,
      width: 160,
    },
    Director_Qualification: {
      displayName: "Director Qualification",
      headerStyle: styles.headerBluePastel,
      width: 160,
    },
  };

  const specification_employees = {
    Entity_Unique_ID: {
      displayName: "Entity Unique ID",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Relevant_Activity: {
      displayName: "Relevant Activity",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Financial_Period_End_Date: {
      displayName: "Financial Period End Date (DD/MM/YYYY)",
      headerStyle: styles.headerGreen,
      width: 280,
    },
    Employee_Name: {
      displayName: "Employee Name",
      headerStyle: styles.headerRedDark,
      width: 120,
    },
    Employee_Qualification: {
      displayName: "Employee Qualification",
      headerStyle: styles.headerRedDark,
      width: 160,
    },
    Years_Relevant_Experience: {
      displayName: "Years of Relevant Experience",
      headerStyle: styles.headerRedDark,
      width: 200,
    },
  };

  const specification_premises = {
    Entity_Unique_ID: {
      displayName: "Entity Unique ID",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Relevant_Activity: {
      displayName: "Relevant Activity",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Financial_Period_End_Date: {
      displayName: "Financial Period End Date (DD/MM/YYYY)",
      headerStyle: styles.headerGreen,
      width: 260,
    },
    Premises_Address_Line1: {
      displayName: "Premises Address Line1",
      headerStyle: styles.headerGreenDark,
      width: 160,
    },
    Premises_Address_Line2: {
      displayName: "Premises Address Line2",
      headerStyle: styles.headerGreenDark,
      width: 160,
    },
    Premises_Country: {
      displayName: "Premises Country",
      headerStyle: styles.headerGreenDark,
      width: 160,
    },
  };

  const specification_CIGA = {
    Entity_Unique_ID: {
      displayName: "Entity Unique ID",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Relevant_Activity: {
      displayName: "Relevant Activity",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Financial_Period_End_Date: {
      displayName: "Financial Period End Date (DD/MM/YYYY)",
      headerStyle: styles.headerGreen,
      width: 260,
    },
    CIGA_Code: {
      displayName: "CIGA Code",
      headerStyle: styles.headerGrey,
      width: 120,
    },
    CIGA_Other_Text: {
      displayName: "CIGA Other Text",
      headerStyle: styles.headerGrey,
      width: 160,
    },
  };


  const specification_outsourcing = {
    Entity_Unique_ID: {
      displayName: "Entity Unique ID",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Relevant_Activity: {
      displayName: "Relevant Activity",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Financial_Period_End_Date: {
      displayName: "Financial Period End Date (DD/MM/YYYY)",
      headerStyle: styles.headerGreen,
      width: 260,
    },
    Provider_Name: {
      displayName: "Provider Name",
      headerStyle: styles.headerPurpleOutsource,
      width: 120,
    },
    Provider_Details: {
      displayName: "Provider Details",
      headerStyle: styles.headerPurpleOutsource,
      width: 120,
    },
    Provider_Number_Staff: {
      displayName: "Provider Number of Staff",
      headerStyle: styles.headerPurpleOutsource,
      width: 200,
    },
    Provider_Staff_Hours: {
      displayName: "Provider Staff Hours",
      headerStyle: styles.headerPurpleOutsource,
      width: 170,
    },
    Provider_Monitor_Control: {
      displayName: "Provider Monitor and Control (Y/N)",
      headerStyle: styles.headerPurpleOutsource,
      width: 260,
    },
  };

  const specification_parent_entities = {
    Entity_Unique_ID: {
      displayName: "Entity Unique ID",
      headerStyle: styles.headerGreen,
      width: 120,
    },
    Financial_Period_End_Date: {
      displayName: "Financial Period End Date (DD/MM/YYYY)",
      headerStyle: styles.headerGreen,
      width: 260,
    },
    Type: {
      displayName: "Immediate Parent/ Ultimate Parent?",
      headerStyle: styles.headerPurpleOutsource,
      width: 250,
    },
    Parent_Name: {
      displayName: "Parent Entity Name",
      headerStyle: styles.headerPurpleOutsource,
      width: 250,
    },
    Alternative_Name: {
      displayName: "Parent Entity Alternative Name",
      headerStyle: styles.headerPurpleOutsource,
      width: 250,
    },
    Jurisdiction: {
      displayName: "Parent Entity's Jurisdiction of Formation",
      headerStyle: styles.headerPurpleOutsource,
      width: 250,
    },
    Incorporation_Number: {
      displayName: "Parent Entity’s Incorporation/ Formation Number",
      headerStyle: styles.headerPurpleOutsource,
      width: 250,
    },
    TIN: {
      displayName: "Parent Entity Taxpayer Identification Number (TIN)",
      headerStyle: styles.headerPurpleOutsource,
      width: 200,
    },
  };

  return { 
    specification_declaration, 
    specification_direction, 
    specification_meetings,
    specification_employees,
    specification_premises,
    specification_CIGA,
    specification_outsourcing,
    specification_parent_entities
  }
}

function createDeclarationRows(entry){
  const dataset_declaration = [];
  let isResidentInBVI = entry.tax_residency ? entry.tax_residency?.resident_in_BVI === true : null;

  if (!entry.relevant_activities) {
    entry.relevant_activities = {
      none: {
        financial_periods: null,
        selected: true,
        part_of_financial_period: false,
      },
    };
  }

  if (entry.relevant_activities?.none?.selected !== true) {

    SE_RELEVANT_ACTIVITIES_FIELDS.forEach((activity) => {
      const relevantActivity = entry.relevant_activities[activity.field];

      if (relevantActivity && relevantActivity?.selected === true) {
        const businessRow = convertBusinessObject(entry, relevantActivity, entry[activity.field], activity.code, isResidentInBVI);
        dataset_declaration.push(businessRow);
      }
    })

  } else {
    if (entry.relevant_activities.none && entry.relevant_activities?.none?.selected === true) {
      const businessRow = convertBusinessObject(entry, entry.relevant_activities.none, null, "NA", isResidentInBVI);
      dataset_declaration.push(businessRow);
    }
  }

  return dataset_declaration;
}

function createDirectionRow(company, activityCode, financialPeriodEnd, director){
  return {
    Entity_Unique_ID: company,
    Relevant_Activity: activityCode,
    Financial_Period_End_Date: financialPeriodEnd,
    Name: director.first_name + " " + (director.last_name ? director.last_name : ''),
    Resident_in_BVI: director.resident_in_bvi === true ? "Y" : "N",
    Relation_to_Entity: director.position_held,
  }
}

function createBoardMeetingRow(company, activityCode, financialPeriodEnd, boardMeeting){
  return {
    Entity_Unique_ID: company,
    Relevant_Activity: activityCode,
    Financial_Period_End_Date: financialPeriodEnd,
    Meeting_Number: boardMeeting.meeting_number,
    Person_Name: boardMeeting.name,
    Physically_Present: boardMeeting.physically_present === true ? "Y" : "N",
    Relation_Company: boardMeeting.relation_to_entity,
    Director_Qualification: boardMeeting.qualification,
  }
}

function createEmployeeRow(company, activityCode, financialPeriodEnd, employee){
  return {
    Entity_Unique_ID: company,
    Relevant_Activity: activityCode,
    Financial_Period_End_Date: financialPeriodEnd,
    Employee_Name: employee.name,
    Employee_Qualification: employee.qualification,
    Years_Relevant_Experience: employee.experience_years,
  }
}

function createCigaRow(company, activityCode, financialPeriodEnd, details){
  return {
    Entity_Unique_ID: company,
    Relevant_Activity: activityCode,
    Financial_Period_End_Date: financialPeriodEnd,
    CIGA_Code: details.activity_ciga_core,
    CIGA_Other_Text: details.activity_ciga_core_other
  }


}

function createParentEntityRow(company, type, financialPeriodEnd, parentEntity){
  return {
    Entity_Unique_ID: company,
    Financial_Period_End_Date: financialPeriodEnd,
    Type: type,
    Parent_Name: parentEntity.parentName,
    Alternative_Name: parentEntity.alternativeName,
    Jurisdiction: parentEntity.jurisdiction,
    Incorporation_Number: parentEntity.incorporationNumber,
    TIN: parentEntity.TIN
  }
}


function convertOutsourcing(company, activityCode, financialPeriodEnd, outsourcing, version) {
  let cigaRow = {};
  if (version < 5){
    cigaRow = {
      Entity_Unique_ID: company,
      Relevant_Activity: activityCode,
      Financial_Period_End_Date: financialPeriodEnd,
      Provider_Name: "Trident Trust",
      Provider_Details: "Please refer to the outsourcing agreement that has been attached",
      Provider_Number_Staff: "",
      Provider_Staff_Hours: "",
      Provider_Monitor_Control: convertBoolean(outsourcing.demonstrate_monitoring_outsourced_activity),
    }
  }else{
    cigaRow = {
      Entity_Unique_ID: company,
      Relevant_Activity: activityCode,
      Financial_Period_End_Date: financialPeriodEnd,
      Provider_Name: outsourcing.entity_name,
      Provider_Details: outsourcing.resource_details,
      Provider_Number_Staff: outsourcing.staff_count,
      Provider_Staff_Hours: outsourcing.hours_per_month,
      Provider_Monitor_Control: convertBoolean(outsourcing.monitoring_control),
    }
  }
        
  return cigaRow
}

function convertPremises(company, activityCode, financialPeriodEnd, premise) {
  let addressLine1 = premise.address_line1;
  
  // for submissions with version < 5 
  if (premise.postalcode != undefined){
    addressLine1 += ", " + premise.postalcode
  }

  return {
    Entity_Unique_ID: company,
    Relevant_Activity: activityCode,
    Financial_Period_End_Date: financialPeriodEnd,
    Premises_Address_Line1: addressLine1,
    Premises_Address_Line2: premise.address_line2,
    Premises_Country: premise.country ? premise.country : "VGB",
  };
}

function convertBusinessObject(entry, activityDates, businessObj, activity, isResidentInBVI) {
  let parentEntity;
  const entryVersion = parseFloat(entry.version || 1);
  let mneGroupName = "";
  if (entryVersion > 1){
    
    if (entryVersion < 5){
      mneGroupName = entry.tax_residency?.MNE_group_name ? entry.tax_residency?.MNE_group_name : "";
    }
    else{
      mneGroupName = entry.entity_details?.nameOfMNEGroup ? entry.entity_details.nameOfMNEGroup : "";
    }

    parentEntity = {
      has_parent_entity: entry.tax_residency?.have_parent_entity ? 'Y' : 'N',
      name: entry.tax_residency?.parent_entity_name ?
        entry.tax_residency?.parent_entity_name : '',
      alternative_name: entry.tax_residency?.parent_entity_alternative_name ?
        entry.tax_residency?.parent_entity_alternative_name : '',
      jurisdiction: entry.tax_residency?.parent_entity_jurisdiction ?
        entry.tax_residency?.parent_entity_jurisdiction : '',
      incorporation_number: entry.tax_residency?.parent_entity_incorporation_number ?
        entry.tax_residency?.parent_entity_incorporation_number : '',
    }
  }else{
    parentEntity = {
      has_parent_entity: (entry.confirmation.ultimate_parent_entity_name && entry.confirmation.ultimate_parent_entity_name.length > 0 ? 'Y' : 'N'),
      name: entry.confirmation.ultimate_parent_entity_name ? entry.confirmation.ultimate_parent_entity_name : '',
      alternative_name: "",
      jurisdiction: entry.confirmation.entity_jurisdiction ? entry.confirmation.entity_jurisdiction : '',
      incorporation_number: entry.confirmation.incorporation_number ? entry.confirmation.incorporation_number : '',
    }
  }

  const entityDetailsInfo = {
    TIN: entryVersion >= 5 ? entry.entity_details?.TIN : "",
    EntityGrossIncome: entryVersion >= 5 && entry.entity_details?.totalAnnualGross !== null ? entry.entity_details?.totalAnnualGross : "",
    isSameBusinessAddress: entryVersion >= 5 ? convertBoolean(entry.entity_details?.isSameBusinessAddress) : "",
    BusinessAddressLine1: entryVersion >= 5  ? 
      entry.entity_details.businessAddress?.address_line1 : "",
    BusinessAddressLine2: entryVersion >= 5  ?
      entry.entity_details.businessAddress?.address_line2 : "",
    BusinessAddressCountry: entryVersion >= 5 ?
      entry.entity_details.businessAddress?.country : "",
    NameOfMNEGroup: mneGroupName,
    HasUltimateParents: convertBoolean(entry.entity_details?.hasUltimateParents),
    HasImmediateParents: convertBoolean(entry.entity_details?.hasImmediateParents),
    Period_Change: convertBoolean(entry.entity_details?.financial_period_changed),
    Financial_Period_Start_Date: entry.entity_details?.financial_period_begins ?
       moment(entry.entity_details?.financial_period_begins).format("DD/MM/YYYY") : "",
    Financial_Period_End_Date: entry.entity_details?.financial_period_ends ?
      moment(entry.entity_details?.financial_period_ends).format("DD/MM/YYYY") : ""
  }

  const relevantActivityInfo = {
    Relevant_Activity: activity,
    Partial_Period: activityDates ? convertBoolean(activityDates.part_of_financial_period) : "",
    Partial_Start_Date: activityDates && activityDates.part_of_financial_period && activityDates.financial_periods?.length > 0 ?
      moment(activityDates.financial_periods[0].financial_period_begins).format("DD/MM/YYYY") : "", 
    Partial_End_Date: activityDates && activityDates.part_of_financial_period && activityDates.financial_periods?.length > 0 ?
      moment(activityDates.financial_periods[0].financial_period_ends).format("DD/MM/YYYY") : "", 
  }

  const taxResidencyInfo = {
    Non_Residence_Claim: convertBoolean(isResidentInBVI !== null ? !isResidentInBVI : isResidentInBVI),
    Jurisdiction_Tax_Residence: entry.tax_residency && isResidentInBVI === false && entry.tax_residency?.entity_jurisdiction !== "" ? 
      convertCountry(entry.tax_residency.entity_jurisdiction ) : "",
    Tax_TIN: isResidentInBVI === false && entry.tax_residency?.foreign_tax_id_number  || "",
    Has_Parent_Entity: entryVersion < 5 && isResidentInBVI === false ? parentEntity.has_parent_entity : "",
    Parent_Name: entryVersion < 5 && isResidentInBVI === false ? parentEntity.name : "",
    Parent_Alternative_Name: entryVersion < 5 && isResidentInBVI === false ? parentEntity.alternative_name : "",
    Parent_Jurisdiction_Formation: entryVersion < 5 && isResidentInBVI === false ? parentEntity.jurisdiction : "",
    Parent_Incorp_Form: entryVersion < 5 && isResidentInBVI === false ? parentEntity.incorporation_number : "",
  }


  let activityInfo = {
    Directed_Managed_in_BVI: "",
    Num_Board_Meetings: "",
    Board_Meetings_in_BVI: "",
    Board_Meetings_Quorum: "",
    Board_Meetings_Quorum_Directors: "",
    Minutes_Kept_in_BVI: "",
    Reporting_Currency: entryVersion >= 5 && entry.entity_details?.totalAnnualGrossCurrency ?
      entry.entity_details?.totalAnnualGrossCurrency : "USD",
    Gross_Income: "",
    Turnover_Type_Income: "",
    Turnover_Amount_Type_Assets: "",
    Turnover_Net_Book_Value: "",
    Total_Expediture: "",
    BVI_Expenditure: "",
    Employees: "",
    Total_Employees: "",
    BVI_Employees: "",
  };

  let ipActivityInfo =  {
    IP_High_Risk: "",
    IP_High_Risk_Rebut: "",
    IP_High_Risk_Tangible_Asset: "",
    IP_High_Risk_Explanation_Asset: "",
    IP_High_Risk_Decisions: "",
    IP_High_Risk_Nature: "",
    IP_High_Risk_Nature_Trading: "",
    IP_Total_Income: "",
    IP_Royalties_Income: "",
    IP_Asset_Sales_Income: "",
    IP_Others_Gross_Income: "",
    IP_Other_CIGA: "",
    IP_Other_CIGA_Rebut: "",
    IP_Other_CIGA_Rebut_Relevant_Asset: "",
    IP_Other_CIGA_Rebut_Detailed_Business: "",
    IP_Other_CIGA_Rebut_Decisions: "",
    IP_Other_CIGA_Rebut_Evidence: "",
    IP_Equipment_Nature: "",
  }


  if (entry.tax_residency && isResidentInBVI === true){
   
    if (activity !== "NA"){
      activityInfo = {
        ...activityInfo,
        Directed_Managed_in_BVI: convertBoolean(businessObj.management_in_bvi),
        Num_Board_Meetings: businessObj.number_of_board_meetings ?? "",
        Board_Meetings_in_BVI: entryVersion >= 5 ? (businessObj.number_of_board_meetings_in_bvi ?? "") :
          (businessObj.number_of_board_meetings - businessObj.number_or_board_meetings_outside_bvi),
        Board_Meetings_Quorum: businessObj.quorum_of_board_meetings ?? "",
        Board_Meetings_Quorum_Directors: convertBoolean(businessObj.are_quorum_of_directors),
        Minutes_Kept_in_BVI: entryVersion >= 5 ? convertBoolean(businessObj.are_minutes_for_board_meetings) : "",
        Gross_Income: entryVersion >= 5 ? (businessObj.gross_income_total ?? "") :
          (businessObj.total_turnover ?? ""),
        Turnover_Type_Income: entryVersion >= 5  ? businessObj.gross_income_type : "",
        Turnover_Amount_Type_Assets: entryVersion >= 5 && activity !== "HLD" ?
          (businessObj.activity_assets_amount ?? "") + " " + businessObj.activity_assets_type : "",
        Turnover_Net_Book_Value: entryVersion >= 5 && activity !== "HLD" && businessObj.activity_netbook_values  != undefined ? 
          businessObj.activity_netbook_values  : "",
        Total_Expediture: businessObj.total_expenditure ?? "",
        BVI_Expenditure: businessObj.total_expenditure_bvi ?? "",
        Employees: businessObj.full_total_employees ?? "",
        Total_Employees: businessObj.total_employees_engaged ?? "",
        BVI_Employees: businessObj.total_employees ??  "",
      };

      
    }

    if (activity === "IP"){
      ipActivityInfo = {
        IP_High_Risk: convertBoolean(businessObj.high_risk_ip),
        IP_High_Risk_Rebut: convertBoolean(businessObj.evidence_high_risk_ip),
        IP_High_Risk_Tangible_Asset: entryVersion >= 5 && businessObj.high_risk_ip === true ? businessObj.tangible_assets_name : "",
        IP_High_Risk_Explanation_Asset: entryVersion >= 5 && businessObj.high_risk_ip === true ? businessObj.tangible_assets_explanation : "",
        IP_High_Risk_Decisions: entryVersion >= 5 && businessObj.high_risk_ip === true ? businessObj.intangible_assets_decisions : "",
        IP_High_Risk_Nature: entryVersion >= 5 && businessObj.high_risk_ip === true ? businessObj.intangible_assets_nature : "",
        IP_High_Risk_Nature_Trading: entryVersion >= 5 && businessObj.high_risk_ip === true ? businessObj.intangible_assets_trading_nature : "",
        IP_Total_Income: entryVersion >= 5 && entry.entity_details?.totalAnnualGross !== null ? entry.entity_details.totalAnnualGross : "",
        IP_Royalties_Income: entryVersion >= 5 && businessObj.high_risk_ip === true ? businessObj.high_risk_gross_income_total : "",
        IP_Asset_Sales_Income: entryVersion >= 5 && businessObj.high_risk_ip === true ? businessObj.high_risk_gross_income_assets : "",
        IP_Others_Gross_Income: entryVersion >= 5 && businessObj.high_risk_ip === true ? businessObj.high_risk_gross_income_others : "",
        IP_Other_CIGA: convertBoolean(businessObj.is_other_ciga_legal_entity),
        IP_Other_CIGA_Rebut: convertBoolean(businessObj.has_other_ciga_evidences),
        IP_Other_CIGA_Rebut_Relevant_Asset: entryVersion >= 5 && businessObj.is_other_ciga_legal_entity === true ?
          businessObj.other_ciga_ip_asset : "",
        IP_Other_CIGA_Rebut_Detailed_Business: entryVersion >= 5 && businessObj.is_other_ciga_legal_entity === true ?
          businessObj.other_ciga_business_details : "",
        IP_Other_CIGA_Rebut_Decisions: entryVersion >= 5 && businessObj.is_other_ciga_legal_entity === true ?
          businessObj.other_ciga_decisions : "",
        IP_Other_CIGA_Rebut_Evidence: entryVersion >= 5 && businessObj.is_other_ciga_legal_entity === true ?
          businessObj.other_ciga_evidence_details : "",
        IP_Equipment_Nature: entryVersion >= 5 ? businessObj.equipment_nature_description : "Please see the attachments in the submission"
      }
    }
  }

  const supportComment = entry.supporting_details?.support_comment ? entry.supporting_details.support_comment : "";

  const declaration = {
    Entity_Unique_ID: entry.company,
    ...entityDetailsInfo,
    ...relevantActivityInfo,
    ...taxResidencyInfo,
    ...ipActivityInfo,
    ...activityInfo,
    CIGA_Outsourced: (entry.tax_residency && isResidentInBVI === true) && activity !== "NA" ? 
      convertBoolean(businessObj.core_income_generating_outsourced) : "",

    Outsourcing_Expenditure: entryVersion >= 5 && isResidentInBVI === true && activity !== "NA" ? 
      businessObj.outsourcing_total_expenditure : "",
    HLD_Stat_Obligations: (entry.tax_residency && isResidentInBVI === true) && activity === "HLD" ?
     convertBoolean(businessObj.compliant_with_statutory_obligations) : "",
    HLD_Active_Equity_Management: (entry.tax_residency && isResidentInBVI === true) && activity === "HLD" ?
      convertBoolean(businessObj.manage_equity_participations) : "",
    Supporting_Comments: entryVersion >= 4.5 ? supportComment : ""
  }


  return declaration
}

function convertBoolean(boolVal) {
  if (boolVal === null || boolVal === undefined) {
    return "";
  }

  return boolVal == true ? "Y" : "N";
}

function convertCountry(countryVal) {
  if(!countryVal){
    return "";
  }

  const countryFound = countries.find((c) => c.alpha_3_code === countryVal || c.name?.toLowerCase() === countryVal.toLowerCase());

  if (countryFound){
    return countryFound.alpha_3_code
  }
  else{
    return countryVal;
  }
}

(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['positionlistmodal'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), alias4=container.hooks.helperMissing, alias5="function", lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "        <tr>\r\n            <td style=\"text-transform: capitalize;\">"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"referenceId") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"fullName") : stack1), depth0))
    + "</td>\r\n            <td style=\"text-transform: capitalize;\">"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"referenceId") : depth0)) != null ? lookupProperty(stack1,"details") : stack1)) != null ? lookupProperty(stack1,"countryBirth") : stack1), depth0))
    + "</td>\r\n            <td style=\"text-transform: capitalize;\">"
    + alias2(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":15,"column":52},"end":{"line":15,"column":62}}}) : helper)))
    + "</td>\r\n            <td class=\"text-center align-middle\">\r\n                <button\r\n                        type=\"button\"\r\n                        class=\"btn solid royal-blue\"\r\n                        data-toggle=\"modal\"\r\n                        data-target=\"#openQualityPositionModal\"\r\n                        data-open-mode=\""
    + alias2(alias1((depths[1] != null ? lookupProperty(depths[1],"openMode") : depths[1]), depth0))
    + "\"\r\n                        data-review-id=\""
    + alias2(alias1((depths[1] != null ? lookupProperty(depths[1],"reviewId") : depths[1]), depth0))
    + "\"\r\n                        data-organization-id=\""
    + alias2(alias1((depths[1] != null ? lookupProperty(depths[1],"organizationId") : depths[1]), depth0))
    + "\"\r\n                        data-position-id=\""
    + alias2(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":25,"column":42},"end":{"line":25,"column":50}}}) : helper)))
    + "\"\r\n                >\r\n                    Open\r\n                </button>\r\n            </td>\r\n        </tr>\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    return "        <tr>\r\n            <td colspan=\"8\" class=\"text-center font-italic\">\r\n                There are no positions.\r\n            </td>\r\n        </tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<table class=\"table table-striped\">\r\n    <thead>\r\n    <tr>\r\n        <th style=\"width: 30%;\">Name</th>\r\n        <th style=\"width: 25%;\">Country</th>\r\n        <th style=\"width: 25%;\">Position</th>\r\n        <th style=\"width: 20%;\"></th>\r\n    </tr>\r\n    </thead>\r\n    <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"positions") : depth0),{"name":"each","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.program(3, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":11,"column":4},"end":{"line":37,"column":13}}})) != null ? stack1 : "")
    + "    </tbody>\r\n</table>\r\n\r\n";
},"useData":true,"useDepths":true});
})();
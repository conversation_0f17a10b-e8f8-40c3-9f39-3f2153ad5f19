const CompanyModel = require("../models/company").schema;
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const xlsx = require('xlsx');
const fs = require('fs');
const moment = require('moment');

dotenv.config();


try {
    mongoose.connect(process.env.MONGODB, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        useFindAndModify: false,
    }, function (err) {
        throw err;
    });
    const db = mongoose.connection;
    db.on('error', console.error.bind(console, 'MongoDB connection error:'));
    // Start process - Read file (change file name as needed)
    const data = new Uint8Array(fs.readFileSync('Import ITA dates PROD.xlsx'));
    const workbook = xlsx.read(data, {
        type: "array",
        cellText: false,
        cellDates: true,
        sheetStubs: true,
    });

    updateITADates(workbook).then(r =>  console.log("FINISH, total updated: ", r));

} catch (e) {
    console.log(e);
}


function getCompaniesRows(workbook) {
    try {
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const companies = [];

        for (let cell in worksheet) {
            const cellAsString = cell.toString();
            const rowNumber = Number(cellAsString.replace(/\D/g, ''));
            const dataStartRow = 2;
            const companyIndex = rowNumber - dataStartRow;
            const rowName = cellAsString.replace(/[0-9]/g, '');

            if (companyIndex >= 0 && worksheet[cell].v) {
                if (!companies[companyIndex]) {
                    companies.push({
                        code: '',
                        applicationITADate: '',
                    })
                }
                if (rowName === "A") {
                    companies[companyIndex].code = worksheet[cell].v;
                }
                if (rowName === "B") {
                    const date = worksheet[cell].v;
                    companies[companyIndex].applicationITADate = date ? new Date(moment(date).format('YYYY-MM-DD')) : undefined;
                }
            }
        }
        return companies;
    } catch (e) {
        console.log("Error processing xlsx data: ", e);
        return []
    }

}


async function updateITADates(workbook) {
    try {

        let importCompanyLog = [['ID', 'Company Code', 'Update date', 'Action']];

        // Get company + bo info
        const companies = getCompaniesRows(workbook);


        console.log("companies length ", companies.length);


        for (let i = 0; i < companies.length; i++) {
            console.log('processing ' + i + '  from ' + companies.length)

            const company = companies[i];

            const existsCompany = await CompanyModel.findOne({  code: company.code });
            console.log('check' + company.code)
            // create companies
            if (existsCompany) {
                console.log('company found ' + company.code)
                if (!company.applicationITADate){
                    importCompanyLog.push(['', company.code, new Date(), 'ERROR: missing date in xlsx']);
                    continue;
                }


                if (!existsCompany.hasITADate || ( existsCompany.hasITADate === true &&  (
                    existsCompany.applicationITADate?.toString() !== company.applicationITADate.toString()))){

                    existsCompany.hasITADate =  true;
                    existsCompany.applicationITADate = company.applicationITADate;
                    await existsCompany.save();
                    importCompanyLog.push([existsCompany._id.toString(), company.code, new Date(), 'UPDATED'])
                }
                else{
                    importCompanyLog.push([existsCompany._id.toString(), company.code, new Date(), 'SKIPPED'])
                }
                break;
            }
            else {
                importCompanyLog.push(['', company.code,  new Date(), 'NOT FOUND COMPANY CREATED FOR THE COMPANY CODE'])
            }
        }
        // create entities bo
        console.log("companies updated ", importCompanyLog.length);

        const filename = 'update_ITA_application_date_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
        const logWorkbook = xlsx.utils.book_new();
        const logWorksheet1 = xlsx.utils.aoa_to_sheet(importCompanyLog);

        xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'companies ' + moment.utc().format('YYYY-MM-DD'));
        xlsx.writeFile(logWorkbook, filename);

        return { "success": true, "totalRows": importCompanyLog.length  };
    } catch (e) {
        console.log(e);
        return { "success": false };
    }
}



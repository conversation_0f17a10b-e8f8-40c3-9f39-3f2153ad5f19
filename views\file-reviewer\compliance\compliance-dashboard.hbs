<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h2>{{ title }}</h2>
                    </div>
                    <!-- CONTENT -->
                    <div class="card-body">
                        <!-- AUTOMATICALY PICKED REVIEWS TABLE -->
                        <div id="automatic-assign-table" class="row">
                            <h5 class="pl-1 pb-1">MY ASSIGNATIONS</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 30%;">Company Name</th>
                                        <th scope="col" style="width: 20%;">Company ID</th>
                                        <th scope="col" style="width: 15%;">Assigned at</th>
                                        <th scope="col" style="width: 20%;">Status</th>
                                        <th scope="col" style="width: 10%;"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each pickedReviews}}
                                        <tr>
                                            <td id="auto-company-name-{{ _id }}">{{ companyName }}</td>
                                            <td id="auto-company-code-{{ _id }}">{{ companyCode }}</td>
                                            <td id="auto-date-assigned-{{ _id }}">
                                                {{#formatDate fileReview.dateAssigned "DD-MM-YYYY"}} {{/formatDate}}
                                            </td>
                                            <td id="auto-status-{{ _id }}" class="text-uppercase">{{ status.code }}</td>
                                            <td class="pl-2 py-1 text-center align-middle">
                                                <a href="/file-reviewer/compliances/{{ _id }}"
                                                   class="btn solid royal-blue w-100">
                                                    Open
                                                </a>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="5" class="text-center font-italic">There are no files
                                                assigned
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- ASSIGNED BY OFFICER TABLE -->
                        <div id="officer-assign-table" class="row mt-3">
                            <h5 class="pl-1 pb-1">ASSIGNATIONS - ASSIGNED TO COMPLIANCE</h5>
                            <div class="table-responsive">
                                <table class="table w-100 nowrap table-striped font-size">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 30%;">Company Name</th>
                                        <th scope="col" style="width: 20%;">Company ID</th>
                                        <th scope="col" style="width: 15%;">Assigned at</th>
                                        <th scope="col" style="width: 20%;">Status</th>
                                        <th scope="col" style="width: 10%;"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each reviewsByOfficers}}
                                        <tr>
                                            <td>{{ companyName }}</td>
                                            <td>{{ companyCode }}</td>
                                            <td>
                                                {{#formatDate fileReview.dateAssigned "DD-MM-YYYY"}} {{/formatDate}}
                                            </td>
                                            <td class="text-uppercase">
                                                {{ status.code }}
                                            </td>
                                            <td class="pl-2 py-1 text-center align-middle">
                                                <button
                                                        type="button"
                                                        id="action-{{ _id }}"
                                                        class="pick-file btn solid royal-blue w-100">
                                                    Pick
                                                </button>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="5" class="text-center font-italic">There are no files
                                                assigned
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- ALL TABLE -->
                        <div id="all-table" class="row mt-3">
                            <div class="row mt-3 pl-2">
                                <h4>Search File Review</h4>
                            </div>

                            <div class="col-md-12">
                                <form method="POST" id="searchForm">
                                    <div class="row pt-1">
                                        <div class="col-md-3">

                                            <select
                                                    name="complianceFilter"
                                                    id='complianceFilter'
                                                    class="custom-select"
                                            >
                                                <option id="" value=""
                                                    {{#unless filters }}selected {{/unless}}>
                                                    Select...
                                                </option>
                                                <option id="all-option" value="all"
                                                    {{#ifEquals filters.complianceFilter "all"}}selected {{/ifEquals}}>
                                                    All
                                                </option>
                                                <option id="validated-option" value="validated"
                                                    {{#ifEquals filters.complianceFilter
                                                                "valdiated"}}selected {{/ifEquals}}>
                                                    Validated QA
                                                </option>
                                            </select>
                                            <label for="complianceFilter"></label>
                                        </div>
                                        <div class="col-md-3">

                                            <input class='form-control' type='text' name='searchText'
                                                   placeholder="Enter at least 3 characters"
                                                   id='searchFilter'
                                                   value="{{filters.searchText}}"
                                            />
                                            <label for="searchFilter"></label>
                                        </div>
                                        <div class="col-md-3">
                                            <input type='SUBMIT' class='btn btn-light btn-sm waves-effect'
                                                   value='Search'/>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="table-responsive">
                                <table id="scroll-horizontal-datatable"
                                       class="table table-small-font table-striped w-100 nowrap">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 25%;">Company Name</th>
                                        <th scope="col" style="width: 15%;">Company ID</th>
                                        <th scope="col" style="width: 20%;">Assigned to</th>
                                        <th scope="col" style="width: 10%;">Assigned at</th>
                                        <th scope="col" style="width: 15%;">Status</th>
                                        <th scope="col" style="width: 5%;">Compliance</th>
                                        <th scope="col" style="width: 15%;"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each allReviews}}
                                        <tr>
                                            <td>{{ companyName }}</td>
                                            <td>{{ companyCode }}</td>
                                            <td id="all-file-reviewer-assigned-{{ _id }}">{{ fileReview.name }}</td>
                                            <td id="all-date-assigned-{{ _id }}">
                                                {{#formatDate fileReview.dateAssigned "DD-MM-YYYY"}} {{/formatDate}}
                                            </td>
                                            <td class="text-uppercase" id="all-status-{{ _id }}">
                                                {{ status.code }}
                                            </td>
                                            <td class="align-content-center text-center">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox" name="reviews[{{_id}}]"
                                                           class="custom-control-input"
                                                           id="compliance-check-{{ @key }}"
                                                        {{#ifEquals status.code 'VALIDATED BY CO'}}
                                                           checked {{/ifEquals}}
                                                           disabled
                                                    />
                                                    <label
                                                            class="custom-control-label"
                                                            for="compliance-check-{{ @key }}"
                                                    ></label>
                                                </div>
                                            </td>
                                            <td class="pl-2 py-1 text-right">

                                                {{#if availableForPick}}
                                                    <button
                                                            type="button"
                                                            id="action-{{ _id }}"
                                                            class="pick-file btn btn-sm solid royal-blue"
                                                            style="min-width: 70px !important;"
                                                    >
                                                        Pick
                                                    </button>
                                                {{else}}
                                                    <a href="/file-reviewer/compliances/{{ _id }}"
                                                       class="btn btn-sm solid royal-blue"
                                                       style="min-width: 70px !important;">
                                                        Open
                                                    </a>
                                                {{/if}}

                                                {{#if availableToUnassigned}}
                                                    <button
                                                            type="button"
                                                            id="unassign-btn-{{ _id }}"
                                                            data-toggle="modal"
                                                            data-target="#unassignModal"
                                                            data-id="{{_id}}"
                                                            data-review-status="{{status.code}}"
                                                            class="btn btn-sm solid royal-blue">
                                                        Unassign
                                                    </button>
                                                {{/if}}

                                                <button class="btn btn-sm btn-danger"
                                                        type="button"
                                                        data-review-id="{{_id}}"
                                                        data-name="{{companyName}}"
                                                        data-pending-invitations="{{pendingElectronicIds}}"
                                                        data-toggle="modal"
                                                        data-target="#deleteReviewModal"
                                                >
                                                    Delete
                                                </button>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="7" class="text-center font-italic">
                                                There are no files
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                        <!-- ALL TABLE END -->
                    </div>
                    <!-- CONTENT END -->
                    <div class="row mt-2 justify-content-between ">
                        <div class="col-md-2">
                            <a href="/file-reviewer/dashboard"
                               class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    {{>file-reviewer/modals/unassign-review-modal}}
    {{>file-reviewer/modals/delete-review-modal}}
</main>
<script type="text/javascript">
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body',
        boundary: 'window'
    });

    $(document).ready(function () {
        $("#scroll-horizontal-datatable").DataTable({
            scrollX: !0,
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>",
                },
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
    });

    $(".pick-file").on("click", function () {
        const id = this.id.split("-")[1];
        $.ajax({
            url: "/file-reviewer/compliances/pick",
            method: "POST",
            data: {
                id: id,
            },
            success: function (res) {
                location.reload();
            },
            error: function (err) {
                if (err.status === 400) {
                    Swal.fire('Error', 'You already picked a review', 'error');
                } else if (err.status === 500) {
                    Swal.fire('Error', 'There was an internal error', 'error');
                }
            },
        });
    });
</script>

exports.securityGroupSubsManagers = process.env.AZURE_AD_SUBS_MANAGERS;
exports.securityGroupSubsSuperUser = process.env.AZURE_AD_SUBS_SUPER_USER;

exports.securityGroupFilereviewerHighRisk = process.env.AZURE_AD_FILEREVIEWER_HIGH_RISK;
exports.securityGroupFilereviewerMediumRisk = process.env.AZURE_AD_FILEREVIEWER_MEDIUM_RISK;
exports.securityGroupFilereviewerLowRisk = process.env.AZURE_AD_FILEREVIEWER_LOW_RISK;
exports.securityGroupFilereviewerCompliance = process.env.AZURE_AD_FILEREVIEWER_COMPLIANCE_RISK;
exports.securityGroupFilereviewerQualityAssurance = process.env.AZURE_AD_FILEREVIEWER_QUALITY_ASSURANCE_RISK;
exports.securityGroupFilereviewerFileObserver = process.env.AZURE_AD_FILEREVIEWER_FILE_OBSERVER_RISK;
exports.securityGroupClientIncorporationSuperAdmin = process.env.AZURE_AD_SECURITY_CLIENT_INCORPORATION_SUPER_ADMIN;
exports.securityGroupAnnouncementManagers = process.env.AZURE_AD_SECURITY_ANNOUNCEMENT_MANAGERS;

exports.securityGroupClientIncorporationOfficer = process.env.AZURE_AD_SECURITY_CLIENT_INCORPORATION;
exports.securityGroupClientDirectorManager = process.env.AZURE_AD_SECURITY_CLIENT_DIRECTOR_MANAGER;

exports.securityGroupTBVIProductionOffice = process.env.AZURE_AD_SECURITY_TBVI_PRODUCTION_OFFICE;
exports.securityGroupTHKOProductionOffice = process.env.AZURE_AD_SECURITY_THKO_PRODUCTION_OFFICE;
exports.securityGroupTCYPProductionOffice = process.env.AZURE_AD_SECURITY_TCYP_PRODUCTION_OFFICE;
exports.securityGroupTPANVGProductionOffice = process.env.AZURE_AD_SECURITY_TPANVG_PRODUCTION_OFFICE;
exports.securityGroupDirBoImportManager = process.env.AZURE_AD_SECURITY_DIR_BO_IMPORT_MANAGER;


exports.securityGroupFinancialReportAccountant = process.env.AZURE_AD_FINANCIAL_REPORTS_ACCOUNTANT;
exports.securityGroupFinancialReportManager = process.env.AZURE_AD_FINANCIAL_REPORTS_MANAGER;
exports.securityGroupFinancialReportStandard = process.env.AZURE_AD_FINANCIAL_REPORTS_STANDARD;
exports.securityGroupFinancialReportSuperUser = process.env.AZURE_AD_FINANCIAL_REPORTS_SUPERUSER;
exports.securityGroupFinancialReportProductOwner = process.env.AZURE_AD_FINANCIAL_REPORTS_PRODUCT_OWNER;

exports.securityGroupClientManagementSuperUser = process.env.AZURE_AD_CLIENTMANAGEMENT_SUPERUSER;


exports.test = true;

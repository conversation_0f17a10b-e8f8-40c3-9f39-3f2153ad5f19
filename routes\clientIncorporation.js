const express = require("express");
const router = express.Router();

const clientController = require("../controllers/client-incorporation/clientIncorporationController");
const uploadController = require("../controllers/client-incorporation/uploadClientDocumentsController");
const downloadController = require('../controllers/downloadController');
const exportFilesController = require("../controllers/client-incorporation/clientExportController");

router.get("/", ensureAuthenticated, clientController.getDashboard);
router.get("/list", ensureAuthenticated, clientController.getIncorporationRequestList);
router.get("/list/export-xls", ensureAuthenticated, exportFilesController.doSearchIncorporationsExportXls);
router.post("/pick-incorporation", ensureAuthenticated, clientController.pickClientIncorporationSubmission);
router.get("/:incorporationId", ensureAuthenticated, clientController.getSubmissionView);
router.get("/:incorporationId/request-information", ensureAuthenticated, clientController.getRequestInformation);
router.post("/:incorporationId/request-information", ensureAuthenticated, clientController.sendToRequestInformation);
router.post("/:incorporationId/update-status", ensureAuthenticated, clientController.updateStatus);
router.post('/:incorporationId/upload-document',ensureAuthenticated, uploadController.uploadIncorporationFile.fields([{ name: 'Document', maxCount: 5}]),
  uploadController.saveUpload);
router.post('/:incorporationId/delete-document',ensureAuthenticated, uploadController.deleteUpload);
router.get('/:incorporationId/download-all-documents',ensureAuthenticated, downloadController.downloadAllIncorporationFiles);
router.get('/:incorporationId/download-document/:fileId',ensureAuthenticated, downloadController.downloadClientIncorporationFiles);
router.get('/:incorporationId/incorporation.pdf', ensureAuthenticated, clientController.downloadIncorporationPdf);

router.get('/:incorporationId/relations/:relationId/file-list',ensureAuthenticated, clientController.getRelationFileList);
router.post('/:incorporationId/relations/:relationId/send-electronic-invitation',ensureAuthenticated, clientController.sendElectronicInvitation);
router.post('/:incorporationId/update-reservation-status',ensureAuthenticated, clientController.updateReservationStatus);
router.post('/:incorporationId/update-officer',ensureAuthenticated, clientController.updateAssignedOfficer);

function ensureAuthenticated(req, res, next) {
  if (req.session.is_authenticated ) {
    if (req.session.authentication.isIncorporationOfficer || req.session.authentication.isIncorporationSuperAdmin) {
      return next();
    } else {
      res.redirect('/not-authorized');
    }
  } else {
    res.redirect('/login');
  }
}



module.exports = router;

const CompanyModel = require("../models/company").schema;
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
const fs = require('fs');

dotenv.config();


const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const data = new Uint8Array(fs.readFileSync('scripts/DissolvedCompanies.xlsx'));
    const workbook = xlsx.read(data, {
        type: "array",
        cellText: false,
        cellDates: true,
        sheetStubs: true,
    });


    const result = await setApprovalCompanies(workbook);

    console.log('Script executed successfully', result);
  } catch (error) {
    console.error('Error in the script:', error);
  } finally {
    mongoose.disconnect();
  }
}


async function setApprovalCompanies(workbook) {
  try {

    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const companies = [];

    for (let cell in worksheet) {
        const cellAsString = cell.toString();
        const rowNumber = Number(cellAsString.replace(/\D/g, ''));
        const dataStartRow = 1;
        const companyIndex = rowNumber - dataStartRow;
        const rowName = cellAsString.replace(/[0-9]/g, '');

        if (companyIndex >= 0 && worksheet[cell].v) {
            if (!companies[companyIndex]) {
                companies.push({
                    incorporationcode: '',
                })
            }
            if (rowName === "A") {
                companies[companyIndex].incorporationcode = worksheet[cell].v.toString();
            }
        }
    }

    let updateLog = [['ID', 'Company Code', 'Update date', 'Action']];
        
    console.log("companiesToUpdate  ", companies.length);
    if (companies.length > 0) {
      for (let i = 0; i < companies.length; i++) {
        console.log('processing ' + i + '  from ' + companies.length)

        const company = companies[i];

        try {
          
          let dbCompany = await CompanyModel.findOne({"incorporationcode": company.incorporationcode}, {_id:1,accountingRecordsModule:1});
          
          if (dbCompany && dbCompany.accountingRecordsModule.active) {
            console.log('update portal company ' + dbCompany._id);
            
            
            const result = await CompanyModel.updateOne({ _id: dbCompany._id }, { $set: { 'accountingRecordsModule.active': false }});
            

            if (result.nModified > 0) {
              updateLog.push([company.incorporationcode, dbCompany.code, new Date(), 'SUCCESS']);
            } else {
              updateLog.push([company.incorporationcode, dbCompany.code, new Date(), 'ERROR: NOT FOUND']);
            }
            
          } else {
            updateLog.push([company.incorporationcode, 'NO PORTAL COMPANY', new Date(), 'ERROR: NOT FOUND']);
          }
        } catch (error) {
          console.error('Error:', error);
          updateLog.push([company.incorporationcode, company.incorporationcode, new Date(), 'ERROR UPDATING']);
        }
      }
    }


    // create entities bo
    console.log("companies updated ", updateLog.length - 1);

    const filename = 'active_ar_company_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
    const logWorkbook = xlsx.utils.book_new();
    const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

    xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'companies ' + moment.utc().format('YYYY-MM-DD'));
    xlsx.writeFile(logWorkbook, filename);

    return { "success": true, "totalRows": updateLog.length - 1 };
  } catch (e) {
    console.log("Error ", e);
    return { "success": false, error: e };
  }
}


runScript();
{{#each data.changeLogs}}
    <tr>
        <td>
            {{#formatDate createdAt "MM/DD/YYYY HH:mm:ss"}} {{/formatDate }}
        </td>
        <td>
            {{modifiedBy}}
        </td>
        <td>
            {{modifiedValuesLength}}
        </td>
        <td>
            <button id="btn-{{_id}}" type="button" class="btn btn-sm btn-secondary text-white  text-center displayCompanyLogDetails"
                 onclick="getLogDetails('{{../data.documentId}}','{{_id}}')">
                <i class="fa fa-search mr-2" aria-hidden="true"></i> View log
            </button>
        </td>
    </tr>
{{else}}
    <tr>
        <td colspan="4">
            Logs not found
        </td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
{{/each}}

<main>
    <div class="container-fluid">
        <div class="card ml-2">
            <div class="card-body">
                <div class="row justify-content-center">
                    <div class="col-12">
                        <h1>{{title}}</h1>
                        <form method="POST" id="submitForm">
                            <div class="row">
                                <div class="col-12 col-md-3">
                                    <label for="filter_company">Company</label>
                                    <input class='form-control' type='text' name='filter_company' id='filter_company'  value="{{filters.filter_company}}"/>
                                </div>

                                <div class="col-12 col-md-6">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label>Show Substance Module Active?</label>
                                            <div class="custom-control custom-radio">
                                                <input type="radio" class="custom-control-input" id="filter_substance_module_yes" name="filter_substance_module"
                                                    value="true" {{#ifEquals filters.filter_substance_module 'true'}} checked {{/ifEquals}}>
                                                <label class="custom-control-label" for="filter_substance_module_yes">Yes</label>
                                            </div>
                                            <div class="custom-control custom-radio">
                                                <input type="radio" class="custom-control-input" id="filter_substance_module_no" name="filter_substance_module"
                                                    value="false"  {{#ifEquals filters.filter_substance_module 'false'}} checked {{/ifEquals}}>
                                                <label class="custom-control-label" for="filter_substance_module_no">No</label>
                                            </div>
                                            <div class="custom-control custom-radio">
                                                <input type="radio" class="custom-control-input" id="filter_substance_module_all" name="filter_substance_module"
                                                    value="" {{#unless filters.filter_substance_module }} checked {{/unless}}>
                                                <label class="custom-control-label" for="filter_substance_module_all">All</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <label>Show Accounting Module Active?</label>
                                            <div class="custom-control custom-radio">
                                                <input type="radio" class="custom-control-input" id="filter_accounting_module_yes" name="filter_accounting_module"
                                                    value="true"  {{#ifEquals filters.filter_accounting_module 'true'}} checked {{/ifEquals}}>
                                                <label class="custom-control-label" for="filter_accounting_module_yes">Yes</label>
                                            </div>
                                            <div class="custom-control custom-radio">
                                                <input type="radio" class="custom-control-input" id="filter_accounting_module_no" name="filter_accounting_module"
                                                    value="false"  {{#ifEquals filters.filter_accounting_module 'false'}} checked {{/ifEquals}}>
                                                <label class="custom-control-label" for="filter_accounting_module_no">No</label>
                                            </div>
                                            <div class="custom-control custom-radio">
                                                <input type="radio" class="custom-control-input" id="filter_accounting_module_all" name="filter_accounting_module"
                                                    value="" {{#unless filters.filter_accounting_module }} checked {{/unless}}>
                                                <label class="custom-control-label" for="filter_accounting_module_all">All</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <label>Show Director/BO Module Active?</label>
                                            <div class="custom-control custom-radio">
                                                <input type="radio" class="custom-control-input" id="filter_dirbo_module_yes" name="filter_dirbo_module"
                                                    value="true" {{#ifEquals filters.filter_dirbo_module 'true'}} checked {{/ifEquals}}>
                                                <label class="custom-control-label" for="filter_dirbo_module_yes">Yes</label>
                                            </div>
                                            <div class="custom-control custom-radio">
                                                <input type="radio" class="custom-control-input" id="filter_dirbo_module_no" name="filter_dirbo_module"
                                                    value="false" {{#ifEquals filters.filter_dirbo_module 'false'}} checked {{/ifEquals}}>
                                                <label class="custom-control-label" for="filter_dirbo_module_no">No</label>
                                            </div>
                                            <div class="custom-control custom-radio">
                                                <input type="radio" class="custom-control-input" id="filter_dirbo_module_all" name="filter_dirbo_module"
                                                    value="" {{#unless filters.filter_dirbo_module }} checked {{/unless}}>
                                                <label class="custom-control-label" for="filter_dirbo_module_all">All</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-md-3" style="padding-top:25px">
                                    <button type='submit' class='btn btn-primary btn-sm waves-effect' >Search</button>
                                </div>
                            </div>
                        </form>
                        <br /><br />
                        <table id="scroll-horizontal-datatable" class="table w-100 nowrap">
                            <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>Name</th>
                                    <th>File Review Name</th>
                                    <th>Address</th>
                                    <th>Code</th>
                                    <th>Incorporation Code</th>
                                    <th>Incorporation Date</th>
                                    <th>Master Client Code</th>
                                    <th>Referral Office</th>
                                    <th>Show Substance Module</th>
                                    <th>Show Accounting Module</th>
                                    <th>Show Dir/BO Module</th>
                                    <th>Has ITA date?</th>
                                    <th>Approved start date</th>
                                    <th>Approved end date</th>
                                    <th>Application date</th>
                                    <th></th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each result}}
                                <tr>
                                    <td>{{_id}}</td>
                                    <td>{{name}}</td>
                                    <td>{{fileReviewName}}</td>
                                    <td>{{address}}</td>
                                    <td>{{code}}</td>
                                    <td>{{incorporationcode}}</td>
                                    <td>{{formatDate incorporationdate ../STANDARD_DATE_FORMAT}}</td>
                                    <td>{{masterclientcode}}</td>
                                    <td>{{referral_office}}</td>
                                    <td>{{#if substanceModule.active}}Yes{{else}}No{{/if}}</td>
                                    <td>{{#if accountingRecordsModule.active}}Yes{{else}}No{{/if}}</td>
                                    <td>{{#if dirboModule.active}}Yes{{else}}No{{/if}}</td>
                                    <td>{{#if hasITADate}}Yes {{else}} No{{/if}}</td>
                                    <td>
                                        {{#if hasITADate}}
                                        {{formatDate approvedITAStartDate ../STANDARD_DATE_FORMAT}}
                                        {{/if}}
                                    </td>
                                    <td>
                                        {{#if hasITADate}}
                                        {{formatDate approvedITAEndDate ../STANDARD_DATE_FORMAT}}
                                        {{/if}}
                                    </td>
                                    <td>
                                        {{#if hasITADate}}
                                        {{formatDate applicationITADate ../STANDARD_DATE_FORMAT}}
                                        {{/if}}
                                    </td>
                                    <td>
                                        <input type="button" class="btn btn-primary waves-effect waves-light" id="editModal"
                                            value="Edit">
                                        {{#ifCond ../authentication.isClientManagementSuperUser '||' ../authentication.isSubsSuperUser}}
                                            <button type="button" id="editNameBtn-{{_id}}" class="btn btn-primary"
                                                data-name="{{name}}" data-code="{{code}}"
                                                onclick="event.stopPropagation(); openEditName('{{_id}}')">Edit
                                                Name</button>
                                        {{/ifCond}}
                                    </td>
                                    <td>
                                        {{#ifCond ../authentication.isClientManagementSuperUser '||' ../authentication.isSubsSuperUser}}
                                            <button class="btn btn-primary px-3 waves-effect waves-light border-white rounded companyLogsBtn"
                                                data-toggle="modal" 
                                                data-target="#companyChangeLogsModal"
                                                data-company-id="{{_id}}">
                                                View Change Logs
                                            </button>
                                        {{/ifCond}}
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                </div>
                <br>
                <div class="row justify-content-center">
                    <div class="col-12">
                        <div class="row">
                            <div class="mb-3 ml-2">
                                <a href='/client-management/'
                                    class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal hide fade edit-modal-lg" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel"
        id="edit_modal" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="myLargeModalLabel">Edit Company</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                </div>
                <div class="modal-body">
                    <div class="col-12">
                        <div class="row ">
                            <div class="col-12 col-lg-4">
                                <label for="editReferralOffice">Referral Office</label>
                                <input type="text" class="form-control mr-2" name="editReferralOffice"
                                    id="editReferralOffice" {{#unless
                                    authentication.isClientManagementSuperUser}} disabled style="background-color: #dddddd !important;"
                                    {{/unless}}>
                            </div>

                            <div class="col-12 col-lg-4">
                                <label for="editMasterClientCode">Master Client Code</label>
                                <input type="text" class="form-control mr-2" name="editMasterClientCode"
                                    id="editMasterClientCode" {{#unless
                                    authentication.isClientManagementSuperUser}} disabled style="background-color: #dddddd !important;"
                                    {{/unless}}>
                            </div>

                            <div class="col-12 col-lg-4">
                                <label for="editIncorporationDate">Incorporation Date</label>
                                <input type="text" class="form-control mr-2" name="editIncorporationDate"
                                    id="editIncorporationDate" placeholder="MM/DD/YYYY" {{#if
                                    disallowChangeIncorporationDate}} disabled style="background-color: #dddddd !important;"
                                    {{/if}}>
                            </div>
                        </div>
                        <br>
                        <div class="row ">
                            {{#ifCond authentication.isClientManagementSuperUser '||' authentication.isSubsSuperUser}}
                            <div class="col-12 col-md-6 col-lg-3 mb-2">
                                <label>Substance Module</label>
                                <div class="custom-control custom-radio">
                                    <input type="radio" class="custom-control-input" id="editSubstanceModuleYes"
                                        name="editSubstanceModule" value="true">
                                    <label class="custom-control-label" for="editSubstanceModuleYes">Yes</label>
                                </div>
                                <div class="custom-control custom-radio">
                                    <input type="radio" class="custom-control-input" id="editSubstanceModuleNo"
                                        name="editSubstanceModule" value="false">
                                    <label class="custom-control-label" for="editSubstanceModuleNo">No</label>
                                </div>
                            </div>
                            {{/ifCond}}
                            {{#if authentication.isAccountingSuperUser}}
                            <div class="col-12 col-md-6 col-lg-3 mb-2">
                                <label>Accouting Module</label>
                                <div class="custom-control custom-radio">
                                    <input type="radio" class="custom-control-input" id="editAccountingModuleYes"
                                        name="editAccountingModule" value="true">
                                    <label class="custom-control-label" for="editAccountingModuleYes">Yes</label>
                                </div>
                                <div class="custom-control custom-radio">
                                    <input type="radio" class="custom-control-input" id="editAccountingModuleNo"
                                        name="editAccountingModule" value="false">
                                    <label class="custom-control-label" for="editAccountingModuleNo">No</label>
                                </div>
                            </div>
                            {{/if}}

                            {{#if authentication.isDirBoImportManager}}
                            <div class="col-12 col-md-6 col-lg-3 mb-2">
                                <label>Directors/BO Module</label>
                                <div class="custom-control custom-radio">
                                    <input type="radio" class="custom-control-input" id="editDirboModuleYes"
                                        name="editDirboModule" value="true">
                                    <label class="custom-control-label" for="editDirboModuleYes">Yes</label>
                                </div>
                                <div class="custom-control custom-radio">
                                    <input type="radio" class="custom-control-input" id="editDirboModuleNo"
                                        name="editDirboModule" value="false">
                                    <label class="custom-control-label" for="editDirboModuleNo">No</label>
                                </div>
                            </div>
                            {{/if}}

                            <div class="col-12 col-md-6 col-lg-3 mb-2">
                                <label>Has ITA date?</label>
                                <div class="custom-control custom-radio">
                                    <input type="radio" class="custom-control-input" id="editHasITADateYes"
                                        name="editHasITADate" value="true" {{#unless
                                    authentication.isSubsSuperUser}} disabled
                                    style="background-color: #dddddd !important;" {{/unless}}>
                                    <label class="custom-control-label" for="editHasITADateYes">Yes</label>
                                </div>
                                <div class="custom-control custom-radio">
                                    <input type="radio" class="custom-control-input" id="editHasITADateNo"
                                        name="editHasITADate" value="false" {{#unless
                                    authentication.isSubsSuperUser}} disabled
                                    style="background-color: #dddddd !important;" {{/unless}}>
                                    <label class="custom-control-label" for="editHasITADateNo">No</label>
                                </div>
                            </div>
                        </div>


                        <div class="row" id="hasITADateYesRow" style="display: none">
                            <div class="col-12 col-lg-4 ">
                                <label for="editApplicationDate">Application date:</label>
                                <input type="text" class="form-control" name="editApplicationDate" id="editApplicationDate"
                                    placeholder="MM/DD/YYYY" {{#unless authentication.isSubsSuperUser}} disabled
                                    style="background-color: #dddddd !important;" {{/unless}}>
                            </div>
                            <div class="col-12 col-6 col-lg-4">
                                <label for="editApprovedStartDate">Approved start date:</label>
                                <input type="text" class="form-control mr-2" name="editApprovedStartDate"
                                    id="editApprovedStartDate" placeholder="MM/DD/YYYY" {{#unless
                                    authentication.isSubsSuperUser}} disabled style="background-color: #dddddd !important;"
                                    {{/unless}}>
                            </div>
                            <div class="col-12 col-6 col-lg-4">
                                <label for="editApprovedEndDate">Approved end date:</label>
                                <input type="text" class="form-control" name="editApprovedEndDate" id="editApprovedEndDate"
                                    placeholder="MM/DD/YYYY" {{#unless authentication.isSubsSuperUser}} disabled
                                    style="background-color: #dddddd !important;" {{/unless}}>
                            </div>
                        </div>

                        <div id="yearlyFee">
                            <div class="row">
                                <div class="col-12">
                                    <label for="YearlyFee">Yearly fee (paid):</label>
                                </div>
                            </div>
                            <div class="row ">
                                <div class="col-12 d-flex">
                                    {{#each paymentYearsList}}
                                    <div class="mr-2">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" name="paymentYears" class="custom-control-input"
                                                value="{{this}}" id="payment-year-{{this}}" {{#if
                                                authentication.isClientManagementSuperUser}} disabled style="background-color: #dddddd !important;"
                                                {{/if}}/>
                                            <label class="custom-control-label "
                                                for="payment-year-{{this}}">{{this}}</label>
                                        </div>
                                    </div>


                                    {{/each}}
                                </div>

                            </div>
                        </div>


                    </div>
                </div><!-- /.modal-content -->
                <div class="modal-footer">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="saveButton"
                                onclick="saveCompany()">Save</button>
                        </div>
                    </div>
                </div>
            </div><!-- /.modal-dialog -->
        </div>
    </div>
    <div class="modal hide fade edit-name-modal-lg" tabindex="-1" role="dialog" aria-labelledby="editNameLabel"
        id="edit_name_modal" style="display: none;" aria-hidden="true">
        <div class="modal-dialog modal-md modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="editNameLabel">Edit Company Name</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                </div>
                <div class="modal-body">
                    <div class="col-12">
                        <div class="row mt-2">
                            <div class="col-12">
                                <label>Company Name</label>
                                <input type="text" class="form-control mr-2" name="editCompanyName" id="editCompanyName">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <label>Please check which name you want to change:</label>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" name="nameChanges" class="custom-control-input"
                                        id="nameChangesClients" />
                                    <label class="custom-control-label" for="nameChangesClients">File Review clients</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" name="nameChanges" class="custom-control-input"
                                        id="nameChangesBeneficialOwners" />
                                    <label class="custom-control-label" for="nameChangesBeneficialOwners">File Review
                                        relations</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" name="nameChanges" class="custom-control-input"
                                        id="nameChangesRequestedFiles" />
                                    <label class="custom-control-label" for="nameChangesRequestedFiles">File Review
                                        requested Files</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" name="nameChanges" class="custom-control-input"
                                        id="nameChangesSubmitted" />
                                    <label class="custom-control-label" for="nameChangesSubmitted">Substance Submitted
                                        (History)</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" name="nameChanges" class="custom-control-input"
                                        id="nameChangesPending" />
                                    <label class="custom-control-label" for="nameChangesPending">Substance Pending (Current
                                        + Future)</label>
                                </div>
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" name="nameChanges" class="custom-control-input"
                                        id="nameChangesAccounting" />
                                    <label class="custom-control-label" for="nameChangesAccounting">Accounting Records</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div><!-- /.modal-content -->
                <div class="modal-footer">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="saveNameButton"
                                onclick="saveCompanyName()">Save</button>
                        </div>
                    </div>
                </div>
            </div><!-- /.modal-dialog -->
        </div>
    </div>
    {{>client-management/modals/show-company-change-logs}}
</main>



<script type="text/javascript">
    let table;
    let companyId;
    let companyCode;

    $(document).ready(function () {

        table = $("#scroll-horizontal-datatable").DataTable({
            "pageLength": 50,
            "columnDefs": [{
              "visible": false,
                "targets": [0] }],
            "order": [[1, "asc"]],
            scrollX: !0,
            language: {
              paginate: {
                previous: "<i class='mdi mdi-chevron-left'>",
                  next: "<i class='mdi mdi-chevron-right'>" } },
            drawCallback: function () { $(".dataTables_paginate > .pagination").addClass("pagination-rounded") } });

        
        table.on('click', 'tr', function (event) {
            if ($(event.target).is("button")) {
                return;
            }
            const rowdata = table.rows(this).data()[0];

            const docName = new DOMParser().parseFromString(rowdata[1], "text/html");
            companyId = rowdata[0];
            companyCode = rowdata[4]; //note this is different index than normal unapproved company search page.

            $.ajax({
                type: 'GET',
                url: './search-companies/' + companyId + "/details",
                timeout: 5000,
                success: (data) => {
                    
                    $('#editIncorporationDate').datepicker( "destroy" );
                    $('#editIncorporationDate').removeClass("hasDatepicker");
                    $('#editApprovedStartDate').datepicker( "destroy" );
                    $('#editApprovedStartDate').removeClass("hasDatepicker");
                    $('#editApprovedEndDate').datepicker( "destroy" );
                    $('#editApprovedEndDate').removeClass("hasDatepicker");
                    $('#editApplicationDate').datepicker( "destroy" );
                    $('#editApplicationDate').removeClass("hasDatepicker");

                    const incorporationDate = data.company.incorporationdate;
                    $("#editIncorporationDate").val(incorporationDate);
                    $("#editIncorporationDate").datepicker({
                        format: 'mm/dd/yyyy',
                        autoclose: true, 
                        defaultDate:incorporationDate
                    }).on('hide', function (event) {
                        event.preventDefault();
                        event.stopPropagation();
                    }); 

                    $("#editMasterClientCode").val(data.company.masterclientcode);
                    $("#editReferralOffice").val(data.company.referral_office);
                    $("#editAmount").val(data.company.amount);

                   

                    if(data.allowEditAccountingModule === true){
                        if (data.company.accountingRecordsModule?.active === true) {
                            $('#editAccountingModuleYes').prop('checked', true);
                        } else {
                            $('#editAccountingModuleNo').prop('checked', true);
                        }
                    }

                    if(data.allowEditDirboModule === true){
                        if (data.company.dirboModule?.active === true) {
                            $('#editDirboModuleYes').prop('checked', true);
                        } else {
                            $('#editDirboModuleNo').prop('checked', true);
                        }
                    }

                    if(data.allowActivatingSubstanceModule) {
                        if (!data.company.isDeleted && data.company.substanceModule?.active === true) {
                            $('#editSubstanceModuleYes').prop('checked', true);
                        } else {
                            $('#editSubstanceModuleNo').prop('checked', true);
                        }
                    } 
                    if ((data.authentication.isClientManagementSuperUser == false && data.authentication.isSubsSuperUser == false) || !data.allowActivatingSubstanceModule) {
                         $('#editSubstanceModuleYes').prop('disabled', true);
                         $('#editSubstanceModuleNo').prop('disabled', true);
                    }



                    const hasITADate = data.company.hasITADate ===true;
                    if (hasITADate){
                        const approvedStartDate = data.company.approvedITAStartDate;
                        const approvedEndDate = data.company.approvedITAEndDate;
                        const applicationITADate = data.company.applicationITADate;
                        $('#editHasITADateYes').prop('checked', true);
                        $("#editApprovedStartDate").val(approvedStartDate);
                        $("#editApprovedEndDate").val(approvedEndDate);
                        $("#editApplicationDate").val(applicationITADate);
                        $("#hasITADateYesRow").show()

                        $("#editApprovedStartDate").datepicker({
                            format: 'mm/dd/yyyy',
                            autoclose: true, 
                            defaultDate:approvedStartDate
                        }).on('hide', function (event) {
                            event.preventDefault();
                            event.stopPropagation();
                        }); 
                        $("#editApprovedEndDate").datepicker({
                            format: 'mm/dd/yyyy',
                            autoclose: true, 
                            defaultDate:approvedEndDate
                        }).on('hide', function (event) {
                            event.preventDefault();
                            event.stopPropagation();
                        }); 
                        $("#editApplicationDate").datepicker({
                            format: 'mm/dd/yyyy',
                            autoclose: true, 
                            defaultDate:applicationITADate
                        }).on('hide', function (event) {
                            event.preventDefault();
                            event.stopPropagation();
                        }); 
                        
                    }else{
                        $('#editHasITADateNo').prop('checked', true);
                        $("#editApprovedStartDate").val('');
                        $("#editApprovedEndDate").val('');
                        $("#editApplicationDate").val('');
                        $("#hasITADateYesRow").hide()
                        
                        $("#editApprovedStartDate").datepicker({
                            format: 'mm/dd/yyyy',
                            autoclose: true,
                            defaultDate:""
                        }).on('hide', function (event) {
                            event.preventDefault();
                            event.stopPropagation();
                        }); 
                        $("#editApprovedEndDate").datepicker({
                            format: 'mm/dd/yyyy',
                            autoclose: true ,
                            defaultDate:""
                        }).on('hide', function (event) {
                            event.preventDefault();
                            event.stopPropagation();
                        }); 
                        $("#editApplicationDate").datepicker({
                            format: 'mm/dd/yyyy',
                            autoclose: true ,
                            defaultDate:""
                        }).on('hide', function (event) {
                            event.preventDefault();
                            event.stopPropagation();
                        }); 
                        

                    }

                    // if no payment years, the UI needs cleared. found in testing checkmarks carry over from last edit unless page refresh
                    [2020,2021,2022,2023,2024].forEach((year) => { 
                        const checkboxYear = $("#payment-year-" +year); 
                        checkboxYear.prop('checked', false); 
                    });
                    if (data.company.paymentYears && data.company.paymentYears.length > 0){
                      data.company.paymentYears.forEach((year) => {
                            const checkboxYear = $("#payment-year-" +year);
                            if (checkboxYear && checkboxYear.val() === year){
                                checkboxYear.prop('checked', true);
                            } 
                      });
                    } 
 



                    $("#edit_modal").modal();
                },
                error: () => {
                    Swal.fire('Error', 'There was an error getting the company details', 'error');
                }
            });
        });

    });

    function openEditName(id) {
        const edtBtn = $("#editNameBtn-" + id);
        companyId = id;
        companyCode = edtBtn.data('code');
        $("input[name='nameChanges']").prop('checked', false);
        $('#editCompanyName').val( edtBtn.data('name'));
        $("#edit_name_modal").modal();
    }

    function saveCompany() {
        $('#saveButton').prop('disabled', true);

        const paymentYears = [];
        $('input[name="paymentYears"]:checked').each(function() {
            paymentYears.push(this.value);
        });




        $.ajax({
            type: "POST",
            url: "./search-companies/update",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({
                id: companyId,
                code: companyCode,
                masterclientcode: $("#editMasterClientCode").val(),
                referralOffice: $("#editReferralOffice").val(),
                activeSubstanceModule: $("input[name='editSubstanceModule']:checked").length ? 
                    $("input[name='editSubstanceModule']:checked").val() === 'true' : null,
                activeAccountingModule: $("input[name='editAccountingModule']").length ? 
                    $("input[name='editAccountingModule']:checked").val() === 'true' : null,
                activeDirBoModule:  $("input[name='editDirboModule']").length ? 
                    $("input[name='editDirboModule']:checked").val() === 'true' : null,
                incorporationDate:  $("#editIncorporationDate").val(),
                hasITADate:$("input[name='editHasITADate']:checked").val() === 'true',
                approvedStartDate: $("#editApprovedStartDate").val(),
                approvedEndDate: $("#editApprovedEndDate").val(),
                applicationITADate: $("#editApplicationDate").val(),
                paymentYears: paymentYears,
            }),
            success: function (data) {
                if (data.success) {
                    toastr.success('Company updated successfully');
                    $('#edit_modal').modal('hide');
                    window.setTimeout(function () {
                        document.location.reload();
                    }, 200)
                } else {
                    $('#saveButton').prop('disabled', false);
                    if (data.invalidCode) {
                        toastr["warning"]('The Master Client Code entered does not exist, please try another one.');
                    } else if(data.error){
                        toastr["warning"](data.error);
                    } else {
                        toastr["warning"]('Sorry, there was an error updating the Company.');
                    }
                }
            }
        });
    }

    function saveCompanyName() {
        $('#saveNameButton').prop('disabled', true);

        const companyUpdate = {
            id: companyId,
            code: companyCode,
            name: $('#editCompanyName').val(),
            nameChangesClients: $("#nameChangesClients").is(':checked', true),
            nameChangesSubmitted: $("#nameChangesSubmitted").is(':checked', true),
            nameChangesPending: $("#nameChangesPending").is(':checked', true),
            nameChangesRequestedFiles: $("#nameChangesRequestedFiles").is(':checked', true),
            nameChangesBeneficialOwners: $("#nameChangesBeneficialOwners").is(':checked', true),
            nameChangesAccounting: $("#nameChangesAccounting").is(':checked', true)
        };


        if (companyUpdate.nameChangesSubmitted === true){
            Swal.fire({
                title: 'Are you sure?',
                text: "You have selected historical substance submissions. This will affect already submitted and exported submissions.",
                type: 'warning',
                backdrop: true,
                showCancelButton: true,
                cancelButtonColor: "#6c757d",
                confirmButtonColor: "#0081B4",
                confirmButtonText: 'Yes, proceed!',
                reverseButtons: true,
                showLoaderOnConfirm: true,
                preConfirm(inputValue) {
                    return fetch('./search-companies/update-name', {
                        method: 'post',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify(companyUpdate)
                    }).then(response => {
                        try {
                            return response.json()
                        } catch (e) {
                            return {success: false, error: e}
                        }
                    }).catch(error => {
                        console.log(error);
                        return {success: false, error: error}
                    });
                },
                allowOutsideClick: () => !Swal.isLoading()
            }).then((result) => {
                if (result.value) {
                    swal.showLoading();
                    if (result.value.success === true) {
                        toastr.success('Company name updated for selection');
                        $('#edit_name_modal').modal('hide');
                        window.setTimeout(function () {
                            document.location.reload();
                        }, 200)
                    } else {
                        $('#saveNameButton').prop('disabled', false);
                        toastr["warning"]('Sorry, there was an error updating the company name.');
                    }
                }
                else{
                    $('#saveNameButton').prop('disabled', false);
                }

            })
        }else{
            $.ajax({
                type: "POST",
                url: "./search-companies/update-name",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(companyUpdate),
                success: function (data) {
                    if (data.success) {
                        toastr.success('Company name updated for selection');
                        $('#edit_name_modal').modal('hide');
                        window.setTimeout(function () {
                            document.location.reload();
                        }, 200)
                    } else {
                        $('#saveNameButton').prop('disabled', false);
                        toastr["warning"]('Sorry, there was an error updating the company name.');
                    }
                }
            });
        }


    }

    $("input[name='editHasITADate']").on('change', function () {
        const val = $("input[name='editHasITADate']:checked").val() === 'true';

        if (val){
          $("#hasITADateYesRow").show();
        }else{
            $("#hasITADateYesRow").hide();
        }
    })


</script>

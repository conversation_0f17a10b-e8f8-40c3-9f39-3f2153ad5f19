const express = require('express');
const router = express.Router();

/* GET home page. */
router.get('/', ensureAuthenticated, function (req, res) {
    res.render('index', {
        title: 'Trident Client Management Tool',
        user: req.session.user,
        authentication: req.session.authentication,
        isFilewReviewer: (req.session.authentication.isFileReviewer || 
            req.session.authentication.isCompliance ||
            req.session.authentication.isQualityAssurance ||
            req.session.authentication.isFileObserver
        ),
        isClientManagement: (req.session.authentication.isSubsManagers || req.session.authentication.isSubsSuperUser || req.session.authentication.isClientManagementSuperUser),
        hasDirectorAccess: req.session.productionOfficeGroups?.length > 0 || req.session.authentication.isDirBoImportManager === true,
        isAccountingUser: req.session.authentication.isAccountingAccountant ||
            req.session.authentication.isAccountingManager ||
            req.session.authentication.isAccountingStandard ||
            req.session.authentication.isAccountingSuperUser ||
            req.session.authentication.isAccountingProductOwner
    });
});

/*
router.get('/roles', ensureAuthenticated, function (req, res) {
    
    res.render('roles', {
        title: 'Trident Client Management Tool',
        user: req.session.user,
        authentication: req.session.authentication
    });
});

router.post('/roles', ensureAuthenticated, function (req, res) {
    req.session.authentication.isSubsManagers = (req.body.cbCSubstanceManager == "1");
    req.session.authentication.isSubsSuperUser = (req.body.cbSubstanceSuperUser == "1");
    req.session.authentication.isClientManagementSuperUser = (req.body.cbClientManagementSuperUser == "1");
    req.session.authentication.isAnnouncementManager = (req.body.cbAnnouncementManager == "1");
    res.render('roles', {
        title: 'Trident Client Management Tool',
        user: req.session.user,
        authentication: req.session.authentication
    });
});
*/

function ensureAuthenticated(req, res, next) {

    if (req.session.is_authenticated ) {
        if (
            req.session.authentication.isCompliance ||
            req.session.authentication.isQualityAssurance ||
            req.session.authentication.isFileObserver ||
            req.session.authentication.isFileReviewer ||
            req.session.authentication.isIncorporationOfficer ||
            req.session.authentication.isIncorporationSuperAdmin ||
            req.session.authentication.isAnnouncementManager ||
            req.session.authentication.isSubsManagers ||
            req.session.authentication.isSubsSuperUser ||
            req.session.authentication.isDirBoImportManager ||
            req.session.authentication.isAccountingAccountant ||
            req.session.authentication.isAccountingManager ||
            req.session.authentication.isAccountingStandard ||
            req.session.authentication.isAccountingSuperUser ||
            req.session.authentication.isAccountingProductOwner ||
            req.session.authentication.isClientManagementSuperUser
        ) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

module.exports = router;

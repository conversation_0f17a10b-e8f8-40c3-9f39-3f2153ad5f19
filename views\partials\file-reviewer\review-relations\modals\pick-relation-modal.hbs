<!-- OPEN OWNER MODAL -->
<div class="modal fade" id="pickRelationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modal-relation-title" class="modal-title">Relation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-relation-body" class="modal-body p-3">
                <form action="" class="form" id="pickForm">
                    <div class="row">
                        <label class="col-form-label col-sm-6 pt-0">Please select the position(s) for this relation:</label>
                        <div class="col-sm-6">
                            <div class="custom-control custom-checkbox" id="beneficialGroup2">
                                <input
                                        type="checkbox"
                                        class="custom-control-input"
                                        name="relationGroup[]"
                                        id="relationGroup1"
                                        value="beneficial"
                                />
                                <label class="custom-control-label" for="relationGroup1">Beneficial Owner</label>
                            </div>
                            <div class="custom-control custom-checkbox" id="shareholderGroup2">

                                <input
                                        type="checkbox"
                                        class="custom-control-input"
                                        name="relationGroup[]"
                                        id="relationGroup2"
                                        value="shareholder"
                                />
                                <label class="custom-control-label" for="relationGroup2">Shareholder</label>
                            </div>
                            <div class="custom-control custom-checkbox" id="directorGroup2">
                                <input
                                        type="checkbox"
                                        class="custom-control-input"
                                        name="relationGroup[]"
                                        id="relationGroup3"
                                        value="director"
                                />
                                <label class="custom-control-label" for="relationGroup3">Director</label>
                                <div class="invalid-feedback">
                                    You must select at least one position.
                                </div>
                            </div>

                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

                <button class="btn solid royal-blue" id="submitPickRelation" type="submit" form="pickForm" name="submit" value="Submit">Save</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="/templates/peekrelation.precompiled.js"></script>
<script type="text/javascript">
    let reviewId;
    let relationId;
    let relationType;
    $('#pickRelationModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        reviewId = button.data('review-id');
        relationId = button.data('relation-id');
        relationType = button.data('type');
        const positionGroups =  button.data('position-groups');
        console.log(positionGroups);
        const beneficialCheck = $('#beneficialGroup2');
        const shareholderCheck = $('#shareholderGroup2');
        const directorCheck = $('#directorGroup2');
        
        beneficialCheck.show();
        shareholderCheck.show();
        directorCheck.show();

        if (positionGroups){
            if (positionGroups.includes("beneficial")){
                beneficialCheck.hide();
            }

            if (positionGroups.includes("shareholder")){
                shareholderCheck.hide();
            }

            if (positionGroups.includes("director")){
                directorCheck.hide();
            }

        }

    });

    $('input[name="relationGroup[]"]').on('change', function () {
        if ($('input[name="relationGroup[]"]:checked').length > 0) {
            $('input[name="relationGroup[]"]').removeClass('is-invalid')
        }
    });

    $('#pickForm').submit( function (event) {
        const submitBtn = $("#submitPickRelation");
        submitBtn.prop('disabled', true);
        event.preventDefault();

        if ($('input[name="relationGroup[]"]:checked').length === 0) {
            $('input[name="relationGroup[]"]').addClass("is-invalid");
            setTimeout(function(){submitBtn.prop('disabled', false); }, 0);
            return false;
        }

        if (!relationType || relationType === "client"){
            relationType = "client";
        }
        // existing
        $.ajax({
            url: '/file-reviewer/reviews/' + reviewId +'/pick-relation/'+ relationType+ '/' + relationId,
            type: 'POST',
            timeout: 3000,
            data: $(this).serialize(),
            success: function () {
                location.href = '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
            },
            error: function (e) {
              console.log(e);
                Swal.fire('Error', 'There was an error creating the beneficial owner', 'error').then(
                        () => {
                            location.href = '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
                        }
                );
            },
        });
        return false;
    });

    $('#pickRelationModal').on('hidden.bs.modal', function (event) {
        const beneficialCheck = $('#relationGroup1');
        const shareholderCheck = $('#relationGroup2');
        const directorCheck = $('#relationGroup3');

        beneficialCheck.show();
        beneficialCheck.prop('checked', false);
        shareholderCheck.show();
        shareholderCheck.prop('checked', false);
        directorCheck.show();
        directorCheck.prop('checked', false);
    });


</script>

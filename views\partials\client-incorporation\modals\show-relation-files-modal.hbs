<div class="modal fade" id="showIncorporationRelationFilesModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable " role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modal-relation-title" class="modal-title">Show Relation Files</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-relation-body" class="modal-body p-3">
                <div class="row">
                    <div class="col-md-12" >
                        <h4>Uploaded Files</h4>
                        <br>
                        <div id="incorporation-relation-files-body">
                            <p style='text-align: center'>Searching files...</p>
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="/templates/incorporationfilestable.precompiled.js"></script>
<script type="text/javascript">

    $('#showIncorporationRelationFilesModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        let incorporationId = button.data('incorporation-id');
        const relationId = button.data('relation-id');
        $.ajax({
            type: 'GET',
            url: '/client-incorporation/' + incorporationId+'/relations/' + relationId+ '/file-list',
            timeout: 5000,
            success: (data) => {
                if (data.status !== 200){
                    toastr["warning"](data.message);
                }
                else{
                    let template = Handlebars.templates.incorporationfilestable;
                    let d = {
                        incorporationId: incorporationId,
                        relationId: relationId,
                        files: data.files ? data.files : [],
                    };
                    let html = template(d);
                    $('#incorporation-relation-files-body').html(html);

                }
            },
            error: (err) => {
                console.log(err);
                Swal.fire('Error', 'There was an error downloading the file', 'error').then(()=>{
                    $('#showIncorporationRelationFilesModal').modal('hide');
                });

            },
        });
    });

    $('#showIncorporationRelationFilesModal').on('hide.bs.modal', function (event) {
        $("#incorporation-relation-files-body").html("<p style='text-align: center'>Searching files...</p>");
    });

</script>

const MasterClientModel = require("../models/masterClientCode");
const xlsx = require('xlsx');
const moment = require('moment');
const dotenv = require('dotenv');
dotenv.config();

const mongoose = require('mongoose');

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const result = await removeOwners();

    console.log('Script executed successfully', result);
  } catch (error) {
    console.error('Error in the script:', error);
  } finally {
    mongoose.disconnect();
  }
}


async function removeOwners() {
  let updateLog = [['ID', 'MC Code', 'Update']];
  const ownerToRemove = "<EMAIL>";
  //const ownerToRemove = "<EMAIL>";
  try {

    
    

    // Get submissions with accounting records module active
    let results = await MasterClientModel.find({owners:ownerToRemove});


    console.log(`Total masterclients ${results.length}`);

    for (let index = 0; index < results.length; index++) {
      const masterclient = results[index];
      try {
      

      console.log(`Start processing (${index}) - masterclient ${masterclient.code}`);
      
      

      let owners = masterclient.owners;
      
      owners.splice(owners.indexOf(ownerToRemove), 1);
           

      let result = await MasterClientModel.findByIdAndUpdate(masterclient._id, {
        $set: { "owners": owners}          
      });
         
      if (result) {
        updateLog.push([masterclient._id?.toString(), masterclient.code, 'REMOVED: ' +ownerToRemove ]);          
      } 
      else {
        updateLog.push([masterclient._id?.toString(), masterclient.code, 'ERROR: NOT FOUND']);
      }   

      
          
      } catch  (error) {
        console.log(error)
        updateLog.push([masterclient._id?.toString(), masterclient.code, 'ERROR ' + error.message]);
      }
    }
  } catch (error) {
    console.error('Error:', error.message);
    updateLog.push(["XXX", "XXX", 'ERROR UPDATING' + error.message]);
  }


  // create entities bo
  console.log("masterclients updated ", updateLog.length - 1);

  const filename = 'remove_owner_from_masterclient_log_' + moment.utc().format('YYYY_MM_DD_hh_mm_ss') + '.xlsx';
  const logWorkbook = xlsx.utils.book_new();
  const logWorksheet1 = xlsx.utils.aoa_to_sheet(updateLog);

  xlsx.utils.book_append_sheet(logWorkbook, logWorksheet1, 'companies ' + moment.utc().format('YYYY-MM-DD'));
  xlsx.writeFile(logWorkbook, filename);

  return { "success": true, "totalRows": updateLog.length - 1 };
}






runScript();
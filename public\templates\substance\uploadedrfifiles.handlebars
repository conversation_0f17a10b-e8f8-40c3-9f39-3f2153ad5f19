{{#if files}}
    <div class="table-responsive">
        <table class="table table-striped mb-0">
            <thead>
            <tr>
                <th>File name</th>
                <th style="width: 20%">Download</th>
                <th style="width: 150px">Delete File</th>
            </tr>
            </thead>
            <tbody>
            {{#each files}}
                <tr>
                    <td>
                        {{#if originalName}}
                            {{originalName}}
                        {{else}}
                            {{originalname}}
                        {{/if}}
                    </td>
                    <td>
                        {{#if ../requestId}}
                            <a type="button" class="btn btn-xs btn-primary waves-effect waves-light border-white" target="_blank" href="/substance/{{../entryId}}/download/{{../requestId}}/{{fileId}}">
                                Download</a>
                        {{else}}
                            <a type="button" class="btn btn-xs btn-primary waves-effect waves-light border-white" target="_blank" href="/substance/{{../entryId}}/download/false/{{fileId}}">
                                Download</a>
                        {{/if}}

                    </td>
                    <td>
                        <button class="demo-delete-row btn btn-danger btn-xs btn-icon"
                                onclick="deleteRfiFile('{{../entryId}}', '{{../requestId}}', '{{fileId}}', '{{ originalname }}');return false"><i class="fa fa-times"></i>
                        </button>
                    </td>
                </tr>
            {{/each}}
            </tbody>
        </table>
    </div>
{{/if}}

<!-- OPEN OWNER MODAL -->
<div class="modal fade" id="openPositionListModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Positions</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-position-list-body" class="modal-body p-3">
                <p class="text-muted text-center">LOADING...</p>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="/templates/positionlistmodal.precompiled.js"></script>
<script type="text/javascript">

    $('#openPositionListModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        let reviewId = button.data('review-id');
        const openMode = button.data('open-mode');
        let organizationId = button.data('organization-id');
        let relationType = button.data('type');
        let relationGroup = button.data('group');

        $.ajax({
            type: 'GET',
            url: '/file-reviewer/peek-relation/' + reviewId,
            data: { group: relationGroup, type: relationType, relationId: organizationId },
            timeout: 5000,
            success: function (data) {
                if (data.success){
                    let template = Handlebars.templates.positionlistmodal;
                    let d = {
                        organizationId: data.relation._id,
                        positions: data.relation.positions,
                        reviewId: reviewId,
                        openMode: openMode
                    };
                    let html = template(d);
                    $('#modal-position-list-body').html(html);
                }
                else{
                    Swal.fire('Error','There was an error while trying to fetch the positions for the relation','error')
                            .then(() => {$('#openRelationModal').modal('hide');});
                }

            },
            error: function () {
                Swal.fire('Error','There was an error while trying to fetch the positions for the relation','error')
                        .then(() => {$('#openRelationModal').modal('hide');
                });
            },
        });
    });
</script>

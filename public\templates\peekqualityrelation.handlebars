<form id="qualityRelationForm" action="">
    {{#ifEquals relation.type 'natural'}}
        <!-- PEP DETAILS -->
        {{#if relation.pep}}
            <div>
                <div class="alert alert-warning" role="alert">This person is a PEP</div>
                <p>PEP DETAILS
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        {{#if relationInformation.pepDetails.complete}}
                            <span class="badge badge-success">Complete</span>
                        {{else}}
                            <span class="badge badge-warning text-dark">Incomplete</span>
                        {{/if}}
                    {{/ifCond}}
                </p>
                <div class="row">
                    <div class="col-2">PEP Information:</div>
                    <div class="col-10 font-weight-bold">{{relation.pepDetails.information}}</div>
                    <div class="col-2">Addtional news check completed?</div>
                    <div class="col-10 font-weight-bold">{{#if relation.pepDetails.confirmAdditionalComments}}
                        Yes {{else}} No{{/if}}</div>
                    <div class="col-2">News Comments:</div>
                    <div class="col-10 font-weight-bold">{{relation.pepDetails.additionalComments}}</div>
                </div>
                <!-- CHECK VALIDATE -->
                <div class="row">
                    <div class="col-12 d-flex justify-content-end">
                        {{#ifCond relation.lockedByFileReview '==' reviewId}}
                            <div class="custom-control custom-checkbox">
                                <input
                                        type="checkbox"
                                        class="custom-control-input validateCheck"
                                        name="pepDetails[validated]"
                                        id="pepDetails[validated]-{{relationInformation.pepDetails.validated}}"
                                    {{#if relationInformation.pepDetails.validated}} checked {{/if}}
                                    {{#if onlyRead }} disabled {{/if}}
                                />
                                <label class="custom-control-label"
                                       for="pepDetails[validated]-{{relationInformation.pepDetails.validated}}"
                                >Validate</label
                                >
                            </div>
                        {{else}}
                            <i class="fa fa-lock"></i>
                        {{/ifCond}}
                    </div>
                </div>
                <hr class="mt-3">
            </div>

        {{/if}}
        <!-- PERSONAL DETAILS -->
        <div>
            <p>
                DETAILS
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    {{#if relationInformation.details.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                {{/ifCond}}
            </p>
            <div class="row">
                <div class="col-2">Full Name:</div>
                <div class="col-4 font-weight-bold">{{ relation.details.fullName }}</div>
                <div class="col-2">First Name:</div>
                <div class="col-4 font-weight-bold">{{ relation.details.firstName }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">Middle Name:</div>
                <div class="col-4 font-weight-bold">{{ relation.details.middleName }}</div>
                <div class="col-2">Last Name:</div>
                <div class="col-4 font-weight-bold">{{ relation.details.lastName }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">Occupation:</div>
                <div class="col-4 font-weight-bold">{{ relation.details.occupation }}</div>
                <div class="col-2">Date of Birth:</div>
                <div class="col-4 font-weight-bold">{{#formatDate relation.details.birthDate
                                                                  "MM/DD/YYYY"}} {{/formatDate }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">Nationality:</div>
                <div class="col-4 font-weight-bold">{{ relation.details.nationality }}</div>
                <div class="col-2">Country of Birth:</div>
                <div class="col-4 font-weight-bold">{{relation.details.countryBirth}}</div>
            </div>
            <!-- CHECK VALIDATE -->

            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input validateCheck"
                                    name="details[validated]"
                                    id="details[validated]-{{ relationInformation.details.validated}}"
                                {{#if relationInformation.details.validated}} checked {{/if}}
                                {{#if onlyRead}} disabled {{/if}}
                            />
                            <label class="custom-control-label"
                                   for="details[validated]-{{ relationInformation.details.validated}}"
                            >Validate</label
                            >
                        </div>
                    {{else}}
                        <i class="fa fa-lock"></i>
                    {{/ifCond}}
                </div>
            </div>
            <hr class="mt-3"/>
        </div>

        <!-- IDENTIFICATION DETAILS -->
        <div>
            <p>
                IDENTIFICATION
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    {{#if relationInformation.identification.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                {{/ifCond}}
            </p>
            <div class="row">
                <div class="col-4">
                    <div class="row">
                        <div class="col-4">Type of Identification:</div>
                        <div class="col-8 font-weight-bold"
                             style="text-transform: capitalize;">{{ relation.identification.identificationType }}</div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="row">
                        <div class="col-4">Country of Issue:</div>
                        <div class="col-8 font-weight-bold">{{ relation.identification.issueCountry }}</div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="row">
                        <div class="col-4">Expiry Date:</div>
                        <div class="col-8 font-weight-bold">
                            {{#formatDate relation.identification.expiryDate "MM/DD/YYYY"}} {{/formatDate }}</div>
                    </div>
                </div>
            </div>

            <!-- CHECK VALIDATE -->
            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input validateCheck"
                                    name="identification[validated]"
                                    id="identification[validated]-{{ relationInformation.identification.validated}}"
                                {{#if relationInformation.identification.validated}} checked {{/if}}
                                {{#if onlyRead}} disabled {{/if}}
                            />
                            <label class="custom-control-label"
                                   for="identification[validated]-{{ relationInformation.identification.validated}}"
                            >Validate</label
                            >
                        </div>
                    {{else}}
                        <i class="fa fa-lock"></i>
                    {{/ifCond}}
                </div>
            </div>
            <hr class="mt-3"/>
        </div>

        <!-- PRINCIPAL ADDRESS DETAILS -->
        <div>
            <p>
                PRINCIPAL ADDRESS
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    {{#if relationInformation.principalAddress.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                {{/ifCond}}
            </p>
            <div class="row">
                <div class="col-2">Address - 1st Line:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.primaryAddress }}</div>
                <div class="col-2">Address - 2nd Line:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.secondaryAddress }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">Country:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.country }}</div>
                <div class="col-2">State:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.state }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">City:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.city }}</div>
                <div class="col-2">Postal Code:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.postalCode }}</div>
            </div>
            <!-- CHECK VALIDATE -->
            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input validateCheck"
                                    name="principalAddress[validated]"
                                    id="principalAddress[validated]-{{ relationInformation.principalAddress.validated}}"
                                {{#if relationInformation.principalAddress.validated}} checked {{/if}}
                                {{#if onlyRead}} disabled {{/if}}
                            />
                            <label class="custom-control-label"
                                   for="principalAddress[validated]-{{ relationInformation.principalAddress.validated}}"
                            >Validate</label
                            >
                        </div>
                    {{else}}
                        <i class="fa fa-lock"></i>
                    {{/ifCond}}
                </div>
            </div>
            <hr class="mt-3"/>
        </div>

    {{else}}
        <!-- ORGANIZATION DETAILS -->
        <div>
            <p>
                DETAILS
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    {{#if relationInformation.details.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                {{/ifCond}}
            </p>
            <div class="row">
                <div class="col-2">Organization Name:</div>
                <div class="col-4 font-weight-bold">{{ relation.details.organizationName }}</div>
                <div class="col-2">Incorporation / Formation Number:</div>
                <div class="col-4 font-weight-bold">{{ relation.details.incorporationNumber }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">Tax Residence:</div>
                <div class="col-4 font-weight-bold">{{ relation.details.taxResidence }}</div>
                <div class="col-2">Business Registration Number (if applicable):</div>
                <div class="col-4 font-weight-bold">{{ relation.details.businessNumber }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">Date of Incorporation:</div>
                <div class="col-4 font-weight-bold">{{#formatDate relation.details.incorporationDate
                                                                  "MM/DD/YYYY"}} {{/formatDate }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">Country of Incorporation:</div>
                <div class="col-4 font-weight-bold">{{relation.details.incorporationCountry }}</div>
            </div>

            <!-- CHECK VALIDATE -->
            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input validateCheck"
                                    name="details[validated]"
                                    id="details[validated]-{{ relationInformation.details.validated}}"
                                {{#if relationInformation.details.validated}} checked {{/if}}
                                {{#if onlyRead}} disabled {{/if}}
                            />
                            <label class="custom-control-label"
                                   for="details[validated]-{{ relationInformation.details.validated}}"
                            >Validate</label
                            >
                        </div>
                    {{else}}
                        <i class="fa fa-lock"></i>
                    {{/ifCond}}
                </div>
            </div>
            <hr class="mt-3"/>
        </div>
        <!-- PRINCIPAL ADDRESS DETAILS -->
        <div>
            <p>
                PRINCIPAL ADDRESS
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    {{#if relationInformation.principalAddress.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                {{/ifCond}}
            </p>
            <div class="row">
                <div class="col-2">Address - 1st Line:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.primaryAddress }}</div>
                <div class="col-2">Address - 2nd Line:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.secondaryAddress }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">Country:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.country }}</div>
                <div class="col-2">State:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.state }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">City:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.city }}</div>
                <div class="col-2">Postal Code:</div>
                <div class="col-4 font-weight-bold">{{ relation.principalAddress.postalCode }}</div>
            </div>
            <!-- CHECK VALIDATE -->
            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input validateCheck"
                                    name="principalAddress[validated]"
                                    id="principalAddress[validated]-{{ relationInformation.principalAddress.validated}}"
                                {{#if relationInformation.principalAddress.validated}} checked {{/if}}
                                {{#if onlyRead}} disabled {{/if}}
                            />
                            <label class="custom-control-label"
                                   for="principalAddress[validated]-{{ relationInformation.principalAddress.validated}}"
                            >Validate</label
                            >
                        </div>
                    {{else}}
                        <i class="fa fa-lock"></i>
                    {{/ifCond}}
                </div>
            </div>
            <hr class="mt-3"/>
        </div>

    {{/ifEquals}}

    <!-- MAILING ADDRESS DETAILS -->
    <div>
        <p>
            MAILING ADDRESS
            {{#ifCond relation.lockedByFileReview '==' reviewId}}
                {{#if relationInformation.mailingAddress.complete}}
                    <span class="badge badge-success">Complete</span>
                {{else}}
                    <span class="badge badge-warning text-dark">Incomplete</span>
                {{/if}}
            {{/ifCond}}
        </p>
        <div class="row">
            <div class="col-2">Address - 1st Line:</div>
            <div class="col-4 font-weight-bold">{{ relation.mailingAddress.primaryAddress }}</div>
            <div class="col-2">Address - 2nd Line:</div>
            <div class="col-4 font-weight-bold">{{ relation.mailingAddress.secondaryAddress }}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">Country:</div>
            <div class="col-4 font-weight-bold">{{ relation.mailingAddress.country }}</div>
            <div class="col-2">State:</div>
            <div class="col-4 font-weight-bold">{{ relation.mailingAddress.state }}</div>
        </div>
        <div class="row pt-2">
            <div class="col-2">City:</div>
            <div class="col-4 font-weight-bold">{{ relation.mailingAddress.city }}</div>
            <div class="col-2">Postal Code:</div>
            <div class="col-4 font-weight-bold">{{ relation.mailingAddress.postalCode }}</div>
        </div>
        <!-- CHECK VALIDATE -->
        <div class="row">
            <div class="col-12 d-flex justify-content-end">
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    <div class="custom-control custom-checkbox">
                        <input
                                type="checkbox"
                                class="custom-control-input validateCheck"
                                name="mailingAddress[validated]"
                                id="mailingAddress[validated]-{{ relationInformation.mailingAddress.validated}}"
                            {{#if relationInformation.mailingAddress.validated}} checked {{/if}}
                            {{#if onlyRead}} disabled {{/if}}
                        />
                        <label class="custom-control-label"
                               for="mailingAddress[validated]-{{ relationInformation.mailingAddress.validated}}"
                        >Validate</label
                        >
                    </div>
                {{else}}
                    <i class="fa fa-lock"></i>
                {{/ifCond}}
            </div>
        </div>
        <hr class="mt-3"/>
    </div>

    {{#ifEquals relation.type 'natural'}}
        <!-- COUNTRY OF TAX RESIDENCE DETAILS -->
        <div>
            <p>
                TAX ADVICE
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    {{#if relationInformation.taxResidence.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                {{/ifCond}}
            </p>
            <div class="row">
                <div class="col-6">Confirmation Regarding Legal / Tax Advice:</div>
                <div class="col-6 font-weight-bold">{{#if relation.taxResidence.confirmation}}
                    Confirmed {{else}} Not confirmed{{/if}}</div>
                <div class="col-6">Tax Residence:</div>
                <div class="col-6 font-weight-bold">{{ relation.taxResidence.taxResidence }}</div>
            </div>
            <!-- CHECK VALIDATE -->
            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input validateCheck"
                                    name="taxResidence[validated]"
                                    id="taxResidence[validated]-{{ relationInformation.taxResidence.validated}}"
                                {{#if relationInformation.taxResidence.validated}} checked {{/if}}
                                {{#if onlyRead}} disabled {{/if}}
                            />
                            <label class="custom-control-label"
                                   for="taxResidence[validated]-{{ relationInformation.taxResidence.validated}}"
                            >Validate</label
                            >
                        </div>
                    {{else}}
                        <i class="fa fa-lock"></i>
                    {{/ifCond}}
                </div>
            </div>
            <hr class="mt-3"/>
        </div>

        <!-- ADVISOR DETAILS -->
        <div>
            <p>
                ADVISOR DETAILS
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    {{#if relationInformation.advisorDetails.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                {{/ifCond}}
            </p>
            <div class="row">
                <div class="col-2">First Name:</div>
                <div class="col-4 font-weight-bold">{{ relation.advisorDetails.firstName }}</div>
                <div class="col-2">Middle Name:</div>
                <div class="col-4 font-weight-bold">{{ relation.advisorDetails.middleName }}</div>
            </div>
            <div class="row">
                <div class="col-2">Last Name:</div>
                <div class="col-4 font-weight-bold">{{ relation.advisorDetails.lastName }}</div>
                <div class="col-2">Name of Firm:</div>
                <div class="col-4 font-weight-bold">{{ relation.advisorDetails.firmName }}</div>
            </div>
            <div class="row">
                <div class="col-2">Phone:</div>
                <div class="col-4 font-weight-bold">{{ relation.advisorDetails.phone }}</div>
                <div class="col-2">E-mail:</div>
                <div class="col-4 font-weight-bold">{{ relation.advisorDetails.email}}</div>
            </div>
            <div class="row">
                <div class="col-2">Nationality:</div>
                <div class="col-4 font-weight-bold">{{ relation.advisorDetails.nationality }}</div>
                <div class="col-2">Country of Incorporation:</div>
                <div class="col-4 font-weight-bold">{{ relation.advisorDetails.incorporationCountry }}</div>
            </div>
            <!-- CHECK VALIDATE -->
            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input validateCheck"
                                    name="advisorDetails[validated]"
                                    id="advisorDetails[validated]-{{ relationInformation.advisorDetails.validated}}"
                                {{#if relationInformation.advisorDetails.validated}} checked {{/if}}
                                {{#if onlyRead}} disabled {{/if}}
                            />
                            <label class="custom-control-label"
                                   for="advisorDetails[validated]-{{ relationInformation.advisorDetails.validated}}"
                            >Validate</label
                            >
                        </div>
                    {{else}}
                        <i class="fa fa-lock"></i>
                    {{/ifCond}}
                </div>
            </div>
            <hr class="mt-3"/>
        </div>

        <!-- PRINCIPAL ADVISOR DETAILS -->
        <div>
            <p>
                PRINCIPAL ADVISOR ADDRESS
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    {{#if relationInformation.residentialAddress.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                {{/ifCond}}
            </p>
            <div class="row">
                <div class="col-2">Address - 1st Line:</div>
                <div class="col-4 font-weight-bold">{{ relation.residentialAddress.primaryAddress }}</div>
                <div class="col-2">Address - 2nd Line:</div>
                <div class="col-4 font-weight-bold">{{ relation.residentialAddress.secondaryAddress }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">Country:</div>
                <div class="col-4 font-weight-bold">{{ relation.residentialAddress.country }}</div>
                <div class="col-2">State:</div>
                <div class="col-4 font-weight-bold">{{ relation.residentialAddress.state }}</div>
            </div>
            <div class="row pt-2">
                <div class="col-2">City:</div>
                <div class="col-4 font-weight-bold">{{ relation.residentialAddress.city }}</div>
                <div class="col-2">Postal Code:</div>
                <div class="col-4 font-weight-bold">{{ relation.residentialAddress.postalCode }}</div>
            </div>
            <!-- CHECK VALIDATE -->
            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input validateCheck"
                                    name="residentialAddress[validated]"
                                    id="residentialAddress[validated]-{{ relationInformation.residentialAddress.validated}}"
                                {{#if relationInformation.residentialAddress.validated}} checked {{/if}}
                                {{#if onlyRead}} disabled {{/if}}
                            />
                            <label class="custom-control-label"
                                   for="residentialAddress[validated]-{{ relationInformation.residentialAddress.validated}}"
                            >Validate</label
                            >
                        </div>
                    {{else}}
                        <i class="fa fa-lock"></i>
                    {{/ifCond}}
                </div>
            </div>
            <hr class="mt-3"/>
        </div>

        <!-- WORLD CHECK -->
        <div>
            <p>
                WORLD CHECK
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    {{#if relationInformation.worldCheck.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                {{/ifCond}}
            </p>
            <!-- CHECK VALIDATE -->
            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input validateCheck"
                                    name="worldCheck[validated]"
                                    id="worldCheck[validated]-{{ relationInformation.worldCheck.validated}}"
                                {{#if relationInformation.worldCheck.validated}} checked {{/if}}
                                {{#if onlyRead}} disabled {{/if}}
                            />
                            <label class="custom-control-label"
                                   for="worldCheck[validated]-{{ relationInformation.worldCheck.validated}}"
                            >Validate</label
                            >
                        </div>
                    {{else}}
                        <i class="fa fa-lock"></i>
                    {{/ifCond}}
                </div>
            </div>
        </div>


    {{else}}
    {{!-- ORGANIZATIONS --}}

    {{!-- LISTED COMPANY DETAILS --}}
        {{#ifCond relation.type '!=' 'trust'}}
            <div>
                <p>
                    LISTED COMPANY
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        {{#if relationInformation.listedCompanyDetails.complete}}
                            <span class="badge badge-success">Complete</span>
                        {{else}}
                            <span class="badge badge-warning text-dark">Incomplete</span>
                        {{/if}}
                    {{/ifCond}}
                </p>
                <div class="row">
                    <div class="col-2">Stock Code / Ticker Symbol:</div>
                    <div class="col-4 font-weight-bold">{{relation.listedCompanyDetails.stockCode }}</div>
                </div>
                <!-- CHECK VALIDATE -->
                <div class="row">
                    <div class="col-12 d-flex justify-content-end">
                        {{#ifCond relation.lockedByFileReview '==' reviewId}}
                            <div class="custom-control custom-checkbox">
                                <input
                                        type="checkbox"
                                        class="custom-control-input validateCheck"
                                        name="listedCompanyDetails[validated]"
                                        id="listedCompanyDetails[validated]-{{ relationInformation.listedCompanyDetails.validated}}"
                                    {{#if relationInformation.listedCompanyDetails.validated}} checked {{/if}}
                                    {{#if onlyRead}} disabled {{/if}}
                                />
                                <label class="custom-control-label"
                                       for="listedCompanyDetails[validated]-{{ relationInformation.listedCompanyDetails.validated}}"
                                >Validate</label
                                >
                            </div>
                        {{else}}
                            <i class="fa fa-lock"></i>
                        {{/ifCond}}
                    </div>
                </div>
                <hr class="mt-3"/>
            </div>

            {{!-- LIMITED DETAILS--}}
            <div>
                <p>
                    LIMITED COMPANY
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        {{#if relationInformation.limitedCompanyDetails.complete}}
                            <span class="badge badge-success">Complete</span>
                        {{else}}
                            <span class="badge badge-warning text-dark">Incomplete</span>
                        {{/if}}
                    {{/ifCond}}
                </p>
                <div class="row">
                    <div class="col-2">Registration Number:</div>
                    <div class="col-4 font-weight-bold">{{ relation.limitedCompanyDetails.registrationNumber }}</div>
                    <div class="col-2">Registration Date:</div>
                    <div class="col-4 font-weight-bold">
                        {{#formatDate relation.limitedCompanyDetails.registrationDate
                                      "MM/DD/YYYY"}} {{/formatDate }}</div>
                </div>
                <!-- CHECK VALIDATE -->
                <div class="row">
                    <div class="col-12 d-flex justify-content-end">
                        {{#ifCond relation.lockedByFileReview '==' reviewId}}
                            <div class="custom-control custom-checkbox">
                                <input
                                        type="checkbox"
                                        class="custom-control-input validateCheck"
                                        name="limitedCompanyDetails[validated]"
                                        id="limitedCompanyDetails[validated]-{{ relationInformation.limitedCompanyDetails.validated}}"
                                    {{#if relationInformation.limitedCompanyDetails.validated}} checked {{/if}}
                                    {{#if onlyRead}} disabled {{/if}}
                                />
                                <label class="custom-control-label"
                                       for="limitedCompanyDetails[validated]-{{ relationInformation.limitedCompanyDetails.validated}}"
                                >Validate</label
                                >
                            </div>
                        {{else}}
                            <i class="fa fa-lock"></i>
                        {{/ifCond}}
                    </div>
                </div>
                <hr class="mt-3"/>
            </div>
        {{/ifCond}}
        {{!-- FOUNDATION DETAILS --}}
        {{#ifCond relation.type '==' 'foundation'}}
            <div>
                <p>
                    FOUNDATION
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        {{#if relationInformation.foundation.complete}}
                            <span class="badge badge-success">Complete</span>
                        {{else}}
                            <span class="badge badge-warning text-dark">Incomplete</span>
                        {{/if}}
                    {{/ifCond}}
                </p>
                <div class="row">
                    <div class="col-2">Country:</div>
                    <div class="col-4 font-weight-bold">{{ relation.foundation.country }}</div>
                </div>
                <!-- CHECK VALIDATE -->
                <div class="row">
                    <div class="col-12 d-flex justify-content-end">
                        {{#ifCond relation.lockedByFileReview '==' reviewId}}
                            <div class="custom-control custom-checkbox">
                                <input
                                        type="checkbox"
                                        class="custom-control-input validateCheck"
                                        name="foundation[validated]"
                                        id="foundation[validated]-{{ relationInformation.foundation.validated}}"
                                    {{#if relationInformation.foundation.validated}} checked {{/if}}
                                    {{#if onlyRead}} disabled {{/if}}
                                />
                                <label class="custom-control-label"
                                       for="foundation[validated]-{{ relationInformation.foundation.validated}}"
                                >Validate</label
                                >
                            </div>
                        {{else}}
                            <i class="fa fa-lock"></i>
                        {{/ifCond}}
                    </div>
                </div>
                <hr class="mt-3"/>
            </div>
        {{/ifCond}}
        {{!-- MUTUALFUND DETAILS--}}
        <div>
            <p>
                REGULATED (Mutual Fund)
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    {{#if relationInformation.mutualFundDetails.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                {{/ifCond}}
            </p>
            <!-- CHECK VALIDATE -->
            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input validateCheck"
                                    name="mutualFundDetails[validated]"
                                    id="mutualFundDetails[validated]-{{ relationInformation.mutualFundDetails.validated}}"
                                {{#if relationInformation.mutualFundDetails.validated}} checked {{/if}}
                                {{#if onlyRead}} disabled {{/if}}
                            />
                            <label class="custom-control-label"
                                   for="mutualFundDetails[validated]-{{ relationInformation.mutualFundDetails.validated}}"
                            >Validate</label
                            >
                        </div>
                    {{else}}
                        <i class="fa fa-lock"></i>
                    {{/ifCond}}

                </div>
            </div>
            <hr class="mt-3"/>
        </div>

        <!-- WORLD CHECK -->
        <div>
            <p>
                WORLD CHECK
                {{#ifCond relation.lockedByFileReview '==' reviewId}}
                    {{#if relationInformation.worldCheck.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                {{/ifCond}}
            </p>
            <!-- CHECK VALIDATE -->
            <div class="row">
                <div class="col-12 d-flex justify-content-end">
                    {{#ifCond relation.lockedByFileReview '==' reviewId}}
                        <div class="custom-control custom-checkbox">
                            <input
                                    type="checkbox"
                                    class="custom-control-input validateCheck"
                                    name="worldCheck[validated]"
                                    id="worldCheck[validated]-{{ relationInformation.worldCheck.validated}}"
                                {{#if relationInformation.worldCheck.validated}} checked {{/if}}
                                {{#if onlyRead}} disabled {{/if}}
                            />
                            <label class="custom-control-label"
                                   for="worldCheck[validated]-{{ relationInformation.worldCheck.validated}}"
                            >Validate</label
                            >
                        </div>
                    {{else}}
                        <i class="fa fa-lock"></i>
                    {{/ifCond}}
                </div>
            </div>
        </div>
    {{/ifEquals}}

    <!-- SHAREHOLDER ADDRESS DETAILS -->
    {{#ifEquals relationType 'shareholder'}}
        {{#if relation.additional}}
            <div>
                <hr class="mt-3"/>
                <p> ADDITIONAL SHAREHOLDER
                    {{#if relationInformation.additional.complete}}
                        <span class="badge badge-success">Complete</span>
                    {{else}}
                        <span class="badge badge-warning text-dark">Incomplete</span>
                    {{/if}}
                </p>
                <div class="row pt-2">
                    <div class="col-2">Percentage:</div>
                    <div class="col-4 font-weight-bold">
                        {{#if
                                relation.additional.percentage}}{{  relation.additional.percentage }} %
                        {{else}}
                            -
                        {{/if}}
                    </div>
                </div>
                <!-- CHECK VALIDATE -->
                <div class="row">
                    <div class="col-12 d-flex justify-content-end">
                        {{#ifCond relation.lockedByFileReview '==' reviewId}}
                            <div class="custom-control custom-checkbox">
                                <input
                                        type="checkbox"
                                        class="custom-control-input validateCheck"
                                        name="additional[validate]"
                                        id="additional[validate]"
                                    {{#if relationInformation.additional.validated}} checked {{/if}}
                                    {{#if onlyRead}} disabled {{/if}}
                                />
                                <label class="custom-control-label" for="additional[validate]"
                                >Validate</label
                                >
                            </div>
                        {{else}}
                            <i class="fa fa-lock"></i>
                        {{/ifCond}}
                    </div>
                </div>
            </div>
        {{/if}}
    {{/ifEquals}}
    <div>
        <hr class="mt-3"/>
        <p>FILES</p>
        <table class="table">
            <thead>
            <tr>
                <th>Name</th>
                <th>Group</th>
                <th>Present</th>
                <th>Explanation</th>
                <th>Download</th>
                <th>Validate</th>
            </tr>
            </thead>
            <tbody>
            {{#each relationFiles}}
                <tr>
                    <td style="text-transform: capitalize;">{{external}}</td>
                    <td style="text-transform: capitalize;">{{fileGroup}}</td>
                    <td class="text-center" style="text-transform: capitalize;">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox"
                                   disabled
                                   class="custom-control-input"
                                   id="standardFilePresent-{{ @key }}"
                                {{#if present}}
                                   checked
                                {{/if}}
                            />
                            <label
                                    class="custom-control-label"
                                    for="standardFilePresent-{{ @key }}"
                            ></label>
                        </div>
                    </td>
                    <td style="text-transform: capitalize;">{{ explanation }}</td>
                    <td class="text-center align-middle">
                        <button class="btn solid royal-blue download-button"
                                id="standardFileDownload-{{ @key }}"
                                type="button"
                                data-toggle="modal"
                                data-target="#downloadFileModal"
                                data-review-id="{{../reviewId }}"
                                data-relation-id="{{../relation._id}}"
                                data-file-id="{{ id }}"
                                data-file-group="{{../relation.type}}"
                            {{#unless uploadFiles}} disabled {{/unless}}
                        >Download
                        </button>
                    </td>
                    <td class="text-center align-middle">
                        {{#ifCond ../relation.lockedByFileReview '==' ../reviewId}}
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" name="files-{{ fileGroup }}-{{id}}-validated"
                                       class="custom-control-input validateCheck"
                                       id="standard-file-validated-{{ fileGroup }}-{{id}}"
                                       form="qualityRelationForm"
                                    {{#if validated}}
                                       checked
                                    {{/if}}
                                    {{#if ../onlyRead}} disabled {{/if}}
                                />
                                <label
                                        class="custom-control-label"
                                        for="standard-file-validated-{{ fileGroup }}-{{id}}"
                                ></label>
                            </div>
                        {{else}}
                            <i class="fa fa-lock"></i>
                        {{/ifCond}}
                    </td>
                    <td></td>
                </tr>
            {{/each}}
            </tbody>
        </table>
    </div>
    {{#ifCond relation.lockedByFileReview '==' reviewId}}
        <div class="row">
            <div class="col-md-12 text-right">
                <div class="custom-control custom-checkbox pt-2">
                    <input type="checkbox" name="allValidateCheck"
                           class="custom-control-input validateCheck"
                           id="allValidateCheck"
                           form="fileReviewForm"
                    />
                    <label class="custom-control-label" for="allValidateCheck">Mark All as Validate</label>
                </div>
            </div>
        </div>
    {{/ifCond}}

</form>

<script type="text/javascript">

    $('.delete-position').click(function () {
        let button = $(this); // Button that triggered the modal
        let positionId = button.data('id');
        let orgId = button.data('org');
        swal({
            title: 'Confirmation',
            text: "Are you sure you want to delete this position?",
            icon: 'danger',
            showCancelButton: true,
            showCloseButton: true,
            reverseButtons: true,
            confirmButtonColor: '#f1556c',
            confirmButtonText: 'Delete',
            cancelButtonText: 'Close'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    type: 'POST',
                    url: '/file-reviewer/delete-position/' + orgId + '/' + positionId,
                    data: {},
                    success: function () {
                        location.reload();
                    },
                    error: function () {
                        Swal.fire('Error', 'There was an error while trying to delete the position', 'error')
                    }
                })
            }
        });
    });

    $('#allValidateCheck').on('change', function () {
        const checked = $(this).is(':checked');
        if (checked) {
            $('.validateCheck:visible').prop('checked', true);
        } else {
            $('.validateCheck:visible').prop('checked', false);
        }
    });
</script>

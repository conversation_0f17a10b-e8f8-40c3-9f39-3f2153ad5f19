<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h1>{{title}}</h1>
                        <p>Welcome {{user.name}}</p>
                        <p hidden>Statistic updated at: {{formatDate allData.UpdatedAt STANDARD_DATETIME_FORMAT}}</p>
                    </div>
                    <div class="row" hidden>
                        <div class="col-xl-6">
                            <div class="card-box">
                                <h4 class="header-title mb-3">Client's response on Directors Information</h4>
                                <div class="table-responsive">
                                    <table class="table table-sm mb-0">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>Status</th>
                                                <th>Number</th>
                                                <th>%</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Confirmed</td>
                                                <td>{{allData.Directors.Confirmed.number}}</td>
                                                <td>{{allData.Directors.Confirmed.percentage}} %</td>
                                            </tr>
                                            <tr>
                                                <td>Request Update</td>
                                                <td>{{allData.Directors.RequestUpdate.number}}</td>
                                                <td>{{allData.Directors.RequestUpdate.percentage}} %</td>
                                            </tr>
                                            <tr>
                                                <td>Pending</td>
                                                <td>{{allData.Directors.PendingResponse.number}}</td>
                                                <td>{{allData.Directors.PendingResponse.percentage}} %</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                         <div class="col-xl-6">
                            <div class="card-box">
                                <h4 class="header-title mb-3">Client's response on BO Information</h4>
                                <div class="table-responsive">
                                    <table class="table table-sm mb-0">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>Status</th>
                                                <th>Number</th>
                                                <th>%</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Confirmed</td>
                                                <td>{{allData.BeneficialOwner.Confirmed.number}}</td>
                                                <td>{{allData.BeneficialOwner.Confirmed.percentage}} %</td>
                                            </tr>
                                            <tr>
                                                <td>Request Update</td>
                                                <td>{{allData.BeneficialOwner.RequestUpdate.number}}</td>
                                                <td>{{allData.BeneficialOwner.RequestUpdate.percentage}} %</td>
                                            </tr>
                                            <tr>
                                                <td>Pending</td>
                                                <td>{{allData.BeneficialOwner.PendingResponse.number}}</td>
                                                <td>{{allData.BeneficialOwner.PendingResponse.percentage}} %</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {{#if hasProductionOfficeGroups}}
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Search</h5>
                                        <a href="/director-and-bo/search"
                                            class="btn btn-light btn-sm waves-effect">Search Director/BO</a>
                                    </div>
                                </div>
                            </div>
                            {{/if}}

                            {{#if showImportDirectorModule}}
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Import</h5>
                                        <a href="/director-and-bo/import-directors" class="btn btn-light btn-sm waves-effect">
                                            Import Director/BO
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {{/if}}
                        </div>


                        <div class="row mt-2">
                            <div class="col-md-2">
                                <a href="/" class="btn solid royal-blue w-100">
                                    Back
                                </a>
                            </div>
                        </div>

                    </div>


                </div>
            </div>
        </div>
    </div>
</main>
<main class="">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h1>{{title}}</h1>
                        <form method='POST' id="generalForm">
                            <div class="row">
                                <div class="col-md-2">
                                    <label for="filter_company">Company</label>
                                    <input class='form-control' type='text' name='filter_company' id='filter_company'
                                        value="{{filters.filter_company}}" />
                                </div>
                                <div class="col-md-2">
                                    <label for="filter_masterclient">Masterclient</label>
                                    <input class='form-control' type='text' name='filter_masterclient'
                                        id='filter_masterclient' value="{{filters.filter_masterclient}}" />
                                </div>
                                <div class="col-md-2">
                                    <label for="filter_referral">Referral Office</label>
                                    <input class='form-control' type='text' name='filter_referral' id='filter_referral'
                                        value="{{filters.filter_referral}}" />
                                </div>
                                <div class="col-md-4">
                                    <div class="row">
                                        <label for="filter_referral">Relevant Activities</label>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    name="relevant_activities" id="none" value="none" {{#if
                                                    filters.relevantActivities.none }} checked {{/if}} />
                                                <label class="custom-control-label" for="none">None</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    name="relevant_activities" id="banking_business"
                                                    value="banking_business" {{#if
                                                    filters.relevantActivities.banking_business }} checked {{/if}} />
                                                <label class="custom-control-label" for="banking_business">Banking
                                                    Business</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    name="relevant_activities" id="insurance_business"
                                                    value="insurance_business" {{#if
                                                    filters.relevantActivities.insurance_business }}checked {{/if}} />
                                                <label class="custom-control-label" for="insurance_business">Insurance
                                                    Business</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    name="relevant_activities" id="fund_management_business"
                                                    value="fund_management_business" {{#if
                                                    filters.relevantActivities.fund_management_business }}checked
                                                    {{/if}} />
                                                <label class="custom-control-label" for="fund_management_business">Fund
                                                    Management Business</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    name="relevant_activities" id="finance_leasing_business"
                                                    value="finance_leasing_business" {{#if
                                                    filters.relevantActivities.finance_leasing_business }}checked
                                                    {{/if}} />
                                                <label class="custom-control-label"
                                                    for="finance_leasing_business">Finance/Leasing Business</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    name="relevant_activities" id="headquarters_business"
                                                    value="headquarters_business" {{#if
                                                    filters.relevantActivities.headquarters_business }}checked
                                                    {{/if}} />
                                                <label class="custom-control-label"
                                                    for="headquarters_business">Headquarters
                                                    Business</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    name="relevant_activities" id="shipping_business"
                                                    value="shipping_business" {{#if
                                                    filters.relevantActivities.shipping_business }}checked {{/if}} />
                                                <label class="custom-control-label" for="shipping_business">Shipping
                                                    Business</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    name="relevant_activities" id="holding_business"
                                                    value="holding_business" {{#if
                                                    filters.relevantActivities.holding_business }}checked {{/if}} />
                                                <label class="custom-control-label" for="holding_business">Holding
                                                    Business</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    name="relevant_activities" id="intellectual_property_business"
                                                    value="intellectual_property_business" {{#if
                                                    filters.relevantActivities.intellectual_property_business }}checked
                                                    {{/if}} />
                                                <label class="custom-control-label"
                                                    for="intellectual_property_business">Intellectual Property
                                                    Business</label>
                                            </div>
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    name="relevant_activities" id="service_centre_business"
                                                    value="service_centre_business" {{#if
                                                    filters.relevantActivities.service_centre_business }}checked
                                                    {{/if}} />
                                                <label class="custom-control-label"
                                                    for="service_centre_business">Service Centre
                                                    Business</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <label for="filter_other">Other</label>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    name="resident_outside_bvi" id="resident_outside_bvi" value="Yes"
                                                    {{#if filters.resident_outside_bvi }} checked {{/if}} />
                                                <label class="custom-control-label" for="resident_outside_bvi">Tax
                                                    residency claim outside BVI</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <label>Show not started only?</label>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" id="filter_not_started_yes"
                                            name="filter_not_started" value="true" {{#ifEquals filters.filter_not_started 'true'}}checked {{/ifEquals}}>
                                        <label class="custom-control-label" for="filter_not_started_yes">Yes</label>
                                    </div>
                                </div>

                            </div>
                            <div class="row my-1">
                                <div class="col-md-2">
                                    <label for="filter_submitted_range_start">Submitted after:</label>
                                    <input class='form-control' type='date' name='filter_submitted_range_start'
                                        id='filter_submitted_range_start' value="{{filters.filter_submitted_range_start}}" />
                                </div>
                                <div class="col-md-2">
                                    <label for="filter_submitted_range_end">Submitted before:</label>
                                    <input class='form-control' type='date' name='filter_submitted_range_end'
                                        id='filter_submitted_range_end' value="{{filters.filter_submitted_range_end}}" />
                                </div>
                            </div>
                            <div class="row my-1">
                                <div class="col-md-2">
                                    <label for="filter_incorporated_range_start">Company incorporated after:</label>
                                    <input class='form-control' type='date' name='filter_incorporated_range_start'
                                        id='filter_incorporated_range_start' value="{{filters.filter_incorporated_range_start}}" />
                                </div>
                                <div class="col-md-2">
                                    <label for="filter_incorporated_range_end">Company incorporated before:</label>
                                    <input class='form-control' type='date' name='filter_incorporated_range_end'
                                        id='filter_incorporated_range_end' value="{{filters.filter_incorporated_range_end}}" />
                                </div>
                            </div>
                            <div class="row my-1">
                                <div class="col-md-2">
                                    <label for="filter_financial_period_range_start">Financial period end after:</label>
                                    <input class='form-control' type='date' name='filter_financial_period_range_start'
                                        id='filter_financial_period_range_start' value="{{filters.filter_financial_period_range_start}}" />
                                </div>
                                <div class="col-md-2">
                                    <label for="filter_financial_period_range_end">Financial period end before:</label>
                                    <input class='form-control' type='date' name='filter_financial_period_range_end'
                                        id='filter_financial_period_range_end' value="{{filters.filter_financial_period_range_end}}" />
                                </div>
                                <div class="col-md-2" style="padding-top:30px">
                                    <input type='SUBMIT' class='btn btn-light btn-sm waves-effect' value='Search' />
                                </div>
                            </div>
                        </form>
                        <br /><br />
                        <table id="scroll-horizontal-datatable" class="table w-100 nowrap">

                            <thead>
                                <tr>
                                    <th>Email</th>
                                    <th>Entity Name</th>
                                    <th>Entity Number</th>
                                    <th>Master Client Code</th>
                                    <th>Company Number</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Submitted</th>
                                    <th>Re-open</th>
                                    <th>Re-submitted</th>
                                    <th>Incorporation Date</th>
                                    <th>Date Paid</th>
                                    <th>Payment Ref</th>
                                    <th>Financial Period Enddate</th>
                                    <th>Referral Office</th>
                                    <th>None</th>
                                    <th title='Banking Business'>BB</th>
                                    <th title='Insurance Business'>IB</th>
                                    <th title='Fund Management Business'>FMB</th>
                                    <th title='Finance/Leasing Business'>FLB</th>
                                    <th title='Headquarters Business'>HQ</th>
                                    <th title='Shipping Business'>SB</th>
                                    <th title='Holding Business'>HB</th>
                                    <th title='Intellectual Property Business'>IP</th>
                                    <th title='Service Centre Business'>SC</th>
                                    <th>Reset Submission</th>
                                    {{#if authentication.isSubsSuperUser}}
                                    <th>Change Financial Period</th>
                                    {{/if}}
                                    <th>Summary</th>
                                    <th>View Information</th>
                                    {{#if authentication.isSubsSuperUser}}
                                    <th>Download support attachments</th>
                                    {{/if}}
                                </tr>
                            </thead>
                            <tbody>
                                {{#each result}}
                                <tr>
                                    <td>{{email}}</td>
                                    <td>{{entity_name}}</td>
                                    <td>{{code}}</td>
                                    <td>{{masterclientcode}}</td>
                                    <td>{{incorporationcode}}</td>
                                    <td>{{status}}</td>
                                    <td data-sort="{{formatDate createdAt 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate createdAt ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td data-sort="{{formatDate submitted_at 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate submitted_at ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td data-sort="{{formatDate reopened_at 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate reopened_at ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td data-sort="{{formatDate resubmitted_at 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate resubmitted_at ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td data-sort="{{formatDate incorporationdate 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate incorporationdate ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td data-sort="{{formatDate date_paid 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate date_paid ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td>{{payment_reference}}</td>
                                    <td data-sort="{{formatDate financial_period_ends 'YYYY-MM-DD HH:mm'}}">
                                        {{formatDate financial_period_ends ../STANDARD_DATE_FORMAT}}
                                    </td>
                                    <td>{{referral_office}}</td>
                                    <td>{{#if relevant_activities.none.selected}}Yes{{else}}No{{/if}}</td>
                                    <td>{{#if relevant_activities.banking_business.selected}}Yes{{else}}No{{/if}}</td>
                                    <td>{{#if relevant_activities.insurance_business.selected}}Yes{{else}}No{{/if}}</td>
                                    <td>{{#if
                                            relevant_activities.fund_management_business.selected}}Yes{{else}}No{{/if}}
                                    </td>
                                    <td>{{#if
                                            relevant_activities.finance_leasing_business.selected}}Yes{{else}}No{{/if}}
                                    </td>
                                    <td>{{#if relevant_activities.headquarters_business.selected}}Yes{{else}}No{{/if}}
                                    </td>
                                    <td>{{#if relevant_activities.shipping_business.selected}}Yes{{else}}No{{/if}}</td>
                                    <td>{{#if relevant_activities.holding_business.selected}}Yes{{else}}No{{/if}}</td>
                                    <td>{{#if
                                            relevant_activities.intellectual_property_business.selected}}Yes{{else}}No{{/if}}
                                    </td>
                                    <td>{{#if relevant_activities.service_centre_business.selected}}Yes{{else}}No{{/if}}
                                    </td>
                                    <td>
                                        {{#if ../authentication.isSubsSuperUser}}
                                            {{#ifEquals status 'PAID'}}
                                                <input type="button" class="btn btn-primary waves-effect waves-light border-white"
                                                       onclick="showConfirmation('{{_id}}', '{{company}}', '{{status}}',
                                                               '{{formatDate financial_period_begins ../STANDARD_DATE_FORMAT}}',
                                                               '{{formatDate financial_period_ends ../STANDARD_DATE_FORMAT}}')" value="Reset to saved">
                                            {{/ifEquals}}
                                            {{#ifEquals status 'SUBMITTED'}}
                                                <input type="button" class="btn btn-primary waves-effect waves-light border-white"
                                                       onclick="showConfirmation('{{_id}}', '{{company}}', '{{status}}',
                                                               '{{formatDate financial_period_begins ../STANDARD_DATE_FORMAT}}',
                                                               '{{formatDate financial_period_ends ../STANDARD_DATE_FORMAT}}')" value="Reset to saved">
                                            {{/ifEquals}}
                                        {{/if}}

                                    </td>
                                    {{#if ../authentication.isSubsSuperUser}}
                                        <td>                                    
                                        {{#ifCond status '!==' 'NOT STARTED'}}
                                            <input type="button" class="btn btn-primary waves-effect waves-light border-white"
                                                       onclick="showFinancialPeriodModal('{{_id}}', '{{company}}', '{{formatDate financial_period_begins ../STANDARD_DATE_FORMAT}}',
                                                              '{{formatDate financial_period_ends ../STANDARD_DATE_FORMAT}}')" value="Change">
                                        {{/ifCond}}
                                        </td>
                                    {{/if}}
                                    <td>

                                        {{#ifEquals status 'PAID'}}
                                            <a href="/substance/{{_id}}/submission.pdf" target="_blank"
                                               class="btn btn-primary waves-effect waves-light  text-white border-white">Download</a>
                                        {{/ifEquals}}
                                        {{#ifEquals status 'SUBMITTED'}}
                                            <a href="/substance/{{_id}}/submission.pdf" target="_blank"
                                               class="btn btn-primary waves-effect waves-light  text-white border-white">Download</a>
                                        {{/ifEquals}}

                                        {{#ifEquals status 'SCHEDULED'}}
                                            <a href="/substance/{{_id}}/submission.pdf" target="_blank"
                                               class="btn btn-primary waves-effect waves-light  text-white border-white">Download</a>
                                        {{/ifEquals}}
                                    </td>
                                    <td>

                                        {{#if ../authentication.isSubsSuperUser}}
                                            {{#if show_info_details}}
                                                <button class="btn btn-primary waves-effect waves-light border-white"
                                                        data-toggle="modal"
                                                        data-target="#showInformationModal"
                                                        data-entry-id="{{_id}}"

                                                > Show Info
                                                </button>
                                            {{/if}}
                                        {{/if}}
                                    </td>
                                    {{#if ../authentication.isSubsSuperUser}}
                                    <td style="vertical-align: middle;">
                                        {{#if show_attachments_download}}
                                        <input type="button" name="downloadFilesBtn" id="download_files_{{_id}}"
                                            class="btn btn-primary waves-effect waves-light"
                                            onclick="event.stopPropagation(); downloadFiles('{{_id}}')" value="Download">
                                        {{/if}}
                                    </td>
                                    {{/if}}
                                </tr>
                                {{/each}}
                                </tbody>
                        </table>
                    </div>
                    <form method="POST" action="./export-search-xls" id="submitFormXls" name="submitFormXls">
                        <input type="hidden" name="entryIds" value="" />
                    </form>
                    <div class="row">
                        <div class="col-12 text-sm-center form-inline">
                            <div class="ml-3 mr-2  mb-3">
                                <a href='/substance/'
                                   class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                                <button class="btn btn-primary width-lg waves-effect waves-light"
                                        id="btn-export-xls">Export xls</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal hide fade confirmation-modal-md" tabindex="-1" role="dialog" aria-labelledby="mediumModal"
                 id="confirmation_modal" style="display: none;" aria-hidden="true">
                <div class="modal-dialog modal-md modal-dialog-scrollable">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title" id="mediumModal">Confirmation</h4>
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-12">
                                    <p>This will re-open the submission for the client to amend changes. Are you sure?
                                    </p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="mt-1" for="reopenReason">Please provide a note for the client:</label>
                                        <textarea
                                                rows="3"
                                                name="reopenReason"
                                                id="reopenReason"
                                                class="form-control"
                                                required minlength="50"
                                        ></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <label>Do you want to correct the Financial Period?*</label>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" class="custom-control-input" id="changeFinancialPeriodYes" required
                                               name="changeFinancialPeriod" value="true">
                                        <label class="custom-control-label" for="changeFinancialPeriodYes">Yes</label>
                                    </div>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" class="custom-control-input" id="changeFinancialPeriodNo"
                                               name="changeFinancialPeriod" value="false">
                                        <label class="custom-control-label" for="changeFinancialPeriodNo">No</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row" id="changePeriodDatesRow" style="display: none">
                                <div class="col-6">
                                    <label for="newFinancialStartDate">Correct start date:</label>
                                    <input type="text" class="form-control datepicker mr-2" placeholder="MM/DD/YYYY" name="newFinancialStartDate" id="newFinancialStartDate">
                                    <div class="w-100 text-right">
                                        <h6 id="currentStartDate"></h6>
                                    </div>

                                </div>
                                <div class="col-6">
                                    <label for="newFinancialEndDate">Correct end date:</label>
                                    <input type="text" class="form-control datepicker" placeholder="MM/DD/YYYY" name="newFinancialEndDate" id="newFinancialEndDate">
                                    <div class="w-100 text-right">
                                        <h6 id="currentEndDate"></h6>
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div class="modal-footer">
                            <input type="button" class="btn btn-secondary waves-effect waves-light" data-dismiss="modal"
                                   value="Cancel">

                            <input type="button" class="btn btn-primary waves-effect waves-light ml-2"
                                   id="sendInvitationButton" onclick="saveSubmission()" value="Confirm">
                        </div>
                    </div><!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </div>
            <div class="modal hide fade" tabindex="-1" role="dialog"
                 id="financial-period-modal" style="display: none;" aria-hidden="true">
                <div class="modal-dialog modal-md modal-dialog-scrollable">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title" id="mediumModal">Change Financial Period</h4>
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                        </div>
                        <div class="modal-body">
                            <div class="row" id="newPeriodDatesRow">
                                <div class="col-6">
                                    <label for="financialStartDateControl">Start Date:</label>
                                    <input type="text" class="form-control datepicker mr-2" placeholder="MM/DD/YYYY" name="financialStartDate" id="financialStartDateControl">
                                </div>
                                <div class="col-6">
                                    <label for="financialEndDateControl">End Date:</label>
                                    <input type="text" class="form-control datepicker" placeholder="MM/DD/YYYY" name="financialEndDate" id="financialEndDateControl">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <input type="button" class="btn btn-secondary waves-effect waves-light" data-dismiss="modal"
                                   value="Cancel">

                            <input type="button" class="btn btn-primary waves-effect waves-light ml-2"
                                   id="confirmFinancialPeriodButton" onclick="saveFinancialPeriod()" value="Confirm">
                        </div>
                    </div><!-- /.modal-content -->
                </div><!-- /.modal-dialog -->
            </div>
        </div>
    </div>

    {{>substance/show-information-modal}}
    {{>substance/request-information-modal}}
</main>
<script type="text/javascript">

    let selectedId;
    let selectedCompany;
    let isSelectedEntryPaid = false;
    let showLimitAlert = "{{showLimitAlert}}" === 'true';

    $(document).ready(function () {

        if (showLimitAlert) {
            toastr["warning"]('Maximum number of records reached. Please refine your search to reduce the size of query.', 'Limit reached!', {
                "timeOut": 100000,
            });
        }

        $("#financialStartDateControl").datepicker({
            format: 'mm/dd/yyyy',
            autoclose: true
        }).on('hide', function (event) {
            event.preventDefault();
            event.stopPropagation();
        });

        $("#financialEndDateControl").datepicker({
            format: 'mm/dd/yyyy',
            autoclose: true
        }).on('hide', function (event) {
            event.preventDefault();
            event.stopPropagation();
        });

        $("#scroll-horizontal-datatable").DataTable({
            scrollX: !0,
            select: true,
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>" }
            },
            drawCallback: function () { $(".dataTables_paginate > .pagination").addClass("pagination-rounded") }
        });
    });

    function showConfirmation(id, company, status, financialPeriodStart, financialPeriodEnd) {
        selectedId = id;
        selectedCompany = company;
        isSelectedEntryPaid = status === "PAID";
        $("#currentStartDate").html('Current: ' + financialPeriodStart);
        $("#currentEndDate").html('Current: ' + financialPeriodEnd);
            $("#newFinancialStartDate").flatpickr({
            dateFormat: "Y-m-d",
            altInput: true,
            altFormat: "m/d/Y",
            autoclose: true,
            monthSelectorType: "dropdown",
            allowInput: true,
            defaultDate: moment(financialPeriodStart, 'MM/DD/YYYY').toDate()
        })

        $("#newFinancialEndDate").flatpickr({
            dateFormat: "Y-m-d",
            altInput: true,
            altFormat: "m/d/Y",
            autoclose: true,
            monthSelectorType: "dropdown",
            allowInput: true,
            defaultDate: moment(financialPeriodEnd, 'MM/DD/YYYY').toDate()
        })
        $("#confirmation_modal").modal();

    }

    function showFinancialPeriodModal(id, company, financialPeriodStart, financialPeriodEnd) {
        selectedId = id;
        selectedCompany = company;
        isSelectedEntryPaid = status === "PAID";
        $("#financialStartDateControl").datepicker("setDate", financialPeriodStart);
        $("#financialEndDateControl").datepicker("setDate", financialPeriodEnd);
        $("#financial-period-modal").modal();

    }

    function downloadFiles(id) {
        window.location.href = `/substance/${id}/support-attachments`;
    }

    function saveFinancialPeriod() {
        $('#confirmFinancialPeriodButton').prop('disabled', true);

        const newFinancialStartDate = $("#financialStartDateControl").val();
        const newFinancialEndDate = $("#financialEndDateControl").val();



        if (!newFinancialStartDate || !newFinancialEndDate){
            toastr["warning"]('Please select financial period start/end dates');
            return;
        }

        $.ajax({
            type: "PUT",
            url: "./search/update-financial-period",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({
                id: selectedId,
                company: selectedCompany,
                newFinancialStartDate: newFinancialStartDate,
                newFinancialEndDate: newFinancialEndDate,
            }),
            success: function (data) {
                if (data.success) {
                    toastr.success('Submission updated successfully');
                    window.setTimeout(function () {
                        document.location.reload();
                    }, 200)
                }else{
                    toastr["error"]('Sorry, there was an error updating the submission.');
                    $('#confirmFinancialPeriodButton').prop('disabled', false);
                }
            },
            error: (err) => {
                if (err.responseJSON?.error){
                    toastr["error"](err.responseJSON?.error);
                    $('#confirmFinancialPeriodButton').prop('disabled', false);
                }
                else {
                    toastr["error"]('Sorry, there was an error updating the submission.');
                    $('#confirmFinancialPeriodButton').prop('disabled', false);
                }
            },

        });
    }

    function saveSubmission() {
        $('#sendInvitationButton').prop('disabled', true);
        const reason = $("#reopenReason").val();
        const changePeriodDate = $("input[name='changeFinancialPeriod']:checked").val();
        if(!reason || reason === ""){
            toastr["warning"]('Please provide the reason to re-open.');
            $('#sendInvitationButton').prop('disabled', false);
            return false;
        }
        else if (reason && reason.length < 50){
            toastr["warning"]('Please provide more information.');
            $('#sendInvitationButton').prop('disabled', false);
            return false;
        }

        if(!changePeriodDate || changePeriodDate === ""){
            toastr["warning"]('Please make a selection to correct the financial period');
            $('#sendInvitationButton').prop('disabled', false);
            return false;
        }

        const newFinancialStartDate = changePeriodDate === "true" ? $("#newFinancialStartDate").val() : null;
        const newFinancialEndDate = changePeriodDate === "true" ? $("#newFinancialEndDate").val() : null;

        if (changePeriodDate === "true" && (!newFinancialStartDate || !newFinancialEndDate)){
            toastr["warning"]('Please select the correct financial start/end dates');
            $('#sendInvitationButton').prop('disabled', false);
            return false;
        }

      
      
        $.ajax({
            type: "POST",
            url: "./search/save",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({
                id: selectedId,
                company: selectedCompany,
                reason: reason,
                changePeriodDate: changePeriodDate === "true",
                newFinancialStartDate: newFinancialStartDate,
                newFinancialEndDate: newFinancialEndDate,
            }),
            success: function (data) {
                if (data.success) {
                    toastr.success('Submission updated successfully');
                    window.setTimeout(function () {
                        document.location.reload();
                    }, 200)
                }else{
                    toastr["error"]('Sorry, there was an error updating the submission.');
                    $('#sendInvitationButton').prop('disabled', false);
                }
            },
            error: (err) => {
                if (err.responseJSON?.error){
                    toastr["error"](err.responseJSON?.error);
                    $('#sendInvitationButton').prop('disabled', false);
                }
                else {
                    toastr["error"]('Sorry, there was an error updating the submission.');
                    $('#sendInvitationButton').prop('disabled', false);
                }
            },

        });
    }

    $('#btn-export-xls').click(function () {
        let elements = document.getElementsByName("relevant_activities")
        let relevant_activities = [];
        for (let i = 0; i < elements.length; i++) {
            if ($(elements[i]).attr('checked')) {
                relevant_activities.push($(elements[i]).val())
            }
        }
        var dataToSend = {
            'filter_company': $('#filter_company').val(),
            'filter_masterclient': $('#filter_masterclient').val(),
            'filter_referral': $('#filter_referral').val(),
            'filter_submitted_range_start': $('#filter_submitted_range_start').val(),
            'filter_submitted_range_end': $('#filter_submitted_range_end').val(),
            'filter_incorporated_range_start': $('#filter_incorporated_range_start').val(),
            'filter_incorporated_range_end': $('#filter_incorporated_range_end').val(),
            'filter_financial_period_range_start': $('#filter_financial_period_range_start').val(),
            'filter_financial_period_range_end': $('#filter_financial_period_range_end').val(),
            'relevant_activities': relevant_activities,
            'filter_not_started': $('#filter_not_started_yes').is(":checked"),
            'resident_outside_bvi':  $('#resident_outside_bvi').is(":checked"),
        };

        $.ajax({
            url: "./export-search-xls",
            data: dataToSend,
            method: 'POST',
            xhrFields: {
                responseType: 'blob' // to avoid binary data being mangled on charset conversion
            },
            success: function (blob, status, xhr) {
                // check for a filename
                var filename = "";
                var disposition = xhr.getResponseHeader('Content-Disposition');
                if (disposition && disposition.indexOf('attachment') !== -1) {
                    var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    var matches = filenameRegex.exec(disposition);
                    if (matches != null && matches[1]) filename = matches[1].replace(/['"]/g, '');
                }

                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    // IE workaround for "HTML7007: One or more blob URLs were revoked by closing the blob for which they were created. These URLs will no longer resolve as the data backing the URL has been freed."
                    window.navigator.msSaveBlob(blob, filename);
                } else {
                    var URL = window.URL || window.webkitURL;
                    var downloadUrl = URL.createObjectURL(blob);

                    if (filename) {
                        // use HTML5 a[download] attribute to specify filename
                        var a = document.createElement("a");
                        // safari doesn't support this yet
                        if (typeof a.download === 'undefined') {
                            window.location.href = downloadUrl;
                        } else {
                            a.href = downloadUrl;
                            a.download = filename;
                            document.body.appendChild(a);
                            a.click();
                        }
                    } else {
                        window.location.href = downloadUrl;
                    }

                    setTimeout(function () { URL.revokeObjectURL(downloadUrl); }, 100); // cleanup
                }
            }
        })
        //xlsForm.submit();
    });



    $('#confirmation_modal').on('hide.bs.modal', function () {
        $("#reopenReason").val('');
        $("input[name='changeFinancialPeriod']").prop('checked', false);
        $("#newFinancialStartDate").val('');
        $("#newFinancialEndDate").val('');
        $("#changePeriodDatesRow").hide();
    });

    $("input[name='changeFinancialPeriod']").on('change', function () {
        const val = $("input[name='changeFinancialPeriod']:checked").val() === 'true';

        if (val){
            $("#changePeriodDatesRow").show();
        }else{
            $("#changePeriodDatesRow").hide();
        }
    })


</script>

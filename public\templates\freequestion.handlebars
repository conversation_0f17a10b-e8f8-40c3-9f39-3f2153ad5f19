<div class="row" id="freeQuestions-{{ row }}">
  <div class="col-1 d-flex pt-2 align-items-center justify-content-end">
    <button class="btn btn-danger btn-xs delete-new-fq" data-row="{{ row }}">
      <i class="dripicons-cross"></i>
    </button>
  </div>
  <div class="col-11">
    <div class="row">
      <div class="col-6 form-group">
        <label for="freeQuestions-{{ row }}-external">External Free Question</label>
        <textarea
          class="form-control"
          name="freeQuestions[{{ row }}][external]"
          id="freeQuestions-{{ row }}-external"
          form="fileReviewForm"
          rows="1"
        ></textarea>
      </div>
      <div class="col-6 form-group">
        <label for="freeQuestions-{{ row }}-internal">Internal Trident Comment</label>
        <textarea
          class="form-control"
          name="freeQuestions[{{ row }}][internal]"
          id="freeQuestions-{{ row }}-internal"
          form="fileReviewForm"
          rows="1"
        ></textarea>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  $('.delete-new-fq').on('click', function () {
    let row = $(this).data('row');
    $('#freeQuestions-' + row).remove();
  });
</script>

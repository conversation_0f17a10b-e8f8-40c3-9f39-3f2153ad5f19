<!-- OPEN OWNER MODAL -->
<div class="modal fade" id="openQualityPositionModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Position:</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-body" class="modal-body p-3">
                <p class="text-muted text-center">LOADING...</p>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button
                        id="submitQualityPositionForm"
                        type="button"
                        form="qualityPositionForm"
                        class="btn solid royal-blue modal-action-btn"
                    >
                    Submit
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="/templates/peekqualityposition.precompiled.js"></script>
<script type="text/javascript">
    let fileReviewId;
    let orgId;
    let positionId;
    $('#openQualityPositionModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        fileReviewId =  button.data('review-id');

        orgId = button.data('organization-id');
        positionId = button.data('position-id');
        const openMode = button.data('open-mode');
        
        if (openMode === "readOnly"){
            $("#submitQualityPositionForm").hide();
        }else {
            $("#submitQualityPositionForm").show();
        }

        $.get('/file-reviewer/reviews/'+ fileReviewId +'/relations/' + orgId + '/positions/' + positionId, function (data) {
            let template = Handlebars.templates.peekqualityposition;
            let d = {
                position: data.position,
                positionFiles: data.positionFiles,
                positionInformation: data.positionInformation,
                reviewId: fileReviewId,
                onlyRead: openMode === "readOnly"
        };
            let html = template(d);
            $("#submitQualityPositionForm").hide();
            $('#openQualityPositionModal .modal-body').html(html);
        });
    });

    $("#submitQualityPositionForm").on('click', function (event) {
        const form = $("#qualityPositionForm").serializeArray();
        event.preventDefault();
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/quality-assurance-review/' + fileReviewId + "/organizations/"+ orgId + "/positions/" + positionId,
            timeout: 2000,
            data: form,
            success: function (data) {
                toastr.success(`Position information saved successfully.`);
                $('#openQualityPositionModal').modal('hide');
            },
            error: function () {
                Swal.fire(
                        'Error',
                        'There was an error while trying to save the relation',
                        'error'
                ).then(() => {
                    $('#openQualityPositionModal').modal('hide');
                });
            },
        });
    });
</script>

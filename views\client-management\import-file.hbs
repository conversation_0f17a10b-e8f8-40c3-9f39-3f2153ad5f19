<main class="">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card">
                    <form method="POST" id="submitForm">
                        <div class="card-body">
                            <h1>{{title}}</h1>
                            <div id="import-table">

                            </div>
                            <div class="row" id="uploadRow">
                                <div class="col-md-12">
                                    <p>
                                        Maximum of 1 file, XLSX only. File must not
                                        be password
                                        protected.
                                    </p>
                                    <div id="uploadFile" class="dropzone">
                                        <div class="fallback">
                                            <input name="fileUploaded" type="file" multiple />
                                        </div>
                                        <div class="dz-message needsclick">
                                            <i class="h1 text-muted dripicons-cloud-upload"></i>
                                            <h3>Drop files here or click to upload.</h3>
                                            <span class="text-muted">Files will be automatically uploaded</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="row">
                                <div class="mb-3 ml-3">
                                    <a href='/client-management/'
                                        class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                                    <button type="button" class="btn btn-primary my-1" id="loadDataBtn"
                                        onclick="loadTable()" style="display: none;">Load Data</button>
                                    <button type="button" class="btn btn-primary my-1" id="saveDataBtn"
                                        onclick="saveData()" style="display: none;">Save Data</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/uploadedfiles.precompiled.js"></script>
<script type="text/javascript" src="/templates/importmcccompaniestable.precompiled.js"></script>

<script type="text/javascript">
    let importedData;
    Dropzone.autoDiscover = false;
    $(function () {
        let field = '';
        var myDropZone = new Dropzone('#uploadFile', {
            url: './import/load-file',
            acceptedFiles: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            uploadMultiple: false,
            autoProcessQueue: true,
            parallelUploads: 1,
            maxFiles: 1,
            maxFilesize: 5,
            paramName: function () {
                return 'fileUploaded';
            },
            init: function () {
                this.on('processing', function () {
                    this.options.url = './import/load-file';
                });
                this.on("success", function (file, response) {
                    if (response.error) {
                        toastr["warning"](response.error);
                        if (this.files.length != 0) {
                            for (i = 0; i < this.files.length; i++) {
                                this.files[i].previewElement.remove();
                            }
                            this.files.length = 0;
                        }
                    } else {
                        $('#loadDataBtn').show();
                        importedData = response;
                    }
                })
                this.on("sending", function (file, xhr, formData) {
                    $("#btnSubmit").prop('disabled', true);
                    if (!formData.has('filetype')) {
                        formData.append("filetype", field);
                    }
                })

                this.on('maxfilesexceeded', function (file) { });

                this.on('resetFiles', function () {
                    if (this.files.length != 0) {
                        for (i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                        this.files.length = 0;
                    }
                    $('#maxUpload').text(this.options.maxFiles);
                });
            },
        });
    });

    function loadTable() {
        $('#loadDataBtn').hide();
        $('#uploadRow').hide();
        let template = Handlebars.templates.importmcccompaniestable;

        let d = {
            importType: importedData.type,
            importData: importedData.data,
        };
        let html = template(d);
        $('#import-table').html(html);

        $('#saveDataBtn').show();
        table = $("#load-import-mcc-companies-table").DataTable({ "pageLength": 50, "order": [[0, "asc"]], scrollX: !0, language: { paginate: { previous: "<i class='mdi mdi-chevron-left'>", next: "<i class='mdi mdi-chevron-right'>" } }, drawCallback: function () { $(".dataTables_paginate > .pagination").addClass("pagination-rounded") } });
    }

    function saveData() {
        setTimeout(function () { $('#saveDataBtn').prop('disabled', true); }, 0);
        $.ajax({
            type: "POST",
            url: "./import",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({ importedData }),
            success: function (data) {
                if (data.success) {
                    if(data.inserted && data.inserted > 0){
                        toastr.success(`Data saved successfully. ${data.inserted} records were inserted.`);
                    }else{
                        toastr["error"]('Data not saved. 0 records were inserted.');
                    }

                    window.setTimeout(function () {
                        document.location.reload();
                    }, 2000)
                } else {
                    toastr["error"]('Sorry, there was an error saving the data.');
                    $('#saveDataBtn').prop('disabled', false);
                }
            }
        });
    }
</script>

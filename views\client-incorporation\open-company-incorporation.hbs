<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            Client company:<span class="font-weight-bold pl-1">{{ companyIncorporation.name }}</span>
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <p>
                                    Please find below the summary of the client submission together with all the
                                    documents that were uploaded.
                                </p>
                            </div>
                        </div>

                        {{#ifCond companyIncorporation.status '!==' 'PAID'}}

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-warning" role="alert">
                                        Please note: This submission has not yet been paid!
                                    </div>
                                </div>
                            </div>
                        {{/ifCond}}

                        {{#if companyIncorporation.nameReservationInfo.approved}}
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-success" role="alert">
                                        Name reservation completed:
                                        <strong>{{companyIncorporation.nameReservationInfo.approvedName}}</strong>
                                    </div>
                                </div>
                            </div>
                        {{/if}}

                        {{#if companyIncorporation.pendingElectronicIds}}
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-warning" role="alert">
                                        Please note: Invitation(s) for digital identification were sent out at
                                        {{#formatDate pendingElectronicInfo.invitationDate ../STANDARD_DATE_FORMAT}}
                                        {{/formatDate}} to {{pendingElectronicInfo.emails}}, however at least one ID is not yet completed.

                                    </div>
                                </div>
                            </div>
                        {{/if}}

                        <br>
                        <div class="row">
                            <div class="col-md-12">
                                <h5>FILES:</h5>
                            </div>
                        </div>
                        <div id="attached-table" class="row">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th scope="col" style="width: 30%">File</th>
                                        <th scope="col" style="width: 15%">Type</th>
                                        <th scope="col" style="width: 45%">Name</th>
                                        <th scope="col" style="width: 10%">Download</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>Incorporation summary</td>
                                        <td>PDF</td>
                                        <td>summary.pdf</td>
                                        <td class="pl-2 py-1 text-center align-middle">
                                            <a href="/client-incorporation/{{companyIncorporation._id }}/incorporation.pdf"
                                               target="_blank">
                                                <i class="fa fa-download" aria-hidden="true">
                                                </i>
                                            </a>
                                        </td>
                                    </tr>
                                    {{#each companyFiles}}
                                        <tr>
                                            <td>{{fileGroup}}</td>
                                            <td>{{ mimeType }}</td>
                                            <td>{{ originalName }}</td>
                                            <td class="pl-2 py-1 text-center align-middle">
                                                <a href="/client-incorporation/{{../companyIncorporation._id}}/download-document/{{fileId}}"
                                                   target="_blank">
                                                    <i class="fa fa-download" aria-hidden="true">
                                                    </i></a>
                                            </td>

                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>

                        <br>

                        {{#if clientReturnedInformation}}
                            <div class="row">
                                <div class="col-md-12">
                                    <h5>FILES RETURNED BY CLIENT:</h5>
                                </div>
                            </div>
                            <br>
                            {{#if clientRequestAnswers}}
                                <div class="row">
                                    <div class="col-md-12">
                                        <h5>Comments:</h5>
                                        {{#each clientRequestAnswers}}
                                            <span>
                                            ({{#formatDate returnedAt ../STANDARD_DATE_FORMAT}} {{/formatDate}}) - {{username}}
                                                : {{comment}}
                                        </span>
                                            <br>
                                        {{/each}}
                                    </div>
                                </div>
                                <br>
                            {{/if}}
                            <div id="request-files-table" class="row">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                        <tr>
                                            <th scope="col" style="width: 30%">File</th>
                                            <th scope="col" style="width: 15%">Type</th>
                                            <th scope="col" style="width: 45%">Name</th>
                                            <th scope="col" style="width: 10%">Download</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {{#each clientReturnedInformation}}
                                            <tr>
                                                <td>{{fileGroup}}</td>
                                                <td>{{ mimeType }}</td>
                                                <td>{{ originalName }}</td>
                                                <td class="pl-2 py-1 text-center align-middle">
                                                    <a href="/client-incorporation/{{../companyIncorporation._id}}/download-document/{{fileId}}?informationId={{fileTypeId}}
                                                    " target="_blank">
                                                    <i class="fa fa-download" aria-hidden="true">
                                                    </i>
                                                    </a>
                                                </td>

                                            </tr>
                                        {{else}}
                                            <tr>
                                                <td colspan="4" class="text-center font-italic">There are no files
                                                </td>
                                            </tr>
                                        {{/each}}
                                        </tbody>
                                    </table>
                                </div>
                                <!-- RESPONSIVE TABLE END -->
                            </div>
                        {{/if}}
                        <br>
                        <div class="row">
                            <div class="col-md-12">
                                <h5>RELATIONS:</h5>
                            </div>
                        </div>
                        <div class="row">
                            <div class="table-responsive  vertical-align-middle">
                                <table class="table table-sm table-small-font table-striped" id="relationsTable">
                                    <thead>
                                    <tr>
                                        <th style="width: 18%;">Name</th>
                                        <th style="width: 12%;">% Ownership</th>
                                        <th style="width: 12%;">Owned Type</th>
                                        <th style="width: 15%;">Relation Type</th>
                                        <th style="width: 10%;">Identification</th>
                                        <th style="width: 15%;">ID Status</th>
                                        <th style="width: 18%;"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each companyIncorporation.relations}}
                                        <tr>
                                            {{!-- MAY NEED REFACTORING --}}
                                            {{#ifEquals type 'natural'}}
                                                <!-- NAME -->
                                                <td style="text-transform: capitalize;">
                                                    {{details.firstName}} {{details.middleName}}  {{details.lastName}}

                                                </td>
                                            {{else}}
                                                <!-- NAME -->
                                                <td style="text-transform: capitalize;">
                                                    {{details.organizationName}}
                                                </td>
                                            {{/ifEquals}}

                                            <!-- PERCENTAGE -->
                                            <td style="text-transform: capitalize;">
                                                {{#if percentage}}
                                                    {{percentage}} %
                                                {{else}}
                                                    0%
                                                {{/if}}
                                            </td>
                                            <td style="text-transform: capitalize;">{{ type }}</td>
                                            <td style="text-transform: capitalize;">{{ groups }}</td>
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural' }}
                                                    {{#if electronicIdInfo.isElectronicId}}
                                                        IEDV
                                                    {{else}}
                                                        Manual
                                                    {{/if}}
                                                {{/ifEquals}}
                                            </td>
                                            <td style="text-transform: capitalize;">
                                                {{#ifEquals type 'natural' }}
                                                    {{electronicIdInfo.status}}
                                                {{/ifEquals}}
                                            </td>
                                            <td class="d-flex justify-content-end">
                                                {{#ifEquals type 'natural' }}
                                                    {{#if electronicIdInfo.isElectronicId}}
                                                        <button
                                                                type="button"
                                                                data-incorporation-id="{{ ../companyIncorporation._id }}"
                                                                data-relation-id="{{ _id }}"
                                                                data-email="{{electronicIdInfo.email}}"
                                                                data-toggle="modal"
                                                                data-target="#resendElectronicInvitationModal"
                                                                class="btn btn-xs mr-1 solid royal-blue"
                                                        >
                                                            Request ID
                                                        </button>
                                                    {{/if}}
                                                {{/ifEquals}}
                                                <button
                                                        type="button"
                                                        data-incorporation-id="{{ ../companyIncorporation._id }}"
                                                        data-relation-id="{{ _id }}"
                                                        data-toggle="modal"
                                                        data-target="#showIncorporationRelationFilesModal"
                                                        class="btn btn-xs solid royal-blue"
                                                >
                                                    Files
                                                </button>
                                            </td>
                                        </tr>
                                    {{else}}
                                        <tr>
                                            <td colspan="8" class="text-center font-italic">
                                                There are no relations
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 text-right">
                            <a class="btn solid royal-blue width-lg waves-effect waves-light"
                               href="/client-incorporation/{{companyIncorporation._id}}/download-all-documents"
                               target="_blank">
                                <em class="fa fa-download mr-1" aria-hidden="true"> </em> DOWNLOAD ALL FILES
                            </a>
                        </div>
                    </div>
                    <br>

                    {{#ifEquals companyIncorporation.incorporationStatus 'APPROVED FOR INCORPORATION'}}
                        <form method="POST" id="approveIncorporationForm" class='enquiry' autocomplete="off">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="companyCode">Company code*</label>
                                        <input type="text"
                                               required
                                               name="companyCode"
                                               id="companyCode"
                                               class="form-control"
                                               form="approveIncorporationForm"
                                        />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="incorporationNr">Incorporation number*</label>
                                        <input type="text"
                                               required
                                               name="incorporationNr"
                                               id="incorporationNr"
                                               class="form-control"
                                               form="approveIncorporationForm"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <label>Please add the official incorporation documents in the box below.</label>
                                    <div id="officialUploadModal" class="dropzone">
                                        <div class="fallback">
                                            <input name="Document" type="file" multiple/>
                                        </div>
                                        <div class="dz-message needsclick">
                                            <i class="h1 text-muted dripicons-cloud-upload"></i>
                                            <h3>Drop files here or click to upload.</h3>
                                            <span class="text-muted font-13">Allowed filetypes: PDF,
                                                                JPG, PNG, GIF</span>
                                        </div>
                                    </div>
                                </div> <!-- end col -->
                            </div>
                        </form>
                    {{/ifEquals}}


                    <hr>
                    <!-- CARD BODY END -->
                    <!-- CARD FOOTER NAV -->
                    <div class="row mt-2">
                        <div class="d-flex col-md-12 justify-content-between">
                            <a href="/client-incorporation/list"
                               class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>

                            {{#if companyIncorporation.requestInformation}}
                                <button class="btn solid royal-blue width-lg waves-effect waves-light"
                                        data-toggle="modal"
                                        data-target="#showRequestedInformationModal"
                                        data-submission-id="{{companyIncorporation._id}}"

                                > Show requested information
                                </button>
                            {{/if}}

                            {{#unless readOnly}}
                                {{#ifEquals companyIncorporation.incorporationStatus 'APPROVED FOR INCORPORATION'}}
                                    <button class="btn btn-success  width-lg waves-effect waves-light"
                                            data-toggle="modal"
                                            data-target="#updateIncorporationStatusModal"
                                            data-status="approve"
                                            data-id="{{companyIncorporation._id}}"
                                    > Approve incorporation
                                    </button>
                                {{else}}
                                    <button class="btn btn-danger  width-lg waves-effect waves-light"
                                            data-toggle="modal"
                                            data-target="#updateIncorporationStatusModal"
                                            data-status="decline"
                                            data-id="{{companyIncorporation._id}}"
                                    > Decline
                                    </button>

                                    <button class="btn btn-warning  width-lg waves-effect waves-light"
                                            data-toggle="modal"
                                            data-target="#requestInformationModal"
                                            data-submission-id="{{companyIncorporation._id}}"

                                    > Request Information
                                    </button>

                                    {{#ifEquals companyIncorporation.nameReservationStatus 'APPROVED'}}
                                        <button class="btn btn-success  width-lg waves-effect waves-light"
                                                data-toggle="modal"
                                                data-target="#updateIncorporationStatusModal"
                                                data-status="approve-for-incorporation"
                                                data-id="{{companyIncorporation._id}}"
                                        > Approve
                                        </button>
                                    {{/ifEquals}}
                                {{/ifEquals}}
                            {{/unless}}

                            {{#if isNameReview}}
                                <button class="btn btn-success  width-lg waves-effect waves-light"
                                        data-toggle="modal"
                                        data-target="#approveNameReservationModal"
                                        data-status="approve"
                                        data-id="{{companyIncorporation._id}}"
                                > Approve Name
                                </button>

                            {{/if}}

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{>client-incorporation/modals/request-information-modal}}
    {{>client-incorporation/modals/show-requested-information-modal}}
    {{>client-incorporation/modals/update-status-modal}}
    {{>client-incorporation/modals/show-relation-files-modal}}
    {{>client-incorporation/modals/approve-name-modal}}
    {{>client-incorporation/modals/resend-electronic-invitation-modal}}
</main>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript">
    $('[data-toggle="tooltip"]').tooltip();

    $('input[required]').on('keyup', function () {
        const empty = $(this).val() === "";
        $(this).toggleClass("is-invalid", empty);
    });


    Dropzone.autoDiscover = false;
    $(function () {
        Dropzone.autoDiscover = false;
        const myDropZone = new Dropzone("#officialUploadModal", {
            url: "/",
            acceptedFiles: 'application/pdf,image/jpeg,image/png,image/gif',
            autoProcessQueue: true,
            parallelUploads: 1,
            maxFiles: 10,
            maxFilesize: 5,
            addRemoveLinks: true,
            paramName: function () {
                return 'Document';
            },
            uploadMultiple: true,
            init: function () {
                this.on("processing", function (file) {
                    this.options.url = window.location.pathname + "/upload-document";
                });
                this.on("success", function () {
                });
                this.on("sending", function (file, xhr, formData) {
                    $("#btnSubmit").prop('disabled', true);
                    if (!formData.has('filetype')) {
                        formData.append("filetype", '');
                    }

                    if (!formData.has('fieldGroup')) {
                        formData.append("fieldGroup", 'official-files');
                    }

                });

                this.on('resetFiles', function () {
                    if (this.files && this.files.length) {
                        for (let file of this.files) {
                            file.previewElement.remove();
                        }
                        this.files.length = 0;
                    }
                });

                this.on("removedfile", function (file) {
                    const name = file.name;
                    $.ajax({
                        type: 'POST',
                        url: window.location.pathname + "/delete-document",
                        data: {
                            name: name,
                            group: 'official-files'
                        },
                        success: function (data) {

                        }
                    });
                });
            }
        })

    })
</script>

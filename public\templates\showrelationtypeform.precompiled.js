(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['showrelationtypeform'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"relation-form-type\" id=\"naturalType\" style=\"display: none\">\r\n"
    + ((stack1 = container.invokePartial(lookupProperty(partials,"file-reviewer/review-relations/natural-form-component"),depth0,{"name":"file-reviewer/review-relations/natural-form-component","hash":{"relation":(depths[1] != null ? lookupProperty(depths[1],"relation") : depths[1])},"data":data,"indent":"        ","helpers":helpers,"partials":partials,"decorators":container.decorators})) != null ? stack1 : "")
    + "    </div>\r\n";
},"3":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"relation-form-type\" id=\"corporateType\" style=\"display: none\">\r\n"
    + ((stack1 = container.invokePartial(lookupProperty(partials,"file-reviewer/review-relations/corporate-form-component"),depth0,{"name":"file-reviewer/review-relations/corporate-form-component","hash":{"relation":(depths[1] != null ? lookupProperty(depths[1],"relation") : depths[1])},"data":data,"indent":"        ","helpers":helpers,"partials":partials,"decorators":container.decorators})) != null ? stack1 : "")
    + "    </div>\r\n";
},"5":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"relation-form-type\" id=\"foundationType\" style=\"display: none\">\r\n"
    + ((stack1 = container.invokePartial(lookupProperty(partials,"file-reviewer/review-relations/foundation-form-component"),depth0,{"name":"file-reviewer/review-relations/foundation-form-component","hash":{"relation":(depths[1] != null ? lookupProperty(depths[1],"relation") : depths[1])},"data":data,"indent":"        ","helpers":helpers,"partials":partials,"decorators":container.decorators})) != null ? stack1 : "")
    + "    </div>\r\n";
},"7":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"relation-form-type\" id=\"trustType\" style=\"display: none\">\r\n"
    + ((stack1 = container.invokePartial(lookupProperty(partials,"file-reviewer/review-relations/trust-form-component"),depth0,{"name":"file-reviewer/review-relations/trust-form-component","hash":{"relation":(depths[1] != null ? lookupProperty(depths[1],"relation") : depths[1])},"data":data,"indent":"        ","helpers":helpers,"partials":partials,"decorators":container.decorators})) != null ? stack1 : "")
    + "    </div>\r\n";
},"9":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"relation-form-type\" id=\"limitedPartnerShipType\" style=\"display: none\">\r\n"
    + ((stack1 = container.invokePartial(lookupProperty(partials,"file-reviewer/review-relations/limited-partnership-form-component"),depth0,{"name":"file-reviewer/review-relations/limited-partnership-form-component","hash":{"relation":(depths[1] != null ? lookupProperty(depths[1],"relation") : depths[1])},"data":data,"indent":"        ","helpers":helpers,"partials":partials,"decorators":container.decorators})) != null ? stack1 : "")
    + "    </div>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"formType") : depth0),"natural",{"name":"ifEquals","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":2,"column":0},"end":{"line":6,"column":13}}})) != null ? stack1 : "")
    + "\r\n\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"formType") : depth0),"corporate",{"name":"ifEquals","hash":{},"fn":container.program(3, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":9,"column":0},"end":{"line":13,"column":13}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"formType") : depth0),"foundation",{"name":"ifEquals","hash":{},"fn":container.program(5, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":15,"column":0},"end":{"line":19,"column":13}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"formType") : depth0),"trust",{"name":"ifEquals","hash":{},"fn":container.program(7, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":21,"column":0},"end":{"line":25,"column":13}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"formType") : depth0),"limited",{"name":"ifEquals","hash":{},"fn":container.program(9, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":27,"column":0},"end":{"line":31,"column":13}}})) != null ? stack1 : "")
    + "\r\n<!--WORLD CHECK DETAILS -->\r\n<div class=\"relation-form-type\" id=\"worldCheckSection\" style=\"display: none\" >\r\n"
    + ((stack1 = container.invokePartial(lookupProperty(partials,"file-reviewer/review-relations/sections/world-check-details-form"),depth0,{"name":"file-reviewer/review-relations/sections/world-check-details-form","hash":{"worldCheck":(lookupProperty(helpers,"ternary")||(depth0 && lookupProperty(depth0,"ternary"))||alias2).call(alias1,(depths[1] != null ? lookupProperty(depths[1],"newRelation") : depths[1]),((stack1 = ((stack1 = (depths[1] != null ? lookupProperty(depths[1],"relation") : depths[1])) != null ? lookupProperty(stack1,"naturalFiles") : stack1)) != null ? lookupProperty(stack1,"worldCheck") : stack1),((stack1 = (depths[1] != null ? lookupProperty(depths[1],"relation") : depths[1])) != null ? lookupProperty(stack1,"worldCheck") : stack1),{"name":"ternary","hash":{},"data":data,"loc":{"start":{"line":36,"column":23},"end":{"line":36,"column":106}}})},"data":data,"indent":"    ","helpers":helpers,"partials":partials,"decorators":container.decorators})) != null ? stack1 : "")
    + "</div>\r\n";
},"usePartial":true,"useData":true,"useDepths":true});
})();
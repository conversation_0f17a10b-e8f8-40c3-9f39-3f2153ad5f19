{{!-- UNASSIGN CLIENT MODAL --}}
<div class="modal fade" id="startNewElectronicRequestModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;"><strong>Send Electronic Id
                    Invitation</strong></h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <h4><strong>Are you sure?</strong></h4>
                    <span>This will cancel the current request and start a new electronic ID request.</span>
                </div>
                <br>
                <br>
                <form class="form" novalidate id="sendElectronicIdFileReviewForm">
                    <div class="row mt-2">
                        <div class="col-6">
                            <label>Would you like to change the e-mail address?*</label>
                        </div>
                        <div class="col-6 text-left">
                            <div class="custom-control custom-radio custom-control-inline">
                                <input
                                        type="radio"
                                        class="custom-control-input"
                                        id="changeEmailYes"
                                        name="changeEmail"
                                        value="YES"
                                        required
                                />
                                <label class="custom-control-label" for="changeEmailYes">Yes</label>
                            </div>
                            <div class="custom-control custom-radio custom-control-inline">
                                <input
                                        type="radio"
                                        class="custom-control-input"
                                        id="changeEmailNo"
                                        name="changeEmail"
                                        checked
                                        value="NO"
                                />
                                <label class="custom-control-label" for="changeEmailNo">No</label>
                            </div>
                        </div>
                    </div>
                    <div class="row  mt-2" id="newEmailRow" style="display: none">
                        <div class="col-6">
                            <label for="newEmailAddress">New e-mail address*</label>
                        </div>
                        <div class="col-6">
                            <input name="newEmailAddress" id="newEmailAddress" type="email"
                                   class="form-control"/>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <label>Please select the template:</label>
                        </div>
                        <div class="col-6">
                            <div class="custom-control custom-radio">
                                <input
                                        type="radio"
                                        class="custom-control-input"
                                        id="firstTimeTemplate"
                                        name="emailTemplate"
                                        value="new-invitation-template" required
                                />
                                <label class="custom-control-label" for="firstTimeTemplate">New first time
                                    template</label>
                            </div>
                            <div class="custom-control custom-radio ">
                                <input
                                        type="radio"
                                        class="custom-control-input"
                                        id="incorrectInformationTemplate"
                                        name="emailTemplate"
                                        value="incorrect-information-template"
                                />
                                <label class="custom-control-label" for="incorrectInformationTemplate">Document
                                    incomplete/incorrect</label>
                            </div>
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="submit" form="sendElectronicIdFileReviewForm" class="btn solid royal-blue"
                        id="confirmNewRequestButton">
                    Confirm
                </button>
                <button id="loaderSending" class="btn  solid royal-blue" type="button" style="display: none" disabled>
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    Loading...
                </button>
            </div>
        </div>
    </div>
</div>

<script>

    let newElectronicRequestButton;

    $('#startNewElectronicRequestModal').on('show.bs.modal', function (event) {
        newElectronicRequestButton = $(event.relatedTarget); // Button that triggered the modal
    });

    $('#sendElectronicIdFileReviewForm input[type="email"]').on('keyup', function () {
        const valid_email = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        const val = $(this).val();
        const bad = !val.match(valid_email);
        $(this).toggleClass('is-invalid', bad);
    });


    $("#sendElectronicIdFileReviewForm input[name='changeEmail']").on('change', function () {
        const val = $(this).val();
        if (val === "YES") {
            $("#newEmailRow").show(200);
            $("#newEmailAddress").prop('required', true);
        } else {
            $("#newEmailRow").hide(200);
            $("#newEmailAddress").prop('required', false);
        }
    });

    $("#sendElectronicIdFileReviewForm").submit(function (event) {
        let invalidRadios = false;
        event.preventDefault();
        $("#loaderSending").show();


        $("#confirmNewRequestButton").hide();
        $('input[required]:visible').trigger('keyup');
        $("input[type='radio'][required]:visible").each(function () {
            const val = $('input[name="' + this.name + '"]:checked').val();
            if (val === undefined) {
                $('input[name="' + this.name + '"]').toggleClass("is-invalid", true);
                invalidRadios = true;
            } else {
                $('input[name="' + this.name + '"]').toggleClass("is-invalid", false);
            }
        });
        if ($(".is-invalid:visible").length === 0 && !invalidRadios) {
            const review = newElectronicRequestButton.data('review-id');
            const relationId = newElectronicRequestButton.data('relation-id');

            const formValues = {
               changeEmail: $("#sendElectronicIdFileReviewForm input[name='changeEmail']:checked").val() === "YES",
                email: "",
                emailType: $('input[name="electronicIdInfo[emailType]"]:checked').val(),
                emailTemplate: $("#incorrectInformationTemplate").val()
            };
            const newEmail = $("#sendElectronicIdFileReviewForm input[name='newEmailAddress']").val();
            if (formValues.changeEmail){
              formValues.email = newEmail
            }
            else{
              if(formValues.emailType === "default"){
                  formValues.email =  $('input[name="electronicIdInfo[defaultEmail]"]').val();
              }
              else{
                  formValues.email =  $('input[name="electronicIdInfo[email]"]').val();
              }
            }

            $.ajax({
                type: 'POST',
                url: '/file-reviewer/reviews/' + review + '/relations/' + relationId + '/send-electronic-invitation',
                data: formValues,
                success: (resp) => {
                  if (resp && resp.status === 200){
                      Swal.fire('Success', 'Electronic Id invitation has been send successfully', 'success').then(() => {
                          if ($("#sendElectronicIdFileReviewForm input[name='changeEmail']:checked").val() === "YES") {
                              $("#identification-email").val(newEmail);
                              $("#natural-send-to-free-email").prop('checked', true);
                              $("#natural-send-to-default").prop('checked', false);
                          }

                          $("#confirmNewRequestButton").show();
                          $("#loaderSending").hide();
                          $("#startNewElectronicRequestModal").modal('hide');
                      });
                  }
                  else{
                      $("#confirmNewRequestButton").show();
                      $("#loaderSending").hide();
                      Swal.fire('Error', resp.message, 'error');
                  }

                },
                error: (err) => {
                    $("#confirmNewRequestButton").show();
                    $("#loaderSending").hide();
                    Swal.fire('Error', 'There was an error sending the invitation', 'error');
                },
            });
        } else {
            $("#loaderSending").hide();
            $("#confirmNewRequestButton").show();
        }
    });

    $('#startNewElectronicRequestModal').on('hide.bs.modal', function (event) {
        $("#newEmailRow").hide(200);
        $("#newEmailAddress").prop('required', false);
        $("#sendElectronicIdFileReviewForm").trigger("reset");

    });


</script>

<!-- OPEN OWNER MODAL -->
<div class="modal fade" id="pickPositionModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modal-relation-title" class="modal-title">Relation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-relation-body" class="modal-body p-3">
                <form action="" class="form" id="pickForm">
                    <div class="row">
                        <label class="col-form-label col-sm-6 pt-0">Please select the position(s):</label>
                        <div class="col-sm-6">
                            <div class="custom-control custom-checkbox" id="founderGroup">
                                <input
                                        type="checkbox"
                                        class="custom-control-input"
                                        name="relationGroup[]"
                                        id="relationGroup1"
                                        value="founder"
                                />
                                <label class="custom-control-label" for="relationGroup1">Founder</label>
                            </div>
                            <div class="custom-control custom-checkbox" id="trusteeGroup">

                                <input
                                        type="checkbox"
                                        class="custom-control-input"
                                        name="relationGroup[]"
                                        id="relationGroup2"
                                        value="trustee"
                                />
                                <label class="custom-control-label" for="relationGroup2">Trustee</label>
                            </div>
                            <div class="custom-control custom-checkbox" id="councilGroup">

                                <input
                                        type="checkbox"
                                        class="custom-control-input"
                                        name="relationGroup[]"
                                        id="relationGroup3"
                                        value="council member"
                                />
                                <label class="custom-control-label" for="relationGroup3">Council Member</label>
                                <div class="invalid-feedback">
                                    You must select at least one position.
                                </div>
                            </div>
                            <div class="custom-control custom-checkbox" id="protectorGroup">

                                <input
                                        type="checkbox"
                                        class="custom-control-input"
                                        name="relationGroup[]"
                                        id="relationGroup4"
                                        value="protector"
                                />
                                <label class="custom-control-label" for="relationGroup4">Protector</label>
                                <div class="invalid-feedback">
                                    You must select at least one position.
                                </div>
                            </div>

                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

                <button class="btn solid royal-blue" id="submitPickPosition" type="submit" form="pickForm" name="submit" value="Submit">Save</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    let reviewId;
    let positionId;
    let relationType;
    let organizationId;

    $('#pickPositionModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        reviewId = button.data('review-id');
        positionId = button.data('position-id');
        organizationId = button.data('organization-id');
        relationType = button.data('type');
        const positionGroups =  button.data('position-groups');

        const founderCheck =  $('#founderGroup');
        const trusteeCheck =  $('#trusteeGroup');
        const councilCheck =  $('#councilGroup');
        const protectorCheck =  $('#protectorGroup');

        founderCheck.show();
        trusteeCheck.show();
        councilCheck.show();
        protectorCheck.show();

        if (positionGroups){
            if (positionGroups.includes("founder")){
                founderCheck.hide();
            }

            if (positionGroups.includes("trustee")){
                trusteeCheck.hide();
            }

            if (positionGroups.includes("council member")){
                councilCheck.hide();
            }

            if (positionGroups.includes("protector")){
                protectorCheck.hide();
            }
        }

    });

    $('input[name="relationGroup[]"]').on('change', function () {
        if ($('input[name="relationGroup[]"]:checked').length > 0) {
            $('input[name="relationGroup[]"]').removeClass('is-invalid')
        }
    });

    $('#pickForm').submit( function (event) {
        const submitBtn = $("#submitPickPosition");
        submitBtn.prop('disabled', true);
        event.preventDefault();

        if ($('input[name="relationGroup[]"]:checked').length === 0) {
            $('input[name="relationGroup[]"]').addClass("is-invalid");
            setTimeout(function(){submitBtn.prop('disabled', false); }, 0);
            return false;
        }

        // existing
        $.ajax({
            url: '/file-reviewer/reviews/' + reviewId +'/organizations/'+ organizationId+ '/positions/' + positionId,
            type: 'POST',
            timeout: 2000,
            data: $(this).serialize(),
            success: function () {
                location.href = '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
            },
            error: function () {
                Swal.fire('Error', 'There was an error creating the beneficial owner', 'error').then(
                        () => {
                            location.href = '/file-reviewer/open-file-review/' + reviewId + '/beneficial-owners';
                        }
                );
            },
        });
        return false;
    });


</script>

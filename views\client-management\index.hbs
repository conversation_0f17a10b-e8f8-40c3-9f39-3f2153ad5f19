<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h1>{{title}}</h1>
                        <p>Welcome {{user.name}}</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {{#ifCond authentication.isClientManagementSuperUser '||' authentication.isSubsSuperUser}}                            
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <h5 class="card-title text-white">Master Clients</h5>
                                            <a href="/client-management/invitations"
                                                class="btn btn-light btn-sm waves-effect">Invite
                                                Master Clients</a>
                                        </div>
                                    </div>
                                </div>
                            {{/ifCond}}
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Master Clients</h5>
                                        <a href="/client-management/search-masterclients"
                                            class="btn btn-light btn-sm waves-effect">Search Master Clients</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Companies</h5>
                                        <a href="/client-management/search-companies"
                                            class="btn btn-light btn-sm waves-effect">Search Companies</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Companies</h5>
                                        <a href="/client-management/unapproved-companies" class="btn btn-light btn-sm waves-effect">Approve Companies</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Users</h5>
                                        <a href="/client-management/search-users"
                                            class="btn btn-light btn-sm waves-effect">Search
                                            Users</a>
                                    </div>
                                </div>
                            </div>
                            {{#if authentication.isClientManagementSuperUser}}
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Import</h5>
                                        <a href="/client-management/import"
                                            class="btn btn-light btn-sm waves-effect">Import
                                            entities data</a>
                                    </div>
                                </div>
                            </div>                            
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Delete</h5>
                                        <a href="/client-management/delete-companies"
                                            class="btn btn-light btn-sm waves-effect">Inactivate Modules</a>
                                    </div>
                                </div>
                            </div>  
                            {{/if}}
                            {{#ifCond authentication.isClientManagementSuperUser '||' authentication.isSubsSuperUser}}
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Finance</h5>
                                        <a href="/client-management/report-invoices"
                                            class="btn btn-light btn-sm waves-effect">Export Invoices</a>
                                    </div>
                                </div>
                            </div>
                            {{/ifCond}}                          
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Export</h5>
                                        <a href="/client-management/export-incorporations"
                                           class="btn btn-light btn-sm waves-effect">Export Incorporations</a>
                                    </div>
                                </div>
                            </div>

                            {{#if authentication.isAnnouncementManager}}
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <h5 class="card-title text-white">Communication</h5>
                                            <a href="/client-management/announcements"
                                               class="btn btn-light btn-sm waves-effect">New Announcement</a>
                                        </div>
                                    </div>
                                </div>

                                {{#if canStartMoveFilesProcess}}
                                    <div class="col-md-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body">
                                                <h5 class="card-title text-white">Run APP</h5>
                                                <button  id="startMoveMccFilesBtn"
                                                         class="btn btn-light btn-sm waves-effect">Move MCC Files</button>
                                            </div>
                                        </div>
                                    </div>

                                {{/if}}

                            {{/if}}
                        </div>


                        <div class="row mt-2">
                            <div class="col-md-2">
                                <a href="/" class="btn solid royal-blue w-100">
                                    Back
                                </a>
                            </div>
                        </div>

                    </div>


                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript">

    $("#startMoveMccFilesBtn").on('click', function (event) {
        $(this).prop('disabled', true);

        Swal.fire({
            title: 'Are you sure?',
            text: "This will start a process to move MCC files.",
            type: 'warning',
            backdrop: true,
            showCancelButton: true,
            cancelButtonColor: "#6c757d",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes, do it!',
            reverseButtons: true,
            showLoaderOnConfirm: true,
            preConfirm(inputValue) {
                return fetch('/client-management/move-mcc-files', {method: 'POST'})
                        .then(response => {
                            try {
                                return response.json()
                            } catch (e) {
                                throw new Error(response.statusText)
                            }

                        })
                        .catch(error => {
                            console.log(error);
                            return {status: 500, error: error}

                        });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.value) {
                swal.showLoading();
                if (result.value.status === 200) {
                    Swal.fire('Success','The process has been started successfully.', 'success').then(() => {
                        location.reload();
                    });
                } else if (result.value.status === 400) {
                    Swal.fire('Error', result.value.error, 'error').then(() => {
                        $("#startMoveMccFilesBtn").prop('disabled', false);
                    })
                } else {
                    Swal.fire('Error', 'There was an error starting the process.', 'error').then(() => {
                        $("#startMoveMccFilesBtn").prop('disabled', false);
                    });
                }
            }
            if (result.dismiss=== 'cancel'){
                $("#startMoveMccFilesBtn").prop('disabled', false);
            }

        });
    })

</script>
